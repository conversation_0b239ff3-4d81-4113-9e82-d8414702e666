<?php

/**
 * PHPUnit実行方法
 *
 * pwd
 * /home/<USER>/developer-freegames
 *
 * php ./vendor/bin/phpunit -c phpunit.xml ./tests/Services/ApplyNotificationServiceTest.php
 */

namespace App\Services;

class ApplyNotificationServiceTest extends \TestCase
{
    /**
     * @test
     * getNotificationTest
     *
     * @return void
     * @dataProvider getNotificationTestCase
     */
    public function getNotificationTest($appId, $device){
        $service = $this->app[ApplyNotificationService::class];

        $result = $service->getNotificationAddress($appId, $device);
    }

    public function getNotificationTestCase(){
        // config利用
        $this->createApplication();
        
        return [
            [
                'appId' => '1',
                'device' => 'pc',
            ]
        ];
    }

    /**
     * @test
     * notificationTest
     *
     * @return void
     * @dataProvider notificationTestCase
     */
    public function notificationTest(
        $id, $device, $address
    ){
        $service = $this->app[ApplyNotificationService::class];
        $result = $service->updateNotificationAddress(
            $id, $device, $address
        );
        $this->assertEquals($result['addressList'],$address);
    }

    public function notificationTestCase(){
        // config利用
        $this->createApplication();
        
        return [
            [
                'id' => '1',
                'device' => 'pc',
                'address' => 'a@b'
            ],
            [
                'id' => '1',
                'device' => 'pc',
                'address' => "a@b\naa@bb\naaa@bbb.c"
            ]
        ];
    }
}