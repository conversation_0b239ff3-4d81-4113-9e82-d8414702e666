<?php

namespace App\Services;

use GuzzleHttp\Client as HttpClient;
use Mockery;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use TestCase;

/**
 * phpunit テスト実行方法(developer-freegamesコンテナ上で実行)
 *
 *  * pwd
 * /home/<USER>/developer-freegames
 *
 * ./vendor/bin/phpunit -c phpunit.xml ./tests/Services/ApplyExaminationFileApiServiceTest.php
 */
class ApplyExaminationFileApiServiceTest extends TestCase
{
    /**
     * アップロードした審査ファイル情報の取得APIテスト
     *
     * @test
     * @param integer $appId アプリID
     * @param string $kind アプリ種別
     * @param $expected
     * @dataProvider getExaminationFileInfoTestTestCase
     */
    public function getExaminationFileInfoTest($appId, $kind, $expected)
    {
        // Arrange
        $examinationFileApiService = $this->app[ApplyExaminationFileApiService::class];

        // Act
        $result = $examinationFileApiService->getExaminationFileInfo($appId, $kind);

        // Assert
        // MEMO: Uematsu 2024/10/07 現在はAPIの疎通確認のみ
        $this->assertEquals($result['status'], $expected);
    }

    /**
     * テストケース: アプリ内定期購入アイテムの一覧取得
     *
     * @return array
     */
    public function getExaminationFileInfoTestTestCase()
    {
        // config利用
        $this->createApplication();

        return [
            [
                'appId' => '1',
                'kind' => 'application',
                'expected' => 200
            ],
        ];
    }
}
