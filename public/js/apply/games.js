/**
 * 申請共通処理
 */
$(function () {
    const $forms = $('.fn-form-check');
    const InitialFormValues = {};

    /**
     * 画面描画時実行処理
     */
    function _init() {
        _setupHashScroll();
        _setupRadioInput();
        _setupApplyStatus();
        _saveFormValues();
    }

    /**
     * ページが非表示になった時のイベント
     */
    $(document).on('visibilitychange', function () {
        if (document.hidden) {
            // ページ非表示（リロードまたはタブの移動）のイベントが発生したらハッシュにスクロール位置を記憶する
            const currentPosition = Math.round(window.scrollY);
            history.replaceState(null, null, `#${ currentPosition }`);
            $(window).off('beforeunload');
        } else {
            // ページが再表示のイベントが発生したらリロードを行う
            // CSRFトークンの有効確認を実施する無効であれば、リロードを実施
            $.ajax({
                url: '/games/apply/preregistration/checkCsrf',
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf_token"]').attr('content') // メタタグから取得
                },
                error: function (xhr) {
                    location.reload();
                }
            });
        }
    });

    /**
     * フォームに何か入力されたら、ページ遷移時に警告ポップアップを表示させるイベントを登録する
     */
    $(window).on('beforeunload', (event) => {
        if (_isFormChanged()) {
            return "確認";
        }
    });

    /**
     * 別画面表示ボタン処理
     */
    $('.fn-openwindowButton').on('click', function (event) {
        const url = $(this).attr('href');
        if (!_isFormChanged()) {
            window.open(url);
            return;
        }

        const result = window.confirm(
            '別画面を表示します。審査に提出していないデータは消える可能性がありますが、よろしいですか？'
        );

        if (result) {
            window.open(url);
        } else {
            event.preventDefault();
        }
    });

    /**
     * デバイスセレクタを変更した際の処理
     */
    $('#devicecelector-select').on('change', function () {
        const device = $(this).val();

        window.location.href = $(this).data('url') + "&device=" + device;
    });

    /**
     * 審査ステータスを変更する(審査完了しているものは変更されない)
     * @param { string } name 審査項目名
     * @param { string } status 審査ステータス
     * @return { boolean }
     */
    function _changeApplyStatus(name, status) {
        const lastStatus = _getApplyStatus(name)
        // 審査完了している場合は、ステータス変更を受け付けない
        if (status != lastStatus && (lastStatus == 'examination_completed' || lastStatus == 'skip')) {
            return false;
        }

        // ステータスアイコンの切り替え
        const statusClassName = ".fn-" + name + "-status";
        const statusClassElement = $(statusClassName);
        statusClassElement.removeClass('fn-' + lastStatus);
        $(statusClassName + " .fn-applay-status").each(function (i, elem) {
            if ($(elem).hasClass('fn-' + status)) {
                $(elem).css("display", "inline-block");
            } else {
                $(elem).css("display", "none");
            }
        });

        statusClassElement.attr('data-status', status);
        // ステータスによる入力の有効化と無効化
        const inputClassName = ".fn-" + name + "-input";
        switch (status) {
            case 'unapplied':
                $(inputClassName).css("pointer-events", "auto");
                $(inputClassName + " :input").css({ "background-color": "", "color": "" });
                break;
            case 'awaiting_examination':
                $(inputClassName).css("pointer-events", "none");
                $(inputClassName + " .button").addClass("is-disabled");
                $(inputClassName + " :input").css({ "background-color": "#ccc", "color": "#666" });
                break;
            case 'inexamination':
                $(inputClassName).css("pointer-events", "none");
                $(inputClassName + " .button").addClass("is-disabled");
                $(inputClassName + " :input").css({ "background-color": "#ccc", "color": "#666" });
                break;
            case 'examination_completed':
                $(inputClassName).css("pointer-events", "none");
                $(inputClassName + " .button").addClass("is-disabled");
                $(inputClassName + " :input").css({ "background-color": "#ccc", "color": "#666" });
                break;
            case 'skip':
                $(inputClassName).css("pointer-events", "none");
                $(inputClassName + " .button").addClass("is-disabled");
                $(inputClassName + " :input").css({ "background-color": "#ccc", "color": "#666" });
                $(inputClassName + " :input").attr('disabled', 'disabled');
                break;
            case 'reject':
                $(inputClassName).css("pointer-events", "auto");
                $(inputClassName + " :input").css({ "background-color": "", "color": "" });
                break;
            default:
                $(inputClassName).css("pointer-events", "none");
                $(inputClassName + " .button").addClass("is-disabled");
                $(inputClassName + " :input").css({ "background-color": "#ccc", "color": "#666" });
                break;
        }

        return true;
    }

    /**
     * 設定されているステータスを取得する
     * @param { string } name 審査項目名
     * @return { string }
     */
    function _getApplyStatus(name) {
        return $(".fn-" + name + "-status").data('status');
    }

    /**
     * 審査に提出する
     * @param { string } id 審査提出フォームのid
     * @param { string[] } items 審査対象の項目名
     * @return { {sendApply: sendApply} }
     */
    function _applyer(id, items) {
        const sendApply = function () {
            const $formData = $('#' + id + '-form');
            const $errorElement = $('#' + id + '-errors');
            $('#' + id).addClass('is-disabled');

            $.ajax({
                type: 'post',
                url: $formData.attr('action'),
                dataType: 'json',
                data: $formData.serializeArray(),
            }).done(function (resData) {
                // 成功時の処理
                $errorElement.css("display", "none");
                // エラー箇所のハイライトのリセット
                $formData.find('th, td').removeClass('error');
                // 全てのアイテムを審査中に変更する
                _setApplyStatusIcon(id, items, 'awaiting_examination');
            }).fail(function (resData) {
                // エラー時の処理
                $('#' + id).removeClass('is-disabled');
                $errorElement.css("display", "block");

                // 古いエラーメッセージの削除
                $errorElement.find('.fn-errorMsg').remove();
                // エラー箇所のハイライトのリセット
                $formData.find('th, td').removeClass('error');

                const errors = JSON.parse(resData.responseText).errors;

                // エラー箇所のハイライト
                const $sectionElements = $formData.find('tr');
                if ($sectionElements.length > 0) {
                    $sectionElements.each(function () {
                        const errorsKeysData = $(this).data('errors-keys');
                        if (errorsKeysData === undefined) {
                            return;
                        }

                        const errorsKeys = errorsKeysData.split(',');
                        errorsKeys.forEach((errorsKey) => {
                            if (errors.hasOwnProperty(errorsKey)) {
                                $(this).find('> th, > td').addClass('error');
                            }
                        });
                    });
                }

                // エラーメッセージの表示
                for (const [key, value] of Object.entries(errors)) {
                    value.forEach(errorMsg => {
                        $errorElement.append(`<p class="fn-errorMsg">${ errorMsg }</p>`);
                    });
                }
            });
        };

        return {
            sendApply: sendApply
        };
    }

    /**
     * 審査に提出イベント設定と審査ステータス制御をまとめて行う
     * @param { string } id 審査提出フォームのid
     * @param { string[] } items 審査対象の項目名
     */
    function _setApplyEventAndStatus(id, items) {
        // 審査に提出イベントの設定
        $('#' + id).on('click', _applyer(id, items).sendApply);

        // ステータス制御
        _setApplyStatusIcon(id, items, null);
    }

    /**
     * 申請ステータスアイコンの再読み込み
     * @param { string } id 審査提出フォームのid
     * @param { string[] } items 審査対象の項目名
     * @param { string | null } status 審査項目ステータス
     */
    function _setApplyStatusIcon(id, items, status) {
        let isApplyNeeded = false; // 申請の必要な項目があるか
        for (const item of items) {
            let applyStatus = null;
            if (status == null) {
                applyStatus = _getApplyStatus(item);
            } else {
                applyStatus = status;
            }

            _changeApplyStatus(item, applyStatus);
            if (!isApplyNeeded) {
                switch (applyStatus) {
                    case 'unapplied':
                        isApplyNeeded = true;
                        break;
                    case 'awaiting_examination':
                        isApplyNeeded = false;
                        break;
                    case 'inexamination':
                        isApplyNeeded = false;
                        break;
                    case 'examination_completed':
                        isApplyNeeded = false;
                        break;
                    case 'skip':
                        isApplyNeeded = false;
                        break;
                    case 'reject':
                        isApplyNeeded = true;
                        break;
                    default:
                        isApplyNeeded = false;
                        break;
                }
            }
        }

        // 申請の必要な項目がない場合は、審査に提出するボタンを押下できない様にする
        if (!isApplyNeeded) {
            $('#' + id).addClass('is-disabled');
        }else{
            $('#' + id).removeClass('is-disabled');
        }
    }

    /**
     * ラジオボタンとテキストを連動させる
     * @param { string } radioName ラジオボタンのid
     * @param { string } textName 連動させるinputのname
     */
    function _linkedRadioAndText(radioName, textName) {
        $('#' + radioName + '_false').each(function (index, elem) {
            const $radioFalse = $(this);
            const $radioTrue = $('#' + radioName + '_true');
            const $textarea = $('[name=' + textName + ']');

            // 初期制御
            if ($radioFalse.is(':checked')) {
                $textarea.prop('disabled', true);
            }
            // 変更時制御
            $radioFalse.on('change', function () {
                if ($(this).is(':checked')) {
                    $textarea.prop('disabled', true);
                }
            });
            $radioTrue.on('change', function () {
                if ($(this).is(':checked')) {
                    $textarea.prop('disabled', false);
                }
            });
        });
    }

    /**
     * ラジオボタンとラジオボタンを連動させる
     * @param { string } radioName ラジオボタンのid
     * @param { string } linkedRadioName 連動させるラジオボタンのid
     */
    function _linkedRadioAndRadio(radioName, linkedRadioName) {
        $('#' + radioName + '_false').each(function (index, elem) {
            const $radioFalse = $(this);
            const $radioTrue = $('#' + radioName + '_true');
            const serviceRadio = $('input[name=' + linkedRadioName + ']');

            // 初期制御
            if ($radioFalse.is(':checked')) {
                serviceRadio.each(function (index, elem) {
                    $(elem).prop('disabled', true);
                    $(elem).prop('checked', false);
                });
            }
            // 変更時制御
            $radioFalse.on('change', function () {
                if ($(this).is(':checked')) {
                    serviceRadio.each(function (index, elem) {
                        $(elem).prop('disabled', true);
                        $(elem).prop('checked', false);
                    });
                }
            });
            $radioTrue.on('change', function () {
                if ($(this).is(':checked')) {
                    serviceRadio.each(function (index, elem) {
                        $(elem).prop('disabled', false);
                        $(elem).prop('checked', false);
                    });
                }
            });
        });
    }

    /**
     * URLのハッシュに保存されているスクロール位置を使って画面をスクロールさせる
     */
    function _setupHashScroll() {
        if (location.hash) {
            const scrollPosition = location.hash.slice(1);
            if (scrollPosition) {
                window.scrollTo(0, parseInt(scrollPosition, 10));
                location.hash = "";
            }
        }
    }

    /**
     * ラジオボタンと紐付くテキストエリアorラジオボタンの制御
     */
    function _setupRadioInput() {
        // ゲーム紹介ページのキャッチコピー/イメージテキストのラジオボタンとテキストを連動させる
        _linkedRadioAndText('catchphrase_image', 'catchphrase_image_text');
        // ゲーム紹介ページのキャラクターの優先度/注意事項の有無のラジオボタンとテキストを連動させる
        _linkedRadioAndText('character_priority_notes', 'character_priority_notes_text');
        // ゲーム紹介ページのコピーライトのラジオボタンとテキストを連動させる
        _linkedRadioAndText('copyright', 'copyright_text');
        // 動作検証のInspectionAPIの利用とInspectionAPIの確認方法を連動させる
        _linkedRadioAndText('using_inspection_api', 'inspection_api_verification_method');
        // Linksmateののラジオボタンとテキストを連動させる
        _linkedRadioAndText('linksmate_copyright', 'linksmate_copyright_text');
        // ゲーム内に以下の機能があるかの条件に該当する取引機能とゲームチップを導入の確認方法を連動させる
        _linkedRadioAndRadio('has_trade_feature_in_game', 'has_gamechip');
        // 動作検証のDMMポイントで購入できる仮想通貨と仮想通貨の実装方法を連動させる
        _linkedRadioAndRadio('in_game_virtual_currency', 'is_following_terms_of_service');
    }

    /**
     * 申請ステータスに関連する設定を行う
     */
    function _setupApplyStatus() {
        // 審査用の画像
        _setApplyEventAndStatus('examinationimages', [
            'similarityCheckMaterials',
            'ethicalCheckMaterials'
        ]);

        // ゲーム紹介ページ：デザイン部分素材
        _setApplyEventAndStatus('introductionimages', [
            'characterImages',
            'titleLogoImages',
            'backgroundImages',
            'screenshotImageDiagram',
            'catchphraseImage',
            'isCharacterPriorityNotes',
            'copyright'
        ]);

        // プラットフォーム上に掲載される画像
        _setApplyEventAndStatus('platformimages', [
            'thumbnailImage',
            'preReleaseSmallBanner',
            'preReleaseBigBanner',
            'releaseScheduleSeason',
            'gameIntroductionImage',
            'overallRatedThumbnailImage',
            'android192x192',
            'android512x512',
            'appleTouchIcon',
            'gameIntroductionMovie',
            'gameIntroductionThumbnail',
            'onlineGameThumbnail'
        ]);

        // 公式サイト検証
        _setApplyEventAndStatus('releasesite', [
            'designDelivery',
            'createdOfficialSite',
            'meansVerification',
            'officialSiteDetail'
        ]);

        // サンドボックス環境 事前登録検証
        _setApplyEventAndStatus('sandboxverification', [
            'sandboxPreRegistrationVerifications',
            'sandboxTestGameAppId'
        ]);

        // 動作検証
        _setApplyEventAndStatus('verification', [
            'preRegistrationVerifications',
            'productionVerification',
            'gameStartUrl',
            'apkUpload',
            'otherWorksUsedAndUnMoralityExpressionUnUsed',
            'followingTermsDevelop',
            'followingTermsManagement',
            'gameVirtualCurrency',
            'implementsVirtualCurrency',
            'usedInspectionApi',
            'inspectionApiVerification',
            'gamesTags',
            'digitalSignature',
            'externalInstaller',
            'module',
            'tradeFeatureInGame',
            'ngwordVersion',
            'ngwordCheck',
            'isMonthlyPaymentServiceOrSubscription'
        ]);

        // ゲーム情報入力
        _setApplyEventAndStatus('gameinformation', [
            'contactMailAddress',
            'gameIntroduction',
            'gameIntroductionDetail',
            'channelingGameIntroduction',
            'supportedDevice',
            'preReleaseGameAnnouncement',
            'recommendationAgeDivision',
            'clientGameIntroduction',
            'taxIncludedPrice',
            'taxExcludedPrice'
        ]);

        // コミュニティ作成
        _setApplyEventAndStatus('community', [
            'communityCreate',
            'topicCreate'
        ]);

        // Linksmate
        _setApplyEventAndStatus('linksmate', [
            'linksmateTitleLogoImages',
            'linksmateCopyright'
        ]);

        // 事前登録用の公式サイト
        _setApplyEventAndStatus('preregistrationsite', [
            'designDelivery',
            'createdOfficialSite',
            'serverDeploy',
            'meansVerification',
            'officialSiteDetail'
        ]);

        // Win対応環境(macと区別しないとバグるのでフィールド名にwin-を付与)
        _setApplyEventAndStatus('windowsSupportedEnvironment', [
            'win-osVersion',
            'win-processor',
            'win-memory',
            'win-graphics',
            'win-diskFreeSpace',
            'win-note'
        ]);

        // Mac対応環境(winと区別しないとバグるのでフィールド名にmac-を付与)
        _setApplyEventAndStatus('macSupportedEnvironment', [
            'mac-osVersion',
            'mac-processor',
            'mac-memory',
            'mac-graphics',
            'mac-diskFreeSpace',
            'mac-note'
        ]);

        // CERO
        _setApplyEventAndStatus('cero', [
            'classification',
            'contentIcons'
        ]);
    }

    /**
     * 画面のフォームの初期値を保存する
     */
    function _saveFormValues() {
        $forms.each(function (index, form) {
            const $form = $(form);
            InitialFormValues[$form.attr('id')] = $form.serializeArray();
        });
    }

    /**
     * フォームが変更されているかどうかを判定する
     * @returns { boolean }
     */
    function _isFormChanged() {
        let isChanged = false;

        $forms.each(function (index, form) {
            const $form = $(form);
            const currentValues = $form.serializeArray();
            const initialValues = InitialFormValues[$form.attr('id')];

            // InitialFormValuesに記録されていないidがある、または要素数が異なる場合は変更があるとみなす
            if (!initialValues || currentValues.length !== initialValues.length) {
                isChanged = true;
                return;
            }

            // currentValuesとinitialValuesの各要素を比較
            for (let i = 0; i < currentValues.length; i++) {
                if (currentValues[i].name !== initialValues[i].name ||
                    currentValues[i].value !== initialValues[i].value) {
                    isChanged = true;
                    return;
                }
            }
        });

        return isChanged;
    }

    _init();
});