#fancybox-content,
#fancybox-inner {
    background: transparent !important;
    border: none !important;
}

.board-edit-modal {
    width: 100%;
    max-height: 80vh;
    height: auto;
    display: flex;
    flex-direction: column;
}

.board-edit-modal__content {
    width: 100%;
    max-width: 920px;
    max-height: 80vh;
    background-color: #ffffff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    padding: 30px 20px;
    overflow: hidden;
    position: relative;
    border-radius: 0;
    box-sizing: border-box;
    margin: 0 auto;
}

.board-edit-modal__header {
    margin-bottom: 20px;
}

.board-edit-modal__header-title {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 0;
}

.board-edit-modal__header-title span {
    font-size: 21px;
    font-weight: 600;
    color: #4c677a;
}

.board-edit-modal__header-line {
    width: 100%;
    height: 5px;
    background-color: #4c677a;
    margin-top: 10px;
}

.board-edit-modal__input-section {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
}

.board-edit-modal__form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
}

.board-edit-modal__error {
    color: #dc3545;
    background-color: #fdecea;
    border: 1px solid #f5c2c7;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 13px;
}

.board-edit-modal__error.d-none {
    display: none;
}

.board-edit-modal__input-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
}

.board-edit-modal__input-container {
    position: relative;
    padding: 5px 6px;
    max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.board-edit-modal__textarea {
    display: block;
    width: 100%;
    border: none;
    background: transparent;
    resize: vertical;
    font: inherit;
    line-height: 1.5;
    color: inherit;
    box-sizing: border-box;
    overflow: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.board-edit-modal__textarea:focus {
    outline: none;
}

.board-edit-modal__textarea::-webkit-scrollbar {
    display: none;
}

.board-edit-modal__attachments {
    display: none;
}

.board-edit-modal__attachments.is-visible {
    display: flex;
    flex-direction: column;
}

.board-edit-modal__attachments.is-visible .attachment-display {
    max-height: 120px;
    overflow-y: auto;
    padding-right: 4px;
}

.board-edit-modal__button-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.board-edit-modal__left-buttons {
    display: flex;
    gap: 10px;
}

.board-edit-modal__right-buttons {
    display: flex;
    gap: 12px;
}

.board-edit-modal__button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 6px 20px;
    border-radius: 4px;
    border: 1px solid #cccccc;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    min-width: 40px;
    transition: all 0.2s ease;
}

.board-edit-modal__button i {
    font-size: 14px;
}

.board-edit-modal__button--secondary {
    background: linear-gradient(to bottom, #ffffff, #f3f3f3);
    color: #666666;
}

.board-edit-modal__button--secondary:hover {
    background: linear-gradient(to bottom, #f8f8f8, #e8e8e8);
}

.board-edit-modal__button--primary {
    background: linear-gradient(to bottom, #4c677a, #405b6e);
    border-color: #3d617a;
    color: white;
    font-weight: 700;
}

.board-edit-modal__button--primary:hover {
    background: linear-gradient(to bottom, #405b6e, #3a5466);
}

.board-edit-modal__button[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

@media (max-width: 1024px) {
    .board-edit-modal__content {
        width: 90vw;
        max-width: 920px;
    }
}

@media (max-width: 768px) {
    .board-edit-modal__content {
        width: 95vw;
        padding: 20px 15px;
    }

    .board-edit-modal__button-section {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .board-edit-modal__left-buttons,
    .board-edit-modal__right-buttons {
        justify-content: center;
    }

    .board-edit-modal__button {
        flex: 1;
        min-width: auto;
    }
}

/* FancyBox modal height constraints */
#fancybox-wrap,
#fancybox-outer,
#fancybox-inner,
#fancybox-content {
    max-height: 80vh;
}

#fancybox-content {
    height: auto !important;
}

@media (max-width: 480px) {
    .board-edit-modal__header-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .board-edit-modal__left-buttons,
    .board-edit-modal__right-buttons {
        flex-direction: column;
        gap: 8px;
    }
}

/* 既存の投稿 */
.existing-post {
    margin-bottom: 20px;
}
 
.post-item {
    background-color: #fafafa;
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
    padding: 10px;
}
 
.post-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}
 
.post-number {
    font-size: 14px;
    color: #333333;
    font-weight: 400;
}
 
.post-header i {
    font-size: 14px;
    color: #333333;
}
 
.post-author {
    font-size: 14px;
    color: #333333;
    font-weight: 400;
}
 
.post-date {
    font-size: 14px;
    color: #999;
    font-weight: 400;
}
 
.post-content {
    margin-bottom: 10px;
}
 
.post-content p {
    font-size: 14px;
    color: #333333;
    line-height: 1.4;
}

#replyPreview {
    flex: 1;
    max-height: 120px;
    overflow-y: auto;
    padding-right: 4px;
}

.post-attachments-container {
    display: flex;/*縦並びにする際は消してください*/
    flex-wrap: wrap;
    gap: 15px;
}
 
.post-attachment {
    display: flex;
    align-items: center;
    gap: 5px;
}
 
.post-attachment i {
    font-size: 12px;
    color: #777777;
}
 
.post-attachment span {
    font-size: 12px;
    color: #005fc0;
}
