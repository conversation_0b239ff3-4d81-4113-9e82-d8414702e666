.comment-action-container {
    position: relative;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.comment-action-btn {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px 12px;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 32px;
    text-decoration: none;
}

.comment-action-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.comment-action-btn:active {
    background-color: #dee2e6;
    transform: scale(0.98);
}

.comment-post-btn {
    background-color: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.comment-post-btn:hover {
    background-color: #0056b3;
    border-color: #004085;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.comment-post-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.comment-post-btn i {
    margin-right: 8px;
}

/* コメントセクション */
.comments-section {
    margin-bottom: 20px;
}
 
.comments-header {
    margin-bottom: 10px;
}
 
.comments-header h3 {
    font-size: 14px;
    font-weight: 700;
    color: #333333;
    margin: 0;
}
 
.comments-content {
    background-color: #fafafa;
    padding: 20px;
    text-align: center;
}
 
.comments-content p {
    font-size: 14px;
    color: #333333;
    margin: 0;
    white-space: nowrap;
}

/* コメント投稿ボタン */
.comment-post-section {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}
 
.post-comment-btn {
    background: linear-gradient(to bottom, #4c677a, #405b6e);
    border: 1px solid #3d617a;
    color: white;
    padding: 14px 30px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    width: 200px;
}
 
.post-comment-btn:hover {
    background: linear-gradient(to bottom, #405b6e, #3a5465);
}

/* ピンアイコンのON/OFF切り替え */
.pin-icon {
    color: #777;
    transition: color 0.2s ease;
    cursor: pointer;
}
 
.pin-icon.off {
    color: #DCE1E4;
}
 
.pin-icon.on {
    color: #777;
}
.data-table tr td:nth-child(3) {
    width: 100%;
}
.data-table tr td a {
    display: block;
}
.data-table tr td:last-child {
    text-align: right;
    white-space: nowrap;
}
 
/* コメントセクション */
.comments-section {
    margin-bottom: 20px;
}
 
.comments-section h3 {
    font-size: 14px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 10px;
}
 
.comment-item {
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
    border-bottom: none;
    padding: 10px;
}
 
.comment-item:last-child {
    border-bottom: 1px solid #cccccc;
}
 
.comment-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    height: 21px;
}
 
.comment-header i {
    width: 14px;
    height: 14px;
    color: #333;
}
 
.comment-number {
    font-size: 14px;
    color: #333333;
}
 
.commenter {
    font-size: 14px;
    color: #333333;
}
 
.comment-date {
    font-size: 14px;
    color: #999;
}
 
.comment-status {
    font-size: 12px;
    color: #999;
}
 
.comment-content {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
    line-height: 1.4;
}
 
.comment-attachment {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-right: 15px;
    margin-bottom: 10px;
}
 
.comment-attachment i {
    width: 12px;
    height: 12px;
    color: #777;
}
 
.comment-attachment span {
    font-size: 12px;
    color: #268cbf;
}
 
.comment-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
 
.edit-btn,
.reply-btn {
    background: linear-gradient(to bottom, #ffffff, #f3f3f3);
    border: 1px solid #cccccc;
    color: #666666;
    padding: 6px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
}
 
.edit-btn:hover,
.reply-btn:hover {
    background: linear-gradient(to bottom, #f3f3f3, #e8e8e8);
}
 
.reply-summary {
    background-color: #fafafa;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 5px;
    margin-top: 10px;
}
 
.reply-summary i {
    width: 14px;
    height: 14px;
    color: #777;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    min-width: 14px;
}
 
.reply-summary span:first-of-type {
    font-size: 14px;
    color: #268cbf;
}
 
.reply-date {
    font-size: 12px;
    color: #666666;
}
 
/* コメント投稿ボタン */
.comment-post-section {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}
 
.post-comment-btn {
    background: linear-gradient(to bottom, #4c677a, #405b6e);
    border: 1px solid #3d617a;
    color: white;
    padding: 14px 30px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    width: 200px;
}
 
.post-comment-btn:hover {
    background: linear-gradient(to bottom, #405b6e, #3a5465);
}
 
 
/* 返信コメント用スタイル */
.reply-comments {
    margin-top: 10px;
    background-color: white;
    border-top: 1px solid #cccccc;
}
.reply-comments div:first-child {
    border-top: none;
}
.reply-comments div:last-child {
    border-bottom: none;
}
 
.reply-comment {
    border-top: 1px solid #cccccc;
    border-bottom: none;
    padding: 10px;
    margin-left: 30px;
    position: relative;
}
 
.reply-comment:last-child {
    border-bottom: 1px solid #cccccc;
}
 
/*
.reply-comment::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #999999;
}
    */
 
 
.reply-comment-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    height: 21px;
}
 
.reply-comment-header i {
    width: 14px;
    height: 14px;
    color: #777;
}
 
.reply-comment-header .comment-number {
    font-size: 14px;
    color: #333333;
}
 
.reply-comment-header .commenter {
    font-size: 14px;
    color: #333333;
}
 
.reply-comment-header .comment-date {
    font-size: 14px;
    color: #999;
}
 
.reply-comment-header .comment-status {
    font-size: 12px;
    color: #999;
}
 
.reply-comment-content {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
    line-height: 1.4;
}
 
.reply-comment-content p {
    margin: 0 0 5px 0;
}
 
/* 引用スタイル */
.quote-content {
    background-color: #fafafa;
    border-left: 2px solid #999999;
    padding: 10px 20px;
    margin: 10px 0;
    font-size: 14px;
    color: #333333;
    line-height: 1.4;
}
 
.reply-comment-attachment {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-right: 15px;
    margin-bottom: 10px;
}
 
.reply-comment-attachment i {
    width: 12px;
    height: 12px;
    color: #777;
}
 
.reply-comment-attachment span {
    font-size: 12px;
    color: #268cbf;
}
 
.reply-comment-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
 
/* 添付ファイル表示エリア */
.attachment-display {
    margin-top: 10px;
    display: flex;/*縦並びの時は削除してください*/
    flex-wrap: wrap;
    gap: 15px;
}
 
.attachment-item {
    display: flex;
    align-items: center;
    gap: 5px;
    /*縦並びの時に追加してください
    margin-bottom: 5px;
    */
}
 
.attachment-item i:first-child {
    font-size: 12px;
    color: #777777;
}
 
.attachment-item span {
    font-size: 12px;
    color: #268cbf;
    flex: 1;
}
 
/* ファイルリンクのスタイル */
.file-link {
    font-size: 12px;
    color: #268cbf;
    text-decoration: none;
    flex: 1;/*縦並びの時は削除してください*/
    transition: all 0.2s ease;
    cursor: pointer;
}
 
.file-link:hover {
    color: #268cbf;
    text-decoration: underline;
    transform: translateY(1px);
}
 
.file-link:active {
    color: #002a5c;
    transform: translateY(0);
}
 
.attachment-item i:last-child {
    font-size: 12px;
    color: #777777;
    cursor: pointer;
    position: relative;
     display: inline-flex;
     align-items: center;
     justify-content: center;
 }
 
 .attachment-item__remove {
     display: flex;
     align-items: center;
     justify-content: center;
     background: transparent;
     border: none;
}
 
.attachment-item i:last-child::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background-image: url('/images/board/fa-trash-o.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transition: filter 0.2s ease;
}
 
.attachment-item i:last-child:hover::before {
    filter: brightness(0) saturate(100%) invert(20%) sepia(100%) saturate(100%) hue-rotate(0deg) brightness(100%) contrast(100%);
}
 
/* 返信表示切り替えのアニメーション */
.reply-comments {
    transition: all 0.3s ease;
    overflow: hidden;
}
 
/* 返信サマリーのホバー効果 */
.reply-summary {
    cursor: pointer;
    transition: background-color 0.2s;
}
 
.reply-summary:hover {
    background-color: #f0f0f0;
}
 
/* 返信サマリーのアイコン回転 */
.reply-summary.expanded i {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}