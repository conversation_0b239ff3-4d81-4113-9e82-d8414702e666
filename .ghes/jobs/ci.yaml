container:
  image: stg-docker-registry.devops.dmmga.me/devops/games-ci/php-5.6-node14.x-mongodb-composer2:latest
  args: "--add-host=dev.gateway.dmm.com:*************"
  sidecars:
    - name: mysql56
      image: stg-docker-registry.devops.dmmga.me/devops/games-ci/mysql:5.6-ddl
    - name: redis32
      image: redis:3.2.1-alpine
    - name: memcached
      image: memcached:1.5-alpine
    - name: mongo26
      image: stg-docker-registry.devops.dmmga.me/devops/games-ci/mongodb:2.6
 
# 通知チャンネルが決まったら指定する
# Botの招待方法 : https://confl.arms.dmm.com/pages/viewpage.action?pageId=830584982
# notify:
#   channel: team-devops-notify-test
 
stages:
  - name: setup .env
    type: script
    script: |
      cp .env.ci .env
 
  - name: composer install
    type: script
    script: |
      composer install
 
  - name: unittest
    type: script
    script: |
      ./vendor/bin/phing
