//vim: set tabstop=4:softtabstop=4:shiftwidth=4:noexpandtab
 
node {
  // JenkinsによるブランチやPR検知を利用する為、リポジトリとブランチ名は指定しない。
  ExnoaPipelineYAML(pipelineYAML: ".ghes/jobs/ci.yaml")
 
  junit 'build/logs/junit.xml'
 
  // 以下は warnings-ng plugin を入れないと機能しないため一時コメントアウト
  //pmd canRunOnFailed: true, pattern: 'build/logs/pmd.xml'
  //checkstyle pattern: 'build/logs/checkstyle.xml'
  //dry canRunOnFailed: true, pattern: 'build/logs/pmd-cpd.xml'
 
  archiveArtifacts artifacts: 'build/**', fingerprint: true
}