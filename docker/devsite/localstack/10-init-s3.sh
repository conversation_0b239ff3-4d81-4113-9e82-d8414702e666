#!/usr/bin/env sh

set -e
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-ap-northeast-1}

BUCKETS="local-test"
for b in $BUCKETS; do
  echo "Creating bucket: $b"
  if [ "$AWS_DEFAULT_REGION" = "us-east-1" ]; then
    awslocal s3api create-bucket --bucket "$b" >/dev/null 2>&1 || true
  else
    awslocal s3api create-bucket --bucket "$b" --create-bucket-configuration LocationConstraint="$AWS_DEFAULT_REGION" >/dev/null 2>&1 || true
  fi

  awslocal s3api put-bucket-cors --bucket "$b" --cors-configuration '{
      "CORSRules": [
          {
              "AllowedHeaders": ["*"],
              "AllowedMethods": ["GET", "PUT", "POST"],
              "AllowedOrigins": ["*"],
              "ExposeHeaders": [],
              "MaxAgeSeconds": 3000
          }
      ]
  }'
done

awslocal s3api list-buckets || true

