version: "3.8"

services:
  nginx:
    image: nginx:1.27.0
    container_name: nginx1.27.0-developer-freegame
    restart: always
    ports:
      - "8081:80"
    volumes:
      - .:/var/www
      - ./files/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php-fpm
    networks:
      - app-network
  php-fpm:
    build: .
    container_name: php-fpm-developer-freegame
    restart: always
    volumes:
      - .:/var/www
      - ./files/php-fpm/php.ini:/etc/php/5.6/fpm/php.ini
    networks:
      - app-network
  freegame-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame:latest
    platform: linux/amd64
    container_name: freegame-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame
    ports:
      - "3506:3306"
    networks:
      - app-network
  redis:
    image: redis:6
    container_name: redis-developer-freegame
    restart: always
    ports:
      - "6479:6379"
    networks:
      - app-network
    command: ["redis-server", "--appendonly", "yes"]
  freegame-developer-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_developer:latest
    platform: linux/amd64
    container_name: freegame-developer-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_developer
      MYSQL_ROOT_HOST: "%"
    ports:
      - "3507:3306"
    networks:
      - app-network
  freegame-setting-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_setting:latest
    platform: linux/amd64
    container_name: freegame-setting-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_setting
    ports:
      - "3508:3306"
    networks:
      - app-network
  freegame-community-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_community:latest
    platform: linux/amd64
    container_name: freegame-community-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_community
    ports:
      - "3510:3306"
    networks:
      - app-network
  freegame-content-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_content:latest
    platform: linux/amd64
    container_name: freegame-content-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_content
    ports:
      - "3511:3306"
    networks:
      - app-network
  freegame-guide-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_guide:latest
    platform: linux/amd64
    container_name: freegame-guide-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_guide
    ports:
      - "3519:3306"
    networks:
      - app-network
  freegame-inspection-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_inspection:latest
    platform: linux/amd64
    container_name: freegame-inspection-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_inspection
    ports:
      - "3518:3306"
    networks:
      - app-network
  freegame-report-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_report:latest
    platform: linux/amd64
    container_name: freegame-report-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_report
    ports:
      - "3515:3306"
    networks:
      - app-network
  freegame-sandbox-db:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_sandbox:latest
    platform: linux/amd64
    container_name: freegame-sandbox-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: freegame_sandbox
    ports:
      - "3516:3306"
    networks:
      - app-network
  freegame-community-mongo:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_community_mongo:latest
    platform: linux/amd64
    container_name: freegame-community-mongo-dg
    restart: always
    ports:
      - "28017:27017"
    networks:
      - app-network
  freegame-platform-message-mongo:
    image: docker-registry.devops.dmmga.me/database/stg/freegame_platform_message_mongo:latest
    platform: linux/amd64
    container_name: freegame-platform-message-mongo-dg
    restart: always
    ports:
      - "28018:27017"
    networks:
      - app-network
  netgame_cstool_db:
    image: docker-registry.devops.dmmga.me/database/stg/netgame_cstool:latest
    platform: linux/amd64
    container_name: netgame-cstool-db-dg
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: netgame_cstool
    ports:
      - "3520:3306"
    networks:
      - app-network
  coupon-api.dev.games.dmm.com:
    image: wiremock/wiremock:3.12.0
    container_name: coupon-api-dg
    restart: always
    volumes:
      - ./files/api/coupon-api:/home/<USER>
    command: ["--port", "80", "--verbose"]
    networks:
      - app-network
  account-api.dev.games.dmm.com:
    image: wiremock/wiremock:3.12.0
    container_name: account-api-dg
    restart: always
    volumes:
      - ./files/api/account-api:/home/<USER>
    command: ["--port", "80", "--verbose"]
    networks:
      - app-network
  subscription-api.dev.games.dmm.com:
    image: wiremock/wiremock:3.12.0
    container_name: subscription-api-dg
    restart: always
    volumes:
      - ./files/api/subscription-api:/home/<USER>
    command: ["--port", "80", "--verbose"]
    networks:
      - app-network
  # added for verifications
  apply-api.dev.games.dmm.com:
    image: wiremock/wiremock:3.12.0
    container_name: apply-api-dg
    restart: always
    volumes:
      - ./files/api/apply-api:/home/<USER>
    command: ["--port", "80", "--verbose"]
    networks:
      - app-network
  dev-administrator.recibo.games.dmm.com:
    image: wiremock/wiremock:3.12.0
    container_name: dev-administrator-recibo-dg
    restart: always
    volumes:
      - ./files/api/dev-administrator-recibo:/home/<USER>
    command: ["--port", "80", "--verbose"]
    networks:
      - app-network
  localstack:
    container_name: devsite-localstack
    image: localstack/localstack:latest
    environment:
      - AWS_DEFAULT_REGION=ap-northeast-1
      - SERVICES=s3
      - DEBUG=1
      - CORS=http://localhost:8081
    ports:
      - "4567:4566"
    volumes:
      # PRO版でないと、コンテナ停止するとデータが消える
      - ./docker/devsite/localstack:/etc/localstack/init/ready.d
    networks:
      - app-network
# end of added
networks:
  app-network:
    driver: bridge
volumes:
  shared:
    
