<?php

namespace App\Libs\OAuth;

use OAuth\OAuth1\Token\StdOAuth1Token;
use OAuth\Common\Http\Exception\TokenResponseException;
use OAuth\Common\Http\Uri\Uri;
use OAuth\Common\Consumer\CredentialsInterface;
use OAuth\Common\Storage\TokenStorageInterface;
use OAuth\Common\Http\Client\ClientInterface;
use OAuth\OAuth1\Token\TokenInterface;
use OAuth\Common\Http\Uri\UriInterface;
use OAuth\OAuth1\Signature\SignatureInterface;
use OAuth\OAuth1\Service\AbstractService;

class PrivateNetgameApiOAuth extends AbstractService
{
    /**
     * {@inheritdoc}
     */
    public function getRequestTokenEndpoint()
    {
        throw new BadMethodCallException("illegal method call");
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthorizationEndpoint()
    {
        throw new BadMethodCallException("illegal method call");
    }

    /**
     * @param string $authorizationEndpoint
     *
     * @throws Exception
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function setAuthorizationEndpoint($endpoint)
    {
        throw new BadMethodCallException("illegal method call");
    }

    /**
     * {@inheritdoc}
     */
    public function getAccessTokenEndpoint()
    {
        throw new BadMethodCallException("illegal method call");
    }

    /**
     * {@inheritdoc}
     */
    protected function parseRequestTokenResponse($responseBody)
    {
        parse_str($responseBody, $data);

        if (null === $data || !is_array($data)) {
            throw new TokenResponseException('Unable to parse response.');
        } elseif (!isset($data['oauth_callback_confirmed']) || $data['oauth_callback_confirmed'] !== 'true') {
            throw new TokenResponseException('Error in retrieving token.');
        }

        return $this->parseAccessTokenResponse($responseBody);
    }

    /**
     * {@inheritdoc}
     */
    protected function parseAccessTokenResponse($responseBody)
    {
        parse_str($responseBody, $data);

        if (null === $data || !is_array($data)) {
            throw new TokenResponseException('Unable to parse response.');
        } elseif (isset($data['error'])) {
            throw new TokenResponseException('Error in retrieving token: "' . $data['error'] . '"');
        }

        $token = new StdOAuth1Token();

        $token->setRequestToken($data['oauth_token']);
        $token->setRequestTokenSecret($data['oauth_token_secret']);
        $token->setAccessToken($data['oauth_token']);
        $token->setAccessTokenSecret($data['oauth_token_secret']);

        $token->setEndOfLife(StdOAuth1Token::EOL_NEVER_EXPIRES);
        unset($data['oauth_token'], $data['oauth_token_secret']);
        $token->setExtraParams($data);

        return $token;
    }

    /**
     * {@inheritdoc}
     */
    protected function buildAuthorizationHeaderForAPIRequest(
        $method,
        UriInterface $uri,
        TokenInterface $token,
        $bodyParams = null
    )
    {
        if ($token->getAccessTokenSecret()) {
            $bodyParams = array_merge($bodyParams, [
                "oauth_token_secret" => $token->getAccessTokenSecret(),
            ]);
        }
        return parent::buildAuthorizationHeaderForAPIRequest($method, $uri, $token, $bodyParams);
    }

    /**
     * setAccessToken
     *
     * @param \OAuth\OAuth1\Token\TokenInterface $token
     */
    public function setAccessToken(TokenInterface $token)
    {
        $this->storage->storeAccessToken($this->service(),  $token);
        return $this;
    }
}
