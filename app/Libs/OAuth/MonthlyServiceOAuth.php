<?php

namespace App\Libs\OAuth;

use Log;
use <PERSON>Auth\OAuth1\Token\StdOAuth1Token;
use OAuth\Common\Http\Exception\TokenResponseException;
use OAuth\Common\Http\Uri\Uri;
use OAuth\Common\Consumer\CredentialsInterface;
use OAuth\Common\Storage\TokenStorageInterface;
use OAuth\Common\Http\Client\ClientInterface;
use OAuth\OAuth1\Token\TokenInterface;
use OAuth\Common\Http\Uri\UriInterface;
use OAuth\OAuth1\Signature\SignatureInterface;
use OAuth\OAuth1\Service\AbstractService;

class MonthlyServiceOAuth extends AbstractService
{
    /**
     * Sends an authenticated API request to the path provided.
     * If the path provided is not an absolute URI, the base API Uri (must be passed into constructor) will be used.
     *
     * @param string|UriInterface $path
     * @param string              $method       HTTP method
     * @param array               $body         Request body if applicable (key/value pairs)
     * @param array               $extraHeaders Extra headers if applicable.
     *                                          These will override service-specific any defaults.
     *
     * @return string
     */
    public function requestGet($path, $body = null, array $extraHeaders = array(), $token = null)
    {
        $uri = $this->determineRequestUriFromPath($path, $this->baseApiUri);

        $extraHeaders = array_merge($this->getExtraApiHeaders(), $extraHeaders);
        $authorizationHeader = array(
            'Authorization' => $this->buildAuthorizationHeaderForAPIRequest('GET', $uri, $token, $body)
        );

        $headers = array_merge($authorizationHeader, $extraHeaders);
        return $this->httpClient->retrieveResponse($uri, $body, $headers, 'GET');
    }

    /**
     * Sends an authenticated API request to the path provided.
     * If the path provided is not an absolute URI, the base API Uri (must be passed into constructor) will be used.
     *
     * @param string|UriInterface $path
     * @param string              $method       HTTP method
     * @param array               $body         Request body if applicable (key/value pairs)
     * @param array               $extraHeaders Extra headers if applicable.
     *                                          These will override service-specific any defaults.
     *
     * @return string
     */
    public function requestPost($path, $body = null, array $extraHeaders = array(), $token = null)
    {
    }
    
    /**
     * {@inheritDoc}
     */
    public function getRequestTokenEndpoint()
    {
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthorizationEndpoint()
    {
    }

    /**
     * {@inheritdoc}
     */
    public function getAccessTokenEndpoint()
    {
    }

    /**
     * {@inheritdoc}
     */
    protected function parseRequestTokenResponse($responseBody)
    {
    }

    /**
     * {@inheritdoc}
     */
    protected function parseAccessTokenResponse($responseBody)
    {
    }

    protected function buildAuthorizationHeaderForAPIRequest(
        $method,
        UriInterface $uri,
        TokenInterface $token,
        $bodyParams = null
    ) {
        $this->signature->setTokenSecret($token->getAccessTokenSecret());
        $authParameters = $this->getBasicAuthorizationHeaderInfo();

        // getBasicAuthorizationHeaderInfo でoauth_callback が有無を言わさず配列に設定されるのでデフォルト解除
        unset($authParameters['oauth_callback']);

        $authParameters = array_merge($authParameters, array('oauth_token' => $token->getAccessToken()));
        $authParameters = array_merge($authParameters, array('oauth_token_secret' => $token->getAccessTokenSecret()));
        $signatureParams = (is_array($bodyParams)) ? array_merge($authParameters, $bodyParams) : $authParameters;
        $authParameters['oauth_signature'] = $this->signature->getSignature($uri, $signatureParams, $method);

        if (is_array($bodyParams) && isset($bodyParams['oauth_session_handle'])) {
            $authParameters['oauth_session_handle'] = $bodyParams['oauth_session_handle'];
            unset($bodyParams['oauth_session_handle']);
        }

        $authorizationHeader = 'OAuth ';
        $delimiter = '';

        foreach ($authParameters as $key => $value) {
            $authorizationHeader .= $delimiter . rawurlencode($key) . '="' . rawurlencode($value) . '"';
            $delimiter = ',';
        }

        return $authorizationHeader;
    }
}
