<?php

namespace App\Libs\Validation\Validator;

use Illuminate\Validation\Validator;

class Secure extends Validator
{
    /**
     * Validate secure_url
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array $parameters 特定条件でバリデーションの有効・無効を変化させるフラグ
     * @return boolean
     */
    public static function validateSecureUrl($attribute, $value, $parameters)
    {
        // フラグ未指定の場合は有効
        $isActive = empty($parameters) ? true : $parameters[0];

        // URLがhttpである場合
        if ($isActive && preg_match('/^https:\/\//', $value) !== 1) {
            return false;
        }
        return true;
    }
}
