<?php

namespace App\Libs\Validation\Validator;

use Illuminate\Validation\Validator;

class Numeric extends Validator
{
    /**
     * Validate max integer 32 bit
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateMaxInteger($attribute, $value, $parameters)
    {
        $bits = isset($parameters[0]) ? $parameters[0] : 32;
        return $value < pow(2, $bits);
    }
}
