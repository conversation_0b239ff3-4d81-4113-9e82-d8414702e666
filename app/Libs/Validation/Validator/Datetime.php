<?php

namespace App\Libs\Validation\Validator;

use Illuminate\Validation\Validator;

class Datetime extends Validator
{
    /**
     * Validate date time with a configured format datetime
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateDateTime($attribute, $value, $parameters)
    {
        $dateFormat = isset($parameters[0]) ? $parameters[0] : config('forms.common.datetimeDisplayFormat');
        $createDateFormat = \DateTime::createFromFormat($dateFormat, $value);
        $validateCheck = $createDateFormat && ($createDateFormat->format($dateFormat) == $value);

        return $validateCheck;
    }
}
