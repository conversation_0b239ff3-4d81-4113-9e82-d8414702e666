<?php
namespace App\Libs\Validation\Validator;

use DOMDocument;

class ExceptTagHtml
{
    private $attributeName = null;
    private $errorTagList  = null;

    /**
     * Custom validation rule
     *
     * @param  string  $attribute
     * @param  string  $value
     * @param  array  $parameters
     * @param  \Illuminate\Validation\Validator $validator
     * @return boolean
     */
    public function validate($attribute, $value, $parameters, $validator)
    {
        $exceptHtmlTagBaseList = !empty($parameters) ? $parameters : config('forms.common.exceptTagHtml');

        $this->errorTagList  = [];
        $this->attributeName = $validator->getCustomAttributes()[$attribute];

        $domDocument = new DOMDocument();

        // loadHTMLメソッド不具合により、やむをえずtry-catchではなくエラー制御演算子を使用しています。
        // ※<video>や<canvas> 使用時にエラーが発生し、これらの文字が登録できなくなってしまっているため。
        @$domDocument->loadHTML($value);
        
        $htmlTagList = [];

        foreach ($exceptHtmlTagBaseList as $tag) {
            if ($domDocument->getElementsByTagName($tag)->length) {
                $htmlTagList[] = $tag;
            }
        }

        $htmlTagList = $this->handleSpecialTags($value, $htmlTagList);

        $this->errorTagList = array_intersect($exceptHtmlTagBaseList, $htmlTagList);

        return count($this->errorTagList) == 0;
    }

    /**
     * Custom validation error message
     *
     * @param  string  $message
     * @param  string  $attribute
     * @param  string  $rule
     * @param  array  $parameters
     * @return string
     */
    public function errorMessage($message, $attribute, $rule, $parameters)
    {
        return str_replace(
            [':attribute', ':tag'],
            [$this->attributeName, '<' . implode('>、<', array_unique($this->errorTagList)) . '>'], $message
        );
    }

    /**
     * Handle special tags
     *
     * @param  string  $value
     * @param  array   $htmlTagList
     * @return array
     */
    public function handleSpecialTags($value, $htmlTagList)
    {
        if (!preg_match('/(<|&lt;)html/i', $value) && array_search('html', $htmlTagList) !== false) {
            unset($htmlTagList[array_search('html', $htmlTagList)]);
        }

        if (preg_match('/(<|&lt;)head/i', $value)) {
            $htmlTagList[] = 'head';
        } elseif (!preg_match('/(<|&lt;)head/i', $value) && array_search('head', $htmlTagList) !== false) {
            unset($htmlTagList[array_search('head', $htmlTagList)]);
        }

        if (preg_match('/javascript:/i', $value)) {
            $htmlTagList[] = 'script';
        }

        return $htmlTagList;
    }
}
