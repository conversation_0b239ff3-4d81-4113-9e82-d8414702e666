<?php

namespace App\Libs\Validation\Validator;

use Illuminate\Validation\Validator;

class String extends Validator
{
    private static $platformDependentCharacters = [
        //SJIS13区
        '①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ'
        . '㍉㌔㌢㍍㌘㌧㌃㌶㍑㍗㌍㌦㌣㌫㍊㌻㎜㎝㎞㎎㎏㏄㎡㍻〝〟№㏍'
        . '℡㊤㊥㊦㊧㊨㈱㈲㈹㍾㍽㍼',
        //NECのIBM拡張文字89区
        '纊褜鍈銈蓜俉炻昱棈鋹曻彅丨仡仼伀伃伹佖侒侊侚侔俍偀倢俿倞偆偰'
        . '偂傔僴僘兊兤冝冾凬刕劜劦勀勛匀匇匤卲厓厲叝﨎咜咊咩哿喆坙'
        . '坥垬埈埇﨏塚增墲夋奓奛奝奣妤妺孖寀甯寘寬尞',
        //NECのIBM拡張文字90区
        '忞恝悅悊惞惕愠惲愑愷愰憘戓抦揵摠撝擎敎昀昕昻昉昮昞昤晥晗晙晴晳'
        . '暙暠暲暿曺朎朗杦枻桒柀栁桄棏﨓楨﨔榘槢樰橫橆橳橾櫢櫤毖氿'
        . '汜沆汯泚洄涇浯涖涬淏淸淲淼渹湜渧渼溿澈澵',
        //NECのIBM拡張文字91区
        '犾猤猪獷玽珉珖珣珒琇珵琦琪琩琮瑢璉璟甁畯皂皜皞皛皦益睆劯砡硎'
        . '硤硺礰礼神祥禔福禛竑竧靖竫箞精絈絜綷綠緖繒罇羡羽茁荢荿菇'
        . '菶葈蒴蕓蕙蕫﨟薰蘒﨡蠇裵訒訷詹誧誾諟諸諶譓',
        //NECのIBM拡張文字92区
        '釗釞釭釮釤釥鈆鈐鈊鈺鉀鈼鉎鉙鉑鈹鉧銧鉷鉸鋧鋗鋙鋐﨧鋕鋠鋓錥錡'
        . '鋻﨨錞鋿錝錂鍰鍗鎤鏆鏞鏸鐱鑅鑈閒隆﨩隝隯霳霻靃靍靏靑靕顗'
        . '顥飯飼餧館馞驎髙髜魵魲鮏鮱鮻鰀鵰鵫鶴鸙黑',
        //IBM拡張文字115区
        'ⅰⅱⅲⅳⅴⅵⅶⅷⅸⅹⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ￢￤＇＂㈱№℡∵'
        . '纊褜鍈銈蓜俉炻昱棈鋹曻彅丨仡仼伀伃伹佖侒侊侚侔俍偀倢俿倞'
        . '偆偰偂傔僴僘兊兤冝冾凬刕劜劦勀勛匀匇匤卲厓厲叝',
        //IBM拡張文字116区
        '夋奓奛奝奣妤妺孖寀甯寘寬尞岦岺峵崧嵓﨑嵂嵭嶸嶹巐弡弴彧德忞恝'
        . '悅悊惞惕愠惲愑愷愰憘戓抦揵摠撝擎敎昀昕昻昉昮昞昤晥晗晙晴'
        . '晳暙暠暲暿曺朎朗杦枻桒柀栁桄棏﨓楨﨔榘槢樰',
        //IBM拡張文字117区
        '涖涬淏淸淲淼渹湜渧渼溿澈澵濵瀅瀇瀨炅炫焏焄煜煆煇凞燁燾犱犾猤'
        . '猪獷玽珉珖珣珒琇珵琦琪琩琮瑢璉璟甁畯皂皜皞皛皦益睆劯砡硎'
        . '硤硺礰礼神祥禔福禛竑竧靖竫箞精絈絜綷綠緖繒',
        //IBM拡張文字118区
        '蘒﨡蠇裵訒訷詹渹誾諟諸諶譓譿賰賴贒赶﨣軏﨤逸遧郞都鄕鄧釚釗釞'
        . '釭釮釤釥鈆鈐鈊鈺鉀鈼鉎鉙鉑鈹鉧銧鉷鉸鋧鋗鋙鋐﨧鋕鋠鋓錥錡'
        . '鋻﨨錞鋿錝錂鍰鍗鎤鏆鏞鏸鐱鑅鑈閒隆﨩隝隯霳',
        //IBM拡張文字119区
        '髜魵魲鮏鮱鮻鰀鵰鵫鶴鸙黑',
    ];

    /**
     * Validate hiragana_only
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateHiraganaOnly($attribute, $value, $parameters)
    {
        $result = true;
        // 旧処理と同じチェック方法
        // 一つも一致しなければ false
        if (mb_ereg('^[ぁ-んー]+$', $value) === false) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate alfa_num_line_only
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateAlfaNumLineOnly($attribute, $value, $parameters)
    {
        $result = true;
        if (mb_ereg('^[a-zA-Z0-9_-]+$', $value) === false) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate alfa_num_line_dot_only
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateAlfaNumLineDotOnly($attribute, $value, $parameters)
    {
        $result = true;
        if (mb_ereg('^[a-zA-Z0-9._\-]+$', $value) === false) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate alfa_num_line_dot_slash_only
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateAlfaNumLineDotSlashOnly($attribute, $value, $parameters)
    {
        $result = true;
        if (mb_ereg('^[a-zA-Z0-9._\-/]+$', $value) === false) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate special_characters<br>
     * 文字化けする文字を「&#x16進コード」に変換して特殊文字判定
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateSpecialCharacters($attribute, $value, $parameters)
    {
        // 文字コード変換
        $substrchar = mb_substitute_character();
        mb_substitute_character('entity');
        $value = mb_convert_encoding($value, 'eucJP-win', 'UTF-8');
        mb_substitute_character($substrchar);
        $value = mb_convert_encoding($value, 'UTF-8', 'eucJP-win');

        // 「&#x16進コード」があれば、特殊文字を含んだ文字列として判定
        $result = true;
        if (preg_match('/&#x[0-9a-fA-F]+;/', $value) == true) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate alfa_num_only
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateAlfaNumOnly($attribute, $value, $parameters)
    {
        $result = true;
        if (mb_ereg('^[a-zA-Z0-9]+$', $value) === false) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate script_tag
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateScriptTag($attribute, $value, $parameters)
    {
        $result = true;
        if (preg_match('/(<|&lt;)script/i', $value)) {
            $result = false;
        } elseif (preg_match('/javascript:/i', $value)) {
            $result = false;
        }
        return $result;
    }

    /**
     * Validate platform_dependent
     * 機種依存文字
     *
     * ・13区の特殊文字（0x8740～0x879F 83文字）
     * ・NEC選定IBM拡張文字（0xED40～0xEEFC　374文字）
     * ・IBM拡張文字（0xFA40～0xFC4B　388文字）
     *
     * @param  string  $attribute
     * @param  string  $value
     * @param  array   $parameters
     * @return boolean
     */
    public static function validatePlatformDependent($attribute, $value, $parameters)
    {
        $result = true;
        // 比較用の文字を格納する
        $strArray = array();
        //1文字ずつに分解
        while ($iLen = mb_strlen($value, 'UTF-8')) {
            array_push($strArray, mb_substr($value, 0, 1, 'UTF-8'));
            $value = mb_substr($value, 1, $iLen, 'UTF-8');
        }
        //1文字ずつ比較
        foreach ($strArray as $val) {
            foreach (self::$platformDependentCharacters as $character) {
                if (preg_match('@' . preg_quote($val, '@') . '@', $character)) {
                    //"機種依存文字あり";
                    $result = false;
                    break 2;
                }
            }
        }
        //機種依存無
        return $result;
    }

    /**
     * Validate picture_characters
     * 絵文字
     *
     * @param  string  $attribute
     * @param  string  $value
     * @param  array   $parameters
     * @return boolean $result
     */
    public static function validatePictureCharacters($attribute, $value, $parameters)
    {
        $result = true;

        //docomo絵文字判定開始
        $docomo  = '[\\x81-\\x9F\\xE0-\\xF7\\xFA-\\xFC][\\x40-\\x7E\\x80-\\xFC]|[\\x00-\\x7F]|[\\xA1-\\xDF]';
        $docomoEmoji = '[\\xF8\\xF9][\\x40-\\x7E\\x80-\\xFC]';
        if (preg_match('/\\G((?:$docomo)*)(?:($docomoemoji))/', $value)) {
            $result = false;
        }

        // TODO：リプレース前の処理からAU・SoftBankの絵文字には未対応になっている

        //Au絵文字判定(未)

        //SoftBank絵文字判定(未)

        return $result;
    }

    /**
     * Validate control_characters
     * 制御文字
     *
     * @param  string  $attribute
     * @param  string  $value
     * @param  array   $parameters
     * @return boolean $result
     */
    public static function validateControlCharacters($attribute, $value, $parameters)
    {
        $result = true;

        if (!empty($value)) {
            if (preg_match('/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/', $value)) {
                return false;
            }
        }

        return $result;
    }

    /**
     * Validate max_emoji
     * 絵文字タグを含む場合の文字数制限
     * 絵文字タグを1文字としてカウントします
     *
     * @param  string  $attribute
     * @param  string  $value
     * @param  array   $parameters
     * @return boolean true/false
     */
    public static function validateMaxEmoji($attribute, $value, $parameters)
    {
        if (!empty($value)) {
            $data = preg_replace('/<emoji id="(.*?)">/', 'e', $value);

            if ($data instanceof UploadedFile && ! $data->isValid()) {
                return false;
            }

            $length = 0;
            if (is_array($data)) {
                $length = count($data);
            } elseif ($data instanceof File) {
                $length = $data->getSize() / 1024;
            } elseif (is_string($data)) {
                $length = mb_strlen($data);
            }

            if ($length > $parameters[0]) {
                return false;
            }
        }

        return true;
    }

    /**
     * Replace all place-holders for the max_emoji rule.
     *
     * @param  string  $message
     * @param  string  $attribute
     * @param  string  $rule
     * @param  array   $parameters
     * @return string
     */
    public static function replaceMaxEmoji($message, $attribute, $rule, $parameters)
    {
        return str_replace([':max'], [$parameters[0]], $message);
    }

    /**
     * Validate single
     * 半角文字
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateSingle($attribute, $value, $parameters)
    {
        $result = true;
        if (strlen($value) != mb_strlen($value)) {
            $result = false;
        }
        return $result;
    }

    /**
     * Prevent all HTML tags
     *
     * @param  string  $attribute
     * @param  string  $value
     * @param  array   $parameters
     * @return boolean true/false
     */
    public static function validateNoAllHtmlTag($attribute, $value, $parameters)
    {
        return strip_tags($value) === $value;
    }

    /**
     * Replace all place-holders for the no_all_html_tag rule.
     *
     * @param  string  $message
     * @param  string  $attribute
     * @param  string  $rule
     * @param  array   $parameters
     * @return string
     */
    public static function replaceNoAllHtmlTag($message, $attribute, $rule, $parameters)
    {
        return str_replace([':attribute', ':tag'], [$attribute, 'HTML'], $message);
    }

    /**
     * Validate restricted_characters
     * 4バイト文字制限
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateRestrictedCharacters($attribute, $value, $parameters)
    {
        $result = true;
        // コードから文字に変換
        $str = html_entity_decode($value);
        if (preg_match('/[\xF0-\xF7][\x80-\xBF]{3}/', $str)) {
            return false;
        }
        return $result;
    }

    /**
     * Validate forbidden_chars_in_encoding
     * 文字列に禁止文字が含まれているか判定
     *
     * @param  string $attribute
     * @param  string $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateForbiddenCharsInEncoding($attribute, $value, $parameters)
    {
        // valueを使うと禁止文字がUnicodeになってしまうので生データを取得
        $pureValue = $_POST[$attribute];

        // mb_convert_encodingで一度euc-jpに変換することで禁止文字が「?」に変換される
        $encordValue = mb_convert_encoding(
            mb_convert_encoding($pureValue, 'euc-jp', 'utf-8'),
            'utf-8',
            'euc-jp'
        );

        if ($encordValue === false || $pureValue != $encordValue) {
            // 変換失敗または、変換前後の文字列が異なる(=禁止文字あり)場合
            return false;
        }

        return true;
    }
}
