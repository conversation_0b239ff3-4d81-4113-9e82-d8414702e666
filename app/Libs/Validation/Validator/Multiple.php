<?php
namespace App\Libs\Validation\Validator;

use Illuminate\Validation\Validator;

class Multiple extends Validator
{
    /**
     * Validate required_array
     * @param  string  $attribute
     * @param  array   $value
     * @param  array   $parameters(one：ひとつでも値があれば正常  all：全てに値があれば正常)
     * @return boolean $return
     */
    public static function validateRequiredArray($attribute, $value, $parameters)
    {
        $return = false;

        if (!is_array($value)) {
            return $return;
        }

        foreach ($value as $key => $val) {
            if (empty($val)) {
                // ひとつでも空白があればエラー
                if ($parameters[0] == 'all') {
                    $return = false;
                    break;
                }
            } else {
                // ひとつでも値があれば正常
                if ($parameters[0] == 'one') {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }
}
