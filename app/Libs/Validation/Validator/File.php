<?php

namespace App\Libs\Validation\Validator;

use Illuminate\Validation\Validator;

class File extends Validator
{
    /**
     * Validate image_size
     * @param  string $attribute
     * @param         $value
     * @param  array  $parameters
     * @return boolean
     */
    public static function validateImageSize($attribute, $value, $parameters)
    {
        if (is_object($value)) {
            $path = $value->getRealPath();
            if (!file_exists($path) || is_dir($path)) {
                return false;
            }

            $size = getimagesize($path);
            if ($size[0] != $parameters[0] || $size[1] != $parameters[1]) {
                return false;
            }
        } elseif (is_string($value)) {
            list($width, $height) = @getimagesize($value);
            if ($width != $parameters[0] || $height != $parameters[1]) {
                return false;
            }
        } else {
            return false;
        }

        return true;
    }

    /**
     * Replace all place-holders for the image_size rule.
     *
     * @param  string  $message
     * @param  string  $attribute
     * @param  string  $rule
     * @param  array   $parameters
     * @return string
     */
    public static function replaceImageSize($message, $attribute, $rule, $parameters)
    {
        return str_replace([':file_width', ':file_height'], [$parameters[0], $parameters[1]], $message);
    }

    /**
     * Validate image_max
     * @param  string $attribute
     * @param         $value
     * @param  array  $parameters
     * @return boolean
     * 引数の単位はMBのため変換してから比較します
     */
    public static function validateImageMax($attribute, $value, $parameters)
    {
        if (!empty($value)) {
            if (((filesize($value->getPathname()) / 1024) / 1024) > $parameters[0]) {
                return false;
            }
        }

        return true;
    }

    /**
     * Replace all place-holders for the image_max rule.
     *
     * @param  string  $message
     * @param  string  $attribute
     * @param  string  $rule
     * @param  array   $parameters
     * @return string
     */
    public static function replaceImageMax($message, $attribute, $rule, $parameters)
    {
        return str_replace([':max'], [$parameters[0]], $message);
    }
}
