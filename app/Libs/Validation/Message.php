<?php

namespace App\Libs\Validation;

class Message
{
    /**
     * Get validation messsage by message code.
     *
     * @param string $messageCode
     * @return string
     */
    public static function get($messageCode)
    {
        if ($messageCode != null) {
            $errorMessage = trans('validationmessage.' . $messageCode);
            if ($errorMessage != null) {
                return $errorMessage;
            }
        }
        return null;
    }
}
