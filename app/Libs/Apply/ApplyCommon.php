<?php

namespace App\Libs\Apply;

use Exception;
use Log;
use Carbon\Carbon;

/**
 * 申請共通
 */
class ApplyCommon {

    private $config;

    protected $body;

    protected $contentName;

    // 審査項目別デバイス毎の必要有無MAP
    protected $items = [];

    public function __construct($body = null){
        if(!empty($body)){
            $this->body = $body;
        }else{
            $this->body = [
                'targetPreregistrationDate' => 0,
                'targetReleaseDate' => 0,
                'releaseStatus' => 'unsubmitted',
                'isPreRegistrationActive' => false,
                $this->contentName => []
            ];
        }
    }

    public function getTargetPreregistrationDate($default = null){
        $value = isset($this->body['targetPreregistrationDate']) ? $this->body['targetPreregistrationDate'] : '';
        if(!empty($value)){
            return $this->convertTitmestampToString($value);
        }else{
            if(!empty($default)){
                return $default;
            }
            return $value;
        }
    }

    public function getTargetReleaseDate($default = null){
        $value = isset($this->body['targetReleaseDate']) ? $this->body['targetReleaseDate'] : '';
        if(!empty($value)){
            return $this->convertTitmestampToString($value);
        }else{
            if(!empty($default)){
                return $default;
            }
            return $value;
        }
    }

    public function getReleaseStatus(){
        $status = isset($this->body['releaseStatus']) ? $this->body['releaseStatus'] : '';
        $result = config("forms.RegistrationAndReleaseApply.releaseStatus.$status", "未提出");
        return $result;
    }

    public function getCategoryData($category, $param1 = null, $param2 = null, $default = null){
        $content = [];
        if(!empty($this->body[$this->contentName][$category])){
            $content = $this->body[$this->contentName][$category];
        }
        
        if($param1 === null){
            return $content;
        }

        if($param2 === null){
            if(isset($content[$param1])){
                switch ($param1) {
                    case 'completionDate':
                        $value = $content[$param1];
                        if(empty($value)){
                            return $default;
                        }else{
                            return $this->convertTitmestampToString($content[$param1]);
                        }
                        break;
                    
                    default:
                        return $content[$param1];
                        break;
                }
            }else{
                return $default;
            }
        }else{
            if(isset($content[$param1][$param2])){
                $value =$content[$param1][$param2];
                // applyStatusが空文字の場合はデフォルト値を返す(審査中に新しい審査項目が増えた場合を想定)
                if($param2 === 'applyStatus'){
                    if(empty($value)){
                        return $default;
                    }
                }
                return $value;
            }else{
                return $default;
            }
        }
        
    }

    /**
     * 指定デバイスが対象かどうかを確認する
     *
     * @param mixed $device
     * @return boolean
     */
    public function isTargetDevice($device){
        foreach ($this->items as $category => $items) {
            foreach ($items as $item => $deviceList) {
                foreach ($deviceList as $enableDevice => $enable) {
                    if ($enableDevice == $device) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 審査項目が指定デバイスで対象かどうかを確認する
     *
     * @param mixed $device
     * @param mixed $category
     * @param mixed $item
     */
    public function isTarget($device, $category, $item){
        if(isset($this->items[$category][$item][$device])){
            return $this->items[$category][$item][$device];
        }else{
            // 審査項目が存在しない場合は、例外を発生させる
            throw new Exception(vsprintf('device=%s category=%s item=%s は設定されていません。', [$device, $category, $item]));
        }
    }

    /**
     * 審査項目から指定デバイスで対象のものを返す
     *
     * @param string $device
     * @param array $content
     * @return array
     */
    public function getTargetContent($device, $content){
        foreach ($content as $category => $items) {
            foreach ($items as $item => $itemInfo) {
                if (!$this->isTarget($device, $category, $item)) {
                    unset($content[$category][$item]);
                }
            }
        }

        return $content;
    }

    /**
     * タイムスタンプを文字列（Y/m/dフォーマット）に変換する
     */
    private function convertTitmestampToString($timestamp){
        $carbon = new Carbon();
        $carbon->timestamp = $timestamp;
        return $carbon->format('Y/m/d');
    }

    /**
     * エラー発生時のjsonデータを取得。
     * 同時にエラー内容をログに保存する。
     *
     * @param Exception $exception
     * @param Request $request
     * @param array|null $query
     * @return array
     */
    public static function getJsonError($exception, $request, $query=null)
    {
        $log = $exception->getMessage()."\n"
            .'request : '.json_encode($request->all())."\n";

        if (!empty($query)) {
            $log .= 'query : '.json_encode($query)."\n";
        }

        $log .= $exception->getTraceAsString();
        Log::error($log);

        return [
            'status' => 500,
            'message' => trans('validationmessage.MSG162'),
            "data" => [
                'content' => trans('validationmessage.MSG162'),
            ]
        ];
    }

    /**
     * 問い合わせ先メールアドレスの編集URLを生成
     *
     * @param  mixed $id
     * @return string
     */
    public function makeMailEditUrl($id){
        $baseUrl = env('HTTP_DEVELOPER_FREEGAMES_URL');
        return "{$baseUrl}/games/basic/edit/{$id}";
    }

    /**
     * 問い合わせ先メールアドレス以外のゲーム情報の編集URLを生成
     *
     * @param  mixed $id
     * @param  mixed $device
     * @return string
     */
    public function makeGameInfoEditUrl($id, $device){
        $baseUrl = env('HTTP_DEVELOPER_FREEGAMES_URL');
        if (strpos($device, 'channeling') === false) {
            $repDevice = str_replace('_', '/', $device);
            return "{$baseUrl}/games/device/{$repDevice}/edit/{$id}";
        } else {
            return "{$baseUrl}/chgames/edit/{$id}";
        }
    }
    
    /**
     * ゲーム内画像申請のURLを生成
     *
     * @param  mixed $id
     * @param  mixed $device
     * @return string
     */
    public function makeGameContentImageUrl($id, $device){
        if (strpos($device, 'client') !== false) {
            $baseUrl = env('HTTP_CLIENTGAME_DEVELOPER_SITE_URL');
            $queryParams = http_build_query(['id' => $id]);
            return sprintf('%s/game/image-content?%s', $baseUrl, $queryParams);
        } elseif (strpos($device, 'channeling') !== false) {
            return route('ChGameContentImage.index', ['id' => $id]);
        } else {
            return route('GameContentImage.index', ['id' => $id]);
        }
    }
    
    /**
     * ゲーム画像 動画設定・申請のURLを生成
     *
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $imageType
     * @return string
     */
    public function makeGameImageUrl($id, $device, $imageType){
        if (strpos($device, 'client') !== false) {
            $baseUrl = env('HTTP_CLIENTGAME_DEVELOPER_SITE_URL');
            $queryParams = http_build_query(['id' => $id]);
            return sprintf('%s/game/image?%s', $baseUrl, $queryParams);
        } elseif (strpos($device, 'channeling') !== false) {
            return route('ChGameImage.index', ['id' => $id, 'imageType' => config('forms.ChGameImage.' . $imageType)]);
        } else {
            return route('GameImage.index', ['id' => $id, 'imageType' => config('forms.GameImage.' . $imageType)]);
        }
    }
    
    /**
     * 申請フォーム掲示板のURLを生成
     *
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $imageType
     * @return string
     */
    public function makeGameBoardUrl($id, $device){
        if (strpos($device, 'client') !== false) {
            $baseUrl = env('HTTP_CLIENTGAME_DEVELOPER_SITE_URL');
            $queryParams = http_build_query(['id' => $id]);
            return sprintf('%s/game/developer/board?%s', $baseUrl, $queryParams);
        } elseif (strpos($device, 'channeling') !== false) {
            return route('ChGames.index', ['app_id' => $id, 'device' => $device, 'tab' => 'board']);
        } else {
            return route('Games.index', ['app_id' => $id, 'device' => $device, 'tab' => 'board']);
        }
    }

    /**
     * ツールチップ内容を返す
     *
     * @param  string $tab
     * @param  string $device
     * @param  string $category
     * @param  string $item
     * @return string
     */
    public function getTooltipMsg($tab, $device, $category, $item){
        $msg = config('forms.Games.tooltipMassage');
        $tooltip = config('forms.Games.tooltip');

        if (isset($tooltip[$tab][$device][$category][$item])) {
            return $msg[$tooltip[$tab][$device][$category][$item]];
        }

        return '';
    }
}