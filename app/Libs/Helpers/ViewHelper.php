<?php
if (! function_exists('error_class')) {

    function error_class($elementList, $className = 'error')
    {
        $errorList = Session::get('errors', new Illuminate\Support\MessageBag);

        if (! is_array($elementList)) {
            $elementList = [$elementList];
        }

        foreach ($elementList as $element) {
            if ($errorList->has($element)) {
                return 'class=' . $className;
            }
        }

        return null;
    }
}

if (! function_exists('convert_html')) {
    /**
     * ブラウザ表示用に変換
     *
     * @param  string $data
     *
     * @return string $data
     *
     */
    function convert_html($data)
    {
        $data = nl2br(e($data));
        $data = convert_emoji($data);
        return $data;
    }
}

if (! function_exists('convert_emoji')) {
    /**
     * ブラウザ表示用に絵文字タグ置き換え
     *
     * @param  string $data
     *
     * @return string $data
     *
     */
    function convert_emoji($data)
    {
        if (isset($data) && is_string($data)) {
            $data = preg_replace_callback(
                '/(<|&lt;)emoji id=("|&quot;)(.*?)("|&quot;)(>|&gt;)/',
                function ($matches) {
                    return sprintf(
                        '<img src="%s/netgame/emoji/%s">',
                        env('HTTP_PICS_GENERAL_URL', '/images'),
                        config('forms.common.listEmoji.' . $matches[3], '0.gif')
                    );
                },
                $data
            );
        }
        return $data;
    }
}

if (! function_exists('except_html')) {
    /**
     * ブラウザ表示用に特定のタグを削除
     *
     * @param  string $data
     * @param  array  $tags
     * @param  array  $keys
     *
     * @return string $data
     *
     */
    function except_html(
        $data,
        $tags = ['a', 'font', 'b', 'u', 's', 'br', 'emoji'],
        $keys = ['javascript', 'data', 'expression', 'behavior', 'on[\w]+', 'write', 'cookie']
    ) {
        if (isset($data) && is_string($data)) {
            $data = strip_tags($data, '<' . implode('><', $tags) . '>');
            $data = preg_replace_callback('/<(.*)>/',
                function ($matches) use ($keys) {
                    return preg_replace('/(' . implode('|', $keys) . ')/', '#', $matches[0]);
                },
                $data
            );
        }
        return $data;
    }
}

if (! function_exists('get_base64_image')) {
    function get_base64_image($imgPath)
    {
        $result = '';
        if (empty($imgPath)) {
            return '';
        }

        $option = [
            'http' => [
                'ignore_errors' => true,
                'method'=>'GET',
                'follow_location' => 1,
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ],
        ];
        stream_context_set_default($option);
        $canGetContents = false;
        if (is_url($imgPath)) {
            $connectCheck = get_headers($imgPath);
            if (preg_match('#^HTTP/.*\s+[200]+\s#i', $connectCheck[0])) {
                $canGetContents = true;
            }
        } elseif (file_exists($imgPath)) {
            $canGetContents = true;
        }

        if ($canGetContents) {
            $getImg = file_get_contents($imgPath);
            $result = 'data:img/png;base64,' . base64_encode($getImg);
        } else {
            $result = $imgPath;
        }

        return $result;
    }
}

if (! function_exists('is_url')) {
    function is_url($string)
    {
        $urlPattern = '/^(https?|http)(:\/\/[-_.!~*\'()a-zA-Z0-9;\/?:\@&=+\$,%#]+)$/';
        if (preg_match($urlPattern, $string) === 1) {
            return true;
        }
        return false;
    }
}

if (! function_exists('adjustment_path')) {
    function adjustment_path($urlPath)
    {
        if (strpos($urlPath, 'http://') !== 0) {
            $urlPath = 'http://' . $urlPath;
        }

        return $urlPath;
    }
}

if (! function_exists('delete_confirm_message')) {
    function delete_confirm_message($baseMessage, $no)
    {
        return str_replace('%no%', $no, $baseMessage);
    }
}
