<?php
if (!function_exists('now_stamp')) {
    function now_stamp()
    {
        return time();
    }
}

if (!function_exists('format_date')) {
    function format_date($strDate)
    {
        // 変換前のパターンに合わせた変換をする
        $pattern = '/^([0-9]{4}\-[0-9]{2}\-[0-9]{2})';
        $pattern .= '(\s([0-9]{2})(:[0-9]{2})(:[0-9]{2})?)?$/';
        if (preg_match($pattern, $strDate, $matches)) {
            $timestamp = strtotime($strDate);
            if (empty($matches[2])) {
                $strDate = date('Y/m/d', $timestamp);
            } elseif (empty($matches[5])) {
                $strDate = date('Y/m/d H:i', $timestamp);
            } elseif (isset($matches[5])) {
                $strDate = date('Y/m/d H:i:s', $timestamp);
            }
        } elseif ($timestamp = strtotime($strDate)) {
            /**
             * パターンに当てはまらない日付フォーマットの場合は
             * デフォルトの 'Y/m/d H:i' への置換を試みる
            */
            $strDate = date('Y/m/d H:i', $timestamp);
        }

        return $strDate;
    }
}

if (!function_exists('format_date_to_sqldate')) {
    function format_date_to_sqldate($strDate)
    {
        // 変換前のパターンに合わせた変換をする
        $pattern = '/^([0-9]{4}\/[0-9]{2}\/[0-9]{2})';
        $pattern .= '(\s([0-9]{2})(:[0-9]{2})(:[0-9]{2})?)?$/';
        if (preg_match($pattern, $strDate, $matches)) {
            $timestamp = strtotime($strDate);
            if (empty($matches[2])) {
                $strDate = date('Y-m-d', $timestamp);
            } elseif (empty($matches[5])) {
                $strDate = date('Y-m-d H:i', $timestamp);
            } elseif (isset($matches[5])) {
                $strDate = date('Y-m-d H:i:s', $timestamp);
            }
        } elseif ($timestamp = strtotime($strDate)) {
            /**
             * パターンに当てはまらない日付フォーマットの場合は
             * デフォルトの 'Y-m-d H:i:s' への置換を試みる
            */
            $strDate = date('Y-m-d H:i:s', $timestamp);
        }

        return $strDate;
    }
}

if (!function_exists('timestamp_to_date')) {
    function timestamp_to_date($timestamp, $timeFormat = 'H:i:s')
    {
        $format = 'Y/m/d';
        $pattern = '/^(H((:i)(:s)?)?)$/';
        if (preg_match($pattern, $timeFormat)) {
            $format .= ' ' . $timeFormat;
        }
        $strDate = date($format, $timestamp);
        return $strDate;
    }
}

if (!function_exists('timestamp_to_sqldate')) {
    function timestamp_to_sqldate($timestamp, $timeFormat = 'H:i:s')
    {
        $format = 'Y-m-d';
        $pattern = '/^(H((:i)(:s)?)?)$/';
        if (preg_match($pattern, $timeFormat)) {
            $format .= ' ' . $timeFormat;
        }
        $strDate = date($format, $timestamp);
        return $strDate;
    }
}
