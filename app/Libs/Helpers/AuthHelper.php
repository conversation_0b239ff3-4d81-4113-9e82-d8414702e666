<?php
if (! function_exists('auth_is_pf')) {

    function auth_is_pf()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'admin' || auth()->user()->type == 'staff') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_sap')) {

    function auth_is_sap()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'sap') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_route')) {

    function auth_is_route($name, $parameters = [])
    {
        $check = env('AUTH_GATE_REQUEST', false);
        if (! $check) {
            return true;
        }
        $value = route($name, $parameters, false);
        if (auth()->check()) {
            if (auth()->user()->hasAuthRole($value)) {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_admin')) {

    function auth_is_user_admin()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'admin') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_staff')) {

    function auth_is_user_staff()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'staff') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_sap')) {

    function auth_is_user_sap()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'sap') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_kc')) {

    function auth_is_user_kc()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'kc') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_partner')) {

    function auth_is_user_partner()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'partner') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_market')) {

    function auth_is_user_market()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'market') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_is_user_adminforpoint')) {

    function auth_is_user_adminforpoint()
    {
        if (auth()->check()) {
            if (auth()->user()->type == 'adminforpoint') {
                return true;
            }
        }
        return false;
    }
}

if (! function_exists('auth_user_id')) {

    function auth_user_id()
    {
        if (auth()->check()) {
            return auth()->user()->id;
        }
        return 0;
    }
}

if (! function_exists('auth_user_login_id')) {

    function auth_user_login_id()
    {
        if (auth()->check()) {
            return auth()->user()->login_id;
        }
        return null;
    }
}

if (! function_exists('auth_user_name')) {

    function auth_user_name()
    {
        if (auth()->check()) {
            return auth()->user()->name;
        }
        return null;
    }
}
