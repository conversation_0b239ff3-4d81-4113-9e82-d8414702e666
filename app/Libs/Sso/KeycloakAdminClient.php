<?php

namespace App\Libs\Sso;

use Exception;
use GuzzleHttp\Client as HttpClient;
use Log;

/**
 * KeycloakのAdminRESTAPIとの通信を行う
 * AdminRESTAPIについて参考情報: https://confl.arms.dmm.com/pages/viewpage.action?pageId=1650495462
 *
 */
class KeycloakAdminClient
{
    /**
     * @var HttpClient
     */
    private $httpClient;

    /**
     * @var string
     */
    private $clientId;

    /**
     * @var string
     */
    private $clientSecret;

    /**
     * @var string
     */
    private $tokenEndpoint;

    /**
     * @var string
     */
    private $adminAPIEndpoint;

    /**
     * @param HttpClient $httpClient
     * @param $clientId string
     * @param $clientSecret string
     * @param $tokenEndpoint string
     * @param $adminAPIEndpoint string
     */
    public function __construct(HttpClient $httpClient, $clientId, $clientSecret, $tokenEndpoint, $adminAPIEndpoint)
    {
        $this->httpClient = $httpClient;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->tokenEndpoint = $tokenEndpoint;
        $this->adminAPIEndpoint = $adminAPIEndpoint;
    }

    /**
     * @return KeycloakAdminClient
     */
    public static function create()
    {
        $config = config('keycloak');
        $tokenEndPoint = $config['internal_base_uri'] . '/realms/' . $config['realm'] . '/protocol/openid-connect/token';
        $adminAPIEndpoint = $config['internal_base_uri'] . '/admin/realms/' . $config['realm'];
        return new self(
            new HttpClient(),
            $config['admin']['client_id'],
            $config['admin']['client_secret'],
            $tokenEndPoint, $adminAPIEndpoint
        );
    }

    /**
     * @return string
     * @throws Exception
     */
    private function getAccessToken()
    {
        try {
            $res = $this->httpClient->post(
                $this->tokenEndpoint,
                [
                    'form_params' => [
                        'grant_type' => 'client_credentials',
                        'client_id' => $this->clientId,
                        'client_secret' => $this->clientSecret,
                    ]
                ]
            );
        } catch(Exception $e) {
            Log::error('Failed to get admin access_token. StatusCode: ' . $e->getCode() . ', Message: ' . $e->getMessage());
            throw new Exception('Failed to get admin access_token.', $e->getCode());
        }

        $resArray = json_decode($res->getBody()->getContents(), true);

        return $resArray['access_token'];
    }

    /**
     * Keycloakよりユーザー情報を取得する
     * devサイトのログインidがリターンされる
     *
     * @return string
     * @throws Exception
     */
    public function getDeveloperSiteLoginId($userId)
    {
        try {
            $accessToken = $this->getAccessToken();
            $res = $this->httpClient->get(
                $this->adminAPIEndpoint . '/users/' . $userId,
                [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $accessToken,
                        'Content-Type' => 'application/json'
                    ]
                ]
            );
        } catch(Exception $e) {
            Log::error('Failed to get user. userId: ' . $userId . 'Code: ' . $e->getCode() . ', Message: ' . $e->getMessage());
            throw new Exception('Failed to get user. userId: ' . $userId . ', Message: ' . $e->getMessage());
        }
        $resArray = json_decode($res->getBody()->getContents(), true);

        if(!isset($resArray['attributes']['developersite_login_id'][0])) {
            Log::error('developersite_login_id not exist. Keycloak userId: ' . $userId);
            throw new Exception('Failed to get DeveloperSiteLoginId. Keycloak userId: ' . $userId);
        }
        return $resArray['attributes']['developersite_login_id'][0];
    }
}
