<?php

namespace App\Libs\Sso;

use Illuminate\Support\Facades\Session;
use Ju<PERSON>jett\OpenIDConnectClient;

/**
 * OpenIDConnectClientをlaravel向けに一部メソッドをオーバーライド
 * 参考: https://github.com/jumbojett/OpenID-Connect-PHP/issues/374
 */
class OpenIDConnectClientLaravelWrapper extends OpenIDConnectClient
{
    /**
     * @return void
     */
    protected function startSession()
    {
        // セッションの開始はLaravel側で管理されてるので、このメソッドが呼び出されても何も実行しないようオーバーライドする
    }

    /**
     * @return void
     */
    protected function commitSession()
    {
        Session::save();
    }

    /**
     * @param string $key
     */
    protected function getSessionKey($key)
    {
        if (!Session::has($key)) {
            return false;
        }

        return Session::get($key);
    }

    /**
     * @param string $key
     * @param mixed $value mixed
     */
    protected function setSessionKey($key, $value)
    {
        Session::put($key, $value);
    }

    /**
     * @param string $key
     */
    protected function unsetSessionKey($key)
    {
        Session::remove($key);
    }
}
