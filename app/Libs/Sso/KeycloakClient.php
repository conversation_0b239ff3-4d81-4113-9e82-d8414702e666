<?php

namespace App\Libs\Sso;

use Exception;
use Illuminate\Support\Facades\Session;
use <PERSON><PERSON>jett\OpenIDConnectClientException;
use Log;

/**
 * Keycloakとのやり取りを行う
 */
class KeycloakClient
{
    /**
     * @var OpenIDConnectClientLaravelWrapper
     */
    private $oidcClient;

    public function __construct(OpenIDConnectClientLaravelWrapper $oidcClient)
    {
        $this->oidcClient = $oidcClient;
    }

    /**
     * @return KeycloakClient
     */
    public static function create()
    {
        $config = config('keycloak');
        $providerUrl = $config['base_uri'] . '/realms/' . $config['realm'];
        $client = new OpenIDConnectClientLaravelWrapper(
            $providerUrl,
            $config['user']['client_id'],
            $config['user']['client_secret']
        );
        $client->providerConfigParam(
            [
                'authorization_endpoint' => $providerUrl . '/protocol/openid-connect/auth',
                'issuer' => $providerUrl
            ]
        );
        $client->setRedirectURL(route('Users.loginSsoRedirect'));

        return new self($client);
    }

    /**
     * Keycloakへの認証リクエストを実行
     * 成功したらauthenticate()内でKeycloakへリダイレクトされる
     *
     * @return bool
     */
    public function authRequest()
    {
        try {
            return $this->oidcClient->authenticate();
        } catch (OpenIDConnectClientException $e) {
            Log::error("Failed to request sso authentication: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Keycloakでの認証後のリダイレクト時に実行される
     * authenticate()内部でリダイレクト内容の検証やトークンの発行などが行われる
     *
     * @return bool
     */
    public function authRedirect()
    {
        try {
            return $this->oidcClient->authenticate();
        } catch (OpenIDConnectClientException $e) {
            Log::error('Failed to verify auth redirect: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * KeycloakのuserIdを取得する
     *
     * @return string
     * @throws Exception
     */
    public function getUserId()
    {
        try {
            $userId = $this->oidcClient->requestUserInfo('sub');
            if (!$userId) {
                Log::error('Keycloak userId not exist');
                throw new Exception('Keycloak userId not exist');
            }
            return $userId;
        } catch(OpenIDConnectClientException $e) {
            Log::error('Failed to get userInfo: ' . $e->getMessage());
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @return string
     */
    public function getAccessToken()
    {
        return $this->oidcClient->getAccessToken();
    }

    /**
     * @return string
     */
    public function getIdToken()
    {
        return $this->oidcClient->getIdToken();
    }

    /**
     * @return string
     */
    public function getRefreshToken()
    {
        return $this->oidcClient->getRefreshToken();
    }

    /**
     * @return void
     */
    public function putTokens()
    {
        Session::put('sso_access_token', $this->getAccessToken());
        Session::put('sso_id_token', $this->getIdToken());
        Session::put('sso_refresh_token', $this->getRefreshToken());
    }
}