<?php
Route::group([
    'prefix' => 'games/apply/release'
], function () {
    // リリース申請の審査用の画像を審査に提出する
    Route::post('examinationimages/{id}/{device}', [
        'as' => 'Games.apply.release.examinationimages',
        'uses' => 'GameApplyReleaseController@storeExaminationImages',
    ]);
    // リリース申請のゲーム紹介ページ：デザイン部分素材を審査に提出する
    Route::post('introductionimages/{id}/{device}', [
        'as' => 'Games.apply.release.introductionimages',
        'uses' => 'GameApplyReleaseController@storeIntroductionImages',
    ]);
    // リリース申請のプラットフォーム上に掲載される画像を審査に提出
    Route::post('platformimages/{id}/{device}', [
        'as' => 'Games.apply.release.platformimages',
        'uses' => 'GameApplyReleaseController@storePlatformImages',
    ]);
    // リリース申請の公式サイト検証を審査に提出する
    Route::post('releasesite/{id}/{device}', [
        'as' => 'Games.apply.release.releasesite',
        'uses' => 'GameApplyReleaseController@storeReleasesite',
    ]);
    // コミュニティを審査に提出：事前登録
    Route::post('community/{id}/{device}', [
        'as' => 'Games.apply.release.community',
        'uses' => 'GameApplyReleaseController@storeCommunity',
    ]);
    // 動作検証を審査に提出：リリース申請
    Route::post('verification/{id}/{device}', [
        'as' => 'Games.apply.release.verification',
        'uses' => 'GameApplyReleaseController@storeVerification',
    ]);
    // リリース申請のゲーム情報入力を審査に提出する
    Route::post('gameinformation/{id}/{device}', [
        'as' => 'Games.apply.release.gameinformation',
        'uses' => 'GameApplyReleaseController@storeGameInformation',
    ]);
    // リリース申請のLinksmateを審査に提出する
    Route::post('linksmate/{id}/{device}', [
        'as' => 'Games.apply.release.linksmate',
        'uses' => 'GameApplyReleaseController@storeLinksmate',
    ]);
    // Win対応環境を審査に提出する
    Route::post('windowsSupportedEnvironment/{id}/{device}', [
        'as' => 'Games.apply.release.windowssupportedenvironment',
        'uses' => 'GameApplyReleaseController@storeWindowsSupportedEnvironment',
    ]);
    // Mac対応環境を審査に提出する
    Route::post('macSupportedEnvironment/{id}/{device}', [
        'as' => 'Games.apply.release.macsupportedenvironment',
        'uses' => 'GameApplyReleaseController@storeMacSupportedEnvironment',
    ]);
    // CEROを審査に提出する
    Route::post('cero/{id}/{device}', [
        'as' => 'Games.apply.release.cero',
        'uses' => 'GameApplyReleaseController@storeCero',
    ]);
});
