<?php

Route::group([
    'prefix' => 'clgameimage'
], function () {
    Route::get(
        '/{id?}/{imageType?}',
        [
            'as'   => 'ClGameImage.index',
            'uses' => 'ClGameImageController@index',
        ]
    )->where('imageType', '[0-9]+');

    Route::get(
        'index/{id?}/{imageType?}',
        [
            'as'   => 'ClGameImage.index',
            'uses' => 'ClGameImageController@index',
        ]
    )->where('imageType', '[0-9]+');

    Route::get(
        'show/{id}',
        [
            'as'   => 'ClGameImage.show',
            'uses' => 'ClGameImageController@show',
        ]
    );

    Route::post(
        'post/store',
        [
            'as'   => 'ClGameImage.poststore',
            'uses' => 'ClGameImageController@postStore',
        ]
    );
    Route::post(
        'post/update',
        [
            'as'   => 'ClGameImage.postupdate',
            'uses' => 'ClGameImageController@postUpdate',
        ]
    );
    Route::post(
        'post/destroy',
        [
            'as'   => 'ClGameImage.postdestroy',
            'uses' => 'ClGameImageController@postDestroy',
        ]
    );

    Route::get(
        'delete/confirm/{id}',
        [
            'as'   => 'ClGameImage.deleteconfirm',
            'uses' => 'ClGameImageController@deleteConfirm',
        ]
    );
    Route::post(
        'delete/store',
        [
            'as'   => 'ClGameImage.deletestore',
            'uses' => 'ClGameImageController@deleteStore',
        ]
    );
    Route::post(
        'recommend/store',
        [
            'as'   => 'ClGameImage.recommendstore',
            'uses' => 'ClGameImageController@recommendStore',
        ]
    );
    Route::post(
        'recommend/destroy',
        [
            'as'   => 'ClGameImage.recommenddestroy',
            'uses' => 'ClGameImageController@recommendDestroy',
        ]
    );
});
