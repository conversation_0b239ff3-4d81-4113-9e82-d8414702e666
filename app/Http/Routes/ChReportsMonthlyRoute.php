<?php
Route::group([
    'prefix' => 'ch_reports/monthly'
], function () {
    Route::any('/', [
        'as'   => 'ChReportsMonthly.index',
        'uses' => 'ChReportsMonthlyController@index',
    ]);
    Route::any('index', [
        'as'   => 'ChReportsMonthly.index',
        'uses' => 'ChReportsMonthlyController@index',
    ]);
    Route::any('csv/download', [
        'as'   => 'ChReportsMonthly.csvdownload',
        'uses' => 'ChReportsMonthlyController@csvdownload',
    ]);
    Route::any('csv/notax/download', [
        'as'   => 'ChReportsMonthly.csvnotaxdownload',
        'uses' => 'ChReportsMonthlyController@csvdownload',
    ]);
    Route::any('csv/accounting/download', [
        'as'   => 'ChReportsMonthly.csvaccountingdownload',
        'uses' => 'ChReportsMonthlyController@csvdownload',
    ]);
});
