<?php
Route::group([
    'prefix' => 'subscription/inquiry'
], function () {
    // 定期購入問い合わせ
    Route::get('/', [
        'as' => 'SubscriptionInquiry.index',
        'uses' => 'SubscriptionInquiryController@index'
    ]);
    Route::get('index', [
        'as' => 'SubscriptionInquiry.index',
        'uses' => 'SubscriptionInquiryController@index'
    ]);
    Route::post('index', [
        'as' => 'SubscriptionInquiry.search',
        'uses' => 'SubscriptionInquiryController@search'
    ]);
});
