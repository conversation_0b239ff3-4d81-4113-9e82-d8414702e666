<?php
Route::group([
    'prefix' => 'graph/monthly'
], function () {
    Route::get('/', [
        'as' => 'GraphMonthly.index',
        'uses' => 'GraphMonthlyController@index'
    ]);
    Route::get('index', [
        'as' => 'GraphMonthly.index',
        'uses' => 'GraphMonthlyController@index'
    ]);
    Route::post('index', [
        'as' => 'GraphMonthly.search',
        'uses' => 'GraphMonthlyController@search'
    ]);
});
