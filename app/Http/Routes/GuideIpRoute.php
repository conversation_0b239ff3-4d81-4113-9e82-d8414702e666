<?php
Route::group(
    [
        'prefix' => 'guide_ip'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideIp.index',
                'uses' => 'GuideIpController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideIp.index',
                'uses' => 'GuideIpController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideIp.create',
                'uses' => 'GuideIpController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideIp.createconfirm',
                'uses' => 'GuideIpController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideIp.store',
                'uses' => 'GuideIpController@store',
            ]
        );

        Route::any(
            'edit/{id}/{outerIpid}',
            [
                'as'   => 'GuideIp.edit',
                'uses' => 'GuideIpController@edit',
            ]
        )->where('outerIpid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideIp.editconfirm',
                'uses' => 'GuideIpController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideIp.update',
                'uses' => 'GuideIpController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideIp.destroy',
                'uses' => 'GuideIpController@destroy',
            ]
        );
    }
);
