<?php
Route::group(
    [
        'prefix' => 'sbx_users'
    ],
    function () {
        Route::get(
            '/',
            [
                'as' => 'SbxUsers.index',
                'uses' => 'SbxUsersController@index'
            ]
        );
        Route::get(
            'index',
            [
                'as' => 'SbxUsers.index',
                'uses' => 'SbxUsersController@index'
            ]
        );
        Route::any(
            'create',
            [
                'as' => 'SbxUsers.create',
                'uses' => 'SbxUsersController@create'
            ]
        );
        Route::post(
            'createconfirm',
            [
                'as' => 'SbxUsers.createconfirm',
                'uses' => 'SbxUsersController@createconfirm'
            ]
        );
        Route::post(
            'store',
            [
                'as' => 'SbxUsers.store',
                'uses' => 'SbxUsersController@store'
            ]
        );
        Route::any(
            'edit',
            [
                'as' => 'SbxUsers.edit',
                'uses' => 'SbxUsersController@edit'
            ]
        );
        Route::post(
            'editconfirm',
            [
                'as' => 'SbxUsers.editconfirm',
                'uses' => 'SbxUsersController@editconfirm'
            ]
        );
        Route::post(
            'update',
            [
                'as' => 'SbxUsers.update',
                'uses' => 'SbxUsersController@update'
            ]
        );
        Route::post(
            'destroy',
            [
                'as' => 'SbxUsers.destroy',
                'uses' => 'SbxUsersController@destroy'
            ]
        );
    }
);
