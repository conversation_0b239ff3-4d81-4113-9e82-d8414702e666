<?php
Route::group([
    'prefix' => 'games'
], function () {
    Route::get('/', [
        'as' => 'Games.index',
        'uses' => 'GamesController@index'
    ]);
    Route::get('index', [
        'as' => 'Games.index',
        'uses' => 'GamesController@index'
    ]);
    Route::any('basic/edit/{id}', [
        'as' => 'Games.basic.edit',
        'uses' => 'GamesController@basicEdit'
    ]);
    Route::post('basic/editconfirm', [
        'as' => 'Games.basic.editconfirm',
        'uses' => 'GamesController@basicEditConfirm'
    ]);
    Route::post('basic/update', [
        'as' => 'Games.basic.update',
        'uses' => 'GamesController@basicUpdate'
    ]);
    Route::any('device/pc/edit/{id}', [
        'as' => 'Games.device.pc.edit',
        'uses' => 'GamesController@devicePcEdit'
    ]);
    Route::post('device/pc/editconfirm', [
        'as' => 'Games.device.pc.editconfirm',
        'uses' => 'GamesController@devicePcEditConfirm'
    ]);
    Route::post('device/pc/update', [
        'as' => 'Games.device.pc.update',
        'uses' => 'GamesController@devicePcUpdate'
    ]);
    Route::any('device/sp/edit/{id}', [
        'as' => 'Games.device.sp.edit',
        'uses' => 'GamesController@deviceSpEdit'
    ]);
    Route::post('device/sp/editconfirm', [
        'as' => 'Games.device.sp.editconfirm',
        'uses' => 'GamesController@deviceSpEditConfirm'
    ]);
    Route::post('device/sp/update', [
        'as' => 'Games.device.sp.update',
        'uses' => 'GamesController@deviceSpUpdate'
    ]);
    Route::any('device/mobile/edit/{id}', [
        'as' => 'Games.device.mobile.edit',
        'uses' => 'GamesController@deviceMobileEdit'
    ]);
    Route::post('device/mobile/editconfirm', [
        'as' => 'Games.device.mobile.editconfirm',
        'uses' => 'GamesController@deviceMobileEditConfirm'
    ]);
    Route::post('device/mobile/update', [
        'as' => 'Games.device.mobile.update',
        'uses' => 'GamesController@deviceMobileUpdate'
    ]);
    Route::any('device/android/app/edit/{id}', [
        'as' => 'Games.device.android.app.edit',
        'uses' => 'GamesController@deviceAndroidAppEdit'
    ]);
    Route::post('device/android/app/editconfirm', [
        'as' => 'Games.device.android.app.editconfirm',
        'uses' => 'GamesController@deviceAndroidAppEditConfirm'
    ]);
    Route::post('device/android/app/update', [
        'as' => 'Games.device.android.app.update',
        'uses' => 'GamesController@deviceAndroidAppUpdate'
    ]);
    Route::any('device/ios/app/edit/{id}', [
        'as' => 'Games.device.ios.app.edit',
        'uses' => 'GamesController@deviceIosAppEdit'
    ]);
    Route::post('device/ios/app/editconfirm', [
        'as' => 'Games.device.ios.app.editconfirm',
        'uses' => 'GamesController@deviceIosAppEditConfirm'
    ]);
    Route::post('device/ios/app/update', [
        'as' => 'Games.device.ios.app.update',
        'uses' => 'GamesController@deviceIosAppUpdate'
    ]);
    Route::any('device/apkcloud/edit/{id}', [
        'as' => 'Games.device.apkcloud.edit',
        'uses' => 'GamesController@deviceApkCloudEdit'
    ]);
    Route::post('device/apkcloud/editconfirm', [
        'as' => 'Games.device.apkcloud.editconfirm',
        'uses' => 'GamesController@deviceApkCloudEditConfirm'
    ]);
    Route::post('device/apkcloud/update', [
        'as' => 'Games.device.apkcloud.update',
        'uses' => 'GamesController@deviceApkCloudUpdate'
    ]);
    Route::any('device/emulator/edit/{id}', [
        'as' => 'Games.device.emulator.edit',
        'uses' => 'GamesController@deviceEmulatorEdit'
    ]);
    Route::post('device/emulator/editconfirm', [
        'as' => 'Games.device.emulator.editconfirm',
        'uses' => 'GamesController@deviceEmulatorEditConfirm'
    ]);
    Route::post('device/emulator/update', [
        'as' => 'Games.device.emulator.update',
        'uses' => 'GamesController@deviceEmulatorUpdate'
    ]);
    Route::any('apk/edit/{id}', [
        'as' => 'Games.apk.edit',
        'uses' => 'GamesController@apkEdit'
    ]);
    Route::any('apk/editconfirm', [
        'as' => 'Games.apk.editconfirm',
        'uses' => 'GamesController@apkEditConfirm'
    ]);
    Route::post('apk/update', [
        'as' => 'Games.apk.update',
        'uses' => 'GamesController@apkUpdate'
    ]);
    Route::any('ipa/edit/{id}', [
        'as' => 'Games.ipa.edit',
        'uses' => 'GamesController@ipaEdit'
    ]);
    Route::any('ipa/editconfirm/{id}', [
        'as' => 'Games.ipa.editconfirm',
        'uses' => 'GamesController@ipaEditConfirm'
    ]);
    Route::post('ipa/update', [
        'as' => 'Games.ipa.update',
        'uses' => 'GamesController@ipaUpdate'
    ]);
    Route::any('emulatorapk/edit/{id}', [
        'as' => 'Games.emulatorapk.edit',
        'uses' => 'GamesController@emulatorApkEdit'
    ]);
    Route::any('emulatorapk/editconfirm', [
        'as' => 'Games.emulatorapk.editconfirm',
        'uses' => 'GamesController@emulatorApkEditConfirm'
    ]);
    Route::post('emulatorapk/update', [
        'as' => 'Games.emulatorapk.update',
        'uses' => 'GamesController@emulatorApkUpdate'
    ]);
    Route::get('latest/{id}/list', [
        'as' => 'Games.latest.list',
        'uses' => 'GamesController@latestList'
    ]);
    Route::any('latest/{id}/create', [
        'as' => 'Games.latest.create',
        'uses' => 'GamesController@latestCreate'
    ]);
    Route::post('latest/createconfirm', [
        'as' => 'Games.latest.createconfirm',
        'uses' => 'GamesController@latestCreateConfirm'
    ]);
    Route::post('latest/store', [
        'as' => 'Games.latest.store',
        'uses' => 'GamesController@latestStore'
    ]);
    Route::any('latest/edit/{id}', [
        'as' => 'Games.latest.edit',
        'uses' => 'GamesController@latestEdit'
    ]);
    Route::post('latest/editconfirm', [
        'as' => 'Games.latest.editconfirm',
        'uses' => 'GamesController@latestEditConfirm'
    ]);
    Route::post('latest/update', [
        'as' => 'Games.latest.update',
        'uses' => 'GamesController@latestUpdate'
    ]);
    Route::post('latest/destroy', [
        'as' => 'Games.latest.destroy',
        'uses' => 'GamesController@latestDestroy'
    ]);
    Route::any('emulatorkeymapping/edit/{id}', [
        'as' => 'Games.emulatorkeymapping.edit',
        'uses' => 'GamesController@emulatorKeyMappingEdit'
    ]);
    Route::any('emulatorkeymapping/editconfirm', [
        'as' => 'Games.emulatorkeymapping.editconfirm',
        'uses' => 'GamesController@emulatorKeyMappingEditConfirm'
    ]);
    Route::post('emulatorkeymapping/update', [
        'as' => 'Games.emulatorkeymapping.update',
        'uses' => 'GamesController@emulatorKeyMappingUpdate'
    ]);
    Route::get('applicationToken/store', [
        'as' => 'Games.applicationToken.store',
        'uses' => 'GamesController@storeApplicationToken'
    ]);
    Route::post('applicationToken/delete', [
        'as' => 'Games.applicationToken.delete',
        'uses' => 'GamesController@deleteApplicationToken'
    ]);
    Route::match(['get', 'post'], 'titleGroup/edit/{id}', [
        'as' => 'Games.titleGroup.edit',
        'uses' => 'GamesController@titleGroupEdit'
    ]);
    Route::post('titleGroup/editconfirm', [
        'as' => 'Games.titleGroup.editconfirm',
        'uses' => 'GamesController@titleGroupEditConfirm'
    ]);
    Route::post('titleGroup/update', [
        'as' => 'Games.titleGroup.update',
        'uses' => 'GamesController@titleGroupUpdate'
    ]);
    Route::post('board/generate-url/{id}', [
        'as' => 'Games.board.generateUrl',
        'uses' => 'GamesController@generateSignedUrl',
    ]);
    Route::post('board/comment/{id}', [
        'as' => 'Games.board.comment',
        'uses' => 'GamesController@addBoardComment',
    ]);
    Route::get('board/comment/children', [
        'as' => 'Games.board.comment.children',
        'uses' => 'GamesController@getMessageChildren'
    ]);
    Route::put('board/comment/{id}/{commentId}', [
        'as' => 'Games.board.comment.update',
        'uses' => 'GamesController@updateBoardComment',
    ]);
    Route::post('board/comment/{id}/{commentId}/child', [
        'as' => 'Games.board.comment.child',
        'uses' => 'GamesController@addBoardChildComment',
    ]);
    Route::put('board/comment/{id}/{commentId}/child/{childCommentId}', [
        'as' => 'Games.board.comment.update.child',
        'uses' => 'GamesController@updateBoardChildComment',
    ]);
    // 通知先更新
    Route::get('notification/update/{id}/{device}', [
        'as' => 'Games.notification.update',
        'uses' => 'GamesController@updateNotification',
    ]);
});
