<?php
Route::group([
    'prefix' => 'reports/monthly'
], function () {
    Route::get('/', [
        'as' => 'ReportsMonthly.index',
        'uses' => 'ReportsMonthlyController@index'
    ]);
    Route::get('index', [
        'as' => 'ReportsMonthly.index',
        'uses' => 'ReportsMonthlyController@index'
    ]);
    Route::post('csv/download', [
        'as' => 'ReportsMonthly.csv.download',
        'uses' => 'ReportsMonthlyController@csvDownload'
    ]);
    Route::any('csv/notax/download', [
        'as'   => 'ReportsMonthly.csvnotaxdownload',
        'uses' => 'ReportsMonthly@csvdownload',
    ]);
    Route::any('csv/accounting/download', [
        'as'   => 'ReportsMonthly.csvaccountingdownload',
        'uses' => 'ReportsMonthly@csvdownload',
    ]);
});
