<?php
Route::group([
    'prefix' => 'inquiries',
    'where' => [
        'id' => '[a-z0-9-]+'
    ]
], function () {
    Route::get('/', [
        'as' => 'Inquiries.index',
        'uses' => 'InquiriesController@index'
    ]);
    Route::any('index', [
        'as' => 'Inquiries.index',
        'uses' => 'InquiriesController@index'
    ]);
    Route::post('csv/download', [
        'as' => 'Inquiries.csv.download',
        'uses' => 'InquiriesController@csvDownload'
    ]);
    Route::post('status/update', [
        'as' => 'Inquiries.status.update',
        'uses' => 'InquiriesController@statusUpdate'
    ]);
    Route::post('replystatus/update', [
        'as' => 'Inquiries.replystatus.update',
        'uses' => 'InquiriesController@replyStatusUpdate'
    ]);
    Route::post('category/update', [
        'as' => 'Inquiries.category.update',
        'uses' => 'InquiriesController@categoryUpdate'
    ]);
    Route::post('category/getchildren/', [
        'as' => 'Inquiries.category.getchildren',
        'uses' => 'InquiriesController@getChildrenCategory'
    ]);
    Route::post('memo/update', [
        'as' => 'Inquiries.memo.update',
        'uses' => 'InquiriesController@memoUpdate'
    ]);
    Route::any('reply/create/{id}', [
        'as' => 'Inquiries.reply.create',
        'uses' => 'InquiriesController@replyCreate'
    ]);
    Route::post('reply/createconfirm', [
        'as' => 'Inquiries.reply.createconfirm',
        'uses' => 'InquiriesController@replyCreateConfirm'
    ]);
    Route::post('reply/store', [
        'as' => 'Inquiries.reply.store',
        'uses' => 'InquiriesController@replyStore'
    ]);
    Route::any('reply/all/create', [
        'as' => 'Inquiries.reply.all.create',
        'uses' => 'InquiriesController@replyAllCreate'
    ]);
    Route::post('reply/all/createconfirm', [
        'as' => 'Inquiries.reply.all.createconfirm',
        'uses' => 'InquiriesController@replyAllCreateConfirm'
    ]);
    Route::post('reply/all/store', [
        'as' => 'Inquiries.reply.all.store',
        'uses' => 'InquiriesController@replyAllStore'
    ]);
});
