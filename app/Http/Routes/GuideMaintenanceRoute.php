<?php
Route::group(
    [
        'prefix' => 'guide_maintenance'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideMaintenance.index',
                'uses' => 'GuideMaintenanceController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideMaintenance.index',
                'uses' => 'GuideMaintenanceController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideMaintenance.create',
                'uses' => 'GuideMaintenanceController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideMaintenance.createconfirm',
                'uses' => 'GuideMaintenanceController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideMaintenance.store',
                'uses' => 'GuideMaintenanceController@store',
            ]
        );

        Route::any(
            'edit/{id}/{mainteid}',
            [
                'as'   => 'GuideMaintenance.edit',
                'uses' => 'GuideMaintenanceController@edit',
            ]
        )->where('mainteid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideMaintenance.editconfirm',
                'uses' => 'GuideMaintenanceController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideMaintenance.update',
                'uses' => 'GuideMaintenanceController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideMaintenance.destroy',
                'uses' => 'GuideMaintenanceController@destroy',
            ]
        );
    }
);
