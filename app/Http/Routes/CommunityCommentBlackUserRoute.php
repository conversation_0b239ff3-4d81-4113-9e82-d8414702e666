<?php
Route::group([
    'prefix' => 'community_comment_black_user'
], function () {

    Route::get(
        '/',
        [
            'as'   => 'CommunityCommentBlackUser.index',
            'uses' => 'CommunityCommentBlackUserController@index',
        ]
    );
    Route::any(
        'index',
        [
            'as'   => 'CommunityCommentBlackUser.index',
            'uses' => 'CommunityCommentBlackUserController@index',
        ]
    );
    Route::any(
        'destroy',
        [
            'as'   => 'CommunityCommentBlackUser.destroy',
            'uses' => 'CommunityCommentBlackUserController@destroy',
        ]
    );

    Route::any(
        'create',
        [
            'as'   => 'CommunityCommentBlackUser.create',
            'uses' => 'CommunityCommentBlackUserController@create',
        ]
    );

    Route::post(
        'create/confirm',
        [
            'as'   => 'CommunityCommentBlackUser.createConfirm',
            'uses' => 'CommunityCommentBlackUserController@createConfirm',
        ]
    );
    Route::post(
        'create/store',
        [
            'as'   => 'CommunityCommentBlackUser.createStore',
            'uses' => 'CommunityCommentBlackUserController@createStore',
        ]
    );
});
