<?php
Route::group(
    [
        'prefix' => 'guide_twitter_dm'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideTwitterDm.index',
                'uses' => 'GuideTwitterDmController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideTwitterDm.index',
                'uses' => 'GuideTwitterDmController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideTwitterDm.create',
                'uses' => 'GuideTwitterDmController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideTwitterDm.createconfirm',
                'uses' => 'GuideTwitterDmController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideTwitterDm.store',
                'uses' => 'GuideTwitterDmController@store',
            ]
        );

        Route::any(
            'edit/{id}/{dmscheduleid}',
            [
                'as'   => 'GuideTwitterDm.edit',
                'uses' => 'GuideTwitterDmController@edit',
            ]
        )->where('dmscheduleid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideTwitterDm.editconfirm',
                'uses' => 'GuideTwitterDmController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideTwitterDm.update',
                'uses' => 'GuideTwitterDmController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideTwitterDm.destroy',
                'uses' => 'GuideTwitterDmController@destroy',
            ]
        );
    }
);
