<?php
Route::group([
    'prefix' => 'communities'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'Communities.index',
            'uses' => 'CommunitiesController@index',
        ]
    );
    Route::any(
        'index',
        [
            'as'   => 'Communities.index',
            'uses' => 'CommunitiesController@index',
        ]
    );
    Route::any(
        'show/{id}',
        [
            'as'   => 'Communities.show',
            'uses' => 'CommunitiesController@show',
        ]
    );
    Route::any(
        'create',
        [
            'as'   => 'Communities.create',
            'uses' => 'CommunitiesController@create',
        ]
    );
    Route::post(
        'createconfirm',
        [
            'as'   => 'Communities.createconfirm',
            'uses' => 'CommunitiesController@createconfirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'Communities.store',
            'uses' => 'CommunitiesController@store',
        ]
    );
    Route::any(
        'edit/{id}',
        [
            'as'   => 'Communities.edit',
            'uses' => 'CommunitiesController@edit',
        ]
    );
    Route::post(
        'editconfirm',
        [
            'as'   => 'Communities.editconfirm',
            'uses' => 'CommunitiesController@editconfirm',
        ]
    );
    Route::post(
        'update',
        [
            'as'   => 'Communities.update',
            'uses' => 'CommunitiesController@update',
        ]
    );
});
