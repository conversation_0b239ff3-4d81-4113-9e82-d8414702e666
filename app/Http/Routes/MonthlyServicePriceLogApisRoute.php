<?php
Route::group([
    'prefix' => 'monthly_service_price_log_apis'
], function () {
    Route::get('/', [
            'as'   => 'MonthlyServicePriceLogApis.index',
            'uses' => 'MonthlyServicePriceLogApisController@index',
    ]);
    Route::any('index', [
            'as'   => 'MonthlyServicePriceLogApis.index',
            'uses' => 'MonthlyServicePriceLogApisController@index',
    ]);
    Route::any('csv/download', [
            'as'   => 'MonthlyServicePriceLogApis.csvdownload',
            'uses' => 'MonthlyServicePriceLogApisController@csvdownload',
    ]);
});
