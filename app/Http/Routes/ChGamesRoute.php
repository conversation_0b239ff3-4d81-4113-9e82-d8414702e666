<?php

Route::group([
    'prefix' => 'chgames'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'ChGames.index',
            'uses' => 'ChGamesController@index',
        ]
    );
    Route::get(
        'index',
        [
            'as'   => 'ChGames.index',
            'uses' => 'ChGamesController@index',
        ]
    );
    Route::any(
        'edit/{id}',
        [
            'as'   => 'ChGames.edit',
            'uses' => 'ChGamesController@edit',
        ]
    );
    Route::post(
        'editconfirm',
        [
            'as'   => 'ChGames.editconfirm',
            'uses' => 'ChGamesController@editconfirm',
        ]
    );
    Route::post(
        'update',
        [
            'as'   => 'ChGames.update',
            'uses' => 'ChGamesController@update',
        ]
    );
    Route::get('board/{id}', [
        'as' => 'ChGames.board',
        'uses' => 'ChGamesController@board',
    ]);
    Route::post('board/generate-url/{id}', [
        'as' => 'ChGames.board.generateUrl',
        'uses' => 'ChGamesController@generateSignedUrl',
    ]);
    Route::post('board/comment/{id}', [
        'as' => 'ChGames.board.comment',
        'uses' => 'ChGamesController@addBoardComment',
    ]);
    Route::get('board/comment/children', [
        'as' => 'ChGames.board.comment.children',
        'uses' => 'ChGamesController@getMessageChildren'
    ]);
    Route::put('board/comment/{id}/{commentId}', [
        'as' => 'ChGames.board.comment.update',
        'uses' => 'ChGamesController@updateBoardComment',
    ]);
    Route::post('board/comment/{id}/{commentId}/child', [
        'as' => 'ChGames.board.comment.child',
        'uses' => 'ChGamesController@addBoardChildComment',
    ]);
    Route::put('board/comment/{id}/{commentId}/child/{childCommentId}', [
        'as' => 'ChGames.board.comment.update.child',
        'uses' => 'ChGamesController@updateBoardChildComment',
    ]);
});
