<?php

Route::group([
    'prefix' => 'clgamecontentimage'
], function () {
    Route::get(
        'index/{id?}',
        [
            'as' => 'ClGameContentImage.index',
            'uses' => 'ClGameContentImageController@index',
        ]
    );

    Route::get(
        'register',
        [
            'as' => 'ClGameContentImage.register',
            'uses' => 'ClGameContentImageController@register',
        ]
    );

    Route::post(
        'register/store',
        [
            'as' => 'ClGameContentImage.register.store',
            'uses' => 'ClGameContentImageController@registerStore',
        ]
    );

    Route::get(
        'register/completed',
        [
            'as' => 'ClGameContentImage.register.completed',
            'uses' => 'ClGameContentImageController@registerCompleted',
        ]
    );

    Route::post(
        'register/upload/check',
        [
            'as' => 'ClGameContentImage.register.upload.check',
            'uses' => 'ClGameContentImageController@checkUploadFile',
        ]
    );
});
