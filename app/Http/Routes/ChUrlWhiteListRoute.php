<?php

Route::group(
    [
        'prefix' => 'churlwhitelist'
    ],
    function () {
        Route::get(
            '/',
            [
                'as'   => 'ChUrlWhiteList.index',
                'uses' => 'ChUrlWhiteListController@index',
            ]
        );
        Route::get(
            'index',
            [
                'as'   => 'ChUrlWhiteList.index',
                'uses' => 'ChUrlWhiteListController@index',
            ]
        );

        Route::any(
            'create',
            [
                'as'   => 'ChUrlWhiteList.create',
                'uses' => 'ChUrlWhiteListController@create',
            ]
        );
        Route::post(
            'createconfirm',
            [
                'as'   => 'ChUrlWhiteList.createconfirm',
                'uses' => 'ChUrlWhiteListController@createconfirm',
            ]
        );
        Route::post(
            'store',
            [
                'as'   => 'ChUrlWhiteList.store',
                'uses' => 'ChUrlWhiteListController@store',
            ]
        );

        Route::any(
            'edit',
            [
                'as'   => 'ChUrlWhiteList.edit',
                'uses' => 'ChUrlWhiteListController@edit',
            ]
        );
        Route::post(
            'editconfirm',
            [
                'as'   => 'ChUrlWhiteList.editconfirm',
                'uses' => 'ChUrlWhiteListController@editconfirm',
            ]
        );
        Route::post(
            'update',
            [
                'as'   => 'ChUrlWhiteList.update',
                'uses' => 'ChUrlWhiteListController@update',
            ]
        );

        Route::post(
            'destroy',
            [
                'as'   => 'ChUrlWhiteList.destroy',
                'uses' => 'ChUrlWhiteListController@destroy',
            ]
        );
    });
