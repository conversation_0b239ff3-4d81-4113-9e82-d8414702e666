<?php

Route::group([
    'prefix' => 'chgamecontentimage'
], function () {
    Route::get(
        'index/{id?}',
        [
            'as' => 'ChGameContentImage.index',
            'uses' => 'ChGameContentImageController@index',
        ]
    );

    Route::get(
        'register',
        [
            'as' => 'ChGameContentImage.register',
            'uses' => 'ChGameContentImageController@register',
        ]
    );

    Route::post(
        'register/store',
        [
            'as' => 'ChGameContentImage.register.store',
            'uses' => 'ChGameContentImageController@registerStore',
        ]
    );

    Route::get(
        'register/completed',
        [
            'as' => 'ChGameContentImage.register.completed',
            'uses' => 'ChGameContentImageController@registerCompleted',
        ]
    );

    Route::post(
        'register/upload/check',
        [
            'as' => 'ChGameContentImage.register.upload.check',
            'uses' => 'ChGameContentImageController@checkUploadFile',
        ]
    );
});
