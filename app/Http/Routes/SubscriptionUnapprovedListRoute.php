<?php
Route::group([
    'prefix' => 'subscription/unapprovedlist'
], function () {
    // 未承認アイテム一覧
    Route::get('/', [
        'as' => 'SubscriptionUnapprovedList.index',
        'uses' => 'SubscriptionUnapprovedListController@index'
    ]);
    Route::any('index', [
        'as' => 'SubscriptionUnapprovedList.index',
        'uses' => 'SubscriptionUnapprovedListController@index'
    ]);
    // 未承認アイテム詳細
    Route::any('detail', [
        'as' => 'SubscriptionUnapprovedList.detail',
        'uses' => 'SubscriptionUnapprovedListController@detail'
    ]);
    // 未承認アイテム承認
    Route::post('detail/approve', [
        'as' => 'SubscriptionUnapprovedList.approve',
        'uses' => 'SubscriptionUnapprovedListController@approve'
    ]);
});
