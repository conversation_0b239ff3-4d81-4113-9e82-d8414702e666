<?php
Route::group(
    [
        'prefix' => 'guide_form'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideForm.index',
                'uses' => 'GuideFormController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideForm.index',
                'uses' => 'GuideFormController@index'
            ]
        );

        Route::any(
            'edit/{id}',
            [
                'as'   => 'GuideForm.edit',
                'uses' => 'GuideFormController@edit',
            ]
        );
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideForm.editconfirm',
                'uses' => 'GuideFormController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideForm.update',
                'uses' => 'GuideFormController@update',
            ]
        );
    }
);
