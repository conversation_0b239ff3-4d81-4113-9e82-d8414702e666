<?php
Route::group([
    'prefix' => 'topic_autocreations'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'TopicAutocreations.index',
            'uses' => 'TopicAutocreationsController@index',
        ]
    );
    Route::any(
        'index',
        [
            'as'   => 'TopicAutocreations.index',
            'uses' => 'TopicAutocreationsController@index',
        ]
    );
    Route::any(
        'show/{id}',
        [
            'as'   => 'TopicAutocreations.show',
            'uses' => 'TopicAutocreationsController@show',
        ]
    );
    Route::any(
        'create',
        [
            'as'   => 'TopicAutocreations.create',
            'uses' => 'TopicAutocreationsController@create',
        ]
    );
    Route::post(
        'createconfirm',
        [
            'as'   => 'TopicAutocreations.createconfirm',
            'uses' => 'TopicAutocreationsController@createconfirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'TopicAutocreations.store',
            'uses' => 'TopicAutocreationsController@store',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'TopicAutocreations.destroy',
            'uses' => 'TopicAutocreationsController@destroy',
        ]
    );
});
