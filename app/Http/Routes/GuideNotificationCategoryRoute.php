<?php
Route::group(
    [
        'prefix' => 'guide_notification_category'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideNotificationCategory.index',
                'uses' => 'GuideNotificationCategoryController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideNotificationCategory.index',
                'uses' => 'GuideNotificationCategoryController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideNotificationCategory.create',
                'uses' => 'GuideNotificationCategoryController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideNotificationCategory.createconfirm',
                'uses' => 'GuideNotificationCategoryController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideNotificationCategory.store',
                'uses' => 'GuideNotificationCategoryController@store',
            ]
        );

        Route::any(
            'edit/{id}/{noticcatid}',
            [
                'as'   => 'GuideNotificationCategory.edit',
                'uses' => 'GuideNotificationCategoryController@edit',
            ]
        )->where('noticcatid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideNotificationCategory.editconfirm',
                'uses' => 'GuideNotificationCategoryController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideNotificationCategory.update',
                'uses' => 'GuideNotificationCategoryController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideNotificationCategory.destroy',
                'uses' => 'GuideNotificationCategoryController@destroy',
            ]
        );
    }
);
