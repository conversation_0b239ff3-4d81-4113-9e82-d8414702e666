<?php
Route::group(
    [
        'prefix' => 'guide_banner'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideBanner.index',
                'uses' => 'GuideBannerController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideBanner.index',
                'uses' => 'GuideBannerController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideBanner.create',
                'uses' => 'GuideBannerController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideBanner.createconfirm',
                'uses' => 'GuideBannerController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideBanner.store',
                'uses' => 'GuideBannerController@store',
            ]
        );

        Route::any(
            'edit/{id}/{bannerid}',
            [
                'as'   => 'GuideBanner.edit',
                'uses' => 'GuideBannerController@edit',
            ]
        )->where('bannerid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideBanner.editconfirm',
                'uses' => 'GuideBannerController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideBanner.update',
                'uses' => 'GuideBannerController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideBanner.destroy',
                'uses' => 'GuideBannerController@destroy',
            ]
        );
    }
);
