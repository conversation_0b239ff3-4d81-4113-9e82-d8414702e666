<?php
Route::group([
    'prefix' => 'sbx_applications'
], function () {
    Route::get(
        '/',
        [
            'as' => 'SbxApplications.index',
            'uses' => 'SbxApplicationsController@index'
        ]
    );
    Route::get(
        'index',
        [
            'as' => 'SbxApplications.index',
            'uses' => 'SbxApplicationsController@index'
        ]
    );
    Route::any(
        'create',
        [
            'as' => 'SbxApplications.create',
            'uses' => 'SbxApplicationsController@create'
        ]
    );
    Route::any(
        'createconfirm',
        [
            'as' => 'SbxApplications.createconfirm',
            'uses' => 'SbxApplicationsController@createconfirm'
        ]
    );
    Route::post(
        'store',
        [
            'as' => 'SbxApplications.store',
            'uses' => 'SbxApplicationsController@store'
        ]
    );
    Route::any(
        'edit/{id}',
        [
            'as' => 'SbxApplications.edit',
            'uses' => 'SbxApplicationsController@edit'
        ]
    );
    Route::any(
        'editconfirm',
        [
            'as' => 'SbxApplications.editconfirm',
            'uses' => 'SbxApplicationsController@editconfirm'
        ]
    );
    Route::post(
        'update',
        [
            'as' => 'SbxApplications.update',
            'uses' => 'SbxApplicationsController@update'
        ]
    );
    Route::post(
        'destroy',
        [
            'as' => 'SbxApplications.destroy',
            'uses' => 'SbxApplicationsController@destroy'
        ]
    );
});
