<?php

Route::group([
    'prefix' => 'clgameimageapply'
], function () {
    Route::get(
        '/{id}',
        [
            'as'   => 'ClGameImageApply.index',
            'uses' => 'ClGameImageApplyController@index',
        ]
    );
    Route::get(
        'index/{id}',
        [
            'as'   => 'ClGameImageApply.index',
            'uses' => 'ClGameImageApplyController@index',
        ]
    );

    Route::get(
        'register/{id}',
        [
            'as'   => 'ClGameImageApply.register',
            'uses' => 'ClGameImageApplyController@register',
        ]
    );
    Route::post(
        'register/store',
        [
            'as'   => 'ClGameImageApply.registerstore',
            'uses' => 'ClGameImageApplyController@registerStore',
        ]
    );

    Route::post(
        'confirm',
        [
            'as'   => 'ClGameImageApply.confirm',
            'uses' => 'ClGameImageApplyController@confirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'ClGameImageApply.store',
            'uses' => 'ClGameImageApplyController@store',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'ClGameImageApply.destroy',
            'uses' => 'ClGameImageApplyController@destroy',
        ]
    );

    Route::get(
        'review',
        [
            'as'   => 'ClGameImageApply.reviewIndex',
            'uses' => 'ClGameImageApplyController@reviewIndex',
        ]
    );
    Route::any(
        'review/index',
        [
            'as'   => 'ClGameImageApply.reviewIndex',
            'uses' => 'ClGameImageApplyController@reviewIndex',
        ]
    );
    Route::get(
        'show/{id}',
        [
            'as'   => 'ClGameImageApply.show',
            'uses' => 'ClGameImageApplyController@show',
        ]
    );

    Route::post(
        'withdraw/confirm',
        [
            'as'   => 'ClGameImageApply.withdrawconfirm',
            'uses' => 'ClGameImageApplyController@withdrawConfirm',
        ]
    );
    Route::post(
        'withdraw/update',
        [
            'as'   => 'ClGameImageApply.withdrawupdate',
            'uses' => 'ClGameImageApplyController@withdrawUpdate',
        ]
    );
});
