<?php

Route::group(
    [
        'prefix' => 'password'
    ],
    function () {
        Route::any(
            'edit',
            [
                'as' => 'Password.edit',
                'uses' => 'PasswordController@edit'
            ]
        );
        Route::post(
            'update',
            [
                'as' => 'Password.update',
                'uses' => 'PasswordController@update'
            ]
        );
        Route::post(
            'xhrpasswordupdate',
            [
                'as' => 'Password.XHRupdate',
                'uses' => 'PasswordController@xhrPasswordUpdate'
            ]
        );
    }
);
