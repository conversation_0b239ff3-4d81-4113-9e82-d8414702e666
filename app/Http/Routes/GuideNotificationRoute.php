<?php
Route::group(
    [
        'prefix' => 'guide_notification'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideNotification.index',
                'uses' => 'GuideNotificationController@index'
            ]
        );
        Route::any(
            'index/{id}',
            [
                'as' => 'GuideNotification.index',
                'uses' => 'GuideNotificationController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideNotification.create',
                'uses' => 'GuideNotificationController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideNotification.createconfirm',
                'uses' => 'GuideNotificationController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideNotification.store',
                'uses' => 'GuideNotificationController@store',
            ]
        );

        Route::any(
            'edit/{id}/{noticid}',
            [
                'as'   => 'GuideNotification.edit',
                'uses' => 'GuideNotificationController@edit',
            ]
        )->where('noticid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideNotification.editconfirm',
                'uses' => 'GuideNotificationController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideNotification.update',
                'uses' => 'GuideNotificationController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideNotification.destroy',
                'uses' => 'GuideNotificationController@destroy',
            ]
        );
        Route::any(
            'viewstatus/update/',
            [
                'as'   => 'GuideNotification.viewstatusupdate',
                'uses' => 'GuideNotificationController@viewStatusUpdate',
            ]
        );

        Route::any(
            'makepreview/',
            [
                'as'   => 'GuideNotification.makepreview',
                'uses' => 'GuideNotificationController@makePreview',
            ]
        );
        Route::any(
            'image/upload/',
            [
                'as'   => 'GuideNotification.imageupload',
                'uses' => 'GuideNotificationController@imageUpload',
            ]
        );
        Route::any(
            'image/delete/',
            [
                'as'   => 'GuideNotification.imagedelete',
                'uses' => 'GuideNotificationController@imageDelete',
            ]
        );

        Route::get(
            'priority/{id}',
            [
                'as' => 'GuideNotification.priority',
                'uses' => 'GuideNotificationController@priority'
            ]
        );
        Route::post(
            'priority/update/',
            [
                'as'   => 'GuideNotification.priorityupdate',
                'uses' => 'GuideNotificationController@priorityUpdate',
            ]
        );
    }
);
