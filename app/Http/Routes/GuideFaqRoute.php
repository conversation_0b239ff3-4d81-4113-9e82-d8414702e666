<?php
Route::group(
    [
        'prefix' => 'guide_faq'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideFaq.index',
                'uses' => 'GuideFaqController@index'
            ]
        );
        Route::any(
            'index/{id}',
            [
                'as' => 'GuideFaq.index',
                'uses' => 'GuideFaqController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideFaq.create',
                'uses' => 'GuideFaqController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideFaq.createconfirm',
                'uses' => 'GuideFaqController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideFaq.store',
                'uses' => 'GuideFaqController@store',
            ]
        );

        Route::any(
            'edit/{id}/{faqid}',
            [
                'as'   => 'GuideFaq.edit',
                'uses' => 'GuideFaqController@edit',
            ]
        )->where('faqid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideFaq.editconfirm',
                'uses' => 'GuideFaqController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideFaq.update',
                'uses' => 'GuideFaqController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideFaq.destroy',
                'uses' => 'GuideFaqController@destroy',
            ]
        );
        Route::any(
            'viewstatus/update/',
            [
                'as'   => 'GuideFaq.viewstatusupdate',
                'uses' => 'GuideFaqController@viewStatusUpdate',
            ]
        );

        Route::any(
            'makepreview/',
            [
                'as'   => 'GuideFaq.makepreview',
                'uses' => 'GuideFaqController@makePreview',
            ]
        );
        Route::any(
            'image/upload/',
            [
                'as'   => 'GuideFaq.imageupload',
                'uses' => 'GuideFaqController@imageUpload',
            ]
        );
        Route::any(
            'image/delete/',
            [
                'as'   => 'GuideFaq.imagedelete',
                'uses' => 'GuideFaqController@imageDelete',
            ]
        );

        Route::any(
            'priority/{id}',
            [
                'as' => 'GuideFaq.priority',
                'uses' => 'GuideFaqController@priority'
            ]
        );
        Route::post(
            'priority/update/',
            [
                'as'   => 'GuideFaq.priorityupdate',
                'uses' => 'GuideFaqController@priorityUpdate',
            ]
        );
        Route::any(
            'priorityincategory/{id}',
            [
                'as' => 'GuideFaq.priorityincategory',
                'uses' => 'GuideFaqController@priorityInCategory'
            ]
        );
        Route::post(
            'priorityincategory/update/',
            [
                'as'   => 'GuideFaq.priorityincategoryupdate',
                'uses' => 'GuideFaqController@priorityIncategoryUpdate',
            ]
        );
    }
);
