<?php

Route::group([
    'prefix' => 'applicationpolicy'
], function () {
    Route::get(
        'index/{app_id}',
        [
            'as'   => 'ApplicationPolicy.index',
            'uses' => 'ApplicationPolicyController@index',
        ]
    );

    Route::any(
        'create/{app_id?}',
        [
            'as'   => 'ApplicationPolicy.create',
            'uses' => 'ApplicationPolicyController@create',
        ]
    );

    Route::post(
        'createconfirm',
        [
            'as'   => 'ApplicationPolicy.createconfirm',
            'uses' => 'ApplicationPolicyController@createconfirm',
        ]
    );

    Route::post(
        'store',
        [
            'as'   => 'ApplicationPolicy.store',
            'uses' => 'ApplicationPolicyController@store',
        ]
    );

    Route::any(
        'release/confirm/{app_id?}',
        [
            'as'   => 'ApplicationPolicy.release.confirm',
            'uses' => 'ApplicationPolicyController@releaseConfirm',
        ]
    );

    Route::post(
        'release/store',
        [
            'as'   => 'ApplicationPolicy.release.store',
            'uses' => 'ApplicationPolicyController@releaseStore',
        ]
    );
});
