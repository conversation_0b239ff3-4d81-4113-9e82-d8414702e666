<?php
Route::group(
    [
        'prefix' => 'guide_faq_category'
    ],
    function () {
        Route::any(
            '/{id}',
            [
                'as' => 'GuideFaqCategory.index',
                'uses' => 'GuideFaqCategoryController@index'
            ]
        );
        Route::any(
            'index/{id}',
            [
                'as' => 'GuideFaqCategory.index',
                'uses' => 'GuideFaqCategoryController@index'
            ]
        );

        Route::any(
            'create/{id}',
            [
                'as'   => 'GuideFaqCategory.create',
                'uses' => 'GuideFaqCategoryController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideFaqCategory.createconfirm',
                'uses' => 'GuideFaqCategoryController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideFaqCategory.store',
                'uses' => 'GuideFaqCategoryController@store',
            ]
        );

        Route::any(
            'edit/{id}/{faqcatid}',
            [
                'as'   => 'GuideFaqCategory.edit',
                'uses' => 'GuideFaqCategoryController@edit',
            ]
        )->where('faqcatid', '[0-9]+');
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideFaqCategory.editconfirm',
                'uses' => 'GuideFaqCategoryController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideFaqCategory.update',
                'uses' => 'GuideFaqCategoryController@update',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'GuideFaqCategory.destroy',
                'uses' => 'GuideFaqCategoryController@destroy',
            ]
        );

        Route::get(
            'priority/{id}',
            [
                'as' => 'GuideFaqCategory.priority',
                'uses' => 'GuideFaqCategoryController@priority'
            ]
        );
        Route::post(
            'priority/update/',
            [
                'as'   => 'GuideFaqCategory.priorityupdate',
                'uses' => 'GuideFaqCategoryController@priorityUpdate',
            ]
        );
    }
);
