<?php
Route::group([
    'prefix' => 'informations'
], function () {
    Route::get('info/list', [
        'as' => 'Informations.infolist',
        'uses' => 'InformationsController@infoList'
    ]);
    Route::get('failure/list', [
        'as' => 'Informations.failurelist',
        'uses' => 'InformationsController@failureList'
    ]);
    Route::get('show/{id}', [
        'as' => 'Informations.show',
        'uses' => 'InformationsController@show'
    ]);
});
