<?php
Route::group([
    'prefix' => 'profile'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'Profile.index',
            'uses' => 'ProfileController@index',
        ]
    );
    Route::any(
        'index',
        [
            'as'   => 'Profile.index',
            'uses' => 'ProfileController@index',
        ]
    );
    Route::any(
        'update/{app_id?}',
        [
            'as'   => 'Profile.update',
            'uses' => 'ProfileController@update',
        ]
    );
    Route::post(
        'upload/{app_id?}',
        [
            'as'   => 'Profile.upload',
            'uses' => 'ProfileController@upload',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'Profile.destroy',
            'uses' => 'ProfileController@destroy',
        ]
    );
});
