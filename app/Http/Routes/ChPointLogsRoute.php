<?php
Route::group([
    'prefix' => 'point_logs/ch'
], function () {
    Route::get('/', [
            'as'   => 'ChPointLogs.index',
            'uses' => 'ChPointLogsController@index',
    ]);
    Route::any('index', [
            'as'   => 'ChPointLogs.index',
            'uses' => 'ChPointLogsController@index',
    ]);
    Route::any('csv/download', [
        'as'   => 'ChPointLogs.csvdownload',
        'uses' => 'ChPointLogsController@csvdownload',
    ]);
});
