<?php
Route::group([
    'prefix' => 'point_logs'
], function () {
    Route::get('/', [
            'as'   => 'PointLogs.index',
            'uses' => 'PointLogsController@index',
    ]);
    Route::get('index', [
            'as'   => 'PointLogs.index',
            'uses' => 'PointLogsController@index',
    ]);
    Route::any('csv/download', [
        'as'   => 'PointLogs.csvdownload',
        'uses' => 'PointLogsController@csvdownload',
    ]);
});
