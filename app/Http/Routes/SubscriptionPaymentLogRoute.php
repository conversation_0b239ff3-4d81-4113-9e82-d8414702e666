<?php
Route::group([
    'prefix' => 'point_reports/subscription'
], function () {
    // 課金ログ
    Route::get('/', [
        'as' => 'SubscriptionPaymentLog.index',
        'uses' => 'SubscriptionPaymentLogController@index'
    ]);
    Route::get('index', [
        'as' => 'SubscriptionPaymentLog.index',
        'uses' => 'SubscriptionPaymentLogController@index'
    ]);
    // 課金ログCSVダウンロード
    Route::post('csv/download', [
        'as' => 'SubscriptionPaymentLog.csvDownload',
        'uses' => 'SubscriptionPaymentLogController@csvDownload'
    ]);
});
