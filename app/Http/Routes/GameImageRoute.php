<?php

Route::group([
    'prefix' => 'gameimage'
], function () {
    Route::get(
        '/{id?}/{imageType?}',
        [
            'as'   => 'GameImage.index',
            'uses' => 'GameImageController@index',
        ]
    )->where('imageType', '[0-9]+');

    Route::get(
        'index/{id?}/{imageType?}',
        [
            'as'   => 'GameImage.index',
            'uses' => 'GameImageController@index',
        ]
    )->where('imageType', '[0-9]+');

    Route::get(
        'show/{id}',
        [
            'as'   => 'GameImage.show',
            'uses' => 'GameImageController@show',
        ]
    );

    Route::post(
        'post/store',
        [
            'as'   => 'GameImage.poststore',
            'uses' => 'GameImageController@postStore',
        ]
    );
    Route::post(
        'post/update',
        [
            'as'   => 'GameImage.postupdate',
            'uses' => 'GameImageController@postUpdate',
        ]
    );
    Route::post(
        'post/destroy',
        [
            'as'   => 'GameImage.postdestroy',
            'uses' => 'GameImageController@postDestroy',
        ]
    );
    Route::post(
        'post/sort',
        [
            'as'   => 'GameImage.postsort',
            'uses' => 'GameImageController@postSort',
        ]
    );

    Route::get(
        'delete/confirm/{id}',
        [
            'as'   => 'GameImage.deleteconfirm',
            'uses' => 'GameImageController@deleteConfirm',
        ]
    );
    Route::post(
        'delete/store',
        [
            'as'   => 'GameImage.deletestore',
            'uses' => 'GameImageController@deleteStore',
        ]
    );
    Route::post(
        'timer/create',
        [
            'as'   => 'GameImage.createtimer',
            'uses' => 'GameImageController@createTimer',
        ]
    );
    Route::post(
        'timer/update',
        [
            'as'   => 'GameImage.updatetimer',
            'uses' => 'GameImageController@updateTimer',
        ]
    );
    Route::post(
        'timer/delete',
        [
            'as'   => 'GameImage.deletetimer',
            'uses' => 'GameImageController@deleteTimer',
        ]
    );
    Route::post(
        'default/register',
        [
            'as'   => 'GameImage.registerdefault',
            'uses' => 'GameImageController@registerDefault',
        ]
    );
    Route::post(
        'recommend/register',
        [
            'as'   => 'GameImage.registerrecommend',
            'uses' => 'GameImageController@registerRecommend',
        ]
    );
    Route::post(
        'recommend/delete',
        [
            'as'   => 'GameImage.deleterecommend',
            'uses' => 'GameImageController@deleteRecommend',
        ]
    );
    Route::post(
        'pwasetting/store',
        [
            'as' => 'GameImage.storepwasetting',
            'uses' => 'GameImageController@storePwaSetting',
        ]
    );
});
