<?php
Route::group(
    [
        'prefix' => 'sandbox/monthly/service'
    ],
    function () {
        Route::get(
            '/',
            [
                'as' => 'SandboxMonthlyService.index',
                'uses' => 'SandboxMonthlyServiceController@index'
            ]
        );
        Route::get(
            'index',
            [
                'as' => 'SandboxMonthlyService.index',
                'uses' => 'SandboxMonthlyServiceController@index'
            ]
        );
        Route::any(
            'create',
            [
                'as' => 'SandboxMonthlyService.create',
                'uses' => 'SandboxMonthlyServiceController@create'
            ]
        );
        Route::post(
            'createconfirm',
            [
                'as' => 'SandboxMonthlyService.createconfirm',
                'uses' => 'SandboxMonthlyServiceController@createconfirm'
            ]
        );
        Route::post(
            'store',
            [
                'as' => 'SandboxMonthlyService.store',
                'uses' => 'SandboxMonthlyServiceController@store'
            ]
        );
        Route::any(
            'edit/{id}',
            [
                'as' => 'SandboxMonthlyService.edit',
                'uses' => 'SandboxMonthlyServiceController@edit'
            ]
        );
        Route::post(
            'editconfirm',
            [
                'as' => 'SandboxMonthlyService.editconfirm',
                'uses' => 'SandboxMonthlyServiceController@editconfirm'
            ]
        );
        Route::post(
            'update',
            [
                'as' => 'SandboxMonthlyService.update',
                'uses' => 'SandboxMonthlyServiceController@update'
            ]
        );
        Route::post(
            'destroy',
            [
                'as' => 'SandboxMonthlyService.destroy',
                'uses' => 'SandboxMonthlyServiceController@destroy'
            ]
        );
    }
);
