<?php
Route::group(
    [
        'prefix' => 'guide_contents'
    ],
    function () {
        // マスター一覧
        Route::get(
            '/index/{id}',
            [
                'as' => 'GuideContents.index',
                'uses' => 'GuideContentsController@index'
            ]
        );

        // マスター新規追加
        Route::any(
            '/master_create/{id}',
            [ 
                'as'   => 'GuideContents.createMaster',
                'uses' => 'GuideContentsController@createMaster',
            ]
        );
        Route::post(
            '/master_create_confirm',
            [
                'as'   => 'GuideContents.createMasterConfirm',
                'uses' => 'GuideContentsController@createMasterConfirm',
            ]
        );
        Route::post(
            '/master_store',
            [
                'as'   => 'GuideContents.storeMaster',
                'uses' => 'GuideContentsController@storeMaster',
            ]
        );

        // マスター編集
        Route::any(
            '/master_edit/{id}',
            [
                'as'   => 'GuideContents.editMaster',
                'uses' => 'GuideContentsController@editMaster',
            ]
        );
        Route::post(
            '/master_edit_confirm',
            [
                'as'   => 'GuideContents.editMasterConfirm',
                'uses' => 'GuideContentsController@EditMasterConfirm',
            ]
        );
        Route::post(
            '/master_update',
            [
                'as'   => 'GuideContents.updateMaster',
                'uses' => 'GuideContentsController@updateMaster',
            ]
        );

        // マスター削除
        Route::post(
            '/master_delete',
            [
                'as'   => 'GuideContents.deleteMaster',
                'uses' => 'GuideContentsController@deleteMaster',
            ]
        );

        // データ一覧
        Route::get(
            '/data_list/{id}',
            [
                'as' => 'GuideContents.clusterList',
                'uses' => 'GuideContentsController@clusterList'
            ]
        );

        // データ新規追加
        Route::any(
            '/data_create/{id}',
            [ 
                'as'   => 'GuideContents.createCluster',
                'uses' => 'GuideContentsController@createCluster',
            ]
        );
        Route::post(
            '/data_create_confirm',
            [
                'as'   => 'GuideContents.createClusterConfirm',
                'uses' => 'GuideContentsController@createClusterConfirm',
            ]
        );
        Route::post(
            '/data_store',
            [
                'as'   => 'GuideContents.storeCluster',
                'uses' => 'GuideContentsController@storeCluster',
            ]
        );

        // データ編集
        Route::any(
            '/data_edit/{id}',
            [ 
                'as'   => 'GuideContents.editCluster',
                'uses' => 'GuideContentsController@editCluster',
            ]
        );
        Route::post(
            '/data_edit_confirm',
            [
                'as'   => 'GuideContents.editClusterConfirm',
                'uses' => 'GuideContentsController@editClusterConfirm',
            ]
        );
        Route::post(
            '/data_update',
            [
                'as'   => 'GuideContents.updateCluster',
                'uses' => 'GuideContentsController@updateCluster',
            ]
        );

        // データ削除
        Route::post(
            '/data_delete',
            [
                'as'   => 'GuideContents.deleteCluster',
                'uses' => 'GuideContentsController@deleteCluster',
            ]
        );

        // データ表示ステータス切り替え
        Route::post(
            '/data_view_status_switch',
            [
                'as'   => 'GuideContents.switchClusterViewStatus',
                'uses' => 'GuideContentsController@switchClusterViewStatus',
            ]
        );

        // 画像アップロード
        Route::any(
            '/data_image_upload',
            [
                'as'   => 'GuideContents.uploadClusterImage',
                'uses' => 'GuideContentsController@uploadClusterImage',
            ]
        );

        // 画像削除
        Route::any(
            '/data_image_delete',
            [
                'as'   => 'GuideContents.deleteClusterImage',
                'uses' => 'GuideContentsController@deleteClusterImage',
            ]
        );
    }
);
