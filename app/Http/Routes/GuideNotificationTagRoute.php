<?php
Route::group(
    [
        'prefix' => 'guide_notification_tag'
    ],
    function () {
        // タグ一覧
        Route::get(
            '/index/{id}',
            [
                'as' => 'GuideNotificationTag.index',
                'uses' => 'GuideNotificationTagController@index'
            ]
        )->where('id', '[0-9]+');

        // タグ新規追加
        Route::any(
            '/create/{id}',
            [
                'as'   => 'GuideNotificationTag.create',
                'uses' => 'GuideNotificationTagController@create',
            ]
        );
        Route::post(
            '/create_confirm',
            [
                'as'   => 'GuideNotificationTag.createconfirm',
                'uses' => 'GuideNotificationTagController@createConfirm',
            ]
        );
        Route::post(
            '/store',
            [
                'as'   => 'GuideNotificationTag.store',
                'uses' => 'GuideNotificationTagController@store',
            ]
        );

        // タグ編集
        Route::any(
            '/edit/{id}',
            [
                'as'   => 'GuideNotificationTag.edit',
                'uses' => 'GuideNotificationTagController@edit',
            ]
        )->where('id', '[0-9]+');
        Route::post(
            '/edit_confirm',
            [
                'as'   => 'GuideNotificationTag.editconfirm',
                'uses' => 'GuideNotificationTagController@editConfirm',
            ]
        );
        Route::post(
            '/update',
            [
                'as'   => 'GuideNotificationTag.update',
                'uses' => 'GuideNotificationTagController@update',
            ]
        );

        // タグ削除
        Route::post(
            '/delete',
            [
                'as'   => 'GuideNotificationTag.delete',
                'uses' => 'GuideNotificationTagController@delete',
            ]
        );
    }
);
