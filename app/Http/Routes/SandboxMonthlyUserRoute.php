<?php
Route::group(
    [
        'prefix' => 'sandbox/monthly/users'
    ],
    function () {
        Route::get(
            '/',
            [
                'as' => 'SandboxMonthlyUsers.index',
                'uses' => 'SandboxMonthlyUsersController@index'
            ]
        );
        Route::get(
            'index',
            [
                'as' => 'SandboxMonthlyUsers.index',
                'uses' => 'SandboxMonthlyUsersController@index'
            ]
        );
        Route::any(
            'show/{id}',
            [
                'as' => 'SandboxMonthlyUsers.show',
                'uses' => 'SandboxMonthlyUsersController@show'
            ]
        );
        Route::post(
            'sendentry',
            [
                'as' => 'SandboxMonthlyUsers.sendentry',
                'uses' => 'SandboxMonthlyUsersController@sendentry'
            ]
        );
        Route::post(
            'sendcancel',
            [
                'as' => 'SandboxMonthlyUsers.sendcancel',
                'uses' => 'SandboxMonthlyUsersController@sendcancel'
            ]
        );
    }
);
