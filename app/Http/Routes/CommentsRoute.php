<?php
Route::group([
    'prefix' => 'comments'
], function () {
    Route::get(
        '/{topic_id}',
        [
            'as'   => 'Comments.index',
            'uses' => 'CommentsController@index',
        ]
    );
    Route::any(
        'index/{topic_id}',
        [
            'as'   => 'Comments.index',
            'uses' => 'CommentsController@index',
        ]
    );
    Route::any(
        'create/{topic_id}/{comment_id?}',
        [
            'as'   => 'Comments.create',
            'uses' => 'CommentsController@create',
        ]
    );
    Route::post(
        'createconfirm',
        [
            'as'   => 'Comments.createconfirm',
            'uses' => 'CommentsController@createconfirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'Comments.store',
            'uses' => 'CommentsController@store',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'Comments.destroy',
            'uses' => 'CommentsController@destroy',
        ]
    );
});
