<?php
Route::group(
    [
        'prefix' => 'guide_application'
    ],
    function () {
        Route::get(
            '/',
            [
                'as' => 'GuideApplication.index',
                'uses' => 'GuideApplicationController@index'
            ]
        );
        Route::get(
            'index/',
            [
                'as' => 'GuideApplication.index',
                'uses' => 'GuideApplicationController@index'
            ]
        );

        Route::any(
            'create/',
            [
                'as'   => 'GuideApplication.create',
                'uses' => 'GuideApplicationController@create',
            ]
        );
        Route::post(
            'createconfirm/',
            [
                'as'   => 'GuideApplication.createconfirm',
                'uses' => 'GuideApplicationController@createConfirm',
            ]
        );
        Route::post(
            'store/',
            [
                'as'   => 'GuideApplication.store',
                'uses' => 'GuideApplicationController@store',
            ]
        );

        Route::any(
            'edit/{id}',
            [
                'as'   => 'GuideApplication.edit',
                'uses' => 'GuideApplicationController@edit',
            ]
        );
        Route::post(
            'editconfirm/',
            [
                'as'   => 'GuideApplication.editconfirm',
                'uses' => 'GuideApplicationController@editConfirm',
            ]
        );
        Route::post(
            'update/',
            [
                'as'   => 'GuideApplication.update',
                'uses' => 'GuideApplicationController@update',
            ]
        );
    }
);
