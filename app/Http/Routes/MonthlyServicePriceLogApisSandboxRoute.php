<?php
Route::group([
    'prefix' => 'monthly_service_price_log_apis_sandbox'
], function () {
    Route::get('/', [
            'as'   => 'MonthlyServicePriceLogApisSandbox.index',
            'uses' => 'MonthlyServicePriceLogApisSandboxController@index',
    ]);
    Route::any('index', [
            'as'   => 'MonthlyServicePriceLogApisSandbox.index',
            'uses' => 'MonthlyServicePriceLogApisSandboxController@index',
    ]);
    Route::any('csv/download', [
            'as'   => 'MonthlyServicePriceLogApisSandbox.csvdownload',
            'uses' => 'MonthlyServicePriceLogApisSandboxController@csvdownload',
    ]);
});
