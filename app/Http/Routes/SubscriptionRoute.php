<?php
Route::group([
    'prefix' => 'subscription'
], function () {
    // 定期購入：一覧・検索
       Route::get('/', [
        'as' => 'Subscription.index',
        'uses' => 'SubscriptionController@index'
    ]);
    Route::any('index', [
        'as' => 'Subscription.index',
        'uses' => 'SubscriptionController@index'
    ]);
    // 定期購入：登録
    Route::any('create', [
        'as' => 'Subscription.create',
        'uses' => 'SubscriptionController@create'
    ]);
    // 定期購入：登録確認
    Route::post('createConfirm', [
        'as' => 'Subscription.createConfirm',
        'uses' => 'SubscriptionController@createConfirm'
    ]);
    // 定期購入：登録完了
    Route::post('store', [
        'as' => 'Subscription.store',
        'uses' => 'SubscriptionController@store'
    ]);
    // 定期購入：編集
    Route::any('edit', [
        'as' => 'Subscription.edit',
        'uses' => 'SubscriptionController@edit'
    ]);
    // 定期購入：編集確認
    Route::post('editConfirm', [
        'as' => 'Subscription.editConfirm',
        'uses' => 'SubscriptionController@editConfirm'
    ]);
    // 定期購入：編集完了
    Route::post('update', [
        'as' => 'Subscription.update',
        'uses' => 'SubscriptionController@update'
    ]);
    // 定期購入詳細・基本プラン・特典一覧
    Route::get('detail' ,[
        'as' => 'Subscription.detail',
        'uses' => 'SubscriptionController@detail'
    ]);
    // 定期購入基本プラン：登録 新規登録
    Route::any('baseplan/create', [
        'as' => 'Subscription.baseplan.create',
        'uses' => 'SubscriptionController@basePlanCreate'
    ]);
    // 定期購入基本プラン：登録確認
    Route::any('baseplan/createConfirm', [
        'as' => 'Subscription.baseplan.createConfirm',
        'uses' => 'SubscriptionController@basePlanCreateConfirm'
    ]);
    // 定期購入基本プラン：編集
    Route::any('baseplan/edit', [
        'as' => 'Subscription.baseplan.edit',
        'uses' => 'SubscriptionController@basePlanEdit'
    ]);
    // 定期購入基本プラン：編集確認
    Route::any('baseplan/editConfirm', [
        'as' => 'Subscription.baseplan.editConfirm',
        'uses' => 'SubscriptionController@basePlanEditConfirm'
    ]);
    // 定期購入基本プラン：登録完了
    Route::post('baseplan/store', [
        'as' => 'Subscription.baseplan.store',
        'uses' => 'SubscriptionController@basePlanStore'
    ]);
    // 定期購入基本プラン：編集完了
    Route::post('baseplan/update', [
        'as' => 'Subscription.baseplan.update',
        'uses' => 'SubscriptionController@basePlanUpdate'
    ]);
    // 定期購入特典：登録
    Route::any('offer/create', [
        'as' => 'Subscription.offer.create',
        'uses' => 'SubscriptionController@offerCreate'
    ]);
    // 定期購入特典：登録確認
    Route::any('offer/createConfirm', [
        'as' => 'Subscription.offer.createConfirm',
        'uses' => 'SubscriptionController@offerCreateConfirm'
    ]);
    // 定期購入特典：編集
    Route::any('offer/edit', [
        'as' => 'Subscription.offer.edit',
        'uses' => 'SubscriptionController@offerEdit'
    ]);
    // 定期購入特典：編集確認
    Route::any('offer/editConfirm', [
        'as' => 'Subscription.offer.editConfirm',
        'uses' => 'SubscriptionController@offerEditConfirm'
    ]);
    // 定期購入特典：登録完了
    Route::post('offer/store', [
        'as' => 'Subscription.offer.store',
        'uses' => 'SubscriptionController@offerStore'
    ]);
    // 定期購入特典：編集完了
    Route::post('offer/update', [
        'as' => 'Subscription.offer.update',
        'uses' => 'SubscriptionController@offerUpdate'
    ]);
});
