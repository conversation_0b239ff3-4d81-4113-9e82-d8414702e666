<?php
Route::group([
    'prefix' => 'subscription_cancel_reservation'
], function () {
    // 定期購入問い合わせ
    Route::get('/', [
        'as' => 'SubscriptionCancelReservation.index',
        'uses' => 'SubscriptionCancelReservationController@index'
    ]);
    Route::any('index', [
        'as' => 'SubscriptionCancelReservation.index',
        'uses' => 'SubscriptionCancelReservationController@index'
    ]);
    Route::post('cancel', [
        'as' => 'SubscriptionCancelReservation.cancel',
        'uses' => 'SubscriptionCancelReservationController@ajaxCancel'
    ]);
    Route::post('reset', [
        'as' => 'SubscriptionCancelReservation.reset',
        'uses' => 'SubscriptionCancelReservationController@ajaxReset'
    ]);
});
