<?php
Route::group([
    'prefix' => 'reports/daily'
], function () {
    Route::get('/', [
        'as' => 'ReportsDaily.index',
        'uses' => 'ReportsDailyController@index'
    ]);
    Route::get('index', [
        'as' => 'ReportsDaily.index',
        'uses' => 'ReportsDailyController@index'
    ]);
    Route::post('csv/download', [
        'as' => 'ReportsDaily.csv.download',
        'uses' => 'ReportsDailyController@csvDownload'
    ]);
    Route::any('csv/accounting/download', [
        'as'   => 'ReportsDaily.csvaccountingdownload',
        'uses' => 'ReportsDaily@csvdownload',
    ]);
});
