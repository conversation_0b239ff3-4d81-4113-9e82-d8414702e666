<?php
Route::group(
    [
        'prefix' => 'guide_preregistration'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuidePreregistration.index',
                'uses' => 'GuidePreregistrationController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuidePreregistration.index',
                'uses' => 'GuidePreregistrationController@index'
            ]
        );

        Route::any(
            'twitterapi/edit/{id}',
            [
                'as' => 'GuidePreregistration.twitterapiedit',
                'uses' => 'GuidePreregistrationController@twitterapiEdit'
            ]
        );
        Route::post(
            'twitterapi/editconfirm/',
            [
                'as'   => 'GuidePreregistration.twitterapieditconfirm',
                'uses' => 'GuidePreregistrationController@twitterapiEditConfirm',
            ]
        );
        Route::post(
            'twitterapi/update/',
            [
                'as'   => 'GuidePreregistration.twitterapiupdate',
                'uses' => 'GuidePreregistrationController@twitterapiUpdate',
            ]
        );

        Route::any(
            'usercondition/edit/{id}',
            [
                'as' => 'GuidePreregistration.userconditionedit',
                'uses' => 'GuidePreregistrationController@userconditionEdit'
            ]
        );
        Route::post(
            'usercondition/editconfirm/',
            [
                'as'   => 'GuidePreregistration.userconditioneditconfirm',
                'uses' => 'GuidePreregistrationController@userconditionEditConfirm',
            ]
        );
        Route::post(
            'usercondition/update/',
            [
                'as'   => 'GuidePreregistration.userconditionupdate',
                'uses' => 'GuidePreregistrationController@userconditionUpdate',
            ]
        );

        Route::any(
            'usercondition/makecsv/{id}',
            [
                'as'   => 'GuidePreregistration.userconditionmakecsv',
                'uses' => 'GuidePreregistrationController@userconditionMakecsv',
            ]
        );
    }
);
