<?php
Route::group([
    'prefix' => 'inquiriescategory',
    'where' => [
        'id' => '[a-z0-9-]+'
    ]
], function () {
    Route::get(
        '/',
        [
            'as'   => 'InquiriesCategory.index',
            'uses' => 'InquiriesCategoryController@index'
        ]
    );
    Route::get(
        'index',
        [
            'as' => 'InquiriesCategory.index',
            'uses' => 'InquiriesCategoryController@index'
        ]
    );
    Route::any(
        'create/{id}',
        [
            'as'   => 'InquiriesCategory.create',
            'uses' => 'InquiriesCategoryController@create',
        ]
    );
    Route::any(
        'createconfirm',
        [
            'as'   => 'InquiriesCategory.createConfirm',
            'uses' => 'InquiriesCategoryController@createConfirm',
        ]
    );
    Route::any(
        'store',
        [
            'as'   => 'InquiriesCategory.store',
            'uses' => 'InquiriesCategoryController@store',
        ]
    );
    Route::any(
        'edit/{appId}/{large}',
        [
            'as'   => 'InquiriesCategory.edit',
            'uses' => 'InquiriesCategoryController@edit',
        ]
    );
    Route::any(
        'editconfirm',
        [
            'as'   => 'InquiriesCategory.editConfirm',
            'uses' => 'InquiriesCategoryController@editConfirm',
        ]
    );
    Route::any(
        'update',
        [
            'as'   => 'InquiriesCategory.update',
            'uses' => 'InquiriesCategoryController@update',
        ]
    );
    Route::any(
        'copy/create/{appId}/{large}',
        [
            'as'   => 'InquiriesCategory.copy.create',
            'uses' => 'InquiriesCategoryController@copyCreate',
        ]
    );
    Route::any(
        'copy/createconfirm',
        [
            'as'   => 'InquiriesCategory.copy.createConfirm',
            'uses' => 'InquiriesCategoryController@copyCreateConfirm',
        ]
    );
    Route::any(
        'copy/store',
        [
            'as'   => 'InquiriesCategory.copy.store',
            'uses' => 'InquiriesCategoryController@copyStore',
        ]
    );
    Route::any(
        'delete/{appId}/{large}',
        [
            'as'   => 'InquiriesCategory.delete',
            'uses' => 'InquiriesCategoryController@delete',
        ]
    );
    Route::any(
        'deleteconfirm',
        [
            'as'   => 'InquiriesCategory.deleteConfirm',
            'uses' => 'InquiriesCategoryController@deleteConfirm',
        ]
    );
    Route::any(
        'destroy',
        [
            'as'   => 'InquiriesCategory.destroy',
            'uses' => 'InquiriesCategoryController@destroy',
        ]
    );
});
