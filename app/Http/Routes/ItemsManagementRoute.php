<?php
Route::group([
    'prefix' => 'item/management',
], function () {
    Route::get('/', [
        'as' => 'ItemManagement.index',
        'uses' => 'ItemManagementController@index'
    ]);
    Route::get('index', [
        'as' => 'ItemManagement.index',
        'uses' => 'ItemManagementController@index'
    ]);
    Route::post('csv/import', [
        'as' => 'ItemManagement.csv.import',
        'uses' => 'ItemManagementController@csvImport'
    ]);
    Route::get('csv/downloadsample', [
        'as' => 'ItemManagement.csv.downloadSample',
        'uses' => 'ItemManagementController@csvDownloadSample'
    ]);
    Route::get('detail', [
        'as' => 'ItemManagement.detail',
        'uses' => 'ItemManagementController@detail'
    ]);
    Route::any('product/create', [
        'as' => 'ItemManagement.product.create',
        'uses' => 'ItemManagementController@productCreate'
    ]);
    Route::any('product/createconfirm', [
        'as' => 'ItemManagement.product.createConfirm',
        'uses' => 'ItemManagementController@productCreateConfirm'
    ]);
    Route::post('product/store', [
        'as' => 'ItemManagement.product.store',
        'uses' => 'ItemManagementController@productStore'
    ]);
    Route::any('product/edit', [
        'as' => 'ItemManagement.product.edit',
        'uses' => 'ItemManagementController@productEdit'
    ]);
    Route::post('product/editconfirm', [
        'as' => 'ItemManagement.product.editConfirm',
        'uses' => 'ItemManagementController@productEditConfirm'
    ]);
    Route::post('product/update', [
        'as' => 'ItemManagement.product.update',
        'uses' => 'ItemManagementController@productUpdate'
    ]);
    Route::any('price/create', [
        'as' => 'ItemManagement.price.create',
        'uses' => 'ItemManagementController@priceCreate'
    ]);
    Route::any('price/createconfirm', [
        'as' => 'ItemManagement.price.createConfirm',
        'uses' => 'ItemManagementController@priceCreateConfirm'
    ]);
    Route::post('price/store', [
        'as' => 'ItemManagement.price.store',
        'uses' => 'ItemManagementController@priceStore'
    ]);
    Route::any('price/edit', [
        'as' => 'ItemManagement.price.edit',
        'uses' => 'ItemManagementController@priceEdit'
    ]);
    Route::post('price/editconfirm', [
        'as' => 'ItemManagement.price.editConfirm',
        'uses' => 'ItemManagementController@priceEditConfirm'
    ]);
    Route::post('price/update', [
        'as' => 'ItemManagement.price.update',
        'uses' => 'ItemManagementController@priceUpdate'
    ]);
});
