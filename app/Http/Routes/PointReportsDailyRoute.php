<?php
Route::group([
    'prefix' => 'point_reports/daily'
], function () {
    Route::get('/', [
        'as'   => 'PointReportsDaily.index',
        'uses' => 'PointReportsDailyController@index',
    ]);
    Route::get('index', [
        'as'   => 'PointReportsDaily.index',
        'uses' => 'PointReportsDailyController@index',
    ]);
    Route::post('index', [
        'as'   => 'PointReportsDaily.search',
        'uses' => 'PointReportsDailyController@search',
    ]);
    Route::any('csv/download', [
        'as'   => 'PointReportsDaily.csvdownload',
        'uses' => 'PointReportsDailyController@csvdownload',
    ]);
});
