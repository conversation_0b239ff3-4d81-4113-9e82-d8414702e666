<?php
Route::group([
    'prefix' => 'present'
], function () {
    Route::get('/', [
            'as'   => 'Present.index',
            'uses' => 'PresentController@index',
    ]);
    Route::any('index', [
            'as'   => 'Present.index',
            'uses' => 'PresentController@index',
    ]);
    Route::any('show/{id}', [
            'as'   => 'Present.show',
            'uses' => 'PresentController@show',
    ]);
    Route::any('edit/{id}', [
            'as'   => 'Present.edit',
            'uses' => 'PresentController@edit',
    ]);
    Route::post('editconfirm', [
            'as'   => 'Present.editconfirm',
            'uses' => 'PresentController@editconfirm',
    ]);
    Route::post('update', [
            'as'   => 'Present.update',
            'uses' => 'PresentController@update',
    ]);
    Route::post('send', [
            'as'   => 'Present.send',
            'uses' => 'PresentController@send',
    ]);
    Route::any('csv/download/{id}', [
            'as'   => 'Present.csvdownload',
            'uses' => 'PresentController@csvdownload',
    ]);
});
