<?php
Route::group([
    'prefix' => 'games/apply/preregistration'
], function () {
    // 事前登録申請の審査用の画像を審査に提出する
    Route::post('examinationimages/{id}/{device}', [
        'as' => 'Games.apply.preregistration.examinationimages',
        'uses' => 'GameApplyPreregistrationController@storeExaminationImages',
    ]);
    // 事前登録申請のゲーム紹介ページ：デザイン部分素材を審査に提出する
    Route::post('introductionimages/{id}/{device}', [
        'as' => 'Games.apply.preregistration.introductionimages',
        'uses' => 'GameApplyPreregistrationController@storeIntroductionImages',
    ]);
    // 事前登録申請のプラットフォーム上に掲載される画像を審査に提出
    Route::post('platformimages/{id}/{device}', [
        'as' => 'Games.apply.preregistration.platformimages',
        'uses' => 'GameApplyPreregistrationController@storePlatformImages',
    ]);
    // 事前登録サイトを審査に提出
    Route::post('preregistrationsite/{id}/{device}', [
        'as' => 'Games.apply.preregistration.preregistrationsite',
        'uses' => 'GameApplyPreregistrationController@storePreRegistrationSite',
    ]);
    // 動作検証を審査に提出：事前登録申請
    Route::post('sandboxverification/{id}/{device}', [
        'as' => 'Games.apply.preregistration.sandboxverification',
        'uses' => 'GameApplyPreregistrationController@storeSandboxVerification',
    ]);
    // 動作検証を審査に提出：事前登録申請
    Route::post('verification/{id}/{device}', [
        'as' => 'Games.apply.preregistration.verification',
        'uses' => 'GameApplyPreregistrationController@storeVerification',
    ]);
    // コミュニティを審査に提出：事前登録
    Route::post('community/{id}/{device}', [
        'as' => 'Games.apply.preregistration.community',
        'uses' => 'GameApplyPreregistrationController@storeCommunity',
    ]);
    // 事前登録申請のゲーム情報入力を審査に提出する
    Route::post('gameinformation/{id}/{device}', [
        'as' => 'Games.apply.preregistration.gameinformation',
        'uses' => 'GameApplyPreregistrationController@storeGameInformation',
    ]);
    // Csrfトークンチェック
    Route::post('checkCsrf', [
        'as' => 'Games.apply.preregistration.checkcsrf',
        'uses' => 'GameApplyPreregistrationController@checkCsrf',
    ]);
    // Win対応環境を審査に提出する
    Route::post('windowsSupportedEnvironment/{id}/{device}', [
        'as' => 'Games.apply.preregistration.windowssupportedenvironment',
        'uses' => 'GameApplyPreregistrationController@storeWindowsSupportedEnvironment',
    ]);
    // Mac対応環境を審査に提出する
    Route::post('macSupportedEnvironment/{id}/{device}', [
        'as' => 'Games.apply.preregistration.macsupportedenvironment',
        'uses' => 'GameApplyPreregistrationController@storeMacSupportedEnvironment',
    ]);
    // CEROを審査に提出する
    Route::post('cero/{id}/{device}', [
        'as' => 'Games.apply.preregistration.cero',
        'uses' => 'GameApplyPreregistrationController@storeCero',
    ]);
});
