<?php

Route::group([
    'prefix' => 'gamecontentimage'
], function () {
    Route::get(
        'index/{id?}',
        [
            'as' => 'GameContentImage.index',
            'uses' => 'GameContentImageController@index',
        ]
    );

    Route::get(
        'register',
        [
            'as' => 'GameContentImage.register',
            'uses' => 'GameContentImageController@register',
        ]
    );

    Route::post(
        'register/store',
        [
            'as' => 'GameContentImage.register.store',
            'uses' => 'GameContentImageController@registerStore',
        ]
    );

    Route::get(
        'register/completed',
        [
            'as' => 'GameContentImage.register.completed',
            'uses' => 'GameContentImageController@registerCompleted',
        ]
    );

    Route::post(
        'register/upload/check',
        [
            'as' => 'GameContentImage.register.upload.check',
            'uses' => 'GameContentImageController@checkUploadFile',
        ]
    );
});
