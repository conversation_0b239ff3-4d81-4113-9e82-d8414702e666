<?php
Route::group([
    'prefix' => 'user'
], function () {
    Route::get('login', [
        'as' => 'Users.login',
        'uses' => 'UsersController@getLogin'
    ]);
    Route::post('login', [
        'as' => 'Users.postLogin',
        'uses' => 'UsersController@postLogin'
    ]);
    Route::get('logout', [
        'as' => 'Users.logout',
        'uses' => 'UsersController@logout'
    ]);
    Route::get('login/sso', [
        'as' => 'Users.loginSso',
        'uses' => 'UsersController@ssoLogin'
    ]);
    Route::get('login/sso/redirect', [
        'as' => 'Users.loginSsoRedirect',
        'uses' => 'UsersController@ssoRedirect'
    ]);
});
