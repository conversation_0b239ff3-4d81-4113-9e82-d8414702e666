<?php

Route::group([
        'prefix'   => 'pfownerreports/monthly',
], function () {
    Route::get(
        '/',
        [
            'as'   => 'PfOwnerReportsMonthly.index',
            'uses' => 'PfOwnerReportsMonthlyController@index',
        ]
    );
    Route::get(
        'index',
        [
            'as'   => 'PfOwnerReportsMonthly.index',
            'uses' => 'PfOwnerReportsMonthlyController@index',
        ]
    );

    Route::post(
        'csv/download',
        [
            'as'   => 'PfOwnerReportsMonthly.csvDownload',
            'uses' => 'PfOwnerReportsMonthlyController@csvDownload',
        ]
    );
});
