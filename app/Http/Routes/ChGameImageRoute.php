<?php

Route::group([
    'prefix' => 'chgameimage'
], function () {
    Route::get(
        '/{id?}/{imageType?}',
        [
            'as'   => 'ChGameImage.index',
            'uses' => 'ChGameImageController@index',
        ]
    )->where('imageType', '[0-9]+');

    Route::get(
        'index/{id?}/{imageType?}',
        [
            'as'   => 'ChGameImage.index',
            'uses' => 'ChGameImageController@index',
        ]
    )->where('imageType', '[0-9]+');

    Route::get(
        'show/{id}',
        [
            'as'   => 'ChGameImage.show',
            'uses' => 'ChGameImageController@show',
        ]
    );

    Route::post(
        'post/store',
        [
            'as'   => 'ChGameImage.poststore',
            'uses' => 'ChGameImageController@postStore',
        ]
    );
    Route::post(
        'post/update',
        [
            'as'   => 'ChGameImage.postupdate',
            'uses' => 'ChGameImageController@postUpdate',
        ]
    );
    Route::post(
        'post/destroy',
        [
            'as'   => 'ChGameImage.postdestroy',
            'uses' => 'ChGameImageController@postDestroy',
        ]
    );

    Route::get(
        'delete/confirm/{id}',
        [
            'as'   => 'ChGameImage.deleteconfirm',
            'uses' => 'ChGameImageController@deleteConfirm',
        ]
    );
    Route::post(
        'delete/store',
        [
            'as'   => 'ChGameImage.deletestore',
            'uses' => 'ChGameImageController@deleteStore',
        ]
    );
    Route::post(
        'recommend/store',
        [
            'as'   => 'ChGameImage.recommendstore',
            'uses' => 'ChGameImageController@recommendStore',
        ]
    );
    Route::post(
        'recommend/destroy',
        [
            'as'   => 'ChGameImage.recommenddestroy',
            'uses' => 'ChGameImageController@recommendDestroy',
        ]
    );
});
