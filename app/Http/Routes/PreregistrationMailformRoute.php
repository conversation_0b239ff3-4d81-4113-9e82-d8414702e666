<?php
Route::group(
    [
        'prefix' => 'preregistration_mailform'
    ],
    function () {
        Route::get(
            '/',
            [
                'as' => 'PreregistrationMailform.index',
                'uses' => 'PreregistrationMailformController@index'
            ]
        );

        Route::get(
            'index/',
            [
                'as' => 'PreregistrationMailform.index',
                'uses' => 'PreregistrationMailformController@index'
            ]
        );

        Route::any(
            'create/',
            [
                'as'   => 'PreregistrationMailform.create',
                'uses' => 'PreregistrationMailformController@create',
            ]
        );

        Route::post(
            'createconfirm/',
            [
                'as'   => 'PreregistrationMailform.createconfirm',
                'uses' => 'PreregistrationMailformController@createConfirm',
            ]
        );

        Route::post(
            'store/',
            [
                'as'   => 'PreregistrationMailform.store',
                'uses' => 'PreregistrationMailformController@store'
            ]
        );

        Route::any(
            'edit/{id}',
            [
                'as'   => 'PreregistrationMailform.edit',
                'uses' => 'PreregistrationMailformController@edit',
            ]
        );

        Route::post(
            'editconfirm/',
            [
                'as'   => 'PreregistrationMailform.editconfirm',
                'uses' => 'PreregistrationMailformController@editConfirm',
            ]
        );

        Route::post(
            'update/',
            [
                'as'   => 'PreregistrationMailform.update',
                'uses' => 'PreregistrationMailformController@update',
            ]
        );


        Route::post(
            'destroy/',
            [
                'as'   => 'PreregistrationMailform.destroy',
                'uses' => 'PreregistrationMailformController@destroy',
            ]
        );

        Route::get(
            'show/{id}',
            [
                'as'   => 'PreregistrationMailform.show',
                'uses' => 'PreregistrationMailformController@show',
            ]
        );


    }
);
