<?php

Route::group([
        'prefix'   => 'pfmanagementreports/monthly',
], function () {
    Route::get(
        '/',
        [
            'as'   => 'PfManagementReportsMonthly.index',
            'uses' => 'PfManagementReportsMonthlyController@index',
        ]
    );
    Route::get(
        'index',
        [
            'as'   => 'PfManagementReportsMonthly.index',
            'uses' => 'PfManagementReportsMonthlyController@index',
        ]
    );

    Route::post(
        'csv/download',
        [
            'as'   => 'PfManagementReportsMonthly.csvDownload',
            'uses' => 'PfManagementReportsMonthlyController@csvDownload',
        ]
    );
});
