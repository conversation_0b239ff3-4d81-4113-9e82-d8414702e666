<?php

Route::group([
    'prefix' => 'gameimageapply'
], function () {
    Route::get(
        '/{id}',
        [
            'as'   => 'GameImageApply.index',
            'uses' => 'GameImageApplyController@index',
        ]
    );
    Route::get(
        'index/{id}',
        [
            'as'   => 'GameImageApply.index',
            'uses' => 'GameImageApplyController@index',
        ]
    );

    Route::get(
        'register/{id}',
        [
            'as'   => 'GameImageApply.register',
            'uses' => 'GameImageApplyController@register',
        ]
    );
    Route::post(
        'register/store',
        [
            'as'   => 'GameImageApply.registerstore',
            'uses' => 'GameImageApplyController@registerStore',
        ]
    );

    Route::get(
        'registermovie/{id}',
        [
            'as'   => 'GameImageApply.registermovie',
            'uses' => 'GameImageApplyController@registerMovie',
        ]
    );
    Route::post(
        'registermovie/store',
        [
            'as'   => 'GameImageApply.registermoviestore',
            'uses' => 'GameImageApplyController@registerMovieStore',
        ]
    );

    Route::post(
        'confirm',
        [
            'as'   => 'GameImageApply.confirm',
            'uses' => 'GameImageApplyController@confirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'GameImageApply.store',
            'uses' => 'GameImageApplyController@store',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'GameImageApply.destroy',
            'uses' => 'GameImageApplyController@destroy',
        ]
    );

    Route::get(
        'review',
        [
            'as'   => 'GameImageApply.reviewIndex',
            'uses' => 'GameImageApplyController@reviewIndex',
        ]
    );
    Route::any(
        'review/index',
        [
            'as'   => 'GameImageApply.reviewIndex',
            'uses' => 'GameImageApplyController@reviewIndex',
        ]
    );
    Route::get(
        'show/{id}',
        [
            'as'   => 'GameImageApply.show',
            'uses' => 'GameImageApplyController@show',
        ]
    );

    Route::post(
        'withdraw/confirm',
        [
            'as'   => 'GameImageApply.withdrawconfirm',
            'uses' => 'GameImageApplyController@withdrawConfirm',
        ]
    );
    Route::post(
        'withdraw/update',
        [
            'as'   => 'GameImageApply.withdrawupdate',
            'uses' => 'GameImageApplyController@withdrawUpdate',
        ]
    );
});
