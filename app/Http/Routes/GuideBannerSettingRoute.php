<?php
Route::group(
    [
        'prefix' => 'guide_banner_setting'
    ],
    function () {
        Route::get(
            '/{id}',
            [
                'as' => 'GuideBannerSetting.index',
                'uses' => 'GuideBannerSettingController@index'
            ]
        );
        Route::get(
            'index/{id}',
            [
                'as' => 'GuideBannerSetting.index',
                'uses' => 'GuideBannerSettingController@index'
            ]
        );

        Route::any(
            'rotating/edit/{id}',
            [
                'as'   => 'GuideBannerSetting.rotatingedit',
                'uses' => 'GuideBannerSettingController@rotatingEdit',
            ]
        );
        Route::post(
            'rotating/editconfirm/',
            [
                'as'   => 'GuideBannerSetting.rotatingeditconfirm',
                'uses' => 'GuideBannerSettingController@rotatingEditConfirm',
            ]
        );
        Route::post(
            'rotating/update/',
            [
                'as'   => 'GuideBannerSetting.rotatingupdate',
                'uses' => 'GuideBannerSettingController@rotatingUpdate',
            ]
        );
        Route::post(
            'rotating/makepreview/',
            [
                'as'   => 'GuideBannerSetting.rotatingmakepreview',
                'uses' => 'GuideBannerSettingController@rotatingMakePreview',
            ]
        );

        Route::any(
            'fixed/edit/{id}/',
            [
                'as'   => 'GuideBannerSetting.fixededit',
                'uses' => 'GuideBannerSettingController@fixedEdit',
            ]
        );
        Route::post(
            'fixed/editconfirm/',
            [
                'as'   => 'GuideBannerSetting.fixededitconfirm',
                'uses' => 'GuideBannerSettingController@fixedEditConfirm',
            ]
        );
        Route::post(
            'fixed/update/',
            [
                'as'   => 'GuideBannerSetting.fixedupdate',
                'uses' => 'GuideBannerSettingController@fixedUpdate',
            ]
        );
        Route::post(
            'fixed/makepreview/',
            [
                'as'   => 'GuideBannerSetting.fixedmakepreview',
                'uses' => 'GuideBannerSettingController@fixedMakePreview',
            ]
        );
    }
);
