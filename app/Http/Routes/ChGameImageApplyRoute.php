<?php

Route::group([
    'prefix' => 'chgameimageapply'
], function () {
    Route::get(
        '/{id}',
        [
            'as'   => 'ChGameImageApply.index',
            'uses' => 'ChGameImageApplyController@index',
        ]
    );
    Route::get(
        'index/{id}',
        [
            'as'   => 'ChGameImageApply.index',
            'uses' => 'ChGameImageApplyController@index',
        ]
    );

    Route::get(
        'register/{id}',
        [
            'as'   => 'ChGameImageApply.register',
            'uses' => 'ChGameImageApplyController@register',
        ]
    );
    Route::post(
        'register/store',
        [
            'as'   => 'ChGameImageApply.registerstore',
            'uses' => 'ChGameImageApplyController@registerStore',
        ]
    );

    Route::post(
        'confirm',
        [
            'as'   => 'ChGameImageApply.confirm',
            'uses' => 'ChGameImageApplyController@confirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'ChGameImageApply.store',
            'uses' => 'ChGameImageApplyController@store',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'ChGameImageApply.destroy',
            'uses' => 'ChGameImageApplyController@destroy',
        ]
    );

    Route::get(
        'review',
        [
            'as'   => 'ChGameImageApply.reviewIndex',
            'uses' => 'ChGameImageApplyController@reviewIndex',
        ]
    );
    Route::any(
        'review/index',
        [
            'as'   => 'ChGameImageApply.reviewIndex',
            'uses' => 'ChGameImageApplyController@reviewIndex',
        ]
    );
    Route::get(
        'show/{id}',
        [
            'as'   => 'ChGameImageApply.show',
            'uses' => 'ChGameImageApplyController@show',
        ]
    );

    Route::post(
        'withdraw/confirm',
        [
            'as'   => 'ChGameImageApply.withdrawconfirm',
            'uses' => 'ChGameImageApplyController@withdrawConfirm',
        ]
    );
    Route::post(
        'withdraw/update',
        [
            'as'   => 'ChGameImageApply.withdrawupdate',
            'uses' => 'ChGameImageApplyController@withdrawUpdate',
        ]
    );
});
