<?php

Route::group([
        'prefix'   => 'pfownerreports/daily',
], function () {
    Route::get(
        '/',
        [
            'as'   => 'PfOwnerReportsDaily.index',
            'uses' => 'PfOwnerReportsDailyController@index',
        ]
    );
    Route::get(
        'index',
        [
            'as'   => 'PfOwnerReportsDaily.index',
            'uses' => 'PfOwnerReportsDailyController@index',
        ]
    );

    Route::post(
        'csv/download',
        [
            'as'   => 'PfOwnerReportsDaily.csvDownload',
            'uses' => 'PfOwnerReportsDailyController@csvDownload',
        ]
    );
});
