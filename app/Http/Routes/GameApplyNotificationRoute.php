<?php
Route::group([
    'prefix' => 'games/notification'
], function () {
    // 通知メールアドレス登録/更新
    Route::post('applications/{appId}/{device}', [
        'as' => 'Games.apply.notification.update',
        'uses' => 'GameApplyNotificationController@updateNotificationAddress',
    ]);
    // 通知メールアドレス取得
    Route::get('applications/{appId}/{device}', [
        'as' => 'Games.apply.notification.list',
        'uses' => 'GameApplyNotificationController@getNotificationAddress',
    ]);
});
