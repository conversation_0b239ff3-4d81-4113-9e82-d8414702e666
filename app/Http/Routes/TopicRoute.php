<?php
Route::group([
    'prefix' => 'topic'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'Topic.index',
            'uses' => 'TopicController@index',
        ]
    );
    Route::any(
        'index',
        [
            'as'   => 'Topic.index',
            'uses' => 'TopicController@index',
        ]
    );
    Route::any(
        'create',
        [
            'as'   => 'Topic.create',
            'uses' => 'TopicController@create',
        ]
    );
    Route::post(
        'createconfirm',
        [
            'as'   => 'Topic.createconfirm',
            'uses' => 'TopicController@createconfirm',
        ]
    );
    Route::post(
        'store',
        [
            'as'   => 'Topic.store',
            'uses' => 'TopicController@store',
        ]
    );
    Route::any(
        'edit/{id}',
        [
            'as'   => 'Topic.edit',
            'uses' => 'TopicController@edit',
        ]
    );
    Route::post(
        'editconfirm',
        [
            'as'   => 'Topic.editconfirm',
            'uses' => 'TopicController@editconfirm',
        ]
    );
    Route::post(
        'update',
        [
            'as'   => 'Topic.update',
            'uses' => 'TopicController@update',
        ]
    );
    Route::post(
        'destroy',
        [
            'as'   => 'Topic.destroy',
            'uses' => 'TopicController@destroy',
        ]
    );
});
