<?php

Route::group([
    'prefix' => 'preusers'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'PreUsers.index',
            'uses' => 'PreUsersController@index',
        ]
    );
    Route::any(
        'index',
        [
            'as'   => 'PreUsers.index',
            'uses' => 'PreUsersController@index',
        ]
    );

    Route::any(
        'create',
        [
            'as'   => 'PreUsers.create',
            'uses' => 'PreUsersController@create',
        ]
    );
    Route::post(
        'create/confirm',
        [
            'as'   => 'PreUsers.createConfirm',
            'uses' => 'PreUsersController@createConfirm',
        ]
    );
    Route::post(
        'create/store',
        [
            'as'   => 'PreUsers.createStore',
            'uses' => 'PreUsersController@createStore',
        ]
    );

    Route::post(
        'destroy',
        [
            'as'   => 'PreUsers.destroy',
            'uses' => 'PreUsersController@destroy',
        ]
    );
});
