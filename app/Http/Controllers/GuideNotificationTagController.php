<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideNotificationTagRequest;
use App\Services\GuideNotificationService;
use Log;

class GuideNotificationTagController extends Controller
{
    protected $guideNotificationService;

    public function __construct(GuideNotificationService $guideNotificationService)
    {
        parent::__construct();

        $this->guideNotificationService = $guideNotificationService;

        // configの固定パラメタ
        view()->share([
            'formData' => $this->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        // 権限確認
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $params = $request->all();
        $params['guide_application_id'] = $guideAppId;
        $tagList = $this->guideNotificationService->searchTagList($params);

        $pagerLinkNum = config('forms.GuideNotification.pagerLinkNum');
        $pagerView = $this->guideNotificationService->getPagerView($tagList, $pagerLinkNum);

        $data = [
            'search' => $params,
            'tagList' => $tagList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'pagerView' => $pagerView,
        ];
        return view('GuideNotificationTag.index', $data);
    }

    /**
     * GuideNotification create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $formInput = [];
        // バリデーションエラーで戻された場合
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $formInput = $request->old();
        } else { // 戻るボタンで戻ってきた場合
            $formInput = $request->all();
        }

        $orderList = $this->guideNotificationService->getNotificationTagOrderList($guideAppId);

        $data = [
            'formInput' => $formInput,
            'orderList' => $orderList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationTag.create', $data);
    }

    /**
     * お知らせタグ新規登録確認
     * @param GuideNotificationTagRequest $request
     * @return view
     */
    public function createConfirm(GuideNotificationTagRequest $request)
    {
        // 権限確認
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $orderList = $this->guideNotificationService->getNotificationTagOrderList($guideAppId);

        $data = [
            'request' => $request,
            'orderList' => $orderList,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationTag.create_confirm', $data);
    }

    /**
     * お知らせタグ新規登録完了
     * @param GuideNotificationTagRequest $request
     * @return view
     */
    public function store(GuideNotificationTagRequest $request)
    {
        // 権限確認
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideNotificationService->createTag($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideNotificationTag.store', $data);
    }

    /**
     * お知らせタグ編集
     * @param request $request
     * @param integer $masterId
     * @return view
     */
    public function edit(request $request, $tagId)
    {
        $tagData = $this->guideNotificationService->getTagData($tagId);

        // 権限確認
        $guideAppId = $tagData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        if ($request->isMethod('post')) {
            $formInput = array_merge($tagData, $request->all());
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $formInput = array_merge($tagData, $request->old());
        } else {
            $formInput = $tagData;
            unset($formInput['order_no']);
        }

        $orderList = $this->guideNotificationService->getNotificationTagOrderList($guideAppId, $tagId);

        $data = [
            'formInput' => $formInput,
            'orderList' => $orderList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationTag.edit', $data);
    }

    /**
     * お知らせタグ編集確認
     * @param GuideContentsClusterRequest $request
     * @return view
     */
    public function editConfirm(GuideNotificationTagRequest $request)
    {
        // 編集対象データの存在確認
        $tagId = $request->get('id');
        $tagData = $this->guideNotificationService->getTagData($tagId);

        // 権限確認
        $guideAppId = $tagData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $orderList = $this->guideNotificationService->getNotificationTagOrderList($guideAppId, $tagId);

        $data = [
            'request' => $request,
            'orderList' => $orderList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationTag.edit_confirm', $data);
    }

    /**
     * お知らせタグ編集完了
     * @param request $request
     * @return view
     */
    public function update(GuideNotificationTagRequest $request)
    {
        // 権限確認
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideNotificationService->updateTag($request->all());

        $data = [
            'feature' => '編集',
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationTag.store', $data);
    }

    /**
     * お知らせタグ削除
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request)
    {
        // 権限確認
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $tagId = $request->get('id');
        $this->guideNotificationService->deleteTag($tagId);

        return redirect()->route('GuideNotificationTag.index', ['id' => $guideAppId]);
    }

    /**
     * 権限確認
     * @param integer $guideAppId
     * @return string
     */
    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideNotificationService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideNotificationService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }

    /**
     * パンくずリスト情報取得
     * @return array
     */
    private function getFormData()
    {
        $privateConfigs = config('forms.GuideNotificationTag');
        return [
            'screenName' => $privateConfigs['screenName'],
            'menuName' => $privateConfigs['menuName'],
        ];
    }
}
