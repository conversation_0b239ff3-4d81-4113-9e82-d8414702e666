<?php

namespace App\Http\Controllers;

use App\Http\Requests\GameContentImageRegisterRequest;
use Exception;
use Illuminate\Http\JsonResponse;
use Log;
use App\Services\ChGameContentImageService;
use App\Services\GameContentImageRegisterService;
use Illuminate\Http\Request;

/**
 * アップロードページ画面コントローラ
 */
class ChGameContentImageController extends Controller
{
    private $chGameContentImageService;
    private $gameContentImageRegisterService;

    public function __construct(
        ChGameContentImageService       $chGameContentImageService,
        GameContentImageRegisterService $gameContentImageRegisterService
    )
    {
        parent::__construct();

        $this->chGameContentImageService = $chGameContentImageService;
        $this->gameContentImageRegisterService = $gameContentImageRegisterService;

        // 処理が共通のため、アップロードページ画面用のJavaScriptファイルを読み込む
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/controller/GameContentImage.js';
        $javascriptFileList[] = '/js/apply/file.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);

        // configの固定パラメータ
        view()->share(['formData' => $this->chGameContentImageService->getIndexFormData()]);
    }

    /**
     * アップロードページ画面
     *
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return view
     */
    public function index(Request $request, $appId = null)
    {
        $selectAppData = $this->chGameContentImageService->getSelectApplicationList();
        $appId = $appId ?: key(array_slice($selectAppData, 0, 1, true));

        if (!$this->isDeveloperApplication($appId)) {
            abort(404);
        }

        $files = null;
        $selectUrl = null;
        $registerUrl = null;

        if (!empty($appId)) {
            $uploadFiles = [];
            $signedUrls = [];
            $config = config('forms.ChGameContentImage');
            $uploadFiles = $this->gameContentImageRegisterService->getExaminationFileInfo($appId, $config['appType']);
            $signedUrls = $this->gameContentImageRegisterService->getSignedUrlListForRead($uploadFiles, $config['uploadDirectory']);
            $files = $this->gameContentImageRegisterService->formatFileInfoForOutput($uploadFiles, $signedUrls, $config);

            $selectUrl = route('ChGameContentImage.index');
            $registerUrl = route('ChGameContentImage.register', ['app_id' => $appId]);
        }

        // MEMO: 使用するテンプレートはゲーム内画像申請画面と同じものを使用する
        return view('GameContentImage.index', compact(
            'appId',
            'selectAppData',
            'files',
            'selectUrl',
            'registerUrl'
        ));
    }

    /**
     * 画像・ファイル追加モーダル表示
     *
     * @param Request $request リクエスト
     * @return view
     */
    public function register(Request $request)
    {
        $config = config('forms.ChGameContentImage');

        // アプリIDを取得
        $appId = $request->input('app_id');

        if (!$this->isDeveloperApplication($appId)) {
            abort(404);
        }

        // S3へのファイルアップロード用の署名付きデータを取得
        $signedDataJson = '';
        $signedJson = $this->gameContentImageRegisterService->getUploadSignedData($appId, $config['uploadDirectory']);
        $signedDataJson = json_encode($signedJson);

        // 種類セレクタの設定取得
        $selectInfoList = $this->gameContentImageRegisterService->getSelectInfoList($config['imageType']);

        // 各種URLの設定
        $registerStoreUrl = route('ChGameContentImage.register.store');
        $registerUploadCheckUrl = route('ChGameContentImage.register.upload.check');

        // MEMO: 使用するテンプレートはゲーム内画像申請画面と同じものを使用する
        return view('GameContentImage.modals.register', compact(
            'appId',
            'selectInfoList',
            'signedDataJson',
            'registerStoreUrl',
            'registerUploadCheckUrl'
        ));
    }

    /**
     * 画像・ファイル追加処理
     *
     * @param Request $request リクエスト
     * @return JsonResponse
     */
    public function registerStore(Request $request)
    {
        $appId = $request->input('app_id');
        $fileName = $request->input('file_name');
        $fileId = $request->input('file_id');
        $imageType = $request->input('image_type');
        $applyType = $request->input('apply_type');

        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            // 審査ファイル登録
            $appType = config('forms.ChGameContentImage.appType');
            $this->gameContentImageRegisterService->updateExaminationFileInfo($appId, $appType, $fileName, $fileId, $imageType, $applyType);
            // アップロード完了通知
            $this->gameContentImageRegisterService->notifyCompletion($appId, [$fileId]);
        } catch (Exception $e) {
            Log::error("画像・ファイル追加処理に失敗しました。\n"
                .json_encode(compact('appId', 'fileName', 'fileId', 'imageType', 'applyType'))."\n"
                .$e->getTraceAsString());

            return response()->json(['success' => false, 'errors' => [trans('validationmessage.MSG337')]], 500);
        }

        return response()->json(Route('ChGameContentImage.register.completed'));
    }

    /**
     * 完了画面表示
     *
     * @param Request $request リクエスト
     * @return view
     */
    public function registerCompleted(Request $request)
    {
        // MEMO: 使用するテンプレートはゲーム内画像申請画面と同じものを使用する
        return view('GameContentImage.modals.completed');
    }

    /**
     * アップロードするファイルをバリデーションチェックする
     *
     * @param GameContentImageRegisterRequest $request リクエスト
     * @return JsonResponse
     */
    public function checkUploadFile(GameContentImageRegisterRequest $request)
    {
        return response()->json(['success' => true]);
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isDeveloperApplication($appId){
        $selectAppData = $this->chGameContentImageService->getSelectApplicationList();

        return array_key_exists($appId, $selectAppData);
    }
}
