<?php

namespace App\Http\Controllers;

use Exception;
use Log;
use Illuminate\Http\Request;
use App\Libs\Apply\ApplyCommon;
use App\Services\ClGamesService;
use App\Services\ApplyPreregistrationService;
use App\Services\ApplyReleaseService;
use App\Services\ApplyService;
use App\Services\BoardService;
use App\Services\ApplyNotificationService;

class ClGamesController extends Controller
{
    protected $clGamesService;

    /** @var ApplyPreregistrationService */
    protected $applyPreregistrationService;

    /** @var ApplyReleaseService */
    protected $applyReleaseService;

    /** @var ApplyService */
    protected $applyService;

    /** @var BoardService */
    protected $boardService;

    /** @var ApplyNotificationService */
    protected $applyNotificationService;

    public function __construct(
        ClGamesService              $clGamesService,
        ApplyPreregistrationService $applyPreregistrationService,
        ApplyReleaseService         $applyReleaseService,
        ApplyService $applyService,
        BoardService $boardService,
        ApplyNotificationService $applyNotificationService)
    {
        parent::__construct();

        $this->clGamesService = $clGamesService;
        $this->applyPreregistrationService = $applyPreregistrationService;
        $this->applyReleaseService = $applyReleaseService;
        $this->applyService = $applyService;
        $this->boardService = $boardService;
        $this->applyNotificationService = $applyNotificationService;
        view()->share($this->clGamesService->getFormData());

        // JS読み込み
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/controller/Games/Common.js';
        $javascriptFileList[] = '/js/apply/games.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

//*********************************************************************************************************************

    /**
     * 一覧
     * @param Request $request
     * @return view
     */
    public function index(Request $request)
    {
        $appId = $request->input('app_id');
        $device = 'client';
        $tab = $request->get('tab', config('forms.Games.defaultTab'));
        $data = null;

        if (empty($appId)) {
            // メニューからの遷移
            $clAppDataList = $this->clGamesService->getList();
            $data = $clAppDataList->first();
            if (!empty($data)) {
                    $appId = $data->id;    
            }
        } else {
            // タイトルを指定して（セレクトボックスを選択して）遷移
            $clAppDataList = $this->clGamesService->getListByAppId($appId);
            $data = $clAppDataList->first();
        }

        // 事前登録及びリリース申請登録フォームの情報取得
        $preregistration = null;
        $release = null;
        $applyCommonInfomation = null;

        $informationTopics = null;
        $notificationAddress = null;

        if ($tab == 'preregistration' || $tab == 'release') {
            // お知らせの情報取得
            $informationTopics = $this->boardService->getInformationTopics($appId, 'cl_application');
            // 通知メールアドレス取得
            $notificationAddress = $this->applyNotificationService->getNotificationAddress($appId, 'cl_application');

            switch ($tab) {
                case 'preregistration': // 事前登録タブが選択されている場合
                    $preregistration = $this->applyPreregistrationService->getPreregistration($appId, $device);
                    $applyCommonInfomation = $preregistration;
                    break;
                case 'release': // リリース申請タブが選択されている場合
                    $release = $this->applyReleaseService->getRelease($appId, $device);
                    // 事前登録が完了している場合は、事前登録情報クラスをリリース申請情報クラスに設定する
                    if ($release->isPreRegistrationActive()) {
                        $preregistration = $this->applyPreregistrationService->getPreregistration($appId, $device);
                        $release->setPreregistration($preregistration);
                    }
                    $applyCommonInfomation = $release;
                    break;
                default:
                    break;
            }
        } else if ($tab == 'board') {
            if (!$this->isDeveloperApplication($appId)) {
                abort(404);
            }

            // コメントの取得
            $kind = 'cl_application';
            $boardPage = $request->get('boardPage', 1);
            $boardPerPage = $request->get('perPage', 50);
            $informationTopics = $this->boardService->getInformationTopics($appId, $kind);
            $boardPaginator = $this->boardService->getMessageBoardContent($request, $appId, $kind, $boardPage, $boardPerPage);
            $boardPagerView = $this->boardService->getPagerView($boardPaginator, config('forms.common.pagination.pageLinkNum'));

            $response = $this->applyService->getTargetInformation($appId, $kind);
            $releaseStatus = !empty($response['releaseStatus']) ? $response['releaseStatus']: '1';

            $board = [
                'informationTopics' => $informationTopics,
                'paginator' => $boardPaginator,
                'releaseStatus' => $releaseStatus,
                'pagerView' => $boardPagerView
            ];
        }

        $appTitleType = $this->clGamesService->getAppTitleType();

        return view(
            'ClGames.index',
            compact(
                'appId',
                'device',
                'tab',
                'data',
                'appTitleType',
                'applyCommonInfomation',
                'preregistration',
                'release',
                'informationTopics',
                'notificationAddress',
                'board',
                'releaseStatus' 
            )
        );
    }

    /**
     * ファイルアップロード用署名付きURL発行
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return json
     */
    public function generateSignedUrl(Request $request, $appId)
    {
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $uploadFileNum = $request->get('uploadFileNum');
            // ファイルアップロード用の署名付きURLの発行
            $response = $this->boardService->generateSignedUrls($appId, config('forms.ClGames.boardFileUploadDirectory'), $uploadFileNum);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * 掲示板コメント追加
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return json
     */
    public function addBoardComment(Request $request, $appId)
    {
        // 掲示板のコメント追加処理
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $content = $request->get('comment');
            $uploadFileList = $request->get('uploadFileList');
            $directory = config('forms.ClGames.boardFileUploadDirectory');
            $response = $this->boardService->postMessageContent(
                $appId,
                'cl_application',
                $content,
                $uploadFileList,
                $directory
            );

            // メール送信
            $this->registPostCommentSapNotification($appId);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * PF運営宛の掲示板コメント追加の通知メールを送信する
     *
     * @param mixed $id
     * @return void
     */
    private function registPostCommentSapNotification($id){
        // メール送信
        $kind = 'cl_application';
        try{
            $this->applyNotificationService->registPostCommentSapNotification($id, $kind);
        } catch (Exception $e) {
            // 未知のエラー
            Log::error(sprintf("PF運営宛のメール通知に失敗しました。appId:%s kind:%s Exception:%s\n%s", 
                $id, $kind, $e->getMessage(), $e->getTraceAsString()));
        }
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isDeveloperApplication($appId){
        $appTitleType = $this->clGamesService->getAppTitleType();

        return array_key_exists($appId, $appTitleType);
    }

    /**
     * 掲示板コメント更新
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @param integer $messageId メッセージID	
     * @return json
     */
    public function updateBoardComment(Request $request, $appId, $messageId)
    {
        // 掲示板のコメント更新処理
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $content = $request->get('comment');
            $uploadFileList = $request->get('uploadFileList');
            $response = $this->boardService->putMessageContent(
                $appId,
                'cl_application',
                $messageId,
                $content,
                $uploadFileList
            );

            // メール送信
            $this->registPostCommentSapNotification($appId);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * 掲示板子メッセージ投稿
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @param integer $parentMessageId 親メッセージID	
     * @return json
     */
    public function addBoardChildComment(Request $request, $appId, $parentMessageId)
    {
        // 掲示板子メッセージ投稿処理
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $content = $request->get('comment');
            $uploadFileList = $request->get('uploadFileList');
            $directory = config('forms.ClGames.boardFileUploadDirectory');
            $response = $this->boardService->postChildMessageContent(
                $appId,
                'cl_application',
                $parentMessageId,
                $content,
                $uploadFileList,
                $directory
            );

            // メール送信
            $this->registPostCommentSapNotification($appId);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * 子メッセージ一覧取得
     *
     * @param  mixed $request
     * @return json
     */
    public function getMessageChildren(Request $request){
        try {    
            $app_id = $request->get('appId');
            $parentMessageId = $request->get('parentMessageId');
            $kind = 'cl_application';

            $response = $this->boardService->getMessageBoardContentChildren($request, $app_id, $kind, $parentMessageId);

            $data = [];
            for ($i=0,$count=count($response);;$i++)
            {
                if(!isset($response[$i])) break;
                $data[] = $response[$i];
            }

            return response()->json([
                'status' => 200,
                "data" => $data
            ], 200);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }
    /**
     * 掲示板子メッセージ更新
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @param integer $parentMessageId 親メッセージID	
     * @param integer $messageId メッセージID	
     * @return json
     */
    public function updateBoardChildComment(Request $request, $appId, $parentMessageId, $messageId)
    {
        // 掲示板子メッセージ投稿処理
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $content = $request->get('comment');
            $uploadFileList = $request->get('uploadFileList');
            $response = $this->boardService->putChildMessageContent(
                $appId,
                'cl_application',
                $parentMessageId,
                $messageId,
                $content,
                $uploadFileList
            );

            // メール送信
            $this->registPostCommentSapNotification($appId);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }
}
