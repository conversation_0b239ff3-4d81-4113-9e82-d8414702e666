<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\SandboxMonthlyUsersRequest;
use App\Services\SandboxMonthlyUsersService;

/**
 * Sandbox Users
 */
class SandboxMonthlyUsersController extends Controller
{
    protected $sandboxMonthlyUsersService;

    public function __construct(SandboxMonthlyUsersService $sandboxMonthlyUsersService)
    {
        parent::__construct();
        $this->sandboxMonthlyUsersService = $sandboxMonthlyUsersService;

        // configの固定パラメタ
        view()->share(['formData' => $this->sandboxMonthlyUsersService->getFormData()]);
    }

    /**
     * 一覧
     */
    public function index(Request $request)
    {
        // 表示件数の取得
        $condition = $this->sandboxMonthlyUsersService->formatSearchCondition($request->all());
        $paginator = $this->sandboxMonthlyUsersService->getSbxMonthlyUsersList($condition);
        if (! $paginator->isEmpty()) {
            $pagerView = $this->sandboxMonthlyUsersService->getPagerView($paginator, config('forms.SandboxMonthlyUsers.pagerLinkNum'));
        }
        return view('SandboxMonthlyUsers.index', compact('paginator', 'pagerView'));
    }

    /**
     * 詳細
     */
    public function show(Request $request, $id)
    {
        // 月額サービスの情報をDBからデータを取得
        $monthlyService = $this->sandboxMonthlyUsersService->getMonthlyService($id);

        // 該当月額サービスがない場合は終了
        if (empty($monthlyService)) {
            abort(404);
        }

        // アプリに権限があるか確認。さらにapplicationテーブルのデータを取得
        $application = $this->sandboxMonthlyUsersService->getApplication($monthlyService->app_id);
        if (empty($application)) {
            abort(400);
        }

        // 表示、入力したデータを設定
        $data = [];
        $data['id'] = $id;
        $data['service_name'] = $monthlyService->service_name;
        $data['title'] = $application->title;
        $data['callback_url'] = $monthlyService->callback_url;
        $data['callback_url_withdrawal'] = $monthlyService->callback_url_withdrawal;
        $data['user_id'] = null;

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は入力したuser_idを取得
            $data['user_id'] = $request->old()['user_id'];
        }

        // 月額入会者一覧
        $userList = $this->sandboxMonthlyUsersService->getUserList($id);

        return view('SandboxMonthlyUsers.show', compact('data', 'userList'));
    }
    /**
     * 成功通知
     */
    public function sendentry(SandboxMonthlyUsersRequest $request)
    {
        $result = $this->sandboxMonthlyUsersService->entry($request);
        if (count($result) > 0) {
            return back()->withErrors($result)->withInput($request->all());
        }
        // ダイアログの為の情報設定
        $request->session()->flash('message', config('forms.SandboxMonthlyUsers.sendEntryEnd'));

        // 詳細画面へリダイレクト
        return redirect()->route('SandboxMonthlyUsers.show', ['id' => $request->get('id')]);
    }

    /**
     * 解約通知
     */
    public function sendcancel(SandboxMonthlyUsersRequest $request)
    {
        $result = $this->sandboxMonthlyUsersService->cancel($request);
        if (count($result) > 0) {
            return back()->withErrors($result)->withInput($request->all());
        }

        // ダイアログの為の情報設定
        $request->session()->flash('message', config('forms.SandboxMonthlyUsers.sendCancelEnd'));

        // 詳細画面へリダイレクト
        return redirect()->route('SandboxMonthlyUsers.show', ['id' => $request->get('id')]);
    }
}
