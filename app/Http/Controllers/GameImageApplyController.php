<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\GameImageApplyService;
use App\Services\GameImageInfoService;
use App\Http\Requests\GameImageApplyRequest;

class GameImageApply<PERSON><PERSON>roller extends Controller
{
    protected $gameImageApplyService;

    public function __construct(
        GameImageApplyService $gameImageApplyService,
        GameImageInfoService $infoService
    ) {
        parent::__construct();

        $this->infoService           = $infoService;
        $this->gameImageApplyService = $gameImageApplyService;
        $this->gameImageApplyService->setControllerName($this->getControllerName());
        view()->share($this->gameImageApplyService->getFormData());
    }

//*********************************************************************************************************************
    /**
     * 一覧
     * @param  Request $request
     * @param  integer $appId
     * @return view
     */
    public function index(Request $request, $appId)
    {
        $appData      = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->gameImageApplyService->getExamRegisterList($appId);
        $viewInfoList = $this->gameImageApplyService->getViewInfoList($appId);

        return view('GameImageApply.index', compact(
            'appData',
            'registerList',
            'viewInfoList'
        ));
    }

    /**
     * 画像追加
     * @param  Request $request
     * @param  integer $appId
     * @return view
     */
    public function register(Request $request, $appId)
    {
        $appData        = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $selectInfoList = $this->gameImageApplyService->getSelectInfoList($appId);

        return view('GameImageApply.register', compact(
            'appData',
            'selectInfoList'
        ));
    }

    /**
     * 画像追加完了
     * @param  GameImageApplyRequest $request
     * @return view
     */
    public function registerStore(GameImageApplyRequest $request)
    {
        $appId   = $request->input('app_id');
        $appData = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $this->gameImageApplyService->registerStore($request->all());

        return view('GameImageApply.registerstore');
    }

    /**
     * 動画追加
     * @param  Request $request
     * @param  integer $appId
     * @return view
     */
    public function registerMovie(Request $request, $appId)
    {
        $appData        = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }

        // 申請可能チェック
        $this->infoService = $this->infoService->getDataByType(
            $appId,
            config('forms.GameImage.movie'),
            '',
            config('forms.GameImage.android')
        );
        $enableExamApply = $this->infoService->isEnableExamApply();

        $selectInfoList = $this->gameImageApplyService->getSelectInfoList($appId, true);

        return view('GameImageApply.registermovie', compact(
            'appData',
            'enableExamApply',
            'selectInfoList'
        ));
    }

    /**
     * 動画追加完了
     * @param  GameImageApplyRequest $request
     * @return view
     */
    public function registerMovieStore(GameImageApplyRequest $request)
    {
        $appId   = $request->input('app_id');
        $appData = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $this->gameImageApplyService->registerMovieStore($request->all());

        return view('GameImageApply.registermoviestore');
    }

//*********************************************************************************************************************
    /**
     * 画像申請確認
     * @param  Request $request
     * @return view
     */
    public function confirm(Request $request)
    {
        $appId        = $request->input('app_id');
        $appData      = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->gameImageApplyService->getExamRegisterList($appId);

        return view('GameImageApply.createconfirm', compact(
            'appData',
            'registerList'
        ));
    }

    /**
     * 画像申請完了
     * @param  Request $request
     * @return view
     */
    public function store(Request $request)
    {
        $appId        = $request->input('app_id');
        $appData      = $this->gameImageApplyService->getApplication($appId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->gameImageApplyService->getExamRegisterList($appId);
        $title        = $this->gameImageApplyService->getTitleResultPlain($this->screenName, '申請');

        $this->gameImageApplyService->store($registerList);

        return view('GameImageApply.createstore', compact(
            'appData',
            'title'
        ));
    }

    /**
     * 削除
     * @param  Request $request
     * @return view
     */
    public function destroy(Request $request)
    {
        $appId   = $request->input('app_id');
        $imgId   = $request->input('id');
        $imgData = $this->gameImageApplyService->getDetail($imgId);
        $isCheck = $this->gameImageApplyService->isEnableRegisterDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }
        $this->gameImageApplyService->destroy($imgData);

        return redirect()->route('GameImageApply.index', $appId);
    }

//*********************************************************************************************************************
    /**
     * 申請一覧
     * @param  Request $request
     * @return view
     */
    public function reviewIndex(GameImageApplyRequest $request)
    {
        $pagerLinkNum  = config('forms.GameImage.pagerLinkNum');
        $imgDataList   = $this->gameImageApplyService->getSearchList($request->all());
        $pagerView     = $this->gameImageApplyService->getPagerView($imgDataList, $pagerLinkNum);
        $selectAppData = $this->gameImageApplyService->getSelectApplicationList();
        $girlsAppList  = $this->gameImageApplyService->getGirlsAppList();
        return view('GameImageApply.reviewIndex', compact(
            'imgDataList',
            'pagerView',
            'selectAppData',
            'girlsAppList'
        ));
    }

    /**
     * 画像詳細
     * @param  Request $request
     * @param  integer $imgId
     * @return view
     */
    public function show(Request $request, $imgId)
    {
        $imgData = $this->gameImageApplyService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('GameImageApply.show', compact(
            'imgData'
        ));
    }

//*********************************************************************************************************************
    /**
     * 申請取り下げ確認
     * @param  Request $request
     * @return view
     */
    public function withdrawConfirm(Request $request)
    {
        $imgId   = $request->input('id');
        $imgData = $this->gameImageApplyService->getDetail($imgId);
        $isCheck = $this->gameImageApplyService->isEnableReviewDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }

        return view('GameImageApply.withdrawconfirm', compact(
            'imgData'
        ));
    }

    /**
     * 画像申請完了
     * @param  Request $request
     * @return view
     */
    public function withdrawUpdate(Request $request)
    {
        $imgId   = $request->input('id');
        $imgData = $this->gameImageApplyService->getDetail($imgId);
        $isCheck = $this->gameImageApplyService->isEnableReviewDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }
        $title   = $this->gameImageApplyService->getTitleResultPlain($this->screenName, '取り下げ');

        $this->gameImageApplyService->destroy($imgData);

        return view('GameImageApply.withdrawstore', compact(
            'title'
        ));
    }
}
