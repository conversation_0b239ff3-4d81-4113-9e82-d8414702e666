<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\SbxApplications\SbxApplicationsRequest;
use App\Http\Requests\SbxApplications\ApkFileRequest;
use App\Services\SbxApplicationsService;
use Log;

/**
 * Sandbox Applications
 */
class SbxApplicationsController extends Controller
{
    protected $sbxApplicationsService;
    public function __construct(SbxApplicationsService $sbxApplicationsService)
    {
        parent::__construct();
        $this->sbxApplicationsService = $sbxApplicationsService;

        // configの固定パラメタ
        view()->share(['formData' => $this->sbxApplicationsService->getFormData()]);
    }

    /**
     * 一覧トップ
     */
    public function index(Request $request)
    {
        // アプリデータ取得
        $condition = $this->sbxApplicationsService->formatSearchCondition($request->all());
        $paginator = $this->sbxApplicationsService->getSbxApplicationList($condition);
        $pagerView = $this->sbxApplicationsService->getPagerView(
            $paginator,
            config('forms.SbxApplications.pagerLinkNum')
        );
        return view('SbxApplications.index', compact('paginator', 'pagerView'));
    }

    /**
     * 登録TOP
     */
    public function create(Request $request)
    {
        return view('SbxApplications.create', compact('request'));
    }

    /**
     * 登録確認
     */
    public function createConfirm(SbxApplicationsRequest $request)
    {
        // ApkFileRequestによってエラーセッションが設定されている場合
        if ($request->session()->has('errors')) {
            $request->merge($request->old());
        }
        
        return view('SbxApplications.createconfirm', compact('request'));
    }

    /**
     * 登録実行
     */
    public function store(SbxApplicationsRequest $request, ApkFileRequest $fileRequest)
    {
        // サンドボックステストゲーム登録
        $appId = $this->sbxApplicationsService->storeApplication($request);

        $view = view('SbxApplications.store');
        if ($this->shouldUploadApkFile($request)) {
            // APKファイルアップロード
            $result = $this->sbxApplicationsService->apkFileUpdate($fileRequest->all(), $appId);
            if (is_array($result)) {
                // 失敗した場合、一旦サンドボックステストゲーム登録したDBのデータを削除
                $this->sbxApplicationsService->deleteApplication($appId);
                if (array_key_exists('message', $result)) {
                    return $view->withErrors($result);
                } else {
                    return back()->withInput()->withErrors($result);
                }
            }
        }

        return $view;
    }

    /**
     * 変更TOP
     */
    public function edit(Request $request, $id)
    {
        if (! $this->sbxApplicationsService->isDeveloper($id)) {
            Log::error('invalid user : id=' . auth_user_id());
            abort(404);
        }
        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $content = $request->old();
        } elseif ($request->method() == 'POST') {
            // 確認画面から遷移時は入力していたデータを取得
            $content = $request->all();
        } else {
            // 初期表示時はDBからデータを取得
            $content = $this->sbxApplicationsService->getApplication($id);
            $content['type'] = $content['general'] == 1 ? 'general' : 'adult';
        }

        return view('SbxApplications.edit', compact('content'));
    }

    /**
     * 変更確認
     */
    public function editConfirm(SbxApplicationsRequest $request)
    {
        // ApkFileRequestによってエラーセッションが設定されている場合
        if ($request->session()->has('errors')) {
            $request->merge($request->old());
        }

        if (! $this->sbxApplicationsService->isDeveloper($request->get('id'))) {
            Log::error('invalid user : id=' . auth_user_id());
            abort(404);
        }

        return view('SbxApplications.editconfirm', compact('request'));
    }

    /**
     * 変更実行
     */
    public function update(SbxApplicationsRequest $request, ApkFileRequest $fileRequest)
    {
        if (! $this->sbxApplicationsService->isDeveloper($request->get('id'))) {
            Log::error('invalid user : id=' . auth_user_id());
            abort(404);
        }

        $view = view('SbxApplications.update');
        if ($this->shouldUploadApkFile($request)) {
            // APKファイルアップロード
            $result = $this->sbxApplicationsService->apkFileUpdate($fileRequest->all(), $request->get('id'));
            if (is_array($result)) {
                if (array_key_exists('message', $result)) {
                    return $view->withErrors($result);
                } else {
                    return back()->withInput()->withErrors($result);
                }
            }
        }

        // サンドボックステストゲーム変更
        $this->sbxApplicationsService->updateApplication($request);

        return $view;
    }

    /**
     * 削除
     */
    public function destroy(Request $request)
    {
        // ユーザチェック
        if ($this->sbxApplicationsService->isDeveloper($request->get('id'))) {
            $result = $this->sbxApplicationsService->deleteApplication($request->get('id'));
            if (empty($result)) {
                Log::error('Not Found application : id=' . $request->get('id'));
                abort(404);
            }
        }
        // トップへリダイレクト
        return redirect()->route('SbxApplications.index', ['search' => 'on']);
    }

    /**
     * APKファイルがアップロードするかを判定する。
     * 対応デバイスにAndroidAppだけチェックされている＆APKファイルがチェックされている場合のみ
     * APKファイルがアップロードできます
     * 
     * @param Request $request
     * @return bool
     */
    private function shouldUploadApkFile($request)
    {
        return count($request->get('deviceList')) === 1
            && in_array('android_app', $request->get('deviceList'))
            && $request->get('require_apk_file') == 1;
    }
}
