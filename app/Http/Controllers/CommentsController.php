<?php
namespace App\Http\Controllers;

use Illuminate\Routing\Router;
use Illuminate\Http\Request;
use App\Http\Requests\CommentsRequest;
use App\Services\CommentsService;

/**
 * コメント管理
 */
class CommentsController extends Controller
{
    protected $CommentsService;
    protected $config;
    protected $breadcrumbs;
    protected $authAdmin;
    protected $authSap;

    public function __construct(CommentsService $CommentsService, Router $router, Request $request)
    {
        parent::__construct();

        $this->CommentsService = $CommentsService;
        $this->config = config('forms.Comments');
        $this->config['suffixBeforeDelete'] = config('forms.common.suffixBeforeDelete');
        $this->config['suffixAfterDelete']  = config('forms.common.suffixAfterDelete');

        // 管理者および管理者として扱うログインユーザー
        if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
            $this->authAdmin = 1;
        } else {
            $this->authAdmin = 0;
        }

        // SAPおよびSAPとして扱うログインユーザー
        if (auth_is_user_sap()) {
            $this->authSap = 1;
        } else {
            $this->authSap = 0;
        }

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [
            $this->config['screenName'],
            'Comments.index',
            $router->input('topic_id', $request->get('topic_id', 0))
        ];

        view()->share(['formData'  => $this->config]);
        view()->share(['authAdmin' => $this->authAdmin]);
        view()->share(['authSap'   => $this->authSap]);
    }

    /**
     * コミュニティのデベロッパーかチェック
     * トピック情報をVIEWにセット
     *
     * @param  integer $topicId
     *
     * @return boolean
     *
     */
    public function checkAuthAndSetTopic($topicId)
    {
        if (empty($topicId) || !is_numeric($topicId)) {
            return false;
        }

        $dataTopic = $this->CommentsService->getTopicOne($topicId);
        if (empty($dataTopic)) {
            return false;
        }
        view()->share(['dataTopic' => $dataTopic]);

        if ($this->CommentsService->checkStillDeveloper($dataTopic['community_id']) == false) {
            return false;
        }

        return true;
    }

    /**
     * コメント一覧
     *
     * @param  Request $request
     * @param  integer $topic_id トピックID
     *
     */
    public function index(Request $request, $topic_id)
    {
        if ($this->checkAuthAndSetTopic($topic_id) == false && $this->authAdmin != 1) {
            abort(404);
        }

        $search = $this->CommentsService->formatSearchCondition($request->all());
        $search['topic_id'] = $topic_id;

        $listComments = $this->CommentsService->getCommentsList($search);

        $data = array(
            'breadcrumbs'   => $this->breadcrumbs,
            'pageComments'  => $listComments['pageComments'],
            'listComments'  => $listComments['listComments'],
            'pagerViewFrom' => $listComments['pagerViewFrom'],
            'pagerViewTo'   => $listComments['pagerViewTo'],
        );

        return view('Comments.index', $data);
    }

    /**
     * コメント登録
     *
     * @param  Request $request
     * @param  integer $topic_id   トピックID
     * @param  integer $comment_id 返信対象のコメントID
     *
     */
    public function create(Request $request, $topic_id, $comment_id = '')
    {
        if ($this->checkAuthAndSetTopic($topic_id) == false) {
            abort(404);
        }

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $dataComments = $request->old();
        } else {
            $dataComments = $request->all();
            if (!empty($comment_id) && empty($dataComments['text'])) {
                $dataComments['text'] = '>>No.'.$comment_id."\n";
            }
        }

        $screenNameSub = '登録';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'dataComments'  => $dataComments,
        );

        return view('Comments.create', $data);
    }

    /**
     * コメント登録確認
     *
     * @param  CommentsRequest $request
     *
     */
    public function createConfirm(CommentsRequest $request)
    {
        if ($this->checkAuthAndSetTopic($request['topic_id']) == false) {
            abort(404);
        }

        $screenNameSub = '登録確認';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'dataComments'  => $request->all(),
        );

        return view('Comments.createconfirm', $data);
    }

    /**
     * コメント登録完了
     *
     * @param  CommentsRequest $request
     *
     */
    public function store(CommentsRequest $request)
    {
        if ($this->checkAuthAndSetTopic($request['topic_id']) == false) {
            abort(404);
        }

        $this->CommentsService->addComments($request);

        $screenNameSub = '登録完了';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
        );

        return view('Comments.store', $data);
    }

    /**
     * コメント削除
     *
     * @param Request $request
     *
     */
    public function destroy(Request $request)
    {
        if ($this->checkAuthAndSetTopic($request['topic_id']) == false) {
            abort(404);
        }

        $request->session()->flash('message', 'No.'.$request->get('comment_key').$this->config['suffixAfterDelete']);
        $this->CommentsService->delComments($request);
        return redirect()->route('Comments.index', ['topic_id' => $request['topic_id'], 'search' => 'on']);
    }
}
