<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideTwitterDmRequest;
use App\Services\GuideTwitterDmService;
use Log;

class GuideTwitterDmController extends Controller
{
    protected $guideTwitterDmService;

    public function __construct(GuideTwitterDmService $guideTwitterDmService)
    {
        parent::__construct();

        $this->guideTwitterDmService = $guideTwitterDmService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideTwitterDmService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        // 現時点では保持しているのは perPage のみ
        $search = $this->guideTwitterDmService->formatSearchCondition($request->all());

        // ---------
        // process
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideTwitterDmList =  $this->guideTwitterDmService->getList($guideAppId, $search);

        // ページャ表示用のview側で使うパラメタを出す
        $pagerLinkNum = config('forms.GuideTwitterDm.pagerLinkNum');
        $pagerView = $this->guideTwitterDmService
            ->getPagerView($guideTwitterDmList, $pagerLinkNum);

        $data = [
            'guideTwitterDmList' => $guideTwitterDmList,
            'pagerView' => $pagerView,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideTwitterDm.index', $data);
    }

    /**
     * GuideTwitterDm create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $request->all();
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        if (empty($content['send_at'])) {
            $content['send_at'] = timestamp_to_date(now_stamp() + 86400);
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideTwitterDm.create', $data);
    }

    /**
     * GuideTwitterDm create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideTwitterDmRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideTwitterDm.createconfirm', $data);
    }

    /**
     * GuideTwitterDm create store page
     * @param request $request
     * @return view
     */
    public function store(GuideTwitterDmRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideTwitterDmService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideTwitterDm.store', $data);
    }

    /**
     * GuideTwitterDm edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $dmscheduleid
     * @return view
     */
    public function edit(Request $request, $guideAppId, $dmscheduleid)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideTwitterDmService->getOneById($dmscheduleid, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_twitter_dm_schedule : id=' . $dmscheduleid);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        if (empty($content['id'])) {
            $content['id'] = $dmscheduleid;
        }
        if (empty($content['send_at'])) {
            $content['send_at'] = timestamp_to_date(now_stamp() + 86400);
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideTwitterDm.edit', $data);
    }

    /**
     * GuideTwitterDm edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideTwitterDmRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideTwitterDm.editconfirm', $data);
    }

    /**
     * GuideTwitterDm edit update page
     * @param request $request
     * @return view
     */
    public function update(GuideTwitterDmRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideTwitterDmService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideTwitterDm.store', $data);
    }

    /**
     * GuideNotification destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $dmscheduleid = $request->get('dmscheduleid');
        $this->guideTwitterDmService->deleteContent($dmscheduleid, $guideAppId);

        $urlParams = [
            'id' => $guideAppId,
            'search' => 'on',
        ];
        return redirect()->route('GuideTwitterDm.index', $urlParams);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideTwitterDmService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideTwitterDmService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
