<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PreregistrationMailformRequest;
use App\Services\PreregistrationMailformService;
use Log;

class PreregistrationMailformController extends Controller
{
    protected $preregistrationMailform;

    public function __construct(PreregistrationMailformService $preregistrationMailform, Request $request)
    {
        parent::__construct();

        $this->preregistrationMailform = $preregistrationMailform;
        view()->share(['formData' => $this->preregistrationMailform->getFormData()]);
    }

    /**
     * Index page
     * @param Request $request
     * @return view
     */
    public function index(Request $request)
    {
        $params       = $this->preregistrationMailform->formatSearchCondition($request->all());
        $campaignList = $this->preregistrationMailform->getList($params);
        $pagerLinkNum = config('forms.GuideNotification.pagerLinkNum');
        $pagerView    = $this->preregistrationMailform->getPagerView($campaignList, $pagerLinkNum);
        $data = [
            'campaignList' => $campaignList,
            'pagerView'    => $pagerView,
        ];

        return view('PreregistrationMailform.index', $data);
    }

    /**
     * campaign create page
     * @param request $request
     * @return view
     */
    public function create(Request $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        return view('PreregistrationMailform.create', compact('request'));
    }

    /**
     * campaign create confirm page
     * @param PreregistrationMailformRequest $request
     * @return view
     */
    public function createConfirm(PreregistrationMailformRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        return view('PreregistrationMailform.createconfirm', compact('request'));
    }

    /**
     * campaign create store page
     * @param PreregistrationMailformRequest $request
     * @return view
     */
    public function store(PreregistrationMailformRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        $this->preregistrationMailform->insert($request->all());

        $feature = '作成';
        return view('PreregistrationMailform.store', compact('feature'));
    }

    /**
     * campaign edit page
     * @param Request $request
     * @param integer $id
     * @return view
     */
    public function edit(Request $request, $id)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        $campaign = $this->preregistrationMailform->getCampaignById($id);
        if (empty($campaign)) {
            Log::error('Not Found mailform_campaign : id=' . $id);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $campaign = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $campaign = $request->old();
        }
        $campaign['start_at'] = timestamp_to_date(strtotime($campaign['start_at']));
        $campaign['end_at']   = timestamp_to_date(strtotime($campaign['end_at']));

        return view('PreregistrationMailform.edit', compact('campaign', 'id'));
    }

    /**
     * campaign edit confirm page
     * @param Request $request
     * @return view
     */
    public function editConfirm(PreregistrationMailformRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        return view('PreregistrationMailform.editconfirm', compact('request'));
    }

    /**
     * campaign update page
     * @param PreregistrationMailformRequest $request
     * @return view
     */
    public function update(PreregistrationMailformRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        $this->preregistrationMailform->edit($request->all());

        $feature = '編集';
        return view('PreregistrationMailform.store', compact('feature'));
    }

    /**
     * Campaign destroy
     * @param Request $request
     * @return RedirectResponse
     */
    public function destroy(Request $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }

        if ($this->preregistrationMailform->delete($request->all())) {
            return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
        }
        abort(404);
    }

    public function show(Request $reqest, $id)
    {
        $campaign = $this->preregistrationMailform->getCampaignById($id);
        if (empty($campaign)) {
            Log::error('Not Found mailform_campaign : id=' . $id);
            abort(404);
        }

        if (auth_is_pf() === false) {
            $isVisible = $this->preregistrationMailform->isVisible($id);
            if (!$isVisible) {
                $loginId = auth_user_login_id();
                Log::error('Do not have permission : login_id=' . $loginId);
                return redirect()->route('PreregistrationMailform.index', ['search' => 'on']);
            }
        }

        $count = $this->preregistrationMailform->getRegisteredCount($id);

        return view('PreregistrationMailform.show', compact('count', 'campaign'));
    }
}
