<?php
namespace App\Http\Controllers;

use App\Http\Requests\PfManagementReportsMonthlyRequest;
use App\Services\PfManagementAvatarReportsMonthlyService;
use App\Services\PfManagementContentsReportsMonthlyService;
use Illuminate\Http\Request;

class PfManagementReportsMonthlyController extends Controller
{

    protected $pfManagementReportsMonthlyService;
    
    /**
     *
     * @var array
     */
    protected $reportTypes = [
        'contents' => [
            'service' => PfManagementContentsReportsMonthlyService::class,
            'display_name' => 'PFコンテンツ',
        ],
        'avatar' => [
            'service' => PfManagementAvatarReportsMonthlyService::class,
            'display_name' => 'アバター',
        ],
    ];

    public function __construct(Request $request)
    {
        parent::__construct();
        $reportType = $request->report_type;
        $reportType = (is_string($reportType) && isset($this->reportTypes[$reportType])) ? $reportType : 'contents';
        $this->pfManagementReportsMonthlyService =  app($this->reportTypes[$reportType]['service']);
        view()->share($this->pfManagementReportsMonthlyService->getFormData());
    }

    /**
     * TOP
     * @return view
     */
    public function index()
    {
        return view('PfManagementReportsMonthly.index', ['reportTypes' => $this->reportTypes]);
    }

    /**
     * CSVダウンロード
     * @param  PfManagementReportsMonthlyRequest $request
     * @return view
     */
    public function csvDownload(PfManagementReportsMonthlyRequest $request)
    {
        $param    = $request->all();
        $list     = $this->pfManagementReportsMonthlyService->getCsvList($param);
        $header   = $this->pfManagementReportsMonthlyService->getCsvHeader($param);
        $filename = $this->pfManagementReportsMonthlyService->getCsvFileName($param);

        return $this->pfManagementReportsMonthlyService->downloadCsv($filename, $list, $header, true);
    }
}
