<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\ChPointLogsRequest;
use App\Services\ChPointLogsService;

/**
 * 課金ログ比較ツール(チャネリング)
 */
class ChPointLogsController extends Controller
{
    protected $ChPointLogsService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(ChPointLogsService $ChPointLogsService)
    {
        parent::__construct();
        $this->ChPointLogsService = $ChPointLogsService;
        $this->config = config('forms.ChPointLogs');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'ChPointLogs.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * 課金ログ比較ツール(チャネリング)
     *
     * @param  Request $request
     *
     */
    public function index(Request $request)
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'deviceType'   => config('forms.ChPointLogs.deviceType'),
            'applicationList' => $this->ChPointLogsService->getApplicationList(),
        );

        return view('ChPointLogs.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param ChPointLogsRequest $request
     *
     */
    public function csvDownload(ChPointLogsRequest $request)
    {
        return $this->ChPointLogsService->download($request);
    }
}
