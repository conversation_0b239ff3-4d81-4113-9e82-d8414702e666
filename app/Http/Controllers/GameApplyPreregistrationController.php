<?php
namespace App\Http\Controllers;

use Exception;
use Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Libs\Apply\ApplyCommon;
use App\Http\Requests\ApplyPreregistrationRequest;
use App\Services\ApplyPreregistrationService;
use App\Services\ApplyNotificationService;

/**
 * 事前登録申請コントローラー
 */
class GameApplyPreregistrationController extends Controller {

    /** @var ApplyPreregistrationService */
    protected $applyPreregistrationService;

    /** @var ApplyNotificationService */
    protected $applyNotificationService;

    /** 通知メールに記載されるURLに指定するtagパラメータの値 */
    private $tag = 'preregistration';

    public function __construct(
        ApplyPreregistrationService $applyPreregistrationService,
        ApplyNotificationService $applyNotificationService)
    {
        parent::__construct();
        $this->applyPreregistrationService = $applyPreregistrationService;
        $this->applyNotificationService = $applyNotificationService;
    }
    
    /**
     * 審査用の画像を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeExaminationImages(ApplyPreregistrationRequest $request, $id, $device){

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyExaminationImages($id, $device);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'examinationImages';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * 事前登録申請のゲーム紹介ページ：デザイン部分素材を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $applyType
     * @return void
     */
    public function storeIntroductionImages(ApplyPreregistrationRequest $request, $id, $device){
        $catchphraseImage = $request->input('catchphrase_image');
        $catchphraseImageText = $request->input('catchphrase_image_text');
        $characterPriorityNotes = $request->input('character_priority_notes');
        $characterPriorityNotesText = $request->input('character_priority_notes_text');
        $copyright = $request->input('copyright');
        $copyrightText = $request->input('copyright_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyIntroductionImages(
                $id, $device, 
                $catchphraseImage, $catchphraseImageText,
                $characterPriorityNotes, $characterPriorityNotesText,
                $copyright, $copyrightText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'introductionImages';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * プラットフォーム上に掲載される画像を審査に提出
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return JsonResponse
     */
    public function storePlatformImages(ApplyPreregistrationRequest $request, $id, $device){
        $releaseScheduleSeasonText = $request->input('release_schedule_season_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $this->applyPreregistrationService->applyPlatformImages($id, $device, $releaseScheduleSeasonText);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'platformImages';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * 事前登録サイトを審査に提出
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storePreRegistrationSite(ApplyPreregistrationRequest $request, $id, $device){
        $isFollowingTermsCreate = boolval($request->input('is_following_terms_create'));
        $isDeployOfficialSite = boolval($request->input('is_deploy_official_site'));
        $isPreRegisterCheck = boolval($request->input('is_pre_register_check'));
        $meansVerificationText = $request->input('means_verification_text');
        $officialSiteUrl = $request->input('official_site_url');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyPreRegistrationSite(
                $id, $device, 
                $isFollowingTermsCreate,
                $isDeployOfficialSite, $isPreRegisterCheck,
                $meansVerificationText,
                $officialSiteUrl
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'preregistrationSite';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /*
     * サンドボックス環境 事前登録検証を審査に提出
     */
    public function storeSandboxVerification(ApplyPreregistrationRequest $request, $id, $device){
        $isUserTypeSpecialProcessingDone = boolval($request->input('is_user_type_special_processing_done'));
        $sandboxTestGameAppIdText = $request->input('sandbox_test_game_app_id_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applySandboxVerification(
                $id, $device, 
                $isUserTypeSpecialProcessingDone,
                $sandboxTestGameAppIdText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'sandboxVerification';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /*
     * 動作検証を審査に提出
     */
    public function storeVerification(ApplyPreregistrationRequest $request, $id, $device){
        $isProductionTestingPreparationDone = boolval($request->input('is_production_testing_preparation_done'));

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyVerification(
                $id, $device, 
                $isProductionTestingPreparationDone
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'verification';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * コミュニティを審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeCommunity(ApplyPreregistrationRequest $request, $id, $device){

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyCommunity($id, $device);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'community';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * 事前登録申請のゲーム情報入力を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeGameInformation(ApplyPreregistrationRequest $request, $id, $device){
        $preReleaseGameAnnouncement = $request->input('pre_release_game_announcement');
        $recommendationAgeDivision = $request->input('recommendation_age_division');
        $taxIncludedPrice = $request->input('tax_included_price_value');
        $taxExcludedPrice = $request->input('tax_excluded_price_value');
        $clientGameIntroduction = $request->input('client_game_introduction_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyGameInformation(
                $id, $device, 
                $preReleaseGameAnnouncement, $recommendationAgeDivision, $taxIncludedPrice, $taxExcludedPrice, $clientGameIntroduction
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'gameInformation';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * CSRFトークンが有効かどうかを判定する
     * 
     * CSRFトークンが無効だった時に画面のリロードしてCSRFトークンの再取得を行う時の
     * CSRFトークンの有効かどうかを判定を行う為の機能です。
     * ※事前登録申請のコントローラーに実装されていますが、リリース申請でも実行されます。
     * 
     * @param  mixed $request
     * @return void
     */
    public function checkCsrf(Request $request){
        return response()->json(['message' => 'CSRF token is valid']);
    }

    /**
     * 事前登録申請のWin対応環境を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeWindowsSupportedEnvironment(ApplyPreregistrationRequest $request, $id, $device){
        $osVersionText = $request->input('os_version_text');
        $processorText = $request->input('processor_text');
        $memorySize = $request->input('memory_size');
        $memorySizeUnit = $request->input('memory_size_unit');
        $graphicsText = $request->input('graphics_text');
        $capacitySize = $request->input('capacity_size');
        $capacitySizeUnit = $request->input('capacity_size_unit');
        $noteText = $request->input('note_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyWindowsSupportedEnvironment(
                $id, $device, 
                $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'windowsSupportedEnvironment';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * 事前登録申請のMac対応環境を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeMacSupportedEnvironment(ApplyPreregistrationRequest $request, $id, $device){
        $osVersionText = $request->input('os_version_text');
        $processorText = $request->input('processor_text');
        $memorySize = $request->input('memory_size');
        $memorySizeUnit = $request->input('memory_size_unit');
        $graphicsText = $request->input('graphics_text');
        $capacitySize = $request->input('capacity_size');
        $capacitySizeUnit = $request->input('capacity_size_unit');
        $noteText = $request->input('note_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyMacSupportedEnvironment(
                $id, $device, 
                $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'macSupportedEnvironment';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * 事前登録申請のCEROを審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeCero(ApplyPreregistrationRequest $request, $id, $device){
        $classificationText = $request->input('classification_text');
        $contentIcons = $request->input('content_icons');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyPreregistrationService->applyCero(
                $id, $device, 
                $classificationText, $contentIcons
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'cero';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * PF運営宛の審査提出の通知メールを送信する
     *
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $categoryType
     * @return void
     */
    private function registApplyNotification($id, $device, $categoryType){
        // メール送信
        try{
            $this->applyNotificationService->registApplyNotification($id, $device, 
                $this->tag, $categoryType);
        } catch (Exception $e) {
            // 未知のエラー
            Log::error(sprintf("PF運営宛のメール通知に失敗しました。appId:%s device:%s category:%s ExceptionMessage:%s\n%s", 
                $id, $device, $categoryType, $e->getMessage(), $e->getTraceAsString()));
        }
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return bool
     */
    public function isDeveloperApplication($appId, $device){
        return $this->applyNotificationService->isDeveloperApplication($appId, $device);
    }
}
