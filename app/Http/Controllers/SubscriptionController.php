<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Subscription\BasePlanRegistrationRequest;
use App\Http\Requests\Subscription\DetailRequest;
use App\Http\Requests\Subscription\OfferRegistrationRequest;
use App\Http\Requests\Subscription\SubscriptionRequest;
use App\Http\Requests\Subscription\SubscriptionSearchRequest;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;

/**
 * 定期購入
 */
class SubscriptionController extends Controller
{
    /**
     * @var SubscriptionService
     */
    protected $subscriptionService;

    /**
     * コンストラクタ
     * 
     * @param SubscriptionService $subscriptionService
     */
    public function __construct(SubscriptionService $subscriptionService)
    {
        parent::__construct();
        $this->middleware('check.developer');
        $this->subscriptionService = $subscriptionService;
        view()->share([
            'formData' => $this->subscriptionService->getFormData(),
        ]);
    }

    /**
     * 定期購入一覧・検索画面
     *
     * @param  SubscriptionSearchRequest $request
     * @return \Illuminate\View\View
     */
    public function index(SubscriptionSearchRequest $request)
    {
        // 検索
        $condition = $this->subscriptionService->formatSearchCondition($request->all());
        $subscriptions = $this->subscriptionService->getSubscriptionList($condition);

        $data = [
            'subscriptions' => empty($subscriptions) ? [] : collect($subscriptions)->sortBy('updatedAt'),
        ];
        return view('Subscription.index', $data);
    }

    /**
     * 定期購入登録画面
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        if ($request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $formInput = $request->old();
        } elseif ($request->has('is_return')) {
            // 確認画面から遷移時は入力していたデータを取得
            $formInput = $request->all();
        } else {
            // 新規追加ボタンを押下したとき
            $request->merge([
                'sku' => '',
                'title' => '',
                'description' => ''
            ]);
            $formInput = $request->all();
        }
        return view('Subscription.create', compact('formInput'));
    }

    /**
     * 新規確認
     * 
     * @param  SubscriptionRequest $request
     * @return \Illuminate\View\View
     */
    public function createConfirm(SubscriptionRequest $request)
    {
        return view('Subscription.createConfirm');
    }

    /**
     * 定期購入登録
     *
     * @param  SubscriptionRequest $request
     * @return \Illuminate\View\View
     */
    public function store(SubscriptionRequest $request)
    {
        $this->subscriptionService->storeSubscription($request->all());
        return view('Subscription.store');
    }

    /**
     * 定期購入編集画面
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function edit(Request $request)
    {
        if ($request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $formInput = $request->old();
        } elseif ($request->has('is_return')) {
            // 確認画面から遷移時は入力していたデータを取得
            $formInput = $request->all();
        } else {
            // 初期表示時
            $formInput = $this->subscriptionService->getSubscription($request->all());
        }

        return view('Subscription.edit', compact('formInput'));
    }

    /**
     * 定期購入編集画面
     * 
     * @param SubscriptionRequest $request
     * @return \Illuminate\View\View
     */
    public function editConfirm(SubscriptionRequest $request)
    {
        return view('Subscription.editConfirm');
    }

    /**
     * 定期購入更新
     *
     * @param  SubscriptionRequest  $request
     * @return \Illuminate\View\View
     */
    public function update(SubscriptionRequest $request)
    {
        $this->subscriptionService->updateSubscription($request->all());
        return view('Subscription.update');
    }

    /**
     * 定期購入詳細・基本プラン・特典一覧
     * 
     * @param DetailRequest $request
     * @return \Illuminate\View\View
     */
    public function detail(DetailRequest $request)
    {
        $condition = $request->all();
        $detail = $this->subscriptionService->getDetail($condition);

        return view('Subscription.detail', compact('detail', 'condition'));
    }

    /**
     * 定期購入基本プラン登録
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function basePlanCreate(Request $request)
    {
        if ($request->isMethod('get') && !$request->session()->has('errors')) {
            // URL直リンクされた場合
            return redirect()->route('Subscription.index');
        }

        if ($request->isMethod('get') && $request->session()->has('errors')) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
        } else {
            $condition = $request->all();
        }

        return view('Subscription.basePlanCreate', compact('condition'));
    }

    /**
     * 定期購入基本プラン登録 ステータス変更確認(新規登録)
     * 
     * @param BasePlanRegistrationRequest $request
     * @return \Illuminate\Http\Response
     */
    public function basePlanCreateConfirm(BasePlanRegistrationRequest $request)
    {
        $condition = $request->all();

        return view('Subscription.basePlanCreateConfirm', compact('condition'));
    }

    /**
     * 定期購入基本プラン登録 新規登録完了
     * 
     * @param BasePlanRegistrationRequest $request
     * @return \Illuminate\View\View
     */
    public function basePlanStore(BasePlanRegistrationRequest $request)
    {
        $condition = $request->all();
        $this->subscriptionService->storeBasePlan($condition);

        return view('Subscription.basePlanStore', compact('condition'));
    }

    /**
     * 定期購入基本プラン編集
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function basePlanEdit(Request $request)
    {
        if ($request->isMethod('get') && !$request->session()->has('errors')) {
            // URL直リンクされた場合
            return redirect()->route('Subscription.index');
        }

        if ($request->isMethod('get') && $request->session()->has('errors')) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
        } else {
            $condition = $request->all();
        }
        
        $basePlan = $this->subscriptionService->getBasePlan($condition);

        return view('Subscription.basePlanEdit', compact('condition', 'basePlan'));
    }

    /**
     * 定期購入基本プラン更新 ステータス変更確認(編集)
     * 
     * @param BasePlanRegistrationRequest $request
     * @return \Illuminate\Http\Response
     */
    public function basePlanEditConfirm(BasePlanRegistrationRequest $request)
    {
        $condition = $request->all();
        $basePlan = $this->subscriptionService->getBasePlan($condition);

        return view('Subscription.basePlanEditConfirm', compact('condition', 'basePlan'));
    }

    /**
     * 定期購入基本プラン更新
     * 
     * @param BasePlanRegistrationRequest $request
     * @return \Illuminate\View\View
     */
    public function basePlanUpdate(BasePlanRegistrationRequest $request)
    {
        $condition = $request->all();
        $this->subscriptionService->updateBasePlan($condition);

        return view('Subscription.basePlanUpdate', compact('condition'));
    }

    /**
     * 定期購入特典追加
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function offerCreate(Request $request)
    {
        if ($request->isMethod('get') && !$request->session()->has('errors')) {
            // URL直リンクされた場合
            return redirect()->route('Subscription.index');
        }

        if ($request->isMethod('get') && $request->session()->has('errors')) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
        } else {
            $condition = $request->all();
        }

        return view('Subscription.offerCreate', compact('condition'));
    }

    /**
     * 定期購入特典登録 ステータス変更確認(新規登録)
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function offerCreateConfirm(OfferRegistrationRequest $request)
    {
        $condition = $request->all();

        return view('Subscription.offerCreateConfirm', compact('condition'));
    }

    /**
     * 定期購入特典登録 新規登録完了
     * 
     * @param OfferRegistrationRequest $request
     * @return \Illuminate\View\View
     */
    public function offerStore(OfferRegistrationRequest $request)
    {
        $condition = $request->all();
        $this->subscriptionService->storeOffer($condition);

        return view('Subscription.offerStore', compact('condition'));
    }

    /**
     * 定期購入特典編集
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function offerEdit(Request $request)
    {
        if ($request->isMethod('get') && !$request->session()->has('errors')) {
            // URL直リンクされた場合
            return redirect()->route('Subscription.index');
        }

        $isValidationError = $request->isMethod('get') && $request->session()->has('errors');
        if ($isValidationError) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
            $condition['prevOfferSku'] = $request->session()->get('prevOfferSku');
            $request->session()->forget('prevOfferSku');
        } else {
            $condition = $request->all();
        }

        $offer = $this->subscriptionService->getOffer($condition);
        $request->session()->put('prevOfferSku', $offer['offerSku']);

        return view('Subscription.offerEdit', compact('condition', 'offer'));
    }
    
    /**
     * 定期購入特典登録 ステータス変更確認(編集)
     * 
     * @param OfferRegistrationRequest $request
     * @return \Illuminate\Http\Response
     */
    public function offerEditConfirm(OfferRegistrationRequest $request)
    {
        $condition = $request->all();
        $offer = $this->subscriptionService->getOffer($condition);

        return view('Subscription.offerEditConfirm', compact('condition', 'offer'));
    }

    /**
     * 定期購入特典更新
     * 
     * @param OfferRegistrationRequest $request
     * @return \Illuminate\View\View
     */
    public function offerUpdate(OfferRegistrationRequest $request)
    {
        $condition = $request->all();
        $this->subscriptionService->updateOffer($condition);

        return view('Subscription.offerUpdate', compact('condition'));
    }
}