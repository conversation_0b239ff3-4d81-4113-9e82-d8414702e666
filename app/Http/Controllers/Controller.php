<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Route;

abstract class Controller extends BaseController
{
    use DispatchesJobs, ValidatesRequests;

    protected $screeName;

    public function __construct()
    {
        $this->middleware('set.language');
        $this->middleware('auth');
        $this->middleware('auth.gate');
        $this->screenName = config('forms.' . $this->getControllerName() . '.screenName');
        $this->injectStylesheet();
        $this->injectJavascript();
    }

    /**
     * Inject stylesheet for each controller.
     *
     * @param none
     * @return none
     */
    public function injectStylesheet()
    {
        $csstPath = $this->getControllerName() . '.css';

        if (file_exists(public_path('css/controller/' . $csstPath))) {
            view()->share([
                'cssFileList' => ['/css/controller/' . $csstPath],
            ]);
        }
    }

    /**
     * Inject javascript for each controller.
     *
     * @param none
     * @return none
     */
    public function injectJavascript()
    {
        $javascriptPath = $this->getControllerName() . '.js';

        if (file_exists(public_path('js/controller/' . $javascriptPath))) {
            view()->share([
                'javascriptFileList' => ['/js/controller/' . $javascriptPath],
            ]);
        }
    }

    /**
     * Get current controller name.
     *
     * @param none
     * @return string
     */
    public function getControllerName()
    {
        return substr(Route::currentRouteName(), 0, strpos(Route::currentRouteName(), '.'));
    }

    /**
     * Get current action name.
     *
     * @param none
     * @return string
     */
    public function getActionName()
    {
        return substr(Route::currentRouteName(), strpos(Route::currentRouteName(), '.') + 1);
    }
}
