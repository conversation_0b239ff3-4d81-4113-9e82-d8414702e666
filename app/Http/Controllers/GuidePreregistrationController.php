<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuidePreregistrationRequest;
use App\Services\GuidePreregistrationService;
use Log;

class GuidePreregistrationController extends Controller
{
    protected $guidePreregistService;

    public function __construct(GuidePreregistrationService $guidePreregistService)
    {
        parent::__construct();

        $this->guidePreregistService = $guidePreregistService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guidePreregistService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $countList = $this->guidePreregistService->getCountUser($guideAppId);

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'countList' => $countList,
        ];
        return view('GuidePreregistration.index', $data);
    }

    /**
     * twitterapi edit page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function twitterapiEdit(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = [];
        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        } else {
            $content = $this->guidePreregistService->getOneGuideTwitterApplication($guideAppId);
            if (empty($content['start_at'])) {
                $content['start_at'] = timestamp_to_date(now_stamp());
            } else {
                $content['start_at'] = timestamp_to_date(strtotime($content['start_at']));
            }
            if (empty($content['end_at'])) {
                $content['end_at'] = timestamp_to_date(now_stamp());
            } else {
                $content['end_at'] = timestamp_to_date(strtotime($content['end_at']));
            }
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuidePreregistration.twitterapiedit', $data);
    }

    /**
     * twitterapi edit confirm page
     * @param request $request
     * @return view
     */
    public function twitterapiEditConfirm(GuidePreregistrationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuidePreregistration.twitterapieditconfirm', $data);
    }

    /**
     * twitterapi update page
     * @param request $request
     * @return view
     */
    public function twitterapiUpdate(GuidePreregistrationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $this->guidePreregistService->twitterapiEdit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => 'TwitterAPI設定項目',
            'feature' => '編集',
            'route' => 'twitterapiedit',
        ];
        return view('GuidePreregistration.update', $data);
    }

    /**
     * usercondition edit page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function userconditionEdit(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        } else {
            $content = $this->guidePreregistService->getOneGuidePreregistCondition($guideAppId);
            if (empty($content['start_at'])) {
                $content['start_at'] = timestamp_to_date(now_stamp());
            } else {
                $content['start_at'] = timestamp_to_date(strtotime($content['start_at']));
            }
            if (empty($content['end_at'])) {
                $content['end_at'] = timestamp_to_date(now_stamp());
            } else {
                $content['end_at'] = timestamp_to_date(strtotime($content['end_at']));
            }
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuidePreregistration.userconditionedit', $data);
    }

    /**
     * usercondition edit confirm page
     * @param request $request
     * @return view
     */
    public function userconditionEditConfirm(GuidePreregistrationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuidePreregistration.userconditioneditconfirm', $data);
    }

    /**
     * usercondition update page
     * @param request $request
     * @return view
     */
    public function userconditionUpdate(GuidePreregistrationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $this->guidePreregistService->userconditionEdit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => 'DMM会員の事前登録',
            'feature' => '編集',
            'route' => 'userconditionedit',
        ];
        return view('GuidePreregistration.update', $data);
    }

    /**
     * download csv for usercondition
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function userconditionMakecsv(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $fileName = 'preregistration_user_%s.csv';
        $fileName = sprintf(
            $fileName,
            $guideAppId
        );
        return $this->guidePreregistService->downloadCsvPreregistUser($guideAppId, $fileName);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guidePreregistService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guidePreregistService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
