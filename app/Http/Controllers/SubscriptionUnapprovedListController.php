<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Subscription\SubscriptionUnapprovedListSearchRequest;
use App\Http\Requests\Subscription\SubscriptionUnapprovedApproveRequest;
use App\Services\SubscriptionUnapprovedListService;
use Illuminate\Http\Request;

class SubscriptionUnapprovedListController extends Controller
{
    /**
     * @var subscriptionUnapprovedListService
     */
    protected $subscriptionUnapproveListService;

    /**
     * コンストラクタ
     * 
     */
    public function __construct(
        SubscriptionUnapprovedListService $subscriptionUnapprovedListService
    ){
        parent::__construct();
        $this->middleware('check.developer');
        $this->subscriptionUnapproveListService = $subscriptionUnapprovedListService;
        view()->share([
            'formData' => $this->subscriptionUnapproveListService->getFormData(),
        ]);
    }

    /**
     * 未承認アイテム一覧
     *
     * @param SubscriptionUnapprovedListSearchRequest $request
     * @return \Illuminate\Http\Response
     */
    public function index(SubscriptionUnapprovedListSearchRequest $request)
    {
        $unapprovedList = [];
        if ($request->method() === 'POST') {
            // 検索
            $condition = $this->subscriptionUnapproveListService->formatSearchCondition($request->all());
            $unapprovedList = $this->subscriptionUnapproveListService->getSearchList($condition);
        }
        $unapprovedList = collect($unapprovedList);

        return view('SubscriptionUnapprovedList.index', compact('unapprovedList'));
    }

    /**
     * 未承認アイテム詳細
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function detail(Request $request)
    {
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
        } else {
            $condition = $request->all();
        }
        $unapproved = $this->subscriptionUnapproveListService->getItem($condition);
        return view('SubscriptionUnapprovedList.detail', compact('unapproved', 'condition'));
    }

    /**
     * 未承認アイテム承認
     *
     * @param SubscriptionUnapprovedApproveRequest $request
     * @return \Illuminate\Http\Response
     */
    public function approve(SubscriptionUnapprovedApproveRequest $request)
    {
        // 未承認アイテム承認
        $approve = $this->subscriptionUnapproveListService->setUnapproveItemApprove($request->all());

        return view('SubscriptionUnapprovedList.approve', compact('approve'));
    }
}
