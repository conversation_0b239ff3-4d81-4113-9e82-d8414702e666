<?php
namespace App\Http\Controllers;

use App\Http\Requests\PfManagementReportsDailyRequest;
use App\Services\PfManagementAvatarReportsDailyService;
use App\Services\PfManagementContentsReportsDailyService;
use Illuminate\Http\Request;

class PfManagementReportsDailyController extends Controller
{

    protected $pfManagementReportsDailyService;
    
    /**
     *
     * @var array
     */
    protected $reportTypes = [
        'contents' => [
            'service' => PfManagementContentsReportsDailyService::class,
            'display_name' => 'PFコンテンツ',
        ],
        'avatar' => [
            'service' => PfManagementAvatarReportsDailyService::class,
            'display_name' => 'アバター',
        ],
    ];

    public function __construct(Request $request)
    {
        parent::__construct();
        $reportType = $request->report_type;
        $reportType = (is_string($reportType) && isset($this->reportTypes[$reportType])) ? $reportType : 'contents';
        $this->pfManagementReportsDailyService =  app($this->reportTypes[$reportType]['service']);
        view()->share($this->pfManagementReportsDailyService->getFormData());
    }

    /**
     * TOP
     * @return view
     */
    public function index()
    {
        return view('PfManagementReportsDaily.index', ['reportTypes' => $this->reportTypes]);
    }

    /**
     * CSVダウンロード
     * @param  PfManagementReportsDailyRequest $request
     * @return view
     */
    public function csvDownload(PfManagementReportsDailyRequest $request)
    {
        $param    = $request->all();
        $list     = $this->pfManagementReportsDailyService->getCsvList($param);
        $header   = $this->pfManagementReportsDailyService->getCsvHeader($param);
        $filename = $this->pfManagementReportsDailyService->getCsvFileName($param);

        return $this->pfManagementReportsDailyService->downloadCsv($filename, $list, $header, true);
    }
}
