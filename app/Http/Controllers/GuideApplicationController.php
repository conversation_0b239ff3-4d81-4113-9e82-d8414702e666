<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideApplicationRequest;
use App\Services\GuideApplicationService;
use Log;

class GuideApplicationController extends Controller
{
    protected $guideAppService;

    public function __construct(GuideApplicationService $guideAppService)
    {
        parent::__construct();

        $this->guideAppService = $guideAppService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideAppService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @return view
     */
    public function index(Request $request)
    {
        // ---------
        // process
        $guideAppLists = $this->guideAppService->getList();

        // ---------
        // view
        return view('GuideApplication.index', compact('guideAppLists'));
    }

    /**
     * GuideApplication create page
     * @param request $request
     * @return view
     */
    public function create(Request $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        return view('GuideApplication.create', compact('request'));
    }

    /**
     * GuideApplication create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideApplicationRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        return view('GuideApplication.createconfirm', compact('request'));
    }

    /**
     * GuideApplication create store page
     * @param request $request
     * @return view
     */
    public function store(GuideApplicationRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        $this->guideAppService->create($request->all());

        $feature = '登録';
        return view('GuideApplication.store', compact('feature'));
    }

    /**
     * GuideApplication edit page
     * @param request $request
     * @param integer $id
     * @return view
     */
    public function edit(Request $request, $id)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        if ($this->guideAppService->isEnableEdit($id) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideAppService->getOne($id);
        if (empty($content)) {
            Log::error('Not Found guide_application : id=' . $id);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        return view('GuideApplication.edit', compact('content', 'id'));
    }

    /**
     * GuideApplication edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideApplicationRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        $id = $request->get('id');
        if ($this->guideAppService->isEnableEdit($id) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        return view('GuideApplication.editconfirm', compact('request'));
    }

    /**
     * GuideApplication update page
     * @param request $request
     * @return view
     */
    public function update(GuideApplicationRequest $request)
    {
        if (auth_is_pf() === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        $id = $request->get('id');
        if ($this->guideAppService->isEnableEdit($id) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return redirect()->route('GuideApplication.index');
        }

        $this->guideAppService->edit($request->all());

        $feature = '編集';
        return view('GuideApplication.store', compact('feature'));
    }
}
