<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideFormRequest;
use App\Services\GuideFormService;
use Log;

class GuideFormController extends Controller
{
    protected $guideFormService;

    public function __construct(GuideFormService $guideFormService)
    {
        parent::__construct();

        $this->guideFormService = $guideFormService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideFormService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        return redirect()->route('GuideForm.edit', ['id' => $guideAppId]);
    }

    /**
     * GuideForm edit page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function edit(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        } else {
            $guideForm = $this->guideFormService->getDetails($guideAppId);
            $content['data'] = $guideForm->toArray();

            $privacyPolicy = $this->guideFormService->getPrivacyPolicy($guideAppId);
            if (is_null($privacyPolicy)) {
                $content['policy'] = '';
            } else {
                $content['policy'] = $privacyPolicy->policy;
            }
        }

        if (empty($content['data'])) {
            // メールアドレス
            $content['data'][0] = [
                'id' => 0,
                'priority' => 0,
                'name' => '',
                'explanation' => '',
                'is_required' => 1,
                'is_from' => 1,
                'is_subject' => 0,
                'type' => 'text',
                'max_length' => '100',
                'options' => [ 'text' => [0=>['value' => '']]],
            ];
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideForm.edit', $data);
    }

    /**
     * GuideForm edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideFormRequest $request)
    {
        $guideAppId = $request->get('guideAppid');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        return view('GuideForm.editconfirm', compact('request', 'guideAppTitle'));
    }

    /**
     * GuideForm update page
     * @param request $request
     * @return view
     */
    public function update(GuideFormRequest $request)
    {
        $guideAppId = $request->get('guideAppid');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideFormService->edit($request->all());

        $feature = '編集';
        return view('GuideForm.update', compact('feature', 'guideAppId', 'guideAppTitle'));
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideFormService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }

        $guideApplication = $this->guideFormService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
