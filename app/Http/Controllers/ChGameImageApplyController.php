<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ChGameImageApplyService;
use App\Http\Requests\ChGameImageApplyRequest;

class ChGameImageApplyController extends Controller
{
    protected $chGameImageApplyService;

    public function __construct(
        ChGameImageApplyService $chGameImageApplyService
    ) {
        parent::__construct();

        $this->chGameImageApplyService = $chGameImageApplyService;
        $this->chGameImageApplyService->setControllerName($this->getControllerName());
        view()->share($this->chGameImageApplyService->getFormData());
    }

    /**
     * 一覧
     * @param  integer $chAppId
     * @return view
     */
    public function index($chAppId)
    {
        $appData = $this->chGameImageApplyService->getApplication($chAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->chGameImageApplyService->getExamRegisterList($chAppId);
        $viewInfoList = $this->chGameImageApplyService->getViewInfoList($chAppId);

        return view('ChGameImageApply.index', compact(
            'appData',
            'registerList',
            'viewInfoList'
        ));
    }

    /**
     * 画像追加
     * @param  integer $chAppId
     * @return view
     */
    public function register($chAppId)
    {
        $appData = $this->chGameImageApplyService->getApplication($chAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $selectInfoList = $this->chGameImageApplyService->getSelectInfoList($chAppId);

        return view('ChGameImageApply.register', compact(
            'appData',
            'selectInfoList'
        ));
    }

    /**
     * 画像追加完了
     * @param  ChGameImageApplyRequest $request
     * @return view
     */
    public function registerStore(ChGameImageApplyRequest $request)
    {
        $chAppId = $request->input('ch_app_id');
        $appData = $this->chGameImageApplyService->getApplication($chAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $this->chGameImageApplyService->registerStore($request->all());

        return view('ChGameImageApply.registerstore');
    }

    /**
     * 画像申請確認
     * @param  Request $request
     * @return view
     */
    public function confirm(Request $request)
    {
        $chAppId = $request->input('ch_app_id');
        $appData = $this->chGameImageApplyService->getApplication($chAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->chGameImageApplyService->getExamRegisterList($chAppId);

        return view('ChGameImageApply.createconfirm', compact(
            'appData',
            'registerList'
        ));
    }

    /**
     * 画像申請完了
     * @param  Request $request
     * @return view
     */
    public function store(Request $request)
    {
        $chAppId = $request->input('ch_app_id');
        $appData = $this->chGameImageApplyService->getApplication($chAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->chGameImageApplyService->getExamRegisterList($chAppId);
        $title = $this->chGameImageApplyService->getTitleResultPlain($this->screenName, '申請');

        $this->chGameImageApplyService->store($registerList);

        return view('ChGameImageApply.createstore', compact(
            'appData',
            'title'
        ));
    }

    /**
     * 削除
     * @param  Request $request
     * @return view
     */
    public function destroy(Request $request)
    {
        $chAppId = $request->input('ch_app_id');
        $imgId = $request->input('id');
        $imgData = $this->chGameImageApplyService->getDetail($imgId);
        $isCheck = $this->chGameImageApplyService->isEnableRegisterDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }
        $this->chGameImageApplyService->destroy($imgData);

        return redirect()->route('ChGameImageApply.index', $chAppId);
    }

    /**
     * 申請一覧
     * @param  ChGameImageApplyRequest $request
     * @return view
     */
    public function reviewIndex(ChGameImageApplyRequest $request)
    {
        $pagerLinkNum  = config('forms.ChGameImage.pagerLinkNum');
        $imgDataList = $this->chGameImageApplyService->getSearchList($request->all());
        $pagerView = $this->chGameImageApplyService->getPagerView($imgDataList, $pagerLinkNum);
        $selectAppData = $this->chGameImageApplyService->getSelectApplicationList();
        $girlsAppList = $this->chGameImageApplyService->getGirlsAppList();
        return view('ChGameImageApply.reviewindex', compact(
            'imgDataList',
            'pagerView',
            'selectAppData',
            'girlsAppList'
        ));
    }

    /**
     * 画像詳細
     * @param  integer $imgId
     * @return view
     */
    public function show($imgId)
    {
        $imgData = $this->chGameImageApplyService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('ChGameImageApply.show', compact(
            'imgData'
        ));
    }

    /**
     * 申請取り下げ確認
     * @param  Request $request
     * @return view
     */
    public function withdrawConfirm(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageApplyService->getDetail($imgId);
        $isCheck = $this->chGameImageApplyService->isEnableReviewDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }

        return view('ChGameImageApply.withdrawconfirm', compact(
            'imgData'
        ));
    }

    /**
     * 画像申請完了
     * @param  Request $request
     * @return view
     */
    public function withdrawUpdate(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageApplyService->getDetail($imgId);
        $isCheck = $this->chGameImageApplyService->isEnableReviewDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }
        $title = $this->chGameImageApplyService->getTitleResultPlain($this->screenName, '取り下げ');

        $this->chGameImageApplyService->destroy($imgData);

        return view('ChGameImageApply.withdrawstore', compact(
            'title'
        ));
    }
}
