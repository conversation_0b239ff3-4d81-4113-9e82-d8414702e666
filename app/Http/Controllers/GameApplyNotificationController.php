<?php

namespace App\Http\Controllers;

use Exception;
use Log;
use Illuminate\Http\Request;
use App\Libs\Apply\ApplyCommon;
use App\Http\Requests\ApplyNotificationRequest;
use App\Services\ApplyNotificationService;

class GameApplyNotificationController extends Controller
{
    /** @var ApplyNotificationService */
    protected $applyNotificationService;

    public function __construct(ApplyNotificationService $applyNotificationService)
    {
        parent::__construct();
        $this->applyNotificationService = $applyNotificationService;
    }

       /**
     * 通知メールアドレス登録
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     */
    public function updateNotificationAddress(ApplyNotificationRequest $request, $id, $device)
    {
        $address = $request->input('addressList');
        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyNotificationService->updateNotificationAddress(
                $id,
                $device,
                $address
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        return response()->json([
            'status' => 200,
            'message' => "success",
            'data' => [
                'email' => isset($result) ? $result : [],
            ]
        ],200);
    }

    /**
     * 通知メールアドレス取得
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     */
    public function getNotificationAddress(Request $request, $id, $device)
    {
        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(404);
            }

            $result = $this->applyNotificationService->getNotificationAddress(
                $id,
                $this->applyNotificationService->getKind($device)
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        return response()->json([
            'status' => 200,
            'message' => "success",
            'data' => [
                'email' => isset($result) ? $result : [],
            ]
        ],200);
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return bool
     */
    public function isDeveloperApplication($appId, $device){
        return $this->applyNotificationService->isDeveloperApplication($appId, $device);
    }
}
