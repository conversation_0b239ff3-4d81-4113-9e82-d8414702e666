<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideFaqCategoryRequest;
use App\Services\GuideFaqCategoryService;
use Log;

class GuideFaqCategoryController extends Controller
{
    protected $guideFaqCatService;

    public function __construct(GuideFaqCategoryService $guideFaqCatService)
    {
        parent::__construct();

        $this->guideFaqCatService = $guideFaqCatService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideFaqCatService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideFaqCategoryList =  $this->guideFaqCatService->getFaqCategoryList([
            'guide_application_id'        => $guideAppId,
            'guide_faq_category_group_id' => @$request['guide_faq_category_group_id'],
        ]);

        $data = [
            'guideFaqCategoryList' => $guideFaqCategoryList,
            'guideAppId'           => $guideAppId,
            'guideAppTitle'        => $guideAppTitle,
            'categoryGroup'        => $this->guideFaqCatService->getGuideFaqCategoryGroupList($guideAppId),
        ];
        return view('GuideFaqCategory.index', $data);
    }

    /**
     * GuideFaqCategory create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request'       => $request,
            'guideAppId'    => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'categoryGroup' => $this->guideFaqCatService->getGuideFaqCategoryGroupList($guideAppId),
        ];

        return view('GuideFaqCategory.create', $data);
    }

    /**
     * GuideFaqCategory create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideFaqCategoryRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request'       => $request,
            'guideAppTitle' => $guideAppTitle,
            'categoryGroup' => $this->guideFaqCatService->getGuideFaqCategoryGroupList($guideAppId),
        ];
        return view('GuideFaqCategory.createconfirm', $data);
    }

    /**
     * GuideFaqCategory create store page
     * @param request $request
     * @return view
     */
    public function store(GuideFaqCategoryRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideFaqCatService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideFaqCategory.store', $data);
    }

    /**
     * GuideFaqCategory edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $faqcatid
     * @return view
     */
    public function edit(Request $request, $guideAppId, $faqcatid)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideFaqCatService->getOneById($faqcatid, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_faq_category : id=' . $faqcatid);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        $data = [
            'content'       => $content,
            'guideAppId'    => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'categoryGroup' => $this->guideFaqCatService->getGuideFaqCategoryGroupList($guideAppId),
        ];
        return view('GuideFaqCategory.edit', $data);
    }

    /**
     * GuideFaqCategory edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideFaqCategoryRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request'       => $request,
            'guideAppTitle' => $guideAppTitle,
            'categoryGroup' => $this->guideFaqCatService->getGuideFaqCategoryGroupList($guideAppId),
        ];
        return view('GuideFaqCategory.editconfirm', $data);
    }

    /**
     * GuideFaqCategory update page
     * @param request $request
     * @return view
     */
    public function update(GuideFaqCategoryRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideFaqCatService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideFaqCategory.store', $data);
    }

    /**
     * GuideFaqCategory destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(GuideFaqCategoryRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $faqcatid = $request->get('faqcatid');
        $this->guideFaqCatService->deleteContent($faqcatid, $guideAppId);

        return redirect()->route('GuideFaqCategory.index', ['id' => $guideAppId]);
    }

    /**
     * sort page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function priority(Request $request, $guideAppId)
    {
        $isUpdated = $request->get('isUpdated');

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideFaqCategoryList =  $this->guideFaqCatService->getListByGuideAppId($guideAppId);

        $data = [
            'guideFaqCategoryList' => $guideFaqCategoryList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'isUpdated' => $isUpdated,
        ];
        return view('GuideFaqCategory.priority', $data);
    }

    /**
     * sort update page
     * @param request $request
     * @return view
     */
    public function priorityUpdate(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $result = $this->guideFaqCatService->priorityEdit($request->all());
        if ($result === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not Update of Priority : request=' . var_export($request->all(), true));
        }

        return redirect()->route('GuideFaqCategory.priority', ['id' => $guideAppId, 'isUpdated' => 1]);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideFaqCatService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideFaqCatService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
