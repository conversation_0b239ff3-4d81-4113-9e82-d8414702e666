<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\CommunitiesRequest;
use App\Services\CommunitiesService;
use Helpers;

/**
 * コミュニティ管理
 */
class CommunitiesController extends Controller
{
    protected $CommunitiesService;
    protected $config;
    protected $breadcrumbs;
    protected $authAdmin;
    protected $authSap;

    public function __construct(CommunitiesService $CommunitiesService)
    {
        parent::__construct();

        $this->CommunitiesService = $CommunitiesService;
        $this->config = config('forms.Communities');

        // 管理者および管理者として扱うログインユーザー
        if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
            $this->authAdmin = 1;
        } else {
            $this->authAdmin = 0;
        }

        // SAPおよびSAPとして扱うログインユーザー
        if (auth_is_user_sap()) {
            $this->authSap = 1;
        } else {
            $this->authSap = 0;
        }

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'Communities.index'];

        view()->share(['formData'  => $this->config]);
        view()->share(['authAdmin' => $this->authAdmin]);
        view()->share(['authSap'   => $this->authSap]);
    }

    /**
     * コミュニティ一覧
     *
     */
    public function index()
    {
        $listCommunities = array();
        $noCommunitiesAppIds = array();

        if ($this->authAdmin) {
            // 管理者の場合はコミュニティ簡易一覧を表示
            $listCommunities = $this->CommunitiesService->getCommunitiesList();
            $view = 'list';
        } else {
            // 管理者以外の場合はコミュニティ詳細一覧を表示
            $devAppIds = $this->CommunitiesService->getDeveloperApplicationList(auth()->user()->id);
            if (count($devAppIds) > 0) {
                $tmpCommunities  = $this->CommunitiesService->getCommunitiesList($devAppIds);
                $listCommunities = $this->CommunitiesService->getCommunitiesRefList($tmpCommunities);
            }

            // コミュニティ未作成アプリケーション一覧
            $noCommunitiesAppIds = $this->CommunitiesService->getNoCommunitiesApplicationList(auth()->user()->id);

            $view = 'index';
        }

        $data = array(
            'breadcrumbs'         => $this->breadcrumbs,
            'listCommunities'     => $listCommunities,
            'noCommunitiesAppIds' => $noCommunitiesAppIds,
        );

        return view('Communities.'.$view, $data);
    }

    /**
     * コミュニティ詳細
     *
     * @param  integer $id
     *
     */
    public function show($id)
    {
        $tmpCommunities  = $this->CommunitiesService->getCommunitiesOne($id);
        $listCommunities = $this->CommunitiesService->getCommunitiesRefList($tmpCommunities);

        $data = array(
            'breadcrumbs'         => array_merge($this->breadcrumbs, ['詳細']),
            'listCommunities'     => $listCommunities,
            'noCommunitiesAppIds' => array(),
        );

        return view('Communities.index', $data);
    }

    /**
     * コミュニティ登録
     *
     * @param  Request $request
     *
     */
    public function create(Request $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $dataCommunities = $request->old();
        } else {
            $dataCommunities = $request->all();
        }

        // コミュニティ未作成アプリケーション一覧
        $noCommunitiesAppIds = $this->CommunitiesService->getNoCommunitiesApplicationList(auth()->user()->id);
        $listApplication     = $this->CommunitiesService->getApplicationList($noCommunitiesAppIds);

        $screenNameSub = '登録';

        $data = array(
            'breadcrumbs'     => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'   => $screenNameSub,
            'dataCommunities' => $dataCommunities,
            'listApplication' => $listApplication,
            'listBloodType'   => $this->config['listBloodType'],
        );

        return view('Communities.create', $data);
    }

    /**
     * コミュニティ登録確認
     *
     * @param  CommunitiesRequest $request
     *
     */
    public function createConfirm(CommunitiesRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $dataCommunities = $request->all();

        // コミュニティ名
        $dataCommunities['title'] = $this->CommunitiesService->getApplicationName($dataCommunities['app_id']);

        // 血液型
        $dataCommunities['bloodName'] = '';
        if (!empty($dataCommunities['blood_type'])) {
            $listBloodType = config('forms.Communities.listBloodType');
            foreach ($listBloodType as $key => $val) {
                if ($dataCommunities['blood_type'] == $key) {
                    $dataCommunities['bloodName'] = $val;
                    break;
                }
            }
        }

        // 固定トピック
        $dataCommunities['fix_topic_display'] = $this->CommunitiesService->checkFixTopic($dataCommunities['app_id']);

        $screenNameSub = '登録確認';

        $data = array(
            'breadcrumbs'     => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'   => $screenNameSub,
            'dataCommunities' => $dataCommunities,
        );

        return view('Communities.createconfirm', $data);
    }

    /**
     * コミュニティ登録完了
     *
     * @param  CommunitiesRequest $request
     *
     */
    public function store(CommunitiesRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $this->CommunitiesService->addCommunities($request);

        $screenNameSub = '登録完了';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
        );

        return view('Communities.store', $data);
    }

    /**
     * コミュニティ編集
     *
     * @param  Request $request
     * @param  integer $id
     *
     */
    public function edit(Request $request, $id)
    {
        if (!$this->authSap) {
            abort(404);
        }
        if (!$this->CommunitiesService->checkStillDeveloper($id)) {
            abort(404);
        }

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $dataCommunities = $request->old();
        } elseif ($request->method() == 'POST') {
            // 確認画面から遷移時は入力していたデータを取得
            $dataCommunities = $request->all();
        } else {
            // 初期表示時はDBからデータを取得
            $tmpCommunities  = $this->CommunitiesService->getCommunitiesOne($id);
            $listCommunities = $this->CommunitiesService->getCommunitiesRefList($tmpCommunities);
            $dataCommunities = $listCommunities[0];
            $list = $dataCommunities['fix_topic_list'];
            $dataCommunities['fix_topic_list'] = [];
            foreach ($list as $val) {
                $dataCommunities['fix_topic_list'][] = $val['topic_id'];
            }
        }

        // 都道府県一覧と職業一覧
        $listPrefectureJob = $this->CommunitiesService->getPrefectureListJobList();

        // 固定トピック
        if ($dataCommunities['fix_topic_display']) {
            $list = $dataCommunities['fix_topic_list'];
            $dataCommunities['fix_topic_list'] = [];
            foreach ($list as $val) {
                if ($val) {
                    $dataCommunities['fix_topic_list'][] = $val;
                }
            }
            $dataCommunities['fix_topic_count'] = 0;
            if (!is_null($dataCommunities['fix_topic_list'])) {
                if (is_array($dataCommunities['fix_topic_list'])) {
                    $dataCommunities['fix_topic_count'] = count($dataCommunities['fix_topic_list']);
                }
            }
        }

        $screenNameSub = '編集';

        $data = array(
            'breadcrumbs'     => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'   => $screenNameSub,
            'id'              => $id,
            'dataCommunities' => $dataCommunities,
            'listPrefecture'  => $listPrefectureJob['PrefectureName'],
            'listJob'         => $listPrefectureJob['JobName'],
            'listTopic'       => ['' => ''] + $this->CommunitiesService->getTopicList($id),
        );

        return view('Communities.edit', $data);
    }

    /**
     * コミュニティ編集確認
     *
     * @param  CommunitiesRequest $request
     *
     */
    public function editConfirm(CommunitiesRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $listPrefectureJob = $this->CommunitiesService->getPrefectureListJobList();

        $dataCommunities = $request->all();
        $dataCommunities['prefectureName'] = $listPrefectureJob['PrefectureName'][$dataCommunities['prefecture']];
        $dataCommunities['jobName'] = $listPrefectureJob['JobName'][$dataCommunities['job']];

        // 固定トピック
        if (isset($dataCommunities['fix_topic_list'])) {
            $list = $dataCommunities['fix_topic_list'];
            $dataCommunities['fix_topic_list'] = [];
            foreach ($list as $val) {
                if ($val) {
                    $dataCommunities['fix_topic_list'][] = $val;
                }
            }
        }

        $screenNameSub = '編集確認';

        $data = array(
            'breadcrumbs'     => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'   => $screenNameSub,
            'dataCommunities' => $dataCommunities,
            'listTopic'       => $this->CommunitiesService->getTopicList($dataCommunities['id']),
        );

        return view('Communities.editconfirm', $data);
    }

    /**
     * コミュニティ編集完了
     *
     * @param  CommunitiesRequest $request
     *
     */
    public function update(CommunitiesRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $this->CommunitiesService->editCommunities($request);

        $screenNameSub = '編集完了';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
        );

        return view('Communities.store', $data);
    }
}
