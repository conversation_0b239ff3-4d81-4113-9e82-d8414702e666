<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\UsersRequest;
use App\Services\Sso\SsoService;
use App\Services\UsersService;

/**
 * ログイン認証
 */
class UsersController extends Controller
{

    protected $usersService;

    private $ssoService;

    public function __construct(UsersService $usersService, SsoService $ssoService)
    {
        parent::__construct();
        $this->middleware('auth', [
            'except' => [
                'getLogin',
                'postLogin',
                'xhrPasswordUpdate',
                'ssoLogin',
                'ssoRedirect'
            ]
        ]);
        $this->middleware('auth.gate', [
            'except' => [
                'getLogin',
                'postLogin',
                'logout',
                'ssoLogin',
                'ssoRedirect'
            ]
        ]);
        $this->middleware('guest', [
            'except' => [
                'logout',
                'ssoLogin',
                'ssoRedirect'
            ]
        ]);
        $this->usersService = $usersService;
        $this->usersService->setFormData();
        view()->share($this->usersService->getFormData());

        $this->ssoService = $ssoService;
    }

    public function getLogin(Request $request)
    {
        // SSOのエラーメッセージが存在したら表示してsessionから削除
        if($request->session()->has('sso_error')) {
            $msg = $request->session()->get('sso_error');
            $request->session()->remove('sso_error');
            return view('Users.login')->withErrors($msg);
        }

        return view('Users.login');
    }

    public function postLogin(UsersRequest $request)
    {
        $result = $this->usersService->login($request->all());
        if (is_array($result)) {
            return view('Users.login')->withErrors($result);
        }

        if ($request->has('to')) {
            return redirect($request->get('to'));
        }
        return redirect()->route('Index.index');
    }

    public function logout()
    {
        $this->usersService->logout();
        return redirect()->route('Users.login');
    }

    public function ssoLogin(Request $request)
    {
        // リダイレクト先パラメーター
        // SSO認証完了後のssoRedirectで利用されるリダイレクト先パラメーター
        // 任意のパラメーターでない場合は、Topにリダイレクトされます。
        $to = $request->get("to");
        if(!empty($to)){
            // リダイレクト先パラメーターをセッションに保存する
            $request->session()->put('sso_redirect_path', $to);
        }

        $result = $this->ssoService->authRequest();
        if ($result->isFailed()) {
            $request->session()->put('sso_error', $result->getMessage());
            return redirect()->route('Index.index');
        }
    }

    public function ssoRedirect(Request $request)
    {
        $result = $this->ssoService->localLogin();
        if($result->isFailed()) {
            // sessionのflashがなぜか動かないので、通常のsessionにメッセージを保持しておく
            $request->session()->put('sso_error', $result->getMessage());
            return redirect()->route('Index.index');
        }

        // リダイレクト先パラメーターがセッションに保存されていたら、そこにリダイレクトする
        $to = $request->session()->get('sso_redirect_path');
        if(!empty($to)){
            $request->session()->forget('sso_redirect_path');
            return redirect($to);
        }
        return redirect()->route('Index.index');
    }
}
