<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\InquiriesService;
use App\Http\Requests\Inquiries\SearchRequest;
use App\Http\Requests\Inquiries\StatusUpdateRequest;
use App\Http\Requests\Inquiries\ReplyStatusUpdateRequest;
use App\Http\Requests\Inquiries\CategoryUpdateRequest;
use App\Http\Requests\Inquiries\MemoUpdateRequest;
use App\Http\Requests\Inquiries\ReplyRequest;
use App\Http\Requests\Inquiries\ReplyAllRequest;
use Carbon\Carbon;

/**
 * お問い合わせ
 */
class InquiriesController extends Controller
{

    protected $inquiriesService;

    public function __construct(InquiriesService $inquiriesService, Request $request)
    {
        parent::__construct();
        $this->inquiriesService = $inquiriesService;
        $url = ['url' => $this->inquiriesService->formatSearchUrl($request->query())];
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/supports.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
        view()->share(array_merge($this->inquiriesService->getFormData(), $url));
    }

    /**
     * index
     *
     * @param SearchRequest $request
     * @return view
     */
    public function index(SearchRequest $request)
    {
        $condition = $this->inquiriesService->formatSearchCondition($request->all());
        $paginator = $this->inquiriesService->getList($condition);
        $pagerView = $this->inquiriesService->getPagerView($paginator, config('forms.Inquiries.pagerLinkNum'));
        $tabList = $this->inquiriesService->getTabList($condition);
        $categoryOption = $this->inquiriesService->getCategoryOptions($condition, true);
        $from_last_send_date = '';
        $to_last_send_date = '';
        if (!array_key_exists('period', $request->all())) {
            $from_last_send_date = Carbon::now()->subWeek()->format('Y/m/d');
            $to_last_send_date = Carbon::now()->format('Y/m/d');
        }
        return view('Inquiries.index', compact('paginator', 'pagerView', 'tabList', 'categoryOption', 'from_last_send_date', 'to_last_send_date'));
    }

    public function csvDownload(SearchRequest $request)
    {
        $list = $this->inquiriesService->getList($request->all(), true);
        $header = $this->inquiriesService->getCsvHeader();
        $filename = $this->inquiriesService->getCsvFileName();
        return $this->inquiriesService->downloadCsv($filename, $list, $header, true, true);
    }

    public function statusUpdate(StatusUpdateRequest $request)
    {
        $this->inquiriesService->statusUpdate($request->all());
        $message = $this->inquiriesService->getMessage('status.afterUpdate');
        $request->session()->flash('message', $message);
        return redirect()->route('Inquiries.reply.create', [
            'id' => $request->get('id')
        ] + $this->inquiriesService->formatSearchUrl($request->all()));
    }

    public function replyStatusUpdate(ReplyStatusUpdateRequest $request)
    {
        $this->inquiriesService->replyStatusUpdate($request->all());
        $message = $this->inquiriesService->getMessage('replyStatus.afterUpdate');
        $request->session()->flash('message', $message);
        return redirect()->route('Inquiries.reply.create', [
            'id' => $request->get('id')
        ] + $this->inquiriesService->formatSearchUrl($request->all()));
    }

    /**
     * カテゴリ更新
     * @param CategoryUpdateRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function categoryUpdate(CategoryUpdateRequest $request)
    {
        $this->inquiriesService->categoryUpdate($request->all());
        $message = $this->inquiriesService->getMessage('category.afterUpdate');
        $request->session()->flash('message', $message);
        return redirect()->route('Inquiries.reply.create', [
            'id' => $request->get('id')
        ] + $this->inquiriesService->formatSearchUrl($request->all()));
    }

    public function memoUpdate(MemoUpdateRequest $request)
    {
        $this->inquiriesService->memoUpdate($request->all());
        $message = $this->inquiriesService->getMessage('memo.afterUpdate');
        $request->session()->flash('message', $message);
        return redirect()->route('Inquiries.reply.create', [
            'id' => $request->get('id')
        ] + $this->inquiriesService->formatSearchUrl($request->query()));
    }

    public function replyCreate($id)
    {
        $parent = $this->inquiriesService->getParent($id);
        if (empty($parent->exists)) {
            abort(404);
        }

        if (! empty(request('status'))) {
            $parent->status = request('status');
        }
        if (! empty(request('reply_status'))) {
            $parent->reply_status = request('reply_status');
        }
        // チェックボックス初期値指定
        if (request('is_finish')) {
            // 確認画面から修正:is_finishが存在する
            $finishFlg = true;
        } elseif (empty(request('is_finish')) && ! empty(request('body'))) {
            // 確認画面から修正:is_finishが存在しない
            $finishFlg = false;
        } else {
            // 初期表示（常にtrue）
            $finishFlg = true;
        }

        //大中小カテゴリの選択肢を取得
        $categoryOption = $this->inquiriesService->getCategoryOptions([
            'app_id'    => $parent->app_id,
            'large_id'  => $parent->large_id,
            'middle_id' => $parent->middle_id,
        ]);

        //カテゴリを表示用データに書き換え
        if (empty($parent->large_id)) {
            $parent->large_id  = 'not_set';
        }
        if (empty($parent->middle_id)) {
            $parent->middle_id = 'not_set';
        }
        if (empty($parent->small_id)) {
            $parent->small_id  = 'not_set';
        }

        $threadList = $this->inquiriesService->getThreadList($id);
        return view('Inquiries.replycreate', compact('parent', 'threadList', 'finishFlg', 'categoryOption'));
    }

    public function replyCreateConfirm(ReplyRequest $request)
    {
        $parent = $this->inquiriesService->getParent($request->get('id'));
        if (empty($parent->exists)) {
            abort(404);
        }
        $lastReply = $this->inquiriesService->getCreateConfirmText($request->get('id'));
        return view('Inquiries.replycreateconfirm', compact('parent', 'lastReply'));
    }

    public function replyStore(ReplyRequest $request)
    {
        $actionName = '返信';

        if (empty(request('is_finish'))) {
            // 返信時に任意のステータスに変更
            $result = $this->inquiriesService->replyStore($request->all(), false);
        } else {
            $result = $this->inquiriesService->replyStore($request->all());
        }

        $view = view('Inquiries.store', compact('actionName'));
        if (is_array($result)) {
            return $view->withErrors($result);
        }
        return $view;
    }

    public function replyAllCreate(Request $request)
    {
        if (! is_array($request->get('inquiry_id')) && ! is_array($request->old('inquiry_id'))) {
            return redirect()->route('Inquiries.index');
        }
        return view('Inquiries.replyallcreate');
    }

    public function replyAllCreateConfirm(ReplyAllRequest $request)
    {
        return view('Inquiries.replyallcreateconfirm');
    }

    public function replyAllStore(ReplyAllRequest $request)
    {
        $actionName = '一括返信';
        $result = $this->inquiriesService->replyAllStore($request->all());
        $view = view('Inquiries.store', compact('actionName'));
        if (is_array($result)) {
            return $view->withErrors($result);
        }
        return $view;
    }

    /**
     * 指定カテゴリリファレンスIDの子カテゴリの選択肢を取得
     * @param Request $request
     * @return array
     */
    public function getChildrenCategory(Request $request)
    {
        return $this->inquiriesService->getChildrenCategoryOption($request->id);
    }
}
