<?php
namespace App\Http\Controllers;

use Illuminate\Routing\Router;
use Illuminate\Http\Request;
use App\Http\Requests\CommunityCommentBlackUserRequest;
use App\Services\CommunityCommentBlackUserService;

/**
 * コミュニティコメント投稿制限管理
 */
class CommunityCommentBlackUserController extends Controller
{
    protected $CommunityCommentBlackUserService;
    protected $config;
    protected $breadcrumbs;
    protected $authAdmin;
    protected $authSap;

    public function __construct(CommunityCommentBlackUserService $CommentsService, Router $router, Request $request)
    {
        parent::__construct();

        $this->CommunityCommentBlackUserService = $CommentsService;
        $this->config = config('forms.CommunityCommentBlackUser');
        $this->config['suffixBeforeDelete'] = config('forms.common.suffixBeforeDelete');
        $this->config['suffixAfterDelete']  = config('forms.common.suffixAfterDelete');

        $url = array(
            'url' => $this->CommunityCommentBlackUserService->requestUrl($request->query())
        );
        view()->share(array_merge($this->CommunityCommentBlackUserService->getFormData(), $url));


        // 管理者および管理者として扱うログインユーザー
        if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
            $this->authAdmin = 1;
        } else {
            $this->authAdmin = 0;
        }

        // SAPおよびSAPとして扱うログインユーザー
        if (auth_is_user_sap()) {
            $this->authSap = 1;
        } else {
            $this->authSap = 0;
        }

        view()->share(['formData'  => $this->config]);
        view()->share(['authAdmin' => $this->authAdmin]);
        view()->share(['authSap'   => $this->authSap]);
    }

    /**
     * ブラックユーザ一覧
     *
     * @param  Request $request
     *
     */
    public function index()
    {
        $blackUser = $this->CommunityCommentBlackUserService->getBlackUserList();
        $this->breadcrumbs[] = $this->config['breadcrumbs'];
        $this->breadcrumbs[] = [
            $this->config['screenName'],
            'CommunityCommentBlackUser.index'
        ];

        $data = array(
            'breadcrumbs'         => $this->breadcrumbs,
            'screenName'          => $this->config['screenName'],
            'blackUser'       => $blackUser,
        );
        return view('CommunityCommentBlackUser.index', $data);

    }

    /**
     * ブラックユーザ登録
     *
     *
     */
    public function create()
    {
        $this->breadcrumbs[] = $this->config['breadcrumbs'];
        $this->breadcrumbs[] = [
            $this->config['screenName'],
            'CommunityCommentBlackUser.create'
        ];

        $data = array(
            'breadcrumbs'         => $this->breadcrumbs,
            'screenName'          => $this->config['screenName'],
        );

        return view('CommunityCommentBlackUser.create', $data);
    }

    /**
     * ブラックユーザ登録確認
     *
     * @param  CommentsRequest $request
     *
     */
    public function createConfirm(CommunityCommentBlackUserRequest $request)
    {
        $blackData   = $this->CommunityCommentBlackUserService->getMakeDetail($request->all());

        return view('CommunityCommentBlackUser.createconfirm', compact('blackData'));
    }

    /**
     * ブラックユーザ登録完了
     *
     * @param  CommentsRequest $request
     *
     */
    public function createStore(CommunityCommentBlackUserRequest $request)
    {
        $params = $request->all();
        unset($params['perPage'], $params['userSort'], $params['date_desc'], $params['page']);
        $result        = $this->CommunityCommentBlackUserService->createStore($params['user_id']);

        if (! $result) {
            abort(400);
        }

        $data = array(
            'breadcrumbs'         => $this->breadcrumbs,
            'screenName'          => $this->config['screenName'],
        );

        return view('CommunityCommentBlackUser.store', $data);
    }

    /**
     * ブラックユーザ削除
     *
     * @param Request $request
     *
     */
    public function destroy(Request $request)
    {
        $this->CommunityCommentBlackUserService->destroyBlackUser($request->get('user_id'));
        return redirect()->route('CommunityCommentBlackUser.index');
    }
}
