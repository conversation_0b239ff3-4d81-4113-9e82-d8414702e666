<?php
namespace App\Http\Controllers;

use App\Http\Requests\GraphMonthlyRequest;
use App\Services\GraphMonthlyService;

/**
 * レポート：月別表示・グラフ
 */
class GraphMonthlyController extends Controller
{

    protected $graphMonthlyService;

    public function __construct(GraphMonthlyService $graphMonthlyService)
    {
        parent::__construct();
        $this->graphMonthlyService = $graphMonthlyService;
        view()->share($this->graphMonthlyService->getFormData());
    }

    public function index()
    {
        return view('GraphMonthly.index');
    }

    public function search(GraphMonthlyRequest $request)
    {
        $list = $this->graphMonthlyService->getList($request->all());
        return view('GraphMonthly.index', compact('list'));
    }
}
