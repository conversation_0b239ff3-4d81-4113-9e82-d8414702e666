<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\TopicRequest;
use App\Http\Requests\TopicSearchRequest;
use App\Services\TopicService;

/**
 * トピック管理
 */
class TopicController extends Controller
{
    protected $TopicService;
    protected $config;
    protected $breadcrumbs;
    protected $authAdmin;
    protected $authSap;

    public function __construct(TopicService $TopicService)
    {
        parent::__construct();

        $this->TopicService = $TopicService;
        $this->config = config('forms.Topic');
        $this->config['suffixBeforeDelete'] = config('forms.common.suffixBeforeDelete');
        $this->config['suffixAfterDelete']  = config('forms.common.suffixAfterDelete');

        // 管理者および管理者として扱うログインユーザー
        if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
            $this->authAdmin = 1;
        } else {
            $this->authAdmin = 0;
        }

        // SAPおよびSAPとして扱うログインユーザー
        if (auth_is_user_sap()) {
            $this->authSap = 1;
        } else {
            $this->authSap = 0;
        }

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'Topic.index'];

        view()->share(['formData'  => $this->config]);
        view()->share(['authAdmin' => $this->authAdmin]);
        view()->share(['authSap'   => $this->authSap]);
    }

    /**
     * トピック一覧
     *
     * @param  TopicSearchRequest $request
     *
     */
    public function index(TopicSearchRequest $request)
    {
        $condition = $this->TopicService->formatSearchCondition($request->all());

        $listCommunities = array();
        $listTopic       = array();

        if ($this->authAdmin) {
            $listCommunities = $this->TopicService->getCommunitiesList();
            $listTopic = $this->TopicService->getTopicList($condition);
        } else {
            $devAppIds = $this->TopicService->getDeveloperApplicationList(auth()->user()->id);
            if (count($devAppIds) > 0) {
                $listCommunities = $this->TopicService->getCommunitiesList($devAppIds);
                $condition['app_id'] = $devAppIds;
                $listTopic = $this->TopicService->getTopicList($condition);
            }
        }

        $topicList = array_get($listTopic, 'listTopic', []);
        $pagerViewFrom = array_get($listTopic, 'pagerViewFrom', 0);
        $pagerViewTo = array_get($listTopic, 'pagerViewTo', 0);

        foreach ($topicList as $key => $val) {
            $val->create_user_name = $this->TopicService->getUserName($val->create_user_id);
            $val->update_user_name = $this->TopicService->getUserName($val->update_user_id);
            $val->manager_user_name = $this->TopicService->getUserName($val->manager_user_id);
            $val->vice_manager_user_name = $this->TopicService->getUserName($val->vice_manager_user_id);
        }

        $data = array(
            'breadcrumbs'     => $this->breadcrumbs,
            'listCommunities' => $listCommunities,
            'listTopic'       => $topicList,
            'pagerViewFrom'   => $pagerViewFrom,
            'pagerViewTo'     => $pagerViewTo,
        );

        return view('Topic.index', $data);
    }

    /**
     * トピック登録
     *
     * @param  Request $request
     *
     */
    public function create(Request $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $dataTopic = $request->old();
        } else {
            $dataTopic = $request->all();
        }

        $listCommunities = $this->TopicService->getCommunitiesList();

        $screenNameSub = '登録';

        $data = array(
            'breadcrumbs'     => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'   => $screenNameSub,
            'dataTopic'       => $dataTopic,
            'listCommunities' => $listCommunities,
        );

        return view('Topic.create', $data);
    }

    /**
     * トピック登録確認
     *
     * @param  TopicRequest $request
     *
     */
    public function createConfirm(TopicRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $dataTopic = $request->all();

        $listCommunities = $this->TopicService->getCommunitiesList();

        $screenNameSub = '登録確認';

        $data = array(
            'breadcrumbs'     => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'   => $screenNameSub,
            'dataTopic'       => $dataTopic,
            'listCommunities' => $listCommunities,
        );

        return view('Topic.createconfirm', $data);
    }

    /**
     * トピック登録完了
     *
     * @param  TopicRequest $request
     *
     */
    public function store(TopicRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $dataTopic = $request->all();

        $this->TopicService->addTopic($request);

        $screenNameSub = '登録完了';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'dataTopic'     => $dataTopic,
        );

        return view('Topic.store', $data);
    }

    /**
     * トピック編集
     *
     * @param  Request $request
     * @param  integer $id
     *
     */
    public function edit(Request $request, $id)
    {
        if (!$this->authSap) {
            abort(404);
        }

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $dataTopic = $request->old();
        } elseif ($request->method() == 'POST') {
            // 確認画面から遷移時は入力していたデータを取得
            $dataTopic = $request->all();
        } else {
            // 初期表示時はDBからデータを取得
            $dataTopic = $this->TopicService->getTopicOne($id);
        }

        $screenNameSub = '編集';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'dataTopic'     => $dataTopic,
            'id'            => $id,
        );

        return view('Topic.edit', $data);
    }

    /**
     * トピック編集確認
     *
     * @param  TopicRequest $request
     *
     */
    public function editConfirm(TopicRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $dataTopic = $request->all();

        $screenNameSub = '編集確認';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'dataTopic'     => $dataTopic,
        );

        return view('Topic.editconfirm', $data);
    }

    /**
     * トピック編集完了
     *
     * @param  TopicRequest $request
     *
     */
    public function update(TopicRequest $request)
    {
        if (!$this->authSap) {
            abort(404);
        }

        $dataTopic = $request->all();

        $this->TopicService->editTopic($dataTopic);

        $screenNameSub = '編集完了';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'dataTopic'     => $dataTopic,
        );

        return view('Topic.store', $data);
    }

    /**
     * トピック削除
     *
     * @param Request $request
     *
     */
    public function destroy(Request $request)
    {
        $topicId     = $request->get('id');
        $communityId = $request->get('delete_community_id');

        if (!$this->authSap) {
            abort(404);
        }

        if (empty($communityId)) {
            return redirect()->route('Topic.index');
        }

        if ($this->TopicService->checkStillDeveloper($communityId) == false) {
            return redirect()->route('Topic.index', ['search' => 'on']);
        }

        if (empty($topicId)) {
            return redirect()->route('Topic.index', ['community_id' => $communityId, 'search' => 'on']);
        }

        $dataTopic = $this->TopicService->getTopicOne($topicId);
        if (count($dataTopic) == 0) {
            return redirect()->route('Topic.index', ['community_id' => $communityId, 'search' => 'on']);
        }

        if ($this->TopicService->checkStillDeveloper($dataTopic['community_id']) == false) {
            return redirect()->route('Topic.index', ['community_id' => $communityId, 'search' => 'on']);
        }

        $this->TopicService->delTopic($topicId);
        return redirect()->route('Topic.index', ['community_id' => $communityId, 'search' => 'on']);
    }
}
