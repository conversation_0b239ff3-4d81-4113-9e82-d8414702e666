<?php

namespace App\Http\Controllers;

use App\Services\GuestPlayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GuestPlayController extends Controller
{
    /** @var GuestPlayService */
    protected $guestPlayService;

    public function __construct(GuestPlayService $guestPlayService)
    {
        parent::__construct();

        $this->guestPlayService = $guestPlayService;
        view()->share($this->guestPlayService->getFormData());
    }

    public function index(Request $request)
    {
        $guestUserId = $this->guestPlayService->getGuestUserId($request);

        return view('GuestPlay.index', compact('guestUserId'));
    }

    public function create(Request $request)
    {
        // ゲストプレイcookieが既に存在したらindexへリダイレクト
        if ($this->guestPlayService->getGuestUserId($request)) {
            return redirect()->route('GuestPlay.index');
        }

        // ゲストプレイユーザーを作成する
        $this->guestPlayService->createGuestPlayUser();

        // 一般とr18側にcookieが同じIDで発行されるためにexchangeを通る
        return redirect()->to($this->guestPlayService->getDirectExchangeUrl(route('GuestPlay.index')));
    }
}
