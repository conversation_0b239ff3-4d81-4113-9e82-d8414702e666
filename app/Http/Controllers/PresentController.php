<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PresentRequest;
use App\Services\PresentService;

/**
 * プレゼント発送管理
 */
class PresentController extends Controller
{
    protected $PresentService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PresentService $PresentService)
    {
        parent::__construct();
        $this->PresentService = $PresentService;
        $this->config = config('forms.Present');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'Present.index'];

        view()->share(['formData'  => $this->config]);
    }

    /**
     * 一覧
     *
     * @param  Request $request
     *
     */
    public function index(Request $request)
    {
        $search = $this->PresentService->formatSearchCondition($request->all());
        $list   = $this->PresentService->getPresentList($search);

        $data = array(
            'breadcrumbs'       => $this->breadcrumbs,
            'listApplication'   => ['' => '全て'] + $this->PresentService->getApplicationList(),
            'listSendStatus'    => $this->config['listSendStatus'],
            'listPresent'       => $list['listPresent'],
            'pagePresent'       => $list['pagePresent'],
            'pagerViewFrom'     => $list['pagerViewFrom'],
            'pagerViewTo'       => $list['pagerViewTo'],
        );

        return view('Present.index', $data);
    }

    /**
     * URL一覧
     *
     * @param  integer $id
     *
     */
    public function show($id)
    {
        $screenNameSub = 'URL一覧';

        $dataPresent = $this->PresentService->getPresentOne($id);
        $dataPresent->giftNumber = $this->PresentService->makeGiftNumber($id);

        if (empty($dataPresent->exists)) {
            abort(404);
        }

        $dataPresent->appTitle = $this->PresentService->getUnitApplicationTitle($dataPresent->unit_app_id);

        $data = array(
            'breadcrumbs'            => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'          => $screenNameSub,
            'data'                   => $dataPresent,
            'listPresentElectedUser' => $this->PresentService->getPresentElectedUserList($dataPresent->id),
        );

        return view('Present.show', $data);
    }

    /**
     * 景品登録
     *
     * @param  Request $request
     * @param  integer $id
     *
     */
    public function edit(Request $request, $id)
    {
        $screenNameSub = '景品登録';

        $dataPresent = $this->PresentService->getPresentOne($id);

        if (empty($dataPresent->exists)) {
            abort(404);
        }

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'data'          => $dataPresent,
            'appTitle'      => $this->PresentService->getUnitApplicationTitle($dataPresent->unit_app_id),
        );

        return view('Present.edit', $data);
    }

    /**
     * 景品登録確認
     *
     * @param  PresentRequest $request
     *
     */
    public function editConfirm(PresentRequest $request)
    {
        $screenNameSub = '景品登録確認';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
            'appTitle'      => $this->PresentService->getUnitApplicationTitle($request['unit_app_id']),
        );

        return view('Present.editconfirm', $data);
    }

    /**
     * 景品登録完了
     *
     * @param  PresentRequest $request
     *
     */
    public function update(PresentRequest $request)
    {
        $screenNameSub = '景品登録完了';

        $this->PresentService->editPresent($request['id'], ['present_name' => $request['present_name']]);

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
        );

        return view('Present.store', $data);
    }

    /**
     * 発送依頼
     *
     * @param Request $request
     *
     */
    public function send(Request $request)
    {
        $request->session()->flash('message', 'No.'.$request['id'].'の発送の依頼が完了しました。');
        $this->PresentService->editPresent($request['id'], ['is_send_request' => '1']);
        return redirect()->route('Present.index', ['search' => 'on']);
    }

    /**
     * CSVダウンロード
     *
     * @param  integer $id
     *
     */
    public function csvDownload($id)
    {
        return $this->PresentService->downloadCsv(
            $this->PresentService->getCsvFileName($id),
            $this->PresentService->getCsvList($id),
            $this->PresentService->getCsvHeader(),
            true
        );
    }
}
