<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Subscription\SubscriptionPaymentLogCsvDownloadRequest;
use App\Services\SubscriptionPaymentLogService;

class SubscriptionPaymentLogController extends Controller
{
    /**
     * @var SubscriptionPaymentLogService
     */
    protected $subscriptionPaymentLogService;

    /**
     * コンストラクタ
     * 
     */
    public function __construct(
        SubscriptionPaymentLogService $subscriptionPaymentLogService
    ){
        parent::__construct();
        $this->middleware('check.developer');
        $this->subscriptionPaymentLogService = $subscriptionPaymentLogService;
        view()->share([
            'formData' => $this->subscriptionPaymentLogService->getFormData(),
        ]);
    }

    /**
     * 課金ログ
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return view('SubscriptionPaymentLog.index');
    }

    /**
     * 課金ログ出力
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function csvDownload(SubscriptionPaymentLogCsvDownloadRequest $request)
    {
        return $this->subscriptionPaymentLogService->csvDownload($request);
    }
}
