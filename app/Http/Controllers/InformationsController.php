<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\InformationsService;
use App\Http\Requests\Informations\SearchInfoRequest;
use App\Http\Requests\Informations\SearchFailureRequest;

/**
 * お知らせ
 */
class InformationsController extends Controller
{

    protected $informationsService;

    public function __construct(InformationsService $informationsService)
    {
        parent::__construct();
        $this->informationsService = $informationsService;
        view()->share($this->informationsService->getFormData());
    }

    public function infoList(SearchInfoRequest $request)
    {
        $serchRoute = 'Informations.infolist';
        $search = $this->informationsService->formatSearchCondition($request->all(), true);

        $paginator = $this->informationsService->getInfoList($search);
        $pagerView = $this->informationsService->getPagerView($paginator, config('forms.Informations.pagerLinkNum'));
        $keyword = (isset($search['keyword'])) ? $search['keyword'] : null ;

        $serchParam = array(
            'keyword'   => $keyword,
            'maxLength' => config('forms.Informations.keywordLimit.length'),
        );

        return view($serchRoute, compact('paginator', 'pagerView', 'serchParam', 'serchRoute'));
    }

    public function failureList(SearchFailureRequest $request)
    {
        $serchRoute = 'Informations.failurelist';
        $search = $this->informationsService->formatSearchCondition($request->all(), false);

        $paginator = $this->informationsService->getFailureList($search);
        $pagerView = $this->informationsService->getPagerView($paginator, config('forms.Informations.pagerLinkNum'));
        $keyword = (isset($search['keyword'])) ? $search['keyword'] : null ;

        $serchParam = array(
            'keyword'   => $keyword,
            'maxLength' => config('forms.Informations.keywordLimit.length'),
        );

        return view($serchRoute, compact('paginator', 'pagerView', 'serchParam', 'serchRoute'));
    }

    public function show($id)
    {
        $data = $this->informationsService->getData($id);
        if (empty($data->exists)) {
            abort(404);
        }
        $type = $this->informationsService->getType($data->category);
        return view('Informations.show', compact('data', 'type'));
    }
}
