<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideNotificationCategoryRequest;
use App\Services\GuideNotificationCategoryService;
use Log;

class GuideNotificationCategoryController extends Controller
{
    protected $guideNoticCatService;

    public function __construct(GuideNotificationCategoryService $guideNoticCatService)
    {
        parent::__construct();

        $this->guideNoticCatService = $guideNoticCatService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideNoticCatService->getFormData(),
        ]);
        // js を追加して再セット
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/jscolor/jscolor.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideNoticCategoryList =  $this->guideNoticCatService->getListByGuideAppId($guideAppId);

        $data = [
            'guideNoticCategoryList' => $guideNoticCategoryList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationCategory.index', $data);
    }

    /**
     * GuideNotificationCategory create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $content = $request->all();

        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        // jscolor でセットされるデフォルト値に合わせる
        if (empty($content['background_color']) || strlen($content['background_color']) > 6 ||
            !preg_match('/^[a-fA-F0-9]+$/', $content['background_color'])) {
            $content['background_color'] = 'FFFFFF';
        }
        if (empty($content['color']) || strlen($content['color']) > 6 ||
            !preg_match('/^[a-fA-F0-9]+$/', $content['color'])) {
            $content['color'] = '000000';
        }

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationCategory.create', $data);
    }

    /**
     * GuideNotificationCategory create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideNotificationCategoryRequest $request)
    {

        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationCategory.createconfirm', $data);
    }

    /**
     * GuideNotificationCategory create store page
     * @param request $request
     * @return view
     */
    public function store(GuideNotificationCategoryRequest $request)
    {

        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideNoticCatService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideNotificationCategory.store', $data);
    }

    /**
     * GuideNotificationCategory edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $noticcatid
     * @return view
     */
    public function edit(Request $request, $guideAppId, $noticcatid)
    {

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideNoticCatService->getOneById($noticcatid, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_notification_category : id=' . $noticcatid);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        // jscolor でセットされるデフォルト値に合わせる
        if (empty($content['background_color']) || strlen($content['background_color']) > 6 ||
            !preg_match('/^[a-fA-F0-9]+$/', $content['background_color'])) {
            $content['background_color'] = 'FFFFFF';
        }
        if (empty($content['color']) || strlen($content['color']) > 6 ||
            !preg_match('/^[a-fA-F0-9]+$/', $content['color'])) {
            $content['color'] = '000000';
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationCategory.edit', $data);
    }

    /**
     * GuideNotificationCategory edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideNotificationCategoryRequest $request)
    {

        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotificationCategory.editconfirm', $data);
    }

    /**
     * GuideNotificationCategory update page
     * @param request $request
     * @return view
     */
    public function update(GuideNotificationCategoryRequest $request)
    {

        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideNoticCatService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideNotificationCategory.store', $data);
    }

    /**
     * GuideNotificationCategory destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(GuideNotificationCategoryRequest $request)
    {

        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $noticcatid = $request->get('noticcatid');
        $this->guideNoticCatService->deleteContent($noticcatid, $guideAppId);

        return redirect()->route('GuideNotificationCategory.index', ['id' => $guideAppId]);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideNoticCatService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideNoticCatService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
