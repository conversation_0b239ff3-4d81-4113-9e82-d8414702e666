<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PointLogApisRequest;
use App\Services\PointLogApisService;
use Log;

/**
 * 課金LogAPI比較
 */
class PointLogApisController extends Controller
{
    protected $pointLogApisService;

    public function __construct(PointLogApisService $pointLogApisService)
    {
        parent::__construct();
        $this->pointLogApisService = $pointLogApisService;

        // configの固定パラメタ
        view()->share([
            'formData' => $this->pointLogApisService->getFormData(),
        ]);
    }

    /**
     * トップページ
     */
    public function index($type = null)
    {
        // セレクトボックスのタイトル取得
        $appList = $this->pointLogApisService->getAppList($type);
        return view('PointLogApis.index', compact('type', 'appList'));

    }

    /**
     * 課金LogAPI比較csvダウンロード
     */
    public function csvDownload(PointLogApisRequest $request)
    {
        // total_payment_pointと、mismatched_payment_dataを取得
        return $this->pointLogApisService->download($request);
    }
}
