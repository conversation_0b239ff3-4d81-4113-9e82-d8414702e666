<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\ChReportsMonthlyRequest;
use App\Services\ChReportsMonthlyService;

/**
 * CSVダウンロード（チャネリングゲーム）月別
 */
class ChReportsMonthlyController extends Controller
{
    protected $ChReportsMonthlyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(ChReportsMonthlyService $ChReportsMonthlyService)
    {
        parent::__construct();
        $this->ChReportsMonthlyService = $ChReportsMonthlyService;
        $this->config = config('forms.ChReportsMonthly');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'ChReportsMonthly.index'];

        view()->share(['formData'  => $this->config]);
    }

    /**
     * 一覧
     *
     */
    public function index()
    {
        $reportType = config('forms.ChReportsMonthly.reportType');
        if (!auth_is_route('ChReportsMonthly.csvnotaxdownload')) {
            unset($reportType['NoTax_Report']);
        }
        if (!auth_is_route('ChReportsMonthly.csvaccountingdownload')) {
            // 経理用レポートの権限がない場合
            unset($reportType['ReportAccounting']);
            unset($reportType['ReportMonthlyAccounting']);
        }

        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'periodType'   => config('forms.ChReportsMonthly.periodType'),
            'appTitleType' => $this->ChReportsMonthlyService->getChApplicationList(),
            'reportType'   => $reportType,
            'deviceType'   => config('forms.ChReportsMonthly.deviceType'),
        );

        return view('ChReportsMonthly.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param ChReportsMonthlyRequest $request
     *
     */
    public function csvDownload(ChReportsMonthlyRequest $request)
    {
        $condition = $request->all();

        if (!auth_is_route('ChReportsMonthly.csvnotaxdownload') && $condition['report'] == 'NoTax_Report') {
            abort(404);
        }
        $fileName = $this->ChReportsMonthlyService->getCsvFileName($condition);
        $header = $this->ChReportsMonthlyService->getCsvHeader($condition);
        $list = $this->ChReportsMonthlyService->getCsvList($condition);
        $csvData[] = $header;
        $csvData = array_merge($csvData, $list);

        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename=' . $fileName);
        foreach ($csvData as $lineList) {
            echo mb_convert_encoding(implode(',', $lineList), 'sjis-win', 'UTF-8')."\n";
        }

    }
}
