<?php
namespace App\Http\Controllers;

use App\Http\Requests\PfOwnerReportsMonthlyRequest;
use App\Services\PfOwnerReportsMonthlyService;

class PfOwnerReportsMonthlyController extends Controller
{
    protected $pfOwnerReportsMonthlyService;

    public function __construct(PfOwnerReportsMonthlyService $pfOwnerReportsMonthlyService)
    {
        parent::__construct();

        $this->pfOwnerReportsMonthlyService = $pfOwnerReportsMonthlyService;
        view()->share($this->pfOwnerReportsMonthlyService->getFormData());
    }

    /**
     * TOP
     * @return view
     */
    public function index()
    {
        return view('PfOwnerReportsMonthly.index');
    }

    /**
     * CSVダウンロード
     * @param  PfOwnerReportsMonthlyRequest $request
     * @return view
     */
    public function csvDownload(PfOwnerReportsMonthlyRequest $request)
    {
        $param    = $request->all();
        $list     = $this->pfOwnerReportsMonthlyService->getCsvList($param);
        $header   = $this->pfOwnerReportsMonthlyService->getCsvHeader($param);
        $filename = $this->pfOwnerReportsMonthlyService->getCsvFileName($param);

        return $this->pfOwnerReportsMonthlyService->downloadCsv($filename, $list, $header, true);
    }
}
