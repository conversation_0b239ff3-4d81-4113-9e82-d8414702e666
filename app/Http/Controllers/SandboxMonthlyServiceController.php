<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\SandboxMonthlyService\CreateRequest;
use App\Http\Requests\SandboxMonthlyService\EditRequest;
use App\Http\Requests\SandboxMonthlyService\DestroyRequest;
use App\Services\SandboxMonthlyServiceService;

/**
 * Sandbox Users
 */
class SandboxMonthlyServiceController extends Controller
{
    protected $sbxUsersService;

    public function __construct(SandboxMonthlyServiceService $sandboxMonthlyServiceService)
    {
        parent::__construct();
        $this->sandboxMonthlyServiceService = $sandboxMonthlyServiceService;

        // configの固定パラメタ
        view()->share(['formData'  => $this->sandboxMonthlyServiceService->getFormData()]);
    }

    /**
     * 一覧
     */
    public function index(Request $request)
    {
        $condition = $this->sandboxMonthlyServiceService->formatSearchCondition($request->all());
        $paginator = $this->sandboxMonthlyServiceService->getSbxMonthlyServiceList($condition);
        if (! $paginator->isEmpty()) {
            $pagerView = $this->sandboxMonthlyServiceService->getPagerView($paginator, config('forms.SandboxMonthlyService.pagerLinkNum'));
        }
        return view('SandboxMonthlyService.index', compact('paginator', 'pagerView'));
    }

    /**
     * 登録
     */
    public function create(Request $request)
    {
        $appTitleList = $this->sandboxMonthlyServiceService->getAppTitleOfSelectBox();
        return view('SandboxMonthlyService.create', compact('request', 'appTitleList'));
    }

    /**
     * 登録確認
     */
    public function createConfirm(CreateRequest $request)
    {
        // 指定したアプリに権限があるか確認。さらにapplicationテーブルのデータを取得
        $application = $this->sandboxMonthlyServiceService->getApplication($request->get('app_id'));
        if (empty($application)) {
            abort(400);
        }
        $this->sandboxMonthlyServiceService->getAppTitleOfSelectBox();
        return view('SandboxMonthlyService.createconfirm', compact('request', 'application'));
    }

    /**
     * 登録完了
     */
    public function store(CreateRequest $request)
    {
        if (! $this->sandboxMonthlyServiceService->storeMonthlyService($request)) {
            abort(400);
        }
        return view('SandboxMonthlyService.store');
    }

    /**
     * 編集
     */
    public function edit(Request $request, $id)
    {
        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $data = $request->old();
        } elseif ($request->method() == 'POST') {
            // 確認画面から遷移時は入力していたデータを取得
            $data = $request->all();
        } else {
            // 初期表示時はDBからデータを取得
            $data = $this->sandboxMonthlyServiceService->getMonthlyService($id);
        }

        // 該当月額サービスない場合は終了
        if (empty($data)) {
            abort(404);
        }

        // アプリに権限があるか確認。さらにapplicationテーブルのデータを取得
        $application = $this->sandboxMonthlyServiceService->getApplication($data['app_id']);
        if (empty($application)) {
            abort(400);
        }
        return view('SandboxMonthlyService.edit', compact('data', 'application'));
    }

    /**
     * 編集確認
     */
    public function editConfirm(EditRequest $request)
    {
        // 指定したidでmonthly_serviceテーブルを確認
        $monthlyService = $this->sandboxMonthlyServiceService->getMonthlyService($request->get('id'));
        if (empty($monthlyService)) {
            abort(404);
        }

        // アプリに権限があるか確認。さらにapplicationテーブルのデータを取得
        $application = $this->sandboxMonthlyServiceService->getApplication($monthlyService->app_id);
        if (empty($application)) {
            abort(400);
        }
        return view('SandboxMonthlyService.editconfirm', compact('request', 'application'));
    }

    /**
     * 編集完了
     */
    public function update(EditRequest $request)
    {
        // 指定したidでmonthly_serviceテーブルを確認
        $monthlyService = $this->sandboxMonthlyServiceService->getMonthlyService($request->get('id'));
        if (empty($monthlyService)) {
            abort(404);
        }

        // アプリに権限があるか確認
        $application = $this->sandboxMonthlyServiceService->getApplication($monthlyService->app_id);
        if (empty($application)) {
            abort(400);
        }

        if (! $this->sandboxMonthlyServiceService->updateMonthlyService($request)) {
            abort(404);
        }
        return view('SandboxMonthlyService.update');
    }

    /**
     * 削除
     */
    public function destroy(DestroyRequest $request)
    {
        $result = $this->sandboxMonthlyServiceService->deleteMonthlyService($request);
        if (empty($result)) {
            abort(400);
        }

        // 削除終了ダイアログの為の情報設定
        $request->session()->flash('message', config('forms.SandboxMonthlyService.afterDeleteMessage'));

        // トップへリダイレクト
        return redirect()->route('SandboxMonthlyService.index');
    }
}
