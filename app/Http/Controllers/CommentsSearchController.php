<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\CommentsSearchRequest;
use App\Services\CommentsSearchService;

class CommentsSearchController extends Controller
{
    protected $commentsSearchService;
    protected $authAdmin = 0;
    protected $authSap = 0;

    /**
     * Construct
     * @param App\Services\CommentsSearchService $commentsSearchService
     * @return Illuminate\Http\Response;
     */
    public function __construct(CommentsSearchService $commentsSearchService)
    {
        parent::__construct();

        $this->commentsSearchService = $commentsSearchService;

        if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
            $this->authAdmin = 1;
        }

        if (auth_is_user_sap()) {
            $this->authSap = 1;
        }

        view()->share([
            'formData' => $this->commentsSearchService->getFormData(),
            'authSap'  => $this->authSap
        ]);
    }

    /**
     * Index Page
     * @param App\Http\Requests\CommentsSearchRequest $request
     * @return Illuminate\Http\Response;
     */
    public function index(CommentsSearchRequest $request)
    {
        $listCommunities    = [];
        $query              = $this->commentsSearchService->formatSearchCondition($request->all());
        $listCommentsSearch = $this->commentsSearchService->getCommentsSearchList($query);
        $sortUrl            = [
            'sortDesc' => Route('CommentsSearch.index', array_set($query, 'order', 'desc')),
            'sortAsc'  => Route('CommentsSearch.index', array_set($query, 'order', 'asc'))
        ];
        $pagerView = $this->commentsSearchService->getPagerView(
            $listCommentsSearch,
            config('forms.common.pagination.pageLinkNum')
        );
        $commentNo = $listCommentsSearch->total() - ($listCommentsSearch->currentPage() - 1) *
            $listCommentsSearch->perPage();

        $isSearch  = request('search', "off");
        if ($request->session()->has('errors')) {
            $isSearch = 'on';
        }
        if ($this->authAdmin) {
            $listCommunities    = ["" => ""] + $this->commentsSearchService
                ->getCommunitiesList()
                ->lists('title', 'id')->toArray();
        } else {
            $devAppIds = $this->commentsSearchService->getDeveloperApplicationList(auth()->user()->id);
            if (count($devAppIds) > 0) {
                $listCommunities = ["" => ""] + $this->commentsSearchService
                    ->getCommunitiesList($devAppIds)
                    ->lists('title', 'id')->toArray();
            }
        }
        return view(
            'CommentsSearch.index',
            compact(
                'listCommunities',
                'listCommentsSearch',
                'pagerView',
                'isSearch',
                'commentNo',
                'sortUrl'
            )
        );
    }

    /**
     * Destroy Comment
     * @param Illuminate\Http\Request $request
     * @return Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $inputData = $request->all();

        if ($this->authSap != 1) {
            abort(404);
        }

        $commentData = $this->commentsSearchService->getCommentByCommentKey($inputData['comment_key']);

        if (!$commentData) {
            abort(404);
        }

        if ($commentData->status == $this->commentsSearchService->getFormData()['status']['inactive']) {
            abort(404);
        }

        if (! $this->commentsSearchService->checkDeletable($commentData->topic_id)) {
            abort(404);
        }

        if (array_has($inputData, 'comment_key') && $this->commentsSearchService->destroyComment($inputData)) {
            return redirect()->route('CommentsSearch.index', ['isSearch' => 'on']);
        }

        abort(404);
    }
}
