<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\DocumentsService;

/**
 * ドキュメント
 */
class DocumentsController extends Controller
{

    protected $documentsService;

    public function __construct(DocumentsService $documentsService)
    {
        parent::__construct();
        $this->documentsService = $documentsService;
        view()->share($this->documentsService->getFormData());
    }

    public function index(Request $request)
    {
        $list = $this->documentsService->getList($request->all());
        return view('Documents.index', compact('list'));
    }
}
