<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideBannerRequest;
use App\Services\GuideBannerService;
use Log;

class GuideBannerController extends Controller
{
    protected $guideBannerService;

    public function __construct(GuideBannerService $guideBannerService)
    {
        parent::__construct();

        $this->guideBannerService = $guideBannerService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideBannerService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideBannerLists = $this->guideBannerService->getList($guideAppId);
        $bannerBasePath = env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/guide/' . $guideAppId . '/banner/';

        $data = [
            'guideBannerLists' => $guideBannerLists,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'bannerBasePath' => $bannerBasePath,
        ];
        return view('GuideBanner.index', $data);
    }

    /**
     * GuideBanner create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $initDay = [
            'begin' => timestamp_to_date(now_stamp()),
            'end'=> timestamp_to_date(now_stamp()),
        ];

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'initDay' => $initDay,
        ];
        return view('GuideBanner.create', $data);
    }

    /**
     * GuideBanner create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideBannerRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideBannerService->encodeUploadFiles($request->file());

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideBanner.createconfirm', $data);
    }

    /**
     * GuideBanner create store page
     * @param request $request
     * @return view
     */
    public function store(GuideBannerRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideBannerService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideBanner.store', $data);
    }

    /**
     * GuideBanner edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $bannerId
     * @return view
     */
    public function edit(Request $request, $guideAppId, $bannerId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideBannerService->getOne($bannerId, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_banner : id=' . $bannerId);
            abort(404);
        }
        $image = $content['image'];

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        $content['start_datetime'] = timestamp_to_date(strtotime($content['start_datetime']));
        $content['end_datetime'] = timestamp_to_date(strtotime($content['end_datetime']));

        $content['image'] = $image;

        $bannerBasePath = env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/guide/' . $guideAppId . '/banner/';
        $imagePath = $bannerBasePath . $content['image'];

        $initDay = [
            'begin' => timestamp_to_date(now_stamp()),
            'end'=> timestamp_to_date(now_stamp()),
        ];
        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'initDay' => $initDay,
            'imagePath' => $imagePath,
        ];
        return view('GuideBanner.edit', $data);
    }

    /**
     * GuideBanner edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideBannerRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideBannerService->encodeUploadFiles($request->file());

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideBanner.editconfirm', $data);
    }

    /**
     * GuideBanner update page
     * @param request $request
     * @return view
     */
    public function update(GuideBannerRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideBannerService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideBanner.store', $data);
    }

    /**
     * GuideBanner destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $bannerId = $request->get('bannerId');
        $this->guideBannerService->deleteContent($bannerId, $guideAppId);

        return redirect()->route('GuideBanner.index', ['id' => $guideAppId]);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideBannerService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideBannerService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
