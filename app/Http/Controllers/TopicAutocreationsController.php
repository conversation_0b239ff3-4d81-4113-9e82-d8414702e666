<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\TopicAutocreationsRequest;
use App\Services\TopicAutocreationsService;

/**
 * 自動生成トピック管理
 */
class TopicAutocreationsController extends Controller
{
    protected $TopicAutocreationsService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(TopicAutocreationsService $TopicAutocreationsService)
    {
        parent::__construct();

        $this->TopicAutocreationsService = $TopicAutocreationsService;
        $this->config = config('forms.TopicAutocreations');
        $this->config['suffixBeforeDelete'] = config('forms.common.suffixBeforeDelete');
        $this->config['suffixAfterDelete']  = config('forms.common.suffixAfterDelete');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'TopicAutocreations.index'];

        view()->share(['formData'  => $this->config]);
    }

    /**
     * 自動生成トピック一覧
     *
     * @param  Request $request
     *
     */
    public function index(TopicAutocreationsRequest $request)
    {
        $condition = $this->TopicAutocreationsService->formatSearchCondition($request->all());

        $listCommunities = array();
        $listTopicAutocreations = array();

        if (auth_is_user_admin()) {
            $listCommunities = $this->TopicAutocreationsService->getCommunitiesList('', true);
            $listTopicAutocreations = $this->TopicAutocreationsService->getTopicAutocreationsList($condition);
        } else {
            $devAppIds = $this->TopicAutocreationsService->getDeveloperApplicationList(auth()->user()->id);
            if (count($devAppIds) > 0) {
                $listCommunities = $this->TopicAutocreationsService->getCommunitiesList($devAppIds, true);
                $condition['app_id'] = $devAppIds;
                $listTopicAutocreations = $this->TopicAutocreationsService->getTopicAutocreationsList($condition);
            }
        }
        $listCommunities = array('' => '') + $listCommunities;

        $data = array(
            'breadcrumbs'            => $this->breadcrumbs,
            'listCommunities'        => $listCommunities,
            'listTopicAutocreations' => array_get($listTopicAutocreations, 'listTopicAutocreations', []),
            'pagerViewFrom'          => array_get($listTopicAutocreations, 'pagerViewFrom', 0),
            'pagerViewTo'            => array_get($listTopicAutocreations, 'pagerViewTo', 0),
        );

        return view('TopicAutocreations.index', $data);
    }

    /**
     * 自動生成トピック登録
     *
     * @param  Request $request
     *
     */
    public function create(Request $request)
    {
        if (auth_is_user_admin()) {
            return redirect()->route('TopicAutocreations.index');
        }

        $listTopic = array();
        $devAppIds = $this->TopicAutocreationsService->getDeveloperApplicationList(auth()->user()->id);
        if (count($devAppIds) > 0) {
            $listCommunities = $this->TopicAutocreationsService->getCommunitiesList($devAppIds, true);
            if (count($listCommunities) > 0) {
                foreach ($listCommunities as $key => $val) {
                    $params = ['community_id' => $key, 'app_id' => $devAppIds];
                    $listTopic[$val] = $this->TopicAutocreationsService->getTopicList($params);
                }
            }
        }
        // セレクトボックス用にキーと値を揃える
        $topicCreateComment = array_combine(
            config('forms.TopicAutocreations.topicCreateComment'),
            config('forms.TopicAutocreations.topicCreateComment')
        );

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $dataTopicAutocreations = $request->old();
        } else {
            $dataTopicAutocreations = $request->all();
            $dataTopicAutocreations["allow_comment_count"] = config('forms.TopicAutocreations.defaultCreateComment');
        }

        $screenNameSub = '登録';

        $data = array(
            'breadcrumbs'            => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'          => $screenNameSub,
            'listTopic'              => $listTopic,
            'dataTopicAutocreations' => $dataTopicAutocreations,
            'topicCreateComment'     => $topicCreateComment,
        );

        return view('TopicAutocreations.create', $data);
    }

    /**
     * 自動生成トピック登録確認
     *
     * @param  TopicAutocreationsRequest $request
     *
     */
    public function createConfirm(TopicAutocreationsRequest $request)
    {
        if (auth_is_user_admin()) {
            return redirect()->route('TopicAutocreations.index');
        }

        $topicTitle = $this->TopicAutocreationsService->getTopicOne($request->get('last_topic_id'))['title'];

        $screenNameSub = '登録確認';

        $data = array(
            'breadcrumbs'            => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub'          => $screenNameSub,
            'topicTitle'             => $topicTitle,
            'dataTopicAutocreations' => $request->all(),
        );

        return view('TopicAutocreations.createconfirm', $data);
    }

    /**
     * 自動生成トピック登録完了
     *
     * @param  TopicAutocreationsRequest $request
     *
     */
    public function store(TopicAutocreationsRequest $request)
    {
        if (auth_is_user_admin()) {
            return redirect()->route('TopicAutocreations.index');
        }

        $this->TopicAutocreationsService->addTopicAutocreations($request);

        $screenNameSub = '登録完了';

        $data = array(
            'breadcrumbs'   => array_merge($this->breadcrumbs, [$screenNameSub]),
            'screenNameSub' => $screenNameSub,
        );

        return view('TopicAutocreations.store', $data);
    }

    /**
     * 自動生成トピック削除
     *
     * @param Request $request
     *
     */
    public function destroy(Request $request)
    {
        if (auth_is_user_admin()) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        $devAppIds = $this->TopicAutocreationsService->getDeveloperApplicationList(auth()->user()->id);
        if (count($devAppIds) == 0) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        $listCommunities = $this->TopicAutocreationsService->getCommunitiesList($devAppIds);
        if (count($listCommunities) == 0) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        $dataTopicAutocreations = $this->TopicAutocreationsService->getTopicAutocreationsOneByConditions([
            'id' => $request->get('id')
        ]);
        if (count($dataTopicAutocreations) == 0) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        if (in_array($dataTopicAutocreations['community_id'], array_keys($listCommunities)) == false) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        $dataTopic = $this->TopicAutocreationsService->getTopicOne($request->get('last_topic_id'));
        if (count($dataTopic) == 0) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        if (in_array($dataTopic['community_id'], array_keys($listCommunities)) == false) {
            return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
        }

        $this->TopicAutocreationsService->delTopicAutocreations($request->get('id'));
        return redirect()->route('TopicAutocreations.index', ['search' => 'on']);
    }
}
