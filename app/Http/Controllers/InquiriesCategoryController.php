<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\InquiriesCategoryService;
use App\Http\Requests\InquiriesCategory\CreateRequest;
use App\Http\Requests\InquiriesCategory\EditRequest;
use App\Http\Requests\InquiriesCategory\CopyRequest;
use App\Http\Requests\InquiriesCategory\DeleteRequest;
use Input;

/**
 * お問合わせカテゴリ管理
 */
class InquiriesCategoryController extends Controller
{

    protected $inquiriesCategoryService;

    public function __construct(InquiriesCategoryService $inquiriesCategoryService)
    {
        parent::__construct();
        $this->inquiriesCategoryService = $inquiriesCategoryService;
        view()->share($this->inquiriesCategoryService->getFormData());

        // SAPおよびSAPとして扱うログインユーザー
        if (auth_is_user_sap()) {
            $this->authSap = 1;
        } else {
            $this->authSap = 0;
        }

        view()->share(['authSap'   => $this->authSap]);
    }

    public function index(Request $request)
    {
        $this->inquiriesCategoryService->formatSearchCondition();

        $condition = $request->all();
        $paginator = $this->inquiriesCategoryService->getList($condition);
        $pagerView = $this->inquiriesCategoryService->getPagerView($paginator, config('forms.InquiriesCategory.pager.index.pagerLinkNum'));

        $displayId = [];
        foreach ($paginator as $k => $v) {
            $displayId[] = $v->id;
        }
        $registCategory    = $this->inquiriesCategoryService->getRegistCategory($displayId);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructure($registCategory);

        return view('InquiriesCategory.index', compact('registCategory' ,'categoryStructure', 'paginator', 'pagerView'));
    }

    public function create(Request $request, $appId)
    {
        if (! $this->authSap || ! $this->inquiriesCategoryService->checkPermitAppId($appId)) {
            abort(404);
        }

        // バリデーションに失敗した場合は、input::old()に入る値を受け取る。
        $condition = (! empty(input::old())) ? input::old() : $request->all();
        $categoryStructure   = $this->inquiriesCategoryService->createCategoryStructure( $condition, 'initialize');
        $condition['app_id'] = $appId;

        return view('InquiriesCategory.create', compact('categoryStructure', 'condition'));
    }

    public function createConfirm(CreateRequest $request)
    {
        $condition = $request->all();

        if (! $this->authSap || ! $this->inquiriesCategoryService->checkPermitAppId($condition['app_id'])) {
            abort(404);
        }

        $categoryStructure = $this->inquiriesCategoryService->createCategoryStructure( $condition );
        return view('InquiriesCategory.createConfirm', compact('categoryStructure', 'condition'));
    }

    public function store(CreateRequest $request)
    {
        $condition = $request->all();

        if (! $this->authSap || ! $this->inquiriesCategoryService->checkPermitAppId($condition['app_id'])) {
            abort(404);
        }

        $categoryStructure = $this->inquiriesCategoryService->createCategoryStructure( $condition );

        $applicationCreateStatus = $this->inquiriesCategoryService->storeCreate($categoryStructure, $condition['app_id']);
        if (! $applicationCreateStatus) {
            abort(404);
        }

        $taskName = config('forms.InquiriesCategory.taskName.create');
        return view('InquiriesCategory.store', compact('taskName'));
    }

    public function edit(Request $request, $appId, $largeId)
    {
        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($appId)
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($largeId, 'large')
        ) {
            abort(404);
        }

        // バリデーションに失敗した場合は、input::old()に入る値を受け取る。
        $condition = (! empty(input::old())) ? input::old() : $request->all();

        $condition['app_id']   = $appId;
        $condition['large_id'] = $largeId;

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory'         => $registCategory,
            'appId'                  => $condition['app_id'],
            'largeId'                => $condition['large_id'],
            'categoryStructureParam' => ['smallKeyAscend' => true],
        ]);
        $this->inquiriesCategoryService->setEditData($categoryStructure, $condition);

        return view('InquiriesCategory.edit', compact('categoryStructure', 'condition', 'editInput'));
    }

    public function editConfirm(EditRequest $request)
    {
        $condition = $request->all();

        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($condition['app_id'])
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($condition['large_id'], 'large')
        ) {
            abort(404);
        }

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory'         => $registCategory,
            'appId'                  => $condition['app_id'],
            'largeId'                => $condition['large_id'],
            'categoryStructureParam' => ['smallKeyAscend' => true],
        ]);
        $this->inquiriesCategoryService->setEditData($categoryStructure, $condition);

        return view('InquiriesCategory.editConfirm', compact('categoryStructure', 'condition', 'editInput'));
    }

    public function update(Request $request)
    {
        $condition = $request->all();

        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($condition['app_id'])
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($condition['large_id'], 'large')
        ) {
            abort(404);
        }

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory'         => $registCategory,
            'appId'                  => $condition['app_id'],
            'largeId'                => $condition['large_id'],
            'categoryStructureParam' => ['smallKeyAscend' => true],
        ]);
        $this->inquiriesCategoryService->setEditData($categoryStructure, $condition);
        if (! $this->inquiriesCategoryService->storeEdit($categoryStructure, $condition['app_id'])) {
            abort(404);
        }

        $taskName = config('forms.InquiriesCategory.taskName.edit');
        return view('InquiriesCategory.store', compact('taskName'));
    }

    public function copyCreate(Request $request, $appId, $largeId)
    {
        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($appId)
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($largeId, 'large')
        ) {
            abort(404);
        }

        $condition = $request->all();
        $condition['source_app_id'] = $appId;
        $condition['large_id'] = $largeId;

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['source_app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory' => $registCategory,
            'appId'          => $condition['source_app_id'],
            'largeId'        => $condition['large_id']
        ]);

        return view('InquiriesCategory.copy', compact('categoryStructure', 'condition'));
    }

    public function copyCreateConfirm(CopyRequest $request)
    {
        $condition = $request->all();

        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($condition['source_app_id'])
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($condition['large_id'], 'large')
        ) {
            abort(404);
        }

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['source_app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory' => $registCategory,
            'appId'          => $condition['source_app_id'],
            'largeId'        => $condition['large_id']
        ]);

        return view('InquiriesCategory.copyConfirm', compact('categoryStructure', 'condition'));
    }

    public function copyStore(CopyRequest $request)
    {
        $condition = $request->all();

        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($condition['source_app_id'])
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($condition['large_id'], 'large')
        ) {
            abort(404);
        }

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['source_app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory' => $registCategory,
            'appId'          => $condition['source_app_id'],
            'largeId'        => $condition['large_id']
        ]);
        $this->inquiriesCategoryService->categoryCopyStore($categoryStructure, $condition['target_app_id']);

        $taskName = config('forms.InquiriesCategory.taskName.copy');
        return view('InquiriesCategory.store', compact('taskName'));
    }

    public function delete(Request $request, $appId, $largeId)
    {
        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($appId)
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($largeId, 'large')
        ) {
            abort(404);
        }

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$appId]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory' => $registCategory,
            'appId'          => $appId,
            'largeId'        => $largeId
        ]);

        $condition = $request->all();
        $condition['app_id']   = $appId;
        $condition['large_id'] = $largeId;

        $delFlagInput = $this->inquiriesCategoryService->getDeleteInput($categoryStructure);

        return view('InquiriesCategory.delete', compact('categoryStructure', 'condition', 'delFlagInput'));
    }

    public function deleteConfirm(DeleteRequest $request)
    {

        $condition = $request->all();
        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($condition['app_id'])
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($condition['large_id'], 'large')
        ) {
                abort(404);
        }

        $registCategory    = $this->inquiriesCategoryService->getRegistCategory([$condition['app_id']]);
        $categoryStructure = $this->inquiriesCategoryService->getCategoryStructureLarge([
            'registCategory' => $registCategory,
            'appId'          => $condition['app_id'],
            'largeId'        => $condition['large_id']
        ]);

        $delFlagInput = $this->inquiriesCategoryService->getDeleteInput($categoryStructure);

        return view('InquiriesCategory.deleteConfirm', compact('categoryStructure', 'condition', 'delFlagInput'));
    }

    public function destroy(DeleteRequest $request)
    {
        $condition = $request->all();
        if (! $this->authSap
        ||  ! $this->inquiriesCategoryService->checkPermitAppId($condition['app_id'])
        ||  ! $this->inquiriesCategoryService->existCategoryRefID($condition['large_id'], 'large')
        ) {
                abort(404);
        }

        $this->inquiriesCategoryService->setCategoryDelete($condition);

        $taskName = config('forms.InquiriesCategory.taskName.delete');
        return view('InquiriesCategory.store', compact('taskName'));
    }


}
