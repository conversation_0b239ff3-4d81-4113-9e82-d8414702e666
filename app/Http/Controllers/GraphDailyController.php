<?php
namespace App\Http\Controllers;

use App\Http\Requests\GraphDailyRequest;
use App\Services\GraphDailyService;

/**
 * レポート：日別表示・グラフ
 */
class GraphDailyController extends Controller
{

    protected $graphDailyService;

    public function __construct(GraphDailyService $graphDailyService)
    {
        parent::__construct();
        $this->graphDailyService = $graphDailyService;
        view()->share($this->graphDailyService->getFormData());
    }

    public function index()
    {
        return view('GraphDaily.index');
    }

    public function search(GraphDailyRequest $request)
    {
        $list = $this->graphDailyService->getList($request->all());
        return view('GraphDaily.index', compact('list'));
    }
}
