<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PointReportsDailyRequest;
use App\Services\PointReportsDailyService;

/**
 * 課金履歴ダウンロード(日別)
 */
class PointReportsDailyController extends Controller
{
    protected $PointReportsDailyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PointReportsDailyService $PointReportsDailyService)
    {
        parent::__construct();
        $this->PointReportsDailyService = $PointReportsDailyService;
        $this->config = config('forms.PointReportsDaily');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'PointReportsDaily.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * index
     *
     */
    public function index()
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointReportsDailyService->getApplicationList(),
        );

        return view('PointReportsDaily.index', $data);
    }

    /**
     * search
     *
     * @param PointReportsDailyRequest $request
     */
    public function search(PointReportsDailyRequest $request)
    {
        $condition = $request->all();
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointReportsDailyService->getApplicationList(),
            'header' => $this->PointReportsDailyService->getCsvHeader($condition),
            'list' => $this->PointReportsDailyService->getCsvList($condition)
        );

        return view('PointReportsDaily.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param PointReportsDailyRequest $request
     *
     */
    public function csvDownload(PointReportsDailyRequest $request)
    {
        $condition = $request->all();
        return $this->PointReportsDailyService->downloadCsv(
            $this->PointReportsDailyService->getCsvFileName($condition),
            $this->PointReportsDailyService->getCsvList($condition),
            $this->PointReportsDailyService->getCsvHeader($condition),
            true
        );
    }
}
