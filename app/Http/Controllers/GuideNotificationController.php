<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideNotificationRequest;
use App\Http\Requests\GuideNotificationSearchRequest;
use App\Services\GuideNotificationService;
use Log;

class GuideNotificationController extends Controller
{
    protected $guideNoticService;

    public function __construct(GuideNotificationService $guideNoticService)
    {
        parent::__construct();

        $this->guideNoticService = $guideNoticService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideNoticService->getFormData(),
        ]);
        // js を追加して再セット
        $javascriptFileList   = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/tinymce/jquery.tinymce.min.js';
        $javascriptFileList[] = '/js/jquery.fileupload.js';
        $javascriptFileList[] = '/js/jquery.fileupload-process.js';
        $javascriptFileList[] = '/js/jquery.fileupload-validate.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(GuideNotificationSearchRequest $request, $guideAppId)
    {
        $isUpdated = $request->get('isUpdated');
        $search = $this->guideNoticService->formatSearchCondition($request->all());

        // ---------
        // process
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideNoticList =  $this->guideNoticService->getList($guideAppId, $search);

        // 一時間以上過ぎている一時的なファイルがあれば削除する
        $this->guideNoticService->delFileTmpImage();

        // ページャ表示用のview側で使うパラメタを出す
        $pagerLinkNum = config('forms.GuideNotification.pagerLinkNum');
        $pagerView = $this->guideNoticService->getPagerView($guideNoticList, $pagerLinkNum);

        $data = [
            'guideNoticList' => $guideNoticList,
            'pagerView' => $pagerView,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'search' => $search,
            'isUpdated' => $isUpdated,
        ];
        return view('GuideNotification.index', $data);
    }

    /**
     * GuideNotification create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideNoticService->getListNoticCategoryName($guideAppId, false);

        $tagList = $this->guideNoticService->getTagList($guideAppId);

        $content = $request->all();
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        if (empty($content['published_datetime'])) {
            $content['published_datetime'] = timestamp_to_date(now_stamp() + 86400);
        }

        $data = [
            'content' => $content,
            'categoryNameList' => $categoryNameList,
            'tagList' => $tagList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotification.create', $data);
    }

    /**
     * GuideNotification create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideNotificationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');

        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request->get('preview_hash');
        if (empty($previewHash) === false) {
            $this->guideNoticService->delPreview($guideAppId, $previewHash);
        }

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideNoticService->getListNoticCategoryName($guideAppId);
        $tagList = $this->guideNoticService->getTagList($guideAppId);

        $data = [
            'request' => $request,
            'categoryNameList' => $categoryNameList,
            'tagList' => $tagList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotification.createconfirm', $data);
    }

    /**
     * GuideNotification create store page
     * @param request $request
     * @return view
     */
    public function store(GuideNotificationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideNoticService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideNotification.store', $data);
    }

    /**
     * GuideNotification edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $noticid
     * @return view
     */
    public function edit(Request $request, $guideAppId, $noticid)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideNoticService->getListNoticCategoryName($guideAppId, false);
        $tagList = $this->guideNoticService->getTagList($guideAppId);

        $content = $this->guideNoticService->getOneById($noticid, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_notification : id=' . $noticid);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        if (empty($content['id'])) {
            $content['id'] = $noticid;
        }
        if (empty($content['images'])) {
            $guideImage = $this->guideNoticService->getListNoticImage($noticid);
            $content['images'] = $guideImage;
        }
        if (empty($content['top_image']['name'])) {
            $content['top_image'] = $this->guideNoticService->getTopImage($noticid);
            $content['top_image']['name'] = $content['top_image']['file_name'];
        }

        $data = [
            'content' => $content,
            'categoryNameList' => $categoryNameList,
            'tagList' => $tagList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideNotification.edit', $data);
    }

    /**
     * GuideNotification edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideNotificationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request->get('preview_hash');
        if (empty($previewHash) === false) {
            $this->guideNoticService->delPreview($guideAppId, $previewHash);
        }

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideNoticService->getListNoticCategoryName($guideAppId);
        $tagList = $this->guideNoticService->getTagList($guideAppId);

        $data = [
            'request' => $request,
            'categoryNameList' => $categoryNameList,
            'tagList' => $tagList,
            'guideAppTitle' => $guideAppTitle,
            'guideAppId' => $guideAppId,
        ];
        return view('GuideNotification.editconfirm', $data);
    }

    /**
     * GuideNotification update page
     * @param request $request
     * @return view
     */
    public function update(GuideNotificationRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideNoticService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideNotification.store', $data);
    }

    /**
     * GuideNotification destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $noticid = $request->get('noticid');
        $this->guideNoticService->deleteContent($noticid, $guideAppId);

        $urlParams = [
            'id' => $guideAppId,
            'search' => 'on',
        ];
        return redirect()->route('GuideNotification.index', $urlParams);
    }

    /**
     * GuideNotification update view_status
     * @param request $request
     * @return view
     */
    public function viewstatusUpdate(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $noticid = $request->get('noticid');
        $this->guideNoticService->changeViewStatus($noticid, $guideAppId);

        $urlParams = [
            'id' => $guideAppId,
            'search' => 'on',
            'isUpdated' => 1,
        ];
        return redirect()->route('GuideNotification.index', $urlParams);
    }

    /**
     * Edit Preview
     * @param request $request
     * @return view
     */
    public function makePreview(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['success' => false, 'errors' => ['運用サイト情報が取得できません。']]);
        }

        $data = $this->guideNoticService->editPreview($request->all());
        return response()->json($data);
    }

    /**
     * update Image
     * @param request $request
     * @return view
     */
    public function imageUpload(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['files' => ['file' => ['success' => false]]]);
        }

        $data = $this->guideNoticService->uploadFileGuideNotic($request->all());
        return response()->json($data);
    }

    /**
     * delete Image
     * @param request $request
     * @return view
     */
    public function imageDelete(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['success' => false]);
        }

        $data = $this->guideNoticService->delFileGuideNotic($request->all());
        return response()->json($data);
    }

    /**
     * sort page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function priority(Request $request, $guideAppId)
    {
        $isUpdated = $request->get('isUpdated');

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideNoticList =  $this->guideNoticService->getListPriority($guideAppId);

        $data = [
            'guideNoticList' => $guideNoticList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'isUpdated' => $isUpdated,
        ];
        return view('GuideNotification.priority', $data);
    }

    /**
     * sort update page
     * @param request $request
     * @return view
     */
    public function priorityUpdate(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $result = $this->guideNoticService->priorityEdit($request->all());
        if ($result === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not Update of Priority : request=' . var_export($request->all(), true));
        }

        return redirect()->route('GuideNotification.priority', ['id' => $guideAppId, 'isUpdated' => 1]);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideNoticService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideNoticService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
