<?php
namespace App\Http\Controllers;

use App\Services\IndexService;
use App\Services\MaintenanceInformationsService;
use Illuminate\Http\Request;

/**
 * TOP
 */
class IndexController extends Controller
{
    protected $indexService;
    protected $maintenanceInformationsService;

    public function __construct(
        IndexService $indexService,
        MaintenanceInformationsService $maintenanceInformationsService
    ) {
        parent::__construct();

        $this->middleware('auth.gate', [
            'except' => [
                'index',
                'setLanguage'
            ]
        ]);

        $this->indexService = $indexService;
        $this->maintenanceInformationsService = $maintenanceInformationsService;

        view()->share($this->indexService->getFormData());
    }

    public function index()
    {
        $viewData = [
            'maintenanceList' => $this->maintenanceInformationsService->getList([], false),
            'informationList' => $this->indexService->getInformationList(),
            'failureList'     => $this->indexService->getFailureList(),
        ];

        return view('Index.index', $viewData);
    }

    /**
     * Set language action
     *
     * @param Illuminate\Http\Request $request
     * @param string $langCode
     */
    public function setLanguage(Request $request, $langCode)
    {
        if ($request->ajax()) {
            $this->indexService->setLanguage($langCode);
        }
    }
}
