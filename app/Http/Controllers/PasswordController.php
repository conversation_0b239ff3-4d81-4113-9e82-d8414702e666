<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PasswordRequest;
use App\Services\PasswordService;
use Log;

/**
 * Password
 */
class PasswordController extends Controller
{
    protected $passwordService;

    public function __construct(PasswordService $passwordService)
    {
        parent::__construct();
        $this->middleware('auth', [
            'except' => [
                'xhrPasswordUpdate'
            ]
        ]);
        $this->passwordService = $passwordService;

        // config‚ÌŒÅ’èƒpƒ‰ƒƒ^
        view()->share([
            'formData' => $this->passwordService->getFormData(),
        ]);
    }

    public function edit(Request $request)
    {
        // ---------
        // process
        $content = array();
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        } else {
            $content['id'] = auth_user_id();
            $content['login_id'] = auth_user_login_id();
        }
        // ---------
        // view
        return view('Password.edit', compact('content', $content));
    }

    public function update(PasswordRequest $request)
    {
        $this->passwordService->updatePassword($request->all());
        return view('Password.update');
    }
    /**
     *  Ajax update password
     * @param Request $request
     * @return Response
     */
    public function xhrPasswordUpdate(PasswordRequest $req)
    {
        if ($req->ajax()) {
            if ($this->passwordService->xhrUpdatePassword($req->all())) {
                return response(200);
            }
        }
        abort(404);
    }
}
