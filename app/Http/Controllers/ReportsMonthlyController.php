<?php
namespace App\Http\Controllers;

use App\Http\Requests\ReportsMonthlyRequest;
use App\Services\ReportsMonthlyService;

/**
 * レポート：月別レポート
 */
class ReportsMonthlyController extends Controller
{

    protected $reportsMonthlyService;

    public function __construct(ReportsMonthlyService $reportsMonthlyService)
    {
        parent::__construct();
        $this->reportsMonthlyService = $reportsMonthlyService;
        $formData = $this->reportsMonthlyService->getFormData();
        $formData['appTitleType'] = [config('forms.ReportsDaily.reportGameAll')] + $formData['appTitleType'];
        view()->share($formData);
    }

    public function index()
    {
        return view('ReportsMonthly.index');
    }

    public function csvDownload(ReportsMonthlyRequest $request)
    {
        $condition = $request->all();

        if (!auth_is_route('ReportsMonthly.csvnotaxdownload') && $condition['report'] == 'NoTax_Report') {
            abort(404);
        }

        $list = $this->reportsMonthlyService->getCsvList($condition);
        $header = $this->reportsMonthlyService->getCsvHeader($condition);
        $filename = $this->reportsMonthlyService->getCsvFileName($condition);
        return $this->reportsMonthlyService->downloadCsv($filename, $list, $header, true);
    }
}
