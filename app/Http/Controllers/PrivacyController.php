<?php
namespace App\Http\Controllers;

class PrivacyController extends Controller
{

    protected $config;

    public function __construct()
    {
        parent::__construct();
        $this->config = config('forms.Privacy');
        view()->share(['formData'  => $this->config]);
    }

    /**
     * index
     * @return view
     */
    public function index()
    {
        return view('Privacy.index');
    }
}
