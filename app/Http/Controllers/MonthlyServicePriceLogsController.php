<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\MonthlyServicePriceLogsRequest;
use App\Http\Requests\MonthlyServicePriceLogs;
use App\Services\MonthlyServicePriceLogsService;
use Log;

/**
 * 課金LogAPI比較
 */
class MonthlyServicePriceLogsController extends Controller
{
    protected $priceLogService;

    public function __construct(MonthlyServicePriceLogsService $priceLogService)
    {
        parent::__construct();
        $this->priceLogService = $priceLogService;

        // configの固定パラメタ
        view()->share([
            'formData' => $this->priceLogService->getFormData(),
        ]);
    }

    /**
     * トップページ
     */
    public function index(request $request)
    {
        // セレクトボックスのタイトル名取得
        $appTitleList = $this->priceLogService->getMonthlyAppTitleList();

        // セレクトボックスのサービス名取得
        $serviceList = $this->priceLogService->getMonthlyServiceList();

        return view('MonthlyServicePriceLogs.index', compact('appTitleList', 'serviceList'));

    }

    /**
     * 課金LogAPI比較csvダウンロード
     */
    public function download(MonthlyServicePriceLogsRequest $request)
    {

        $result = $this->priceLogService->download($request);
        if (count($result) > 0) {
            return back()->withErrors($result)->withInput($request->all());
        }

        return ;
    }
}
