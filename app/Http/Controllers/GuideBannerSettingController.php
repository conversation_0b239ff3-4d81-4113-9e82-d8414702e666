<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\GuideBannerSettingService;
use Log;

class GuideBannerSettingController extends Controller
{
    protected $guideBannerSettingService;

    public function __construct(GuideBannerSettingService $guideBannerSettingService)
    {
        parent::__construct();

        $this->guideBannerSettingService = $guideBannerSettingService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideBannerSettingService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $isType = empty($request->get('isType')) ? 'is-rotating' : $request->get('isType');

        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];
        $frontTopUrl = adjustment_path($appInfo['domain']);

        $rotatingBanner = $this->guideBannerSettingService->getListRotating($guideAppId);
        $fixedBanner = $this->guideBannerSettingService->getListFixed($guideAppId);

        $data = [
            'rotatingBanner' => $rotatingBanner,
            'fixedBanner' => $fixedBanner,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'frontTopUrl' => $frontTopUrl,
            'isType' => $isType,
        ];
        return view('GuideBannerSetting.index', $data);
    }

    /**
     * RotatingEdit page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function rotatingEdit(Request $request, $guideAppId)
    {
        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];

        $content['rotating'] = $this->guideBannerSettingService->getListRotating($guideAppId);
        $bannerNameList = $this->guideBannerSettingService->getListBannerName($guideAppId);

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        $data = [
            'content' => $content,
            'bannerNameList' => $bannerNameList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => 'ローテーションバナー設定',
        ];
        return view('GuideBannerSetting.rotatingEdit', $data);
    }

    /**
     * RotatingEdit confirm page
     * @param request $request
     * @return view
     */
    public function rotatingEditConfirm(Request $request)
    {
        $guideAppId = $request->get('guideAppId');

        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request->get('preview_hash');
        if (empty($previewHash) === false) {
            $this->guideBannerSettingService->delRotatingPreview($guideAppId, $previewHash);
        }
        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];

        $bannerNameList = $this->guideBannerSettingService->getListBannerName($guideAppId);

        $data = [
            'request' => $request,
            'bannerNameList' => $bannerNameList,
             'guideAppTitle' => $guideAppTitle,
            'subScreenName' => 'ローテーションバナー設定',
        ];
        return view('GuideBannerSetting.rotatingEditConfirm', $data);
    }

    /**
     * RotatingEdit update page
     * @param request $request
     * @return view
     */
    public function rotatingUpdate(Request $request)
    {
        $this->guideBannerSettingService->editRotating($request->all());

        $guideAppId = $request->get('guideAppId');
        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => 'ローテーションバナー設定',
            'feature' => '編集',
            'isType' => 'is-rotating',
        ];
        return view('GuideBannerSetting.update', $data);
    }

    /**
     * RotatingEdit Preview
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function rotatingMakePreview(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppInfoAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['success' => false, 'errors' => ['運用サイト情報が取得できません。']]);
        }

        $data = $this->guideBannerSettingService->editRotatingPreview($request->all());

        return response()->json($data);
    }

    /**
     * RotatingEdit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $placement
     * @return view
     */
    public function fixedEdit(Request $request, $guideAppId)
    {
        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];

        $content['fixed'] = $this->guideBannerSettingService->getListFixed($guideAppId);
        $bannerNameList = $this->guideBannerSettingService->getListBannerName($guideAppId);

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        $data = [
            'content' => $content,
            'bannerNameList' => $bannerNameList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => '固定バナー設定',
        ];
        return view('GuideBannerSetting.fixedEdit', $data);
    }

    /**
     * RotatingEdit confirm page
     * @param request $request
     * @return view
     */
    public function fixedEditConfirm(Request $request)
    {
        $guideAppId = $request->get('guideAppId');

        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request->get('preview_hash');
        if (empty($previewHash) === false) {
            $this->guideBannerSettingService->delFixedPreview($guideAppId, $previewHash);
        }
        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];

        $bannerNameList = $this->guideBannerSettingService->getListBannerName($guideAppId);

        $data = [
            'request' => $request,
            'bannerNameList' => $bannerNameList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => '固定バナー設定',
        ];
        return view('GuideBannerSetting.fixedEditConfirm', $data);
    }

    /**
     * RotatingEdit update page
     * @param request $request
     * @return view
     */
    public function fixedUpdate(Request $request)
    {
        $this->guideBannerSettingService->editFixed($request->all());

        $guideAppId = $request->get('guideAppId');
        $appInfo = $this->getAppInfoAndCheckId($guideAppId);
        if ($appInfo === false) {
            return redirect()->route('GuideApplication.index');
        }
        $guideAppTitle = $appInfo['name'];

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'subScreenName' => '固定バナー設定',
            'feature' => '編集',
            'isType' => 'is-fixed',
        ];
        return view('GuideBannerSetting.update', $data);
    }

    /**
     * FixedEdit Preview
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function fixedMakePreview(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppInfoAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['success' => false, 'errors' => ['運用サイト情報が取得できません。']]);
        }

        $data = $this->guideBannerSettingService->editFixedPreview($request->all());

        return response()->json($data);
    }


    private function getAppInfoAndCheckId($guideAppId)
    {
        if ($this->guideBannerSettingService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideBannerSettingService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication;
    }
}
