<?php
namespace App\Http\Controllers;

use App\Http\Requests\ItemManagementPriceRequest;
use App\Http\Requests\ItemManagementProductRequest;
use App\Http\Requests\ItemManagementCsvImportRequest;
use App\Http\Requests\ItemManagementSearchRequest;
use App\Services\ItemManagementService;
use Illuminate\Http\Request;

/**
 * レシート課金：アイテム管理
 */
class ItemManagementController extends Controller
{
    /** @var ItemManagementService */
    protected $itemManagementService;

    /**
     * ItemManagementController constructor.
     * @param ItemManagementService $itemManagementService
     */
    public function __construct(ItemManagementService $itemManagementService)
    {
        parent::__construct();
        $this->middleware('check.developer');
        $this->itemManagementService = $itemManagementService;
        view()->share($this->itemManagementService->getFormData());
    }

    /**
     * アイテム管理
     *
     * @param Request $request
     * @return view
     */
    public function index(ItemManagementSearchRequest $request)
    {
        $applicationId = '';
        $device = '';
        $itemList = [];
        $paginator = [];
        $pagerView = [];
        // アイテム一覧取得
        if (!empty($request->get('app_id')) && !empty($request->get('device'))) {
            $applicationId = $request->get('app_id');
            $device = $request->get('device');

            $itemList = $this->itemManagementService->getItemList($request->getSearchCondition());
            $paginator = $this->itemManagementService->createPaginator($itemList['productList'], $itemList['totalCount'], $request);
            $pagerView = $this->itemManagementService->getPagerView($paginator, config('forms.common.pagination.pageLinkNum'));
            $paginator->appends($request->input());
        }
        return view('ItemManagement.index', compact('applicationId', 'paginator', 'pagerView', 'itemList', 'device'));
    }

    /**
     * アイテムcsvインポート
     *
     * @param ItemManagementCsvImportRequest $request
     * @return view
     */
    public function csvImport(ItemManagementCsvImportRequest $request)
    {
        $applicationId = $request->get('app_id');
        $device = $request->get('device');
        $csvFileName = $request->file('csv_file')->getClientOriginalName();
        $csvData = new \SplFileObject($request->file('csv_file'));
        $csvData->setFlags(
            \SplFileObject::READ_CSV | // CSV 列として行を読み込みます。
            \SplFileObject::READ_AHEAD | // 先読み/巻き戻しで読み出します。
            \SplFileObject::SKIP_EMPTY // ファイルの空行を読み飛ばします。(READ_AHEAD フラグ有効時)
        );
        $itemCsvImportResult = $this->itemManagementService->itemCsvImport($applicationId, $csvData,  $device);
        return view('ItemManagement.csvImport', compact('applicationId', 'csvFileName', 'itemCsvImportResult', 'device'));
    }

    /**
     * アイテムcsv サンプルダウンロード
     *
     * @return view
     */
    public function csvDownloadSample()
    {
        return $this->itemManagementService->downloadSampleCsv();
    }

    /**
     * アイテム新規作成
     *
     * @param Request $request
     * @return view
     */
    public function productCreate(Request $request)
    {
        return view('ItemManagement.productCreate', compact('request'));
    }

    /**
     * アイテム新規作成確認
     *
     * @param ItemManagementProductRequest $request
     * @return view
     */
    public function productCreateConfirm(ItemManagementProductRequest $request)
    {
        return view('ItemManagement.productCreateConfirm', compact('request'));
    }

    /**
     * アイテム新規作成完了
     *
     * @param Request $request
     * @return view
     */
    public function productStore(Request $request)
    {
        $applicationId = $request->get('app_id');
        $device = $request->get('device');
        // 製品情報登録
        $storeProductResult = $this->itemManagementService->storeProduct($request->all(), $device);
        // 登録失敗時
        if (!$storeProductResult['resultStatus']) {
            return view('ReceiptApi.receiptApiError');
        }
        // 登録結果から製品IDを取り出す
        $productId = $storeProductResult['response']['id'];
        // 製品新規登録後、製品価格新規登録に遷移する
        return redirect()->route('ItemManagement.price.create', ['app_id' => $applicationId, 'product_id' => $productId, 'device' => $device]);
    }

    /**
     * アイテム詳細
     *
     * @param Request $request
     * @return view
     */
    public function detail(Request $request)
    {
        $applicationId = $request->get('app_id');
        $productId = $request->get('product_id');
        $device = $request->get('device');
        $itemDetail = [];
        // ログインユーザーに参照権限が存在するタイトルリストを取得
        $applicationTitleList = $this->itemManagementService->getApplicationTitleList();
        if (array_key_exists($applicationId, $applicationTitleList)) {
            // アイテム詳細情報取得
            $itemDetail = $this->itemManagementService->getItemDetail($productId);
        }
        return view('ItemManagement.detail', compact('applicationId', 'itemDetail', 'device'));
    }

    /**
     * アイテム管理情報編集
     *
     * @param Request $request
     * @return view
     */
    public function productEdit(Request $request)
    {
        // 入力値にエラーがあった場合
        if ($request->session()->has('errors')) {
            $editProductData['productData'] = $request->old();
            $applicationId = $editProductData['productData']['app_id'];
            return view('ItemManagement.productEdit', compact('applicationId', 'editProductData'));
        }

        $applicationId = $request->get('app_id');
        $device = $request->get('device');

        $editProductData['productData'] = $request->all();

        // 編集確認画面から「修正ボタン」を押下された場合
        // requestに保持している編集中の情報を画面に表示する
        if (array_key_exists('title', $editProductData['productData'])) {
            return view('ItemManagement.productEdit', compact('applicationId', 'editProductData', 'device'));
        }

        // 編集画面に最初に遷移してきた場合
        // Apiを利用してアイテム管理情報を取得する
        $receiptProductId = $request->get('product_id');
        $editProductData = $this->itemManagementService->editProduct($receiptProductId);

        // アイテム管理情報取得処理のエラーメッセージチェック
        if (!empty($editProductData['informationLabel'])) {
            return view('ReceiptApi.receiptApiError');
        }
        return view('ItemManagement.productEdit', compact('applicationId', 'editProductData', 'device'));
    }

    /**
     * アイテム管理情報編集確認
     *
     * @param ItemManagementProductRequest $request
     * @return view
     */
    public function productEditConfirm(ItemManagementProductRequest $request)
    {
        return view('ItemManagement.productEditConfirm', compact('request'));
    }

    /**
     * アイテム管理情報更新完了
     *
     * @param Request $request
     * @return view
     */
    public function productUpdate(Request $request)
    {
        $updateResult = $this->itemManagementService->updateProduct($request->all());
        // 更新処理結果チェック
        if (!$updateResult) {
            return view('ReceiptApi.receiptApiError');
        }
        return view('ItemManagement.productUpdate', compact('request'));
    }

    /**
     * 販売情報登録
     *
     * @param Request $request
     * @return view
     */
    public function priceCreate(Request $request)
    {
        return view('ItemManagement.priceCreate', compact('request'));
    }

    /**
     * 販売情報登録確認
     *
     * @param ItemManagementPriceRequest $request
     * @return view
     */
    public function priceCreateConfirm(ItemManagementPriceRequest $request)
    {
        $displayAmount = number_format($request->get('amount'));
        return view('ItemManagement.priceCreateConfirm', compact('request', 'displayAmount'));
    }

    /**
     * 販売情報登録完了
     *
     * @param Request $request
     * @return view
     */
    public function priceStore(Request $request)
    {
        $storeResult = $this->itemManagementService->storePrice($request->all());
        // 登録処理結果チェック
        if (!$storeResult) {
            return view('ReceiptApi.receiptApiError');
        }
        return view('ItemManagement.priceStore', compact('request'));
    }

    /**
     * 販売情報編集
     *
     * @param Request $request
     * @return view
     */
    public function priceEdit(Request $request)
    {
        // 入力値にエラーがあった場合
        if ($request->session()->has('errors')) {
            $editPriceData['priceData'] = $request->old();
            $applicationId = $editPriceData['priceData']['app_id'];
            $receiptProductId = $editPriceData['priceData']['product_id'];
            $device = $editPriceData['priceData']['device'];
            return view('ItemManagement.priceEdit', compact('applicationId', 'receiptProductId', 'editPriceData', 'device'));
        }

        $applicationId = $request->get('app_id');
        $receiptProductId = $request->get('product_id');
        $device = $request->get('device');

        $editPriceData['priceData'] = $request->all();

        // 編集確認画面から「修正ボタン」を押下された場合
        // requestに保持している編集中の情報を画面に表示する
        if (array_key_exists('title', $editPriceData['priceData'])) {
            return view('ItemManagement.priceEdit', compact('applicationId', 'receiptProductId', 'editPriceData', 'device'));
        }

        // 編集画面に最初に遷移してきた場合
        // Apiを利用して販売情報を取得する
        $editPriceData = $this->itemManagementService->editPrice($receiptProductId);

        // 取得処理のエラーメッセージチェック
        if (!empty($editPriceData['informationLabel'])) {
            return view('ReceiptApi.receiptApiError');
        }
        return view('ItemManagement.priceEdit', compact('applicationId', 'receiptProductId', 'editPriceData', 'device'));
    }

    /**
     * 販売情報編集確認
     *
     * @param ItemManagementPriceRequest $request
     * @return view
     */
    public function priceEditConfirm(ItemManagementPriceRequest $request)
    {
        $displayAmount = number_format($request->get('amount'));
        return view('ItemManagement.priceEditConfirm', compact('request', 'displayAmount'));
    }

    /**
     * 販売情報更新完了
     *
     * @param Request $request
     * @return view
     */
    public function priceUpdate(Request $request)
    {
        $updateResult = $this->itemManagementService->updatePrice($request->all());
        // 更新処理結果チェック
        if (!$updateResult) {
            return view('ReceiptApi.receiptApiError');
        }
        return view('ItemManagement.priceUpdate', compact('request'));
    }
}
