<?php

namespace App\Http\Controllers;

use Illuminate\Routing\Router;
use Illuminate\Http\Request;
use App\Services\ChUrlWhiteListService;
use App\Http\Requests\ChUrlWhiteListRequest;

class ChUrlWhiteListController extends Controller
{
    protected $chUrlWhiteListService;
    protected $config;

    public function __construct(ChUrlWhiteListService $chUrlWhiteListService)
    {
        parent::__construct();

        $this->chUrlWhiteListService = $chUrlWhiteListService;
        $this->config = config('forms.ChUrlWhiteList');

        view()->share($this->chUrlWhiteListService->getFormData());
    }

//*********************************************************************************************************************
    /**
     * 一覧
     * @param  Request $request
     * @return view
     */
    public function index(Request $request)
    {
        if (empty($request['ch_app_id'])) {
            abort(404);
        }

        $request->session()->put('stored_id', null);
        $devId = null;

        if (!auth_is_pf()) {
            $devId =  auth_user_id();
        }

        $chAppData = $this->chUrlWhiteListService->getOneByIdAndDevId($devId, $request);

        if (!$chAppData) {
            abort(404);
        }

        $chUrlWhiteList = $this->chUrlWhiteListService->getList($request);
        if ($devId) {
            $request->session()->put('stored_id', $chAppData);
        }

        return view('ChUrlWhiteList.index', compact('chUrlWhiteList', 'chAppData'));
    }

//*********************************************************************************************************************
    /**
     * 一覧
     * @param  Request $request
     * @return view
     */
    public function create(Request $request)
    {
        $chAppData = $request->session()->get('stored_id');
        if ($chAppData == null) {
            abort(404);
        }
        $dataType = $this->config['type'];

        return view('ChUrlWhiteList.create', compact('chAppData', 'dataType'));
    }

    /**
     * 一覧
     * @param  ChUrlWhiteListRequest $request
     * @return view
     */
    public function createconfirm(ChUrlWhiteListRequest $request)
    {
        $chAppData = $request->session()->get('stored_id');
        $data = array_intersect_key($request->all(), array_flip(['type', 'url']));

        return view('ChUrlWhiteList.createconfirm', compact('chAppData', 'data'));
    }

    /**
     * 一覧
     * @param  ChUrlWhiteListRequest $request
     * @return view
     */
    public function store(ChUrlWhiteListRequest $request)
    {
        $chAppData = $request->session()->get('stored_id');
        if (!$chAppData) {
            abort(404);
        }
        $isStored = $this->chUrlWhiteListService->addChUrlWhiteList($chAppData, $request);
        if (!$isStored) {
            abort(404);
        }

        return view('ChUrlWhiteList.store', compact('chAppData'));
    }

//*********************************************************************************************************************
    /**
     * 変更
     * @param  Request $request
     * @return view
     */
    public function edit(Request $request)
    {
        $chAppData = $request->session()->get('stored_id');

        if ($chAppData == null || !$request->get('white_list_id') && !$request->session()->has('errors')) {
            abort(404);
        }
        $dataType = $this->config['type'];

        return view('ChUrlWhiteList.edit', compact('chAppData', 'chUrlWhiteList', 'dataType'));
    }

    /**
     * 変更確認
     * @param  ChUrlWhiteListRequest $request
     * @return view
     */
    public function editConfirm(ChUrlWhiteListRequest $request)
    {
        $chAppData = $request->session()->get('stored_id');
        $data = $request->all();

        return view('ChUrlWhiteList.editconfirm', compact('chAppData', 'data'));
    }

    /*
     * 変更完了
     * @param  ChUrlWhiteListRequest $request
     * @return view
     */
    public function update(ChUrlWhiteListRequest $request)
    {
        $chAppData = $request->session()->get('stored_id');
        $this->chUrlWhiteListService->update($chAppData, $request);

        return view('ChUrlWhiteList.update', compact('chAppData'));
    }

//*********************************************************************************************************************
    /**
     * 変更
     * @param  Request $request
     * @return view
     */
    public function destroy(Request $request)
    {
        $chAppData = $request->session()->get('stored_id');

        if (!$chAppData) {
            abort(404);
        }

        $this->chUrlWhiteListService->destroy($chAppData, $request);

        return redirect()->route('ChUrlWhiteList.index', ['ch_app_id' => $chAppData->id]);
    }
}
