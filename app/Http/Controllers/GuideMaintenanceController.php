<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideMaintenanceRequest;
use App\Services\GuideMaintenanceService;
use Log;

class GuideMaintenanceController extends Controller
{
    protected $guideMainteService;

    public function __construct(GuideMaintenanceService $guideMainteService)
    {
        parent::__construct();

        $this->guideMainteService = $guideMainteService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideMainteService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideMaintenanceList =  $this->guideMainteService->getListMaintenance($guideAppId);

        $data = [
            'guideMaintenanceList' => $guideMaintenanceList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideMaintenance.index', $data);
    }


    /**
     * GuideMaintenance create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $pageNameList = $this->guideMainteService->getListGuidePageName();

        $initDay = [
            'begin' => timestamp_to_date(now_stamp()),
            'end'=> timestamp_to_date(now_stamp()),
        ];

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'pageNameList' => $pageNameList,
            'initDay' => $initDay,
        ];
        return view('GuideMaintenance.create', $data);
    }

    /**
     * GuideMaintenance create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideMaintenanceRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $pageNameList = $this->guideMainteService->getListGuidePageName();

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
            'pageNameList' => $pageNameList,
        ];
        return view('GuideMaintenance.createconfirm', $data);
    }

    /**
     * GuideMaintenance create store page
     * @param request $request
     * @return view
     */
    public function store(GuideMaintenanceRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideMainteService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideMaintenance.store', $data);
    }

    /**
     * GuideMaintenance edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $mainteId
     * @return view
     */
    public function edit(Request $request, $guideAppId, $mainteId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideMainteService->getOneMaintenance($mainteId, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_maintenance_page : id=' . $mainteId);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        $content['start_datetime'] = timestamp_to_date(strtotime($content['start_datetime']));
        $content['end_datetime'] = timestamp_to_date(strtotime($content['end_datetime']));

        $pageNameList = $this->guideMainteService->getListGuidePageName();

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'pageNameList' => $pageNameList,
        ];
        return view('GuideMaintenance.edit', $data);
    }

    /**
     * GuideMaintenance edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideMaintenanceRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $pageNameList = $this->guideMainteService->getListGuidePageName();

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
            'pageNameList' => $pageNameList,
        ];
        return view('GuideMaintenance.editconfirm', $data);
    }

    /**
     * GuideMaintenance update page
     * @param request $request
     * @return view
     */
    public function update(GuideMaintenanceRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideMainteService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideMaintenance.store', $data);
    }

    /**
     * GuideMaintenance destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $mainteId = $request->get('mainteId');
        $this->guideMainteService->deleteContent($mainteId, $guideAppId);

        return redirect()->route('GuideMaintenance.index', ['id' => $guideAppId]);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideMainteService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideMainteService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
