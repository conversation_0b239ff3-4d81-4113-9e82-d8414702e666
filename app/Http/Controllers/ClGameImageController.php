<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ClGameImageService;
use App\Services\ClApplicationImageActiveService;
use App\Http\Requests\ClGameImageRequest;
use App\Exceptions\FileUploadException;

class ClGameImageController extends Controller
{
    protected $clGameImageService;

    public function __construct(
        ClGameImageService $clGameImageService,
        ClApplicationImageActiveService $clImageActiveService
    ) {
        parent::__construct();

        $this->clGameImageService = $clGameImageService;
        $this->clImageActiveService = $clImageActiveService;
        view()->share($this->clGameImageService->getFormData());
    }

    /**
     * 一覧
     * @param  ClGameImageRequest $request
     * @param  integer $clAppId
     * @param  integer $imageType
     * @return view
     */
    public function index(ClGameImageRequest $request, $clAppId = null, $imageType = null)
    {
        $selectAppData = $this->clGameImageService->getSelectApplicationList();
        $clAppId = $clAppId ?: key(array_slice($selectAppData, 0, 1, true));
        $imageType = $imageType ?: config('forms.ClGameImage.thumbnail');
        $errors = [];

        if ($clAppId && $selectAppData) {
            $appData = $this->clGameImageService->getApplication($clAppId);

            if (empty($appData->exists)) {
                abort(404);
            }
            $viewInfoList = $this->clGameImageService->getViewInfoList($clAppId);

            if (!array_key_exists($imageType, $viewInfoList)) {
                $imageType = config('forms.ClGameImage.thumbnail');
            }

            $postImageSizeAndIdList = $this->clGameImageService->getPostImageSizeAndIdByApp($clAppId);

            // 定常画像(メイン画像)が設定されていない場合エラーメッセージを設定
            if (empty($postImageSizeAndIdList['200px'])) $errors[] = preg_replace('/:attribute/', '200px', trans('validationmessage.MSG317'));
            if (!empty($errors)) $errors[] = trans('validationmessage.MSG318');

            // サムネイル（総合トップ等）のエラーを設定
            $overallErrors = $this->getOverallErrors($viewInfoList[config('forms.ClGameImage.overallRatedThumbnail')][0]);
            if (!empty($overallErrors)) $errors = array_merge($errors, $overallErrors);
        } else {
            $appData = [];
            $viewInfoList = [];
            $postImageSizeAndIdList = [];
        }
        // レコメンド候補画像を取得
        $recommendImageList = $this->clImageActiveService->getRecommendImageByClAppId($clAppId);
        //dd($viewInfoList);
        return view('ClGameImage.index', compact(
            'clAppId',
            'appData',
            'viewInfoList',
            'recommendImageList',
            'postImageSizeAndIdList',
            'selectAppData',
            'imageType'
        ))->withErrors(['errors' => $errors]);
    }

    /**
     * 画像詳細
     * @param  integer $imgId
     * @return view
     */
    public function show($imgId)
    {
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('ClGameImage.show', compact(
            'imgData'
        ));
    }

    /**
     * メイン画像掲載設定
     * @param ClGameImageRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function postStore(ClGameImageRequest $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        try {
            $this->clGameImageService->storePostId($imgData);
        } catch (FileUploadException $e){
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ClGameImage.index', [$imgData->cl_app_id, $imgData->image_type]);
    }

    /**
     * 掲載更新
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function postUpdate(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        try {
            $this->clGameImageService->updatePostId($imgData);
        } catch (FileUploadException $e){
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ClGameImage.index', [$imgData->cl_app_id, $imgData->image_type]);
    }

    /**
     * 掲載取消
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function postDestroy(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        try {
            $this->clGameImageService->destroyPostId($imgData);
        } catch (FileUploadException $e){
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ClGameImage.index', [$imgData->cl_app_id, $imgData->image_type]);
    }

    /**
     * 画像削除確認
     * @param  integer $imgId
     * @return view
     */
    public function deleteConfirm($imgId)
    {
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('ClGameImage.deleteconfirm', compact(
            'imgData'
        ));
    }

    /**
     * 画像削除
     * @param  Request $request
     * @return view
     */
    public function deleteStore(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        $this->clGameImageService->updateSapDelete($imgData);

        return view('ClGameImage.deletestore');
    }

    /**
     * レコメンド候補画像登録
     * @param Request $request
     * @return view
     */
    public function recommendStore(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        // 定常画像(メイン画像)が設定されていない場合、登録せずエラーメッセージを表示させる
        if(!$this->clGameImageService->postImageExist($imgData->cl_app_id)) {
            $messages = trans('validationmessage.MSG316');
            return back()->withErrors($messages)->withInput($request->all());
        }

        try {
            // 掲載画像種別管理テーブルにレコメンド候補画像としてレコードを登録
            $this->clGameImageService->storeRecommendData($imgId);

        } catch (ApplicationImageException $e) {
            // エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ClGameImage.index', [$imgData->cl_app_id]);
    }

    /**
     * レコメンド候補画像削除
     * @param Request $request
     * @return view
     */
    public function recommendDestroy(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        $this->clGameImageService->deactiveRecommendImage($imgData);

        return redirect()->route('ClGameImage.index', [$imgData->cl_app_id]);
    }

    /**
     * サムネイル（総合トップ等）エラー一覧取得
     * @param array $info サムネイル（総合トップ等）情報
     * @return array エラー一覧
     */
    private function getOverallErrors($info)
    {
        foreach ($info['examOkList'] as $imgData) {
            if ($imgData->post_id) {
                return [];
            }
        }

        return [trans('validationmessage.MSG347')];
    }
}
