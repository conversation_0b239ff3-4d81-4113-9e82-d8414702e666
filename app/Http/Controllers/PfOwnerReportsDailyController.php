<?php
namespace App\Http\Controllers;

use App\Http\Requests\PfOwnerReportsDailyRequest;
use App\Services\PfOwnerReportsDailyService;

class PfOwnerReportsDailyController extends Controller
{
    protected $pfOwnerReportsDailyService;

    public function __construct(PfOwnerReportsDailyService $pfOwnerReportsDailyService)
    {
        parent::__construct();

        $this->pfOwnerReportsDailyService = $pfOwnerReportsDailyService;
        view()->share($this->pfOwnerReportsDailyService->getFormData());
    }

    /**
     * TOP
     * @return view
     */
    public function index()
    {
        return view('PfOwnerReportsDaily.index');
    }

    /**
     * CSVダウンロード
     * @param  PfOwnerReportsDailyRequest $request
     * @return view
     */
    public function csvDownload(PfOwnerReportsDailyRequest $request)
    {
        $param    = $request->all();
        $list     = $this->pfOwnerReportsDailyService->getCsvList($param);
        $header   = $this->pfOwnerReportsDailyService->getCsvHeader($param);
        $filename = $this->pfOwnerReportsDailyService->getCsvFileName($param);

        return $this->pfOwnerReportsDailyService->downloadCsv($filename, $list, $header, true);
    }
}
