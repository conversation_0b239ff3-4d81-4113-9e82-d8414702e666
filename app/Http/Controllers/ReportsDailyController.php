<?php
namespace App\Http\Controllers;

use App\Http\Requests\ReportsDailyRequest;
use App\Services\ReportsDailyService;

/**
 * レポート：日別レポート
 */
class ReportsDailyController extends Controller
{

    protected $reportsDailyService;

    public function __construct(ReportsDailyService $reportsDailyService)
    {
        parent::__construct();
        $this->reportsDailyService = $reportsDailyService;
        $formData = $this->reportsDailyService->getFormData();
        $formData['appTitleType'] = [config('forms.ReportsDaily.reportGameAll')] + $formData['appTitleType'];
        view()->share($formData);
    }

    public function index()
    {
        return view('ReportsDaily.index');
    }

    public function csvDownload(ReportsDailyRequest $request)
    {
        $condition = $request->all();
        $list = $this->reportsDailyService->getCsvList($condition);
        $header = $this->reportsDailyService->getCsvHeader($condition);
        $filename = $this->reportsDailyService->getCsvFileName($condition);
        return $this->reportsDailyService->downloadCsv($filename, $list, $header, true);
    }
}
