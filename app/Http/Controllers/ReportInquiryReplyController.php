<?php
namespace App\Http\Controllers;

use App\Http\Requests\ReportInquiryReplyRequest;
use App\Services\ReportInquiryReplyService;
use Illuminate\Http\Request;

class ReportInquiryReplyController extends Controller
{
    protected $reportInquiryReplyService;

    public function __construct(ReportInquiryReplyService $reportInquiryReplyService)
    {
        parent::__construct();
        $this->reportInquiryReplyService = $reportInquiryReplyService;
        view()->share($this->reportInquiryReplyService->getFormData());
    }

    public function index()
    {
        return view('ReportInquiryReply.index');
    }

    public function exportCSV(ReportInquiryReplyRequest $request)
    {
        $csvFormatConfig = $this->reportInquiryReplyService->getCsvFormatConfig($request->all());

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename=' . $csvFormatConfig['filename']);

        echo mb_convert_encoding(implode(',', $csvFormatConfig['header']), 'sjis-win', 'UTF-8')."\n";

        $this->reportInquiryReplyService->getCsvList($request->all(), $csvFormatConfig['filter']);
        return;
    }
}
