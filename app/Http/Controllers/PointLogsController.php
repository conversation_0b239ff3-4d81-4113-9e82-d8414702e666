<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PointLogsRequest;
use App\Services\PointLogsService;

/**
 * 課金ログ比較ツール
 */
class PointLogsController extends Controller
{
    protected $PointLogsService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PointLogsService $PointLogsService)
    {
        parent::__construct();
        $this->PointLogsService = $PointLogsService;
        $this->config = config('forms.PointLogs');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'PointLogs.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * 課金ログ比較ツール
     *
     * @param  Request $request
     *
     */
    public function index(Request $request)
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointLogsService->getApplicationList(),
            'deviceType'   => $this->config['deviceType'],
        );

        return view('PointLogs.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param PointLogsRequest $request
     *
     */
    public function csvDownload(PointLogsRequest $request)
    {
        return $this->PointLogsService->download($request);
    }
}
