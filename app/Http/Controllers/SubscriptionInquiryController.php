<?php

namespace App\Http\Controllers;

use App\Http\Requests\Subscription\SubscriptionInquiryRequest;
use App\Services\SubscriptionInquiryService;
use Illuminate\Http\Request;

/**
 * 定期購入：問い合わせ
 */
class SubscriptionInquiryController extends Controller
{
    /** @var subscriptionInquiryService */
    protected $subscriptionInquiryService;

    /**
     * コンストラクタ
     * 
     * @param SubscriptionInquiryService $subscriptionInquiryService
     */
    public function __construct(SubscriptionInquiryService $subscriptionInquiryService)
    {
        parent::__construct();
        $this->middleware('check.developer');
        $this->subscriptionInquiryService = $subscriptionInquiryService;
        view()->share($this->subscriptionInquiryService->getFormData());
    }

    /**
     * 定期購入問い合わせ
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
        } else {
            $condition = $request->all();
        }

        return view('SubscriptionInquiry.index', compact('condition'));
    }

    /**
     * 定期購入問い合わせ
     *
     * @param SubscriptionInquiryRequest $request
     * @return \Illuminate\Http\Response
     */
    public function search(SubscriptionInquiryRequest $request)
    {
        $condition = $request->all();
        $inquiry = $this->subscriptionInquiryService->getSubscriptionInquiry($condition);
        $details = config('forms.SubscriptionInquiry.details');

        return view('SubscriptionInquiry.index', compact('condition', 'inquiry', 'details'));
    }
}
