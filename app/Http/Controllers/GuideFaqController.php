<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideFaqRequest;
use App\Services\GuideFaqService;
use Log;

class GuideFaqController extends Controller
{
    protected $guideFaqService;

    public function __construct(GuideFaqService $guideFaqService)
    {
        parent::__construct();

        $this->guideFaqService = $guideFaqService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideFaqService->getFormData(),
        ]);
        // js を追加して再セット
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/tinymce/jquery.tinymce.min.js';
        $javascriptFileList[] = '/js/jquery.fileupload.js';
        $javascriptFileList[] = '/js/jquery.fileupload-process.js';
        $javascriptFileList[] = '/js/jquery.fileupload-validate.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $isUpdated = $request->get('isUpdated');
        $search = $this->guideFaqService->formatSearchCondition($request->all());

        // ---------
        // process
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideFaqList =  $this->guideFaqService->getList($guideAppId, $search);

        $categoryNameList = $this->guideFaqService->getListFaqCategoryName($guideAppId);

        // 一時間以上過ぎている一時的なファイルがあれば削除する
        $this->guideFaqService->delFileTmpImage();

        // ページャ表示用のview側で使うパラメタを出す
        $pagerLinkNum = config('forms.GuideFaq.pagerLinkNum');
        $pagerView = $this->guideFaqService->getPagerView($guideFaqList, $pagerLinkNum);

        $data = [
            'guideFaqList' => $guideFaqList,
            'categoryNameList' => $categoryNameList,
            'pagerView' => $pagerView,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'search' => $search,
            'isUpdated' => $isUpdated,
        ];
        return view('GuideFaq.index', $data);
    }

    /**
     * GuideFaq create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideFaqService->getListFaqCategoryName($guideAppId, false);

        $content = $request->all();
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        $data = [
            'content' => $content,
            'categoryNameList' => $categoryNameList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideFaq.create', $data);
    }

    /**
     * GuideFaq create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideFaqRequest $request)
    {
        $guideAppId = $request->get('guideAppId');

        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request->get('preview_hash');
        if (empty($previewHash) === false) {
            $this->guideFaqService->delPreview($guideAppId, $previewHash);
        }

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideFaqService->getListFaqCategoryName($guideAppId);

        $data = [
            'request' => $request,
            'categoryNameList' => $categoryNameList,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideFaq.createconfirm', $data);
    }

    /**
     * GuideFaq create store page
     * @param request $request
     * @return view
     */
    public function store(GuideFaqRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideFaqService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'fromEditPriority' => '',
            'fromEditPriorityCategory' => '',
            'feature' => '登録',
        ];
        return view('GuideFaq.store', $data);
    }

    /**
     * GuideFaq edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $faqid
     * @return view
     */
    public function edit(Request $request, $guideAppId, $faqid)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideFaqService->getListFaqCategoryName($guideAppId, false);

        $content = $this->guideFaqService->getOneById($faqid, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_faq : id=' . $faqid);
            abort(404);
        }

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }
        if (empty($content['id'])) {
            $content['id'] = $faqid;
        }
        if (empty($content['images'])) {
            $guideImage = $this->guideFaqService->getListFaqImage($faqid);
            $content['images'] = $guideImage;
        }
        if (empty($content['fromEditPriority'])) {
            $content['fromEditPriority'] = $request->query('fromEditPriority');
        }
        if (empty($content['fromEditPriorityCategory'])) {
            $content['fromEditPriorityCategory'] = $request->query('fromEditPriorityCategory');
            $content['faqCategoryId'] = $request->query('faqCategoryId');
        }

        $data = [
            'content' => $content,
            'categoryNameList' => $categoryNameList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideFaq.edit', $data);
    }

    /**
     * GuideFaq edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideFaqRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request->get('preview_hash');
        if (empty($previewHash) === false) {
            $this->guideFaqService->delPreview($guideAppId, $previewHash);
        }

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideFaqService->getListFaqCategoryName($guideAppId);

        $data = [
            'request' => $request,
            'categoryNameList' => $categoryNameList,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideFaq.editconfirm', $data);
    }

    /**
     * GuideFaq update page
     * @param request $request
     * @return view
     */
    public function update(GuideFaqRequest $request)
    {

        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideFaqService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'fromEditPriority' => $request->get('fromEditPriority'),
            'fromEditPriorityCategory' => $request->get('fromEditPriorityCategory'),
            'faqCategoryId' => $request->get('faqCategoryId'),
            'feature' => '編集',
        ];
        return view('GuideFaq.store', $data);
    }

    /**
     * GuideFaq destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $faqid = $request->get('faqid');
        $this->guideFaqService->deleteContent($faqid, $guideAppId);

        $urlParams = [
            'id' => $guideAppId,
            'search' => 'on',
        ];
        return redirect()->route('GuideFaq.index', $urlParams);
    }

    /**
     * GuideFaq update view_status
     * @param request $request
     * @return view
     */
    public function viewstatusUpdate(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $faqid = $request->get('faqid');
        $this->guideFaqService->changeViewStatus($faqid, $guideAppId);

        $urlParams = [
            'id' => $guideAppId,
            'search' => 'on',
            'isUpdated' => 1,
        ];
        return redirect()->route('GuideFaq.index', $urlParams);
    }

    /**
     * Edit Preview
     * @param request $request
     * @return view
     */
    public function makePreview(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['success' => false, 'errors' => ['運用サイト情報が取得できません。']]);
        }

        $data = $this->guideFaqService->editPreview($request->all());
        return response()->json($data);
    }

    /**
     * update Image
     * @param request $request
     * @return view
     */
    public function imageUpload(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['files' => ['file' => ['success' => false]]]);
        }

        $data = $this->guideFaqService->uploadFileGuideFaq($request->all());
        return response()->json($data);
    }

    /**
     * delete Image
     * @param request $request
     * @return view
     */
    public function imageDelete(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return response()->json(['success' => false]);
        }

        $data = $this->guideFaqService->delFileGuideFaq($request->all());
        return response()->json($data);
    }

    /**
     * sort page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function priority(Request $request, $guideAppId)
    {
        $isUpdated = $request->get('isUpdated');

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideFaqList = $this->guideFaqService->getListPriority([
                'guide_application_id'        => $guideAppId,
                'guide_faq_category_group_id' => @$request['guide_faq_category_group_id'],
        ]);

        $data = [
            'guideFaqList'  => $guideFaqList,
            'guideAppId'    => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'isUpdated'     => $isUpdated,
            'categoryGroup' => $this->guideFaqService->getGuideFaqCategoryGroupList($guideAppId),
        ];
        return view('GuideFaq.priority', $data);
    }

    /**
     * sort update page
     * @param request $request
     * @return view
     */
    public function priorityUpdate(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $result = $this->guideFaqService->priorityEdit($request->all());
        if ($result === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not Update of Priority : request=' . var_export($request->all(), true));
        }

        return redirect()->route('GuideFaq.priority', ['id' => $guideAppId, 'isUpdated' => 1]);
    }

    /**
     * sort page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function priorityInCategory(Request $request, $guideAppId)
    {
        $search = $request->all();
        $isUpdated = $request->get('isUpdated');

        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }
        $categoryNameList = $this->guideFaqService->getListFaqCategoryName($guideAppId, false);

        if (empty($search['faqCategoryId'])) {
            // 1つ目のキーを初期値としてセット
            $search['faqCategoryId'] = key($categoryNameList);
        }

        $guideFaqList =  $this->guideFaqService
            ->getListPriorityCategory($guideAppId, $search['faqCategoryId']);

        $data = [
            'guideFaqList' => $guideFaqList,
            'categoryNameList' => $categoryNameList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'search' => $search,
            'isUpdated' => $isUpdated,
        ];
        return view('GuideFaq.priorityincategory', $data);
    }

    /**
     * sort update page
     * @param request $request
     * @return view
     */
    public function priorityincategoryUpdate(Request $request)
    {
        $faqCategoryId = $request->get('faqCategoryId');
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $result = $this->guideFaqService->priorityCategoryEdit($request->all());
        if ($result === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not Update of Priority : request=' . var_export($request->all(), true));
        }

        $urlParams = ['id' => $guideAppId, 'faqCategoryId' => $faqCategoryId, 'isUpdated' => 1];
        return redirect()->route('GuideFaq.priorityincategory', $urlParams);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideFaqService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideFaqService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
