<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\ProfileRequest;
use App\Services\ProfileService;

/**
 * プロフィール画像管理
 */
class ProfileController extends Controller
{
    protected $ProfileService;
    protected $config;
    protected $breadcrumbs;
    protected $authAdmin;
    protected $authSap;

    public function __construct(ProfileService $ProfileService)
    {
        parent::__construct();

        $this->ProfileService = $ProfileService;
        $this->config = config('forms.Profile');
        $this->config['suffixBeforeDelete'] = config('forms.common.suffixBeforeDelete');
        $this->config['suffixAfterDelete']  = config('forms.common.suffixAfterDelete');
        $this->config['suffixAfterUpdate']  = config('forms.common.suffixAfterUpdate');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'Profile.index'];

        view()->share(['formData'  => $this->config]);

        $javascriptFileList = array();
        $javascriptFileList[] = '/js/jquery.bxslider-4.1.js';
        $javascriptPath = '/js/controller/'.$this->getControllerName().'.js';
        if (file_exists(public_path($javascriptPath))) {
            $javascriptFileList[] = $javascriptPath;
        }
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

    /**
     * プロフィール画像管理
     *
     * @param Request $request
     *
     */
    public function index(Request $request)
    {
        if (!auth_is_user_sap()) {
            abort(404);
        }

        // 公式コミュニティ作成済みのアプリケーション配列
        $listApplication = array();
        $devAppIds = $this->ProfileService->getDeveloperApplicationList(auth_user_id());
        if (count($devAppIds) > 0) {
            $communitiesAppIds = $this->ProfileService->getCommunitiesList($devAppIds);
            if (count($communitiesAppIds) > 0) {
                $listApplication = $this->ProfileService->getApplicationList($communitiesAppIds);
            }
        }

        $listTemplate  = array();   // テンプレート画像
        $listUserImage = array();   // オリジナル画像
        $dataUser      = array();   // ユーザー情報
        if (!empty($request['app_id'])) {
            if ($this->ProfileService->checkStillDeveloper($request['app_id']) == false) {
                abort(404);
            }

            $dataUser = $this->ProfileService->getCommunitiesManagerUser($request['app_id']);
            if (count($dataUser) > 0) {
                $listUserImage = $this->ProfileService->getUserImageList($dataUser['id']);
                $listTemplate  = $this->ProfileService->getTemplateList($dataUser['gender']);
                if ($dataUser['priority'] == 1) {
                    foreach ($listUserImage as $key => $val) {
                        if ($val['number'] == $dataUser['number']) {
                            $dataUser['path'] = $val['path'];
                            $dataUser['image_id'] = $val['id'];
                            break;
                        }
                    }
                } else {
                    foreach ($listTemplate as $key => $val) {
                        if ($val['id'] == $dataUser['thumbnail']) {
                            $dataUser['path'] = $val['path'];
                            $dataUser['image_id'] = $val['id'];
                            break;
                        }
                    }
                }
            }
        }

        $data = array(
            'breadcrumbs'     => $this->breadcrumbs,
            'request'         => $request,
            'listApplication' => $listApplication,
            'listTemplate'    => $listTemplate,
            'listUserImage'   => $listUserImage,
            'dataUser'        => $dataUser,
        );

        return view('Profile.index', $data);
    }

    /**
     * プロフィール画像設定
     *
     * @param ProfileRequest $request
     * @param integer        $app_id  アプリケーションID
     *
     */
    public function update(ProfileRequest $request, $app_id)
    {
        if ($this->ProfileService->checkStillDeveloper($app_id) == false) {
            abort(404);
        }
        $this->ProfileService->editProfile($request);
        $request->session()->flash('message', $this->config['screenName'].$this->config['suffixAfterUpdate']);
        return redirect()->route('Profile.index', ['app_id' => $app_id]);
    }

    /**
     * オリジナル画像アップロード
     *
     * @param ProfileRequest $request
     * @param integer        $app_id  アプリケーションID
     *
     */
    public function upload(ProfileRequest $request, $app_id)
    {
        if ($this->ProfileService->checkStillDeveloper($app_id) == false) {
            abort(404);
        }
        $params = array(
            'app_id'   => $app_id,
            'user_id'  => $request['user_id'],
            'image_id' => $request['image_id'],
            'number'   => $request['number'],
            'file'     => $request['file'],
        );
        $this->ProfileService->uploadUserImage($params);
        $request->session()->flash('message', '画像を登録しました。');
        return redirect()->route('Profile.index', ['app_id' => $app_id]);
    }

    /**
     * オリジナル画像削除
     *
     * @param Request $request
     *
     */
    public function destroy(Request $request)
    {
        if ($this->ProfileService->checkStillDeveloper($request['app_id']) == false) {
            return redirect()->route('Profile.index', ['app_id' => $request['app_id']]);
        }

        if (empty($request['id'])) {
            return redirect()->route('Profile.index', ['app_id' => $request['app_id']]);
        }

        $dataUserImage = $this->ProfileService->getUserImageOne($request['id']);
        if (!$dataUserImage) {
            return redirect()->route('Profile.index', ['app_id' => $request['app_id']]);
        }

        $listCommunities = $this->ProfileService->getCommunities(['manager_user_id' => $dataUserImage->user_id]);
        if ($listCommunities->count() == 0) {
            return redirect()->route('Profile.index', ['app_id' => $request['app_id']]);
        }

        $appId = '';
        foreach ($listCommunities as $val) {
            $appId = $val->app_id;
            break;
        }
        if ($this->ProfileService->checkStillDeveloper($appId) == false) {
            return redirect()->route('Profile.index', ['app_id' => $request['app_id']]);
        }

        $this->ProfileService->delUserImage($request['id']);
        $request->session()->flash('message', 'No.'.$request['number'].$this->config['suffixAfterDelete']);
        return redirect()->route('Profile.index', ['app_id' => $request['app_id']]);
    }
}
