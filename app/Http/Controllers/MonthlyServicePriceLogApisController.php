<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\MonthlyServicePriceLogApisRequest;
use App\Services\MonthlyServicePriceLogApisService;

/**
 * 月額課金比較API
 */
class MonthlyServicePriceLogApisController extends Controller
{
    protected $MonthlyServicePriceLogApisService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(MonthlyServicePriceLogApisService $MonthlyServicePriceLogApisService)
    {
        parent::__construct();
        $this->MonthlyServicePriceLogApisService = $MonthlyServicePriceLogApisService;
        $this->config = config('forms.MonthlyServicePriceLogApis');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'MonthlyServicePriceLogApis.index'];

        view()->share(['formData'  => $this->config]);
    }

    /**
     * 月額課金比較API
     *
     * @param Request $request
     *
     */
    public function index(Request $request)
    {
        $data = array(
            'breadcrumbs' => $this->breadcrumbs,
            'listService' => $this->MonthlyServicePriceLogApisService->getServiceList($request),
        );

        return view('MonthlyServicePriceLogApis.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param Request $request
     *
     */
    public function csvDownload(MonthlyServicePriceLogApisRequest $request)
    {
        return $this->MonthlyServicePriceLogApisService->downloadCsv(
            $this->MonthlyServicePriceLogApisService->getCsvFileName($request),
            $this->MonthlyServicePriceLogApisService->getCsvList($request),
            $this->MonthlyServicePriceLogApisService->getCsvHeader()
        );
    }
}
