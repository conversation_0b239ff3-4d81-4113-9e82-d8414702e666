<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ChGameImageService;
use App\Services\ChApplicationImageActiveService;
use App\Http\Requests\ChGameImageRequest;
use App\Exceptions\FileUploadException;

class ChGameImageController extends Controller
{
    protected $chGameImageService;

    public function __construct(
        ChGameImageService $chGameImageService,
        ChApplicationImageActiveService $chImageActiveService
    ) {
        parent::__construct();

        $this->chGameImageService = $chGameImageService;
        $this->chImageActiveService = $chImageActiveService;
        view()->share($this->chGameImageService->getFormData());
    }

    /**
     * 一覧
     * @param  ChGameImageRequest $request
     * @param  integer $chAppId
     * @param  integer $imageType
     * @return view
     */
    public function index(ChGameImageRequest $request, $chAppId = null, $imageType = null)
    {
        $selectAppData = $this->chGameImageService->getSelectApplicationList();
        $chAppId = $chAppId ?: key(array_slice($selectAppData, 0, 1, true));
        $imageType = $imageType ?: config('forms.ChGameImage.thumbnail');
        $errors = [];

        if ($chAppId && $selectAppData) {
            $appData = $this->chGameImageService->getApplication($chAppId);

            if (empty($appData->exists)) {
                abort(404);
            }
            $viewInfoList = $this->chGameImageService->getViewInfoList($chAppId);

            if (!array_key_exists($imageType, $viewInfoList)) {
                $imageType = config('forms.ChGameImage.thumbnail');
            }

            $postImageSizeAndIdList = $this->chGameImageService->getPostImageSizeAndIdByApp($chAppId);

            // 定常画像(メイン画像)が設定されていない場合エラーメッセージを設定
            if (empty($postImageSizeAndIdList['200px'])) $errors[] = preg_replace('/:attribute/', '200px', trans('validationmessage.MSG317'));
            if (!empty($errors)) $errors[] = trans('validationmessage.MSG318');

            // サムネイル（総合トップ等）のエラーを設定
            $overallErrors = $this->getOverallErrors($viewInfoList[config('forms.ChGameImage.overallRatedThumbnail')][0]);
            if (!empty($overallErrors)) $errors = array_merge($errors, $overallErrors);
        } else {
            $appData = [];
            $viewInfoList = [];
            $postImageSizeAndIdList = [];
        }
        // レコメンド候補画像を取得
        $recommendImageList = $this->chImageActiveService->getRecommendImageByChAppId($chAppId);

        return view('ChGameImage.index', compact(
            'chAppId',
            'appData',
            'viewInfoList',
            'recommendImageList',
            'postImageSizeAndIdList',
            'selectAppData',
            'imageType'
        ))->withErrors(['errors' => $errors]);
    }

    /**
     * 画像詳細
     * @param  integer $imgId
     * @return view
     */
    public function show($imgId)
    {
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('ChGameImage.show', compact(
            'imgData'
        ));
    }

    /**
     * メイン画像掲載設定
     * @param ChGameImageRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function postStore(ChGameImageRequest $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        try {
            $this->chGameImageService->storePostId($imgData);
        } catch (FileUploadException $e){
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ChGameImage.index', [$imgData->ch_app_id, $imgData->image_type]);
    }

    /**
     * 掲載更新
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function postUpdate(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        try {
            $this->chGameImageService->updatePostId($imgData);
        } catch (FileUploadException $e){
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ChGameImage.index', [$imgData->ch_app_id, $imgData->image_type]);
    }

    /**
     * 掲載取消
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function postDestroy(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        try {
            $this->chGameImageService->destroyPostId($imgData);
        } catch (FileUploadException $e){
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ChGameImage.index', [$imgData->ch_app_id, $imgData->image_type]);
    }

    /**
     * 画像削除確認
     * @param  integer $imgId
     * @return view
     */
    public function deleteConfirm($imgId)
    {
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('ChGameImage.deleteconfirm', compact(
            'imgData'
        ));
    }

    /**
     * 画像削除
     * @param  Request $request
     * @return view
     */
    public function deleteStore(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        $this->chGameImageService->updateSapDelete($imgData);

        return view('ChGameImage.deletestore');
    }

    /**
     * レコメンド候補画像登録
     * @param Request $request
     * @return view
     */
    public function recommendStore(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        // 定常画像(メイン画像)が設定されていない場合、登録せずエラーメッセージを表示させる
        if(!$this->chGameImageService->postImageExist($imgData->ch_app_id)) {
            $messages = trans('validationmessage.MSG316');
            return back()->withErrors($messages)->withInput($request->all());
        }

        try {
            // 掲載画像種別管理テーブルにレコメンド候補画像としてレコードを登録
            $this->chGameImageService->storeRecommendData($imgId);

        } catch (ApplicationImageException $e) {
            // エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('ChGameImage.index', [$imgData->ch_app_id]);
    }

    /**
     * レコメンド候補画像削除
     * @param Request $request
     * @return view
     */
    public function recommendDestroy(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->chGameImageService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }
        $this->chGameImageService->deactiveRecommendImage($imgData);

        return redirect()->route('ChGameImage.index', [$imgData->ch_app_id]);
    }

    /**
     * サムネイル（総合トップ等）エラー一覧取得
     * @param array $info サムネイル（総合トップ等）情報
     * @return array エラー一覧
     */
    private function getOverallErrors($info)
    {
        foreach ($info['examOkList'] as $imgData) {
            if ($imgData->post_id) {
                return [];
            }
        }

        return [trans('validationmessage.MSG347')];
    }
}
