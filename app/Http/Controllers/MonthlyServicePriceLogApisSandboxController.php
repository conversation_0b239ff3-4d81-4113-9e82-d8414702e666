<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\MonthlyServicePriceLogApisSandboxRequest;
use App\Services\MonthlyServicePriceLogApisSandboxService;

/**
 * 月額課金比較API
 */
class MonthlyServicePriceLogApisSandboxController extends Controller
{
    protected $MonthlyServicePriceLogApisSandboxService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(MonthlyServicePriceLogApisSandboxService $MonthlyServicePriceLogApisSandboxService)
    {
        parent::__construct();
        $this->MonthlyServicePriceLogApisSandboxService = $MonthlyServicePriceLogApisSandboxService;
        $this->config = config('forms.MonthlyServicePriceLogApisSandbox');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'MonthlyServicePriceLogApisSandbox.index'];

        view()->share(['formData'  => $this->config]);
    }

    /**
     * 月額課金比較API
     *
     * @param Request $request
     *
     */
    public function index(Request $request)
    {
        $data = array(
            'breadcrumbs' => $this->breadcrumbs,
            'listService' => $this->MonthlyServicePriceLogApisSandboxService->getServiceList($request),
        );

        return view('MonthlyServicePriceLogApisSandbox.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param Request $request
     *
     */
    public function csvDownload(MonthlyServicePriceLogApisSandboxRequest $request)
    {
        return $this->MonthlyServicePriceLogApisSandboxService->downloadCsv(
            $this->MonthlyServicePriceLogApisSandboxService->getCsvFileName($request),
            $this->MonthlyServicePriceLogApisSandboxService->getCsvList($request),
            $this->MonthlyServicePriceLogApisSandboxService->getCsvHeader()
        );
    }
}
