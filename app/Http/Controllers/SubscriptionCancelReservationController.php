<?php

namespace App\Http\Controllers;

use App\Http\Requests\Subscription\SubscriptionCancelReservationRequest;
use App\Services\SubscriptionCancelReservationService;
use Illuminate\Http\Request;

/**
 * 定期購入：解約予約
 */
class SubscriptionCancelReservationController extends Controller
{
    /**
     * @var SubscriptionCancelReservationService
     */
    protected $subscriptionCancelReservationService;

    /**
     * コンストラクタ
     * 
     * @param SubscriptionCancelReservationService $subscriptionCancelReservationService
     */
    public function __construct(SubscriptionCancelReservationService $subscriptionCancelReservationService)
    {
        parent::__construct();
        $this->middleware('check.developer');
        $this->subscriptionCancelReservationService = $subscriptionCancelReservationService;
        view()->share([
            'formData' => $this->subscriptionCancelReservationService->getFormData(),
        ]);
    }

    /**
     * 定期購入：解約(サンドボックス)の検索
     *
     * @param SubscriptionCancelReservationRequest $request
     * @return \Illuminate\Http\Response
     */
    public function index(SubscriptionCancelReservationRequest $request)
    {
        if ($request->isMethod('get') && !$request->session()->has('errors')) {
            // バリデーションエラーで戻された場合
            $condition = $request->old();
        } else {
            $condition = $request->all();
        }

        $userItems = $this->subscriptionCancelReservationService->getUserItems($condition);

        return view('SubscriptionCancelReservation.index', compact('condition', 'userItems'));
    }

    /**
     * 解約予約(ajax)
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function ajaxCancel(Request $request)
    {
        if (!$request->ajax()) {
            abort(404);
        }

        $condition = $request->all();
        $result = $this->subscriptionCancelReservationService->cancelReservation($condition);

        return response()->json($result);
    }

    /**
     * 予約解除(ajax)
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function ajaxReset(Request $request)
    {
        if (!$request->ajax()) {
            abort(404);
        }

        $condition = $request->all();
        $result = $this->subscriptionCancelReservationService->resetReservation($condition);

        return response()->json($result);
    }
}
