<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ClGameImageApplyService;
use App\Http\Requests\ClGameImageApplyRequest;

class ClGameImageApplyController extends Controller
{
    protected $clGameImageApplyService;

    public function __construct(
        ClGameImageApplyService $clGameImageApplyService
    ) {
        parent::__construct();

        $this->clGameImageApplyService = $clGameImageApplyService;
        $this->clGameImageApplyService->setControllerName($this->getControllerName());
        view()->share($this->clGameImageApplyService->getFormData());
    }

    /**
     * 一覧
     * @param  integer $clAppId
     * @return view
     */
    public function index($clAppId)
    {
        $appData = $this->clGameImageApplyService->getApplication($clAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->clGameImageApplyService->getExamRegisterList($clAppId);
        $viewInfoList = $this->clGameImageApplyService->getViewInfoList($clAppId);

        return view('ClGameImageApply.index', compact(
            'appData',
            'registerList',
            'viewInfoList'
        ));
    }

    /**
     * 画像追加
     * @param  integer $clAppId
     * @return view
     */
    public function register($clAppId)
    {
        $appData = $this->clGameImageApplyService->getApplication($clAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $selectInfoList = $this->clGameImageApplyService->getSelectInfoList($clAppId);

        return view('ClGameImageApply.register', compact(
            'appData',
            'selectInfoList'
        ));
    }

    /**
     * 画像追加完了
     * @param  ClGameImageApplyRequest $request
     * @return view
     */
    public function registerStore(ClGameImageApplyRequest $request)
    {
        $clAppId = $request->input('cl_app_id');
        $appData = $this->clGameImageApplyService->getApplication($clAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $this->clGameImageApplyService->registerStore($request->all());

        return view('ClGameImageApply.registerstore');
    }

    /**
     * 画像申請確認
     * @param  Request $request
     * @return view
     */
    public function confirm(Request $request)
    {
        $clAppId = $request->input('cl_app_id');
        $appData = $this->clGameImageApplyService->getApplication($clAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->clGameImageApplyService->getExamRegisterList($clAppId);

        return view('ClGameImageApply.createconfirm', compact(
            'appData',
            'registerList'
        ));
    }

    /**
     * 画像申請完了
     * @param  Request $request
     * @return view
     */
    public function store(Request $request)
    {
        $clAppId = $request->input('cl_app_id');
        $appData = $this->clGameImageApplyService->getApplication($clAppId);

        if (empty($appData->exists)) {
            abort(404);
        }
        $registerList = $this->clGameImageApplyService->getExamRegisterList($clAppId);
        $title = $this->clGameImageApplyService->getTitleResultPlain($this->screenName, '申請');

        $this->clGameImageApplyService->store($registerList);

        return view('ClGameImageApply.createstore', compact(
            'appData',
            'title'
        ));
    }

    /**
     * 削除
     * @param  Request $request
     * @return view
     */
    public function destroy(Request $request)
    {
        $clAppId = $request->input('cl_app_id');
        $imgId = $request->input('id');
        $imgData = $this->clGameImageApplyService->getDetail($imgId);
        $isCheck = $this->clGameImageApplyService->isEnableRegisterDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }
        $this->clGameImageApplyService->destroy($imgData);

        return redirect()->route('ClGameImageApply.index', $clAppId);
    }

    /**
     * 申請一覧
     * @param  ClGameImageApplyRequest $request
     * @return view
     */
    public function reviewIndex(ClGameImageApplyRequest $request)
    {
        $pagerLinkNum  = config('forms.ClGameImage.pagerLinkNum');
        $imgDataList = $this->clGameImageApplyService->getSearchList($request->all());
        $pagerView = $this->clGameImageApplyService->getPagerView($imgDataList, $pagerLinkNum);
        $selectAppData = $this->clGameImageApplyService->getSelectApplicationList();
        return view('ClGameImageApply.reviewindex', compact(
            'imgDataList',
            'pagerView',
            'selectAppData'
        ));
    }

    /**
     * 画像詳細
     * @param  integer $imgId
     * @return view
     */
    public function show($imgId)
    {
        $imgData = $this->clGameImageApplyService->getDetail($imgId);

        if (empty($imgData->exists)) {
            abort(404);
        }

        return view('ClGameImageApply.show', compact(
            'imgData'
        ));
    }

    /**
     * 申請取り下げ確認
     * @param  Request $request
     * @return view
     */
    public function withdrawConfirm(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageApplyService->getDetail($imgId);
        $isCheck = $this->clGameImageApplyService->isEnableReviewDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }

        return view('ClGameImageApply.withdrawconfirm', compact(
            'imgData'
        ));
    }

    /**
     * 画像申請完了
     * @param  Request $request
     * @return view
     */
    public function withdrawUpdate(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->clGameImageApplyService->getDetail($imgId);
        $isCheck = $this->clGameImageApplyService->isEnableReviewDelete($imgData);

        if (empty($imgData->exists) || !$isCheck) {
            abort(404);
        }
        $title = $this->clGameImageApplyService->getTitleResultPlain($this->screenName, '取り下げ');

        $this->clGameImageApplyService->destroy($imgData);

        return view('ClGameImageApply.withdrawstore', compact(
            'title'
        ));
    }
}
