<?php

namespace App\Http\Controllers;

use App\Http\Requests\GuideContentsClusterRequest;
use App\Http\Requests\GuideContentsMasterRequest;
use App\Services\GuideApplicationService;
use App\Services\GuideContentsService;
use Illuminate\Http\Request;
use Log;

class GuideContentsController extends Controller
{
    protected $guideApplicationService;
    protected $guideContentsService;

    public function __construct(
        GuideApplicationService $guideApplicationService,
        GuideContentsService $guideContentsService
    ) {
        parent::__construct();

        $this->guideApplicationService = $guideApplicationService;
        $this->guideContentsService = $guideContentsService;

        view()->share([
            'formData' => $this->guideContentsService->getFormData(),
        ]);

        // js を追加して再セット
        $javascriptFileList   = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/jquery.fileupload.js';
        $javascriptFileList[] = '/js/jquery.fileupload-process.js';
        $javascriptFileList[] = '/js/jquery.fileupload-validate.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

    /**
     * コンテンツマスター一覧
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        // 権限確認
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $params = $request->all();
        $params['guide_application_id'] = $guideAppId;
        if (!isset($params['perPage'])) {
            $params['perPage'] = config('forms.GuideContents.perPage');
        }

        $contentsMasterList = $this->guideContentsService->searchMasterList($params);

        $pagerLinkNum = config('forms.GuideContents.pagerLinkNum');
        $pagerView = $this->guideContentsService->getPagerView($contentsMasterList, $pagerLinkNum);

        $data = [
            'search' => $params,
            'contentsMasterList' => $contentsMasterList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'pagerView' => $pagerView,
        ];
        return view('GuideContents.index', $data);
    }

    /**
     * コンテンツマスター新規作成
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function createMaster(Request $request, $guideAppId)
    {
        // 権限確認
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $formInput = [];
        // バリデーションエラーで戻された場合
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $formInput = $request->old();
        } else { // 戻るボタンで戻ってきた場合
            $formInput = $request->all();
        }

        $groupList = $this->guideContentsService->getGroupList($guideAppId);
        $dataTypeList = $this->guideContentsService->getDataTypeList();

        $data = [
            'formInput' => $formInput,
            'groupList' => $groupList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'dataTypeList' => $dataTypeList,
        ];
        return view('GuideContents.master_create', $data);
    }

    /**
     * コンテンツマスター新規登録確認
     * @param GuideContentsMasterRequest $request
     * @return view
     */
    public function createMasterConfirm(GuideContentsMasterRequest $request)
    {
        // 権限確認
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $dataTypeList = $this->guideContentsService->getDataTypeList();

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'dataTypeList' => $dataTypeList,
        ];
        return view('GuideContents.master_create_confirm', $data);
    }

    /**
     * コンテンツマスター新規登録完了
     * @param GuideContentsMasterRequest $request
     * @return view
     */
    public function storeMaster(GuideContentsMasterRequest $request)
    {
        // 権限確認
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideContentsService->createMaster($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideContents.master_store', $data);
    }

    /**
     * コンテンツマスター編集
     * @param request $request
     * @param integer $masterId
     * @return view
     */
    public function editMaster(request $request, $masterId)
    {
        // 編集対象データの存在確認
        $masterData = $this->guideContentsService->getMasterData($masterId);
        if (empty($masterData)) {
            return redirect()->route('GuideApplication.index');
        }

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        if ($request->isMethod('post')) {
            $formInput = array_merge($masterData, $request->all());
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $formInput = array_merge($masterData, $request->old());
        } else {
            $formInput = $masterData;
        }

        $dataTypeList = $this->guideContentsService->getDataTypeList();
        $groupList = $this->guideContentsService->getGroupList($masterData['guide_application_id']);

        $data = [
            'guideAppTitle' => $masterData['app']['name'],
            'formInput' => $formInput,
            'dataTypeList' => $dataTypeList,
            'groupList' => $groupList,
        ];
        return view('GuideContents.master_edit', $data);
    }

    /**
     * コンテンツマスター編集確認
     * @param GuideContentsMasterRequest $request
     * @return view
     */
    public function editMasterConfirm(GuideContentsMasterRequest $request)
    {
        // 編集対象データの存在確認
        $masterData = $this->guideContentsService->getMasterData($request->get('id'));
        if (empty($masterData)) {
            return redirect()->route('GuideApplication.index');
        }

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $dataTypeList = $this->guideContentsService->getDataTypeList();

        $data = [
            'request' => $request,
            'guideAppTitle' => $masterData['app']['name'],
            'masterData' => $masterData,
            'dataTypeList' => $dataTypeList,
        ];
        return view('GuideContents.master_edit_confirm', $data);
    }

    /**
     * コンテンツマスター編集完了
     * @param GuideContentsMasterRequest $request
     * @return view
     */
    public function updateMaster(GuideContentsMasterRequest $request)
    {
        // 編集対象データの存在確認
        $masterData = $this->guideContentsService->getMasterData($request['id']);
        if (empty($masterData)) {
            return redirect()->route('GuideApplication.index');
        }

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideContentsService->updateMaster($request->all());

        $data = [
            'guideAppId' => $masterData['guide_application_id'],
            'guideAppTitle' => $masterData['app']['name'],
            'feature' => '編集',
        ];
        return view('GuideContents.master_store', $data);
    }

    /**
     * コンテンツマスター削除
     * @param Request $request
     * @return Response
     */
    public function deleteMaster(Request $request)
    {
        // 削除対象データの存在確認
        $masterId = $request->get('id');
        $masterData = $this->guideContentsService->getMasterData($masterId);
        if (empty($masterData)) {
            return redirect()->route('GuideApplication.index');
        }

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideContentsService->deleteMaster($masterId);
        return redirect()->route('GuideContents.index', ['id' => $masterData['guide_application_id']]);
    }

    /**
     * コンテンツデータ一覧
     * @param Request $request
     * @param integer $masterId
     * @return view
     */
    public function clusterList(Request $request, $masterId)
    {
        // 表示対象マスターの存在確認
        $masterData = $this->guideContentsService->getMasterData($masterId);
        if (empty($masterData)) {
            return redirect()->route('GuideApplication.index');
        }

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $perPage = $request->get('perPage', config('forms.GuideContents.perPage', null));
        $dataList = $this->guideContentsService->getContentsList($masterId, $perPage);
        $dataArray = $dataList->getCollection()->toArray();
        foreach ($dataArray as $key => $data) {
            $values = collect($data['values'])->keyBy('template_id');
            $dataArray[$key]['values'] = $values->toArray();
        }

        $pagerLinkNum = config('forms.GuideContents.pagerLinkNum');
        $pagerView = $this->guideContentsService->getPagerView($dataList, $pagerLinkNum);

        $data = [
            'masterData' => $masterData,
            'dataList' => $dataList,
            'dataArray' => $dataArray,
            'pagerView' => $pagerView,
        ];
        return view('GuideContents.data_list', $data);
    }

    /**
     * コンテンツデータ新規登録
     * @param Request $request
     * @param int $masterId
     * @return view
     */
    public function createCluster(Request $request, $masterId)
    {
        $masterData = $this->guideContentsService->getMasterData($masterId);

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $formInput = [];
        // バリデーションエラーで戻された場合
        if ($request->isMethod('get') && $request->session()->has('errors')) {
            $formInput = $request->old();
        } else { // 戻るボタンで戻ってきた場合
            $formInput = $request->all();
        }

        $orderList = $this->guideContentsService->getContentsOrderList($masterId);
        $dataTypeList = $this->guideContentsService->getDataTypeList();
        $imageDirPath = env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/guide/' . $masterData['guide_application_id'] . '/contents_data/';
        $data = [
            'masterData' => $masterData,
            'orderList' => $orderList,
            'formInput' => $formInput,
            'dataTypeList' => $dataTypeList,
            'imageDirPath' => $imageDirPath,
        ];

        return view('GuideContents.data_create', $data);
    }

    /**
     * コンテンツデータ新規登録確認
     * @param GuideContentsClusterRequest $request
     * @return view
     */
    public function createClusterConfirm(GuideContentsClusterRequest $request)
    {
        $masterData = $this->guideContentsService->getMasterData($request->get('master_id'));

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $orderList = $this->guideContentsService->getContentsOrderList($request->get('master_id'));

        $data = [
            'request' => $request,
            'masterData' => $masterData,
            'orderList' => $orderList,
        ];
        return view('GuideContents.data_create_confirm', $data);
    }

    /**
     * コンテンツデータ新規登録完了
     * @param GuideContentsClusterRequest $request
     * @return view
     */
    public function storeCluster(GuideContentsClusterRequest $request)
    {
        $masterData = $this->guideContentsService->getMasterData($request->get('master_id'));

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        // 新規登録処理
        $this->guideContentsService->createCluster($request->all());

        $data = [
            'masterData' => $masterData,
            'feature' => '登録',
        ];
        return view('GuideContents.data_store', $data);
    }

    /**
     * コンテンツデータ編集
     * @param Request $request
     * @param int $dataId
     * @return view
     */
    public function editCluster(Request $request, $dataId)
    {
        $contentsData = $this->guideContentsService->getContentsData($dataId);
        $masterData = $this->guideContentsService->getMasterData($contentsData['master_id']);

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        if ($request->isMethod('post')) {
            $formInput = array_merge($contentsData, $request->all());
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $formInput = array_merge($contentsData, $request->old());
        } else {
            $formInput = $contentsData;
            unset($formInput['order_no']);
        }

        $imageDirPath = env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/guide/' . $masterData['guide_application_id'] . '/contents_data/';
        $orderList = $this->guideContentsService->getContentsOrderList($contentsData['master_id'], $dataId);

        $data = [
            'contentsData' => $contentsData,
            'masterData' => $masterData,
            'formInput' => $formInput,
            'orderList' => $orderList,
            'imageDirPath' => $imageDirPath,
        ];

        return view('GuideContents.data_edit', $data);
    }

    /**
     * コンテンツデータ編集確認
     * @param GuideContentsClusterRequest $request
     * @return view
     */
    public function editClusterConfirm(GuideContentsClusterRequest $request)
    {
        // 編集対象データの存在確認
        $dataId = $request->get('id');
        $contentsData = $this->guideContentsService->getContentsData($dataId);
        $masterData = $this->guideContentsService->getMasterData($contentsData['master_id']);

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $orderList = $this->guideContentsService->getContentsOrderList($contentsData['master_id'], $dataId);

        $data = [
            'request' => $request,
            'contentsData' => $contentsData,
            'masterData' => $masterData,
            'orderList' => $orderList,
        ];
        return view('GuideContents.data_edit_confirm', $data);
    }

    /**
     * コンテンツデータ編集完了
     * @param request $request
     * @return view
     */
    public function updateCluster(GuideContentsClusterRequest $request)
    {
        $masterData = $this->guideContentsService->getMasterData($request->get('master_id'));

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideContentsService->updateCluster($request->all());

        $data = [
            'masterData' => $masterData,
            'feature' => '編集',
        ];
        return view('GuideContents.data_store', $data);
    }

    /**
     * コンテンツデータ削除
     * @param Request $request
     * @return Response
     */
    public function deleteCluster(Request $request)
    {
        $dataId = $request->get('id');
        $contentsData = $this->guideContentsService->getContentsData($dataId);
        $masterData = $this->guideContentsService->getMasterData($contentsData['master_id']);

        // 権限確認
        $guideAppId = $masterData['guide_application_id'];
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideContentsService->deleteCluster($dataId);
        return redirect()->route('GuideContents.clusterList', ['master_id' => $contentsData['master_id']]);
    }

    /**
     * コンテンツデータ表示切替
     * @param Request $request
     * @return Response
     */
    public function switchClusterViewStatus(Request $request)
    {
        $dataId = $request->get('id');
        $contentsData = $this->guideContentsService->getContentsData($dataId);
        $this->guideContentsService->switchClusterViewStatus($dataId);
        return redirect()->route('GuideContents.clusterList', ['master_id' => $contentsData['master_id']]);
    }

    /**
     * アクセス権確認、ゲームタイトル取得
     * @param int $guideAppId
     * @return mixed(string|boolean)
     */
    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideContentsService->allowedEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }

        $guideApplication = $this->guideApplicationService->getOne($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }

    /** 
     * 画像アップロード
     * @param Request $request
     * @return Response
     */
    public function uploadClusterImage(Request $request)
    {
        $data = $this->guideContentsService->uploadClusterImage($request->all());
        return response()->json($data);
    }   

    /** 
     * 画像削除
     * @param Request $request
     * @return Response
     */
    public function deleteClusterImage(Request $request)
    {
        $response = ['success' => false];
        try {
            $params = $request->all();
            $response['success'] = $this->guideContentsService->deleteClusterImage($params['guideAppId'], $params['image'], $params['imgId']);
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
        return response()->json($response);
    }
}
