<?php
namespace App\Http\Controllers;

use App\Services\MaintenanceInformationsService;
use App\Http\Requests\MaintenanceInformations\SearchMaintenanceRequest;

class MaintenanceInformationsController extends Controller
{
    protected $maintenanceInformationsService;

    public function __construct(MaintenanceInformationsService $maintenanceInformationsService)
    {
        parent::__construct();
        $this->maintenanceInformationsService = $maintenanceInformationsService;
        view()->share($this->maintenanceInformationsService->getFormData());
    }

    public function maintenanceList(SearchMaintenanceRequest $request)
    {
        $searchRoute = 'MaintenanceInformations.maintenancelist';
        $search = $this->maintenanceInformationsService->formatSearchCondition($request->all(), 'maintenancelist');

        $paginator = $this->maintenanceInformationsService->getList($search);
        $pagerView = $this->maintenanceInformationsService->getPagerView(
            $paginator,
            config('forms.MaintenanceInformations.pagerLinkNum')
        );
        $keyword = (isset($search['keyword'])) ? $search['keyword'] : null;

        $searchParam = array(
            'keyword'   => $keyword,
            'maxLength' => config('forms.MaintenanceInformations.keywordLimit.length')
        );

        return view(
            'MaintenanceInformations.maintenancelist',
            compact('paginator', 'pagerView', 'searchParam', 'searchRoute')
        );
    }

    public function show($id)
    {
        $data = $this->maintenanceInformationsService->getData($id);

        if (empty($data->exists)) {
            abort(404);
        }
        
        $type = 'maintenance';

        return view('MaintenanceInformations.show', compact('data', 'type'));
    }
}
