<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\ApplicationPolicyRequest;
use App\Services\ApplicationPolicyService;
use Log;

/**
 * 
 */
class ApplicationPolicyController extends Controller
{
    protected $ApplicationPolicyService;
    public function __construct(ApplicationPolicyService $applicationPolicyService)
    {
        parent::__construct();
        $this->applicationPolicyService = $applicationPolicyService;

        // configの固定パラメタ
        view()->share(['formData' => $this->applicationPolicyService->getFormData()]);
   }

    /**
     * トップ
     */
    public function index(Request $request, $app_id)
    {
        if (! empty($app_id) && ! $this->applicationPolicyService->isAvailable($app_id)) {
            abort(405);
        }

        $policy = $this->applicationPolicyService->getPolicy($app_id);

        $buttonName = '申請';
        if (isset($policy->examination_status)) {
            $buttonName = '更新申請';
        }

        return view('ApplicationPolicy.index', compact('policy', 'buttonName'));
    }

    /**
     * 申請
     */
    public function create(Request $request, $app_id)
    {
        if (! empty($app_id) && ! $this->applicationPolicyService->isAvailable($app_id)) {
            abort(405);
        }

        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $content = $request->old();
        } else {
            // 初期表示時はDBからデータを取得
            $content = $request->all();
        }

        $policy = $this->applicationPolicyService->getPolicy($app_id);
        $screenName = $this->getScreenName($policy);

        return view('ApplicationPolicy.create', compact('policy', 'screenName'));
    }

    /**
     * 申請確認
     */
    public function createConfirm(ApplicationPolicyRequest $request)
    {
        if (! empty($request->get('app_id')) && ! $this->applicationPolicyService->isAvailable($request->get('app_id'))) {
            abort(405);
        }

        $policy = $this->applicationPolicyService->getPolicy($request->get('app_id'));

        // 規約文言のファイルからテキスト取得
        $policyBodyTxt = $this->applicationPolicyService->getPolicyBodyTxt($request);

        // 文字コードがutf8ではない場合はエラー
        if (mb_detect_encoding($policyBodyTxt) != 'UTF-8') {
            $result[] = preg_replace('/:attribute/', '規約文言', trans('validationmessage.MSG286'));
            return back()->withErrors($result)->withInput($request->all());
        }
        $screenName = $this->getScreenName($policy);

        return view('ApplicationPolicy.createconfirm', compact('request', 'policy', 'policyBodyTxt', 'screenName'));
    }

    /**
     * 申請登録
     */
    public function store(ApplicationPolicyRequest $request)
    {
        if (! empty($request->get('app_id')) && ! $this->applicationPolicyService->isAvailable($request->get('app_id'))) {
            abort(405);
        }

        // 申請済みがあるか
        $policy = $this->applicationPolicyService->getPolicy($request->get('app_id'));

        // 規約登録
        $this->applicationPolicyService->setPolicy($request->get('app_id'), $request->get('policy_body'));
        $screenName = $this->getScreenName($policy);

        return view('ApplicationPolicy.store', compact('policy', 'screenName'));
    }

    /**
     * 公開確認
     */
    public function releaseConfirm(Request $request, $app_id)
    {
        if (! empty($app_id) && ! $this->applicationPolicyService->isAvailable($app_id)) {
            abort(405);
        }

        // これから公開する規約取得
        $policy = $this->applicationPolicyService->getPolicy($app_id);

        // データ不正チェック
        if ($policy->examination_status != 1 || empty($policy->policy_body) || ! empty($policy->release_date)) {
            abort(405);
        }

        // 既に公開済みの規約の有無を確認
        $isReleased = (count($this->applicationPolicyService->getReleasedList($app_id)) > 0) ? true : false ;

        return view('ApplicationPolicy.releaseconfirm', compact('policy', 'isReleased'));
    }

    /**
     * 公開
     */
    public function releaseStore(Request $request)
    {
        if (! empty($request->get('app_id')) && ! $this->applicationPolicyService->isAvailable($request->get('app_id'))) {
            abort(405);
        }

        // これから公開する規約取得
        $policy = $this->applicationPolicyService->getPolicy($request->get('app_id'));

        // データ不正チェック
        if ($policy->examination_status != 1 || empty($policy->policy_body) || ! empty($policy->release_date)) {
            abort(405);
        }

        // 公開
        $this->applicationPolicyService->releasePolicy($policy);
        return view('ApplicationPolicy.releasestore', compact('policy'));
    }

    /**
     * 画面名の切り替え（規約申請済みor未申請）
     */
    public function getScreenName($policy)
    {
        $screenName = '規約申請';
        if (isset($policy->examination_status)) {
            $screenName = '規約更新申請';
        }
        return $screenName;
    }
}
