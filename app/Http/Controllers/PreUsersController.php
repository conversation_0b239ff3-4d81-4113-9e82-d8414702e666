<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\GuestPlayService;
use App\Services\PreUsersService;
use App\Http\Requests\PreUsersRequest;

class PreUsersController extends Controller
{
    protected $preUsersService;

    public function __construct(PreUsersService $preUsersService, Request $request, GuestPlayService $guestPlayService)
    {
        parent::__construct();

        $this->preUsersService = $preUsersService;
        $this->preUsersService->setControllerName($this->getControllerName());
        $url = array(
            'url' => $this->preUsersService->requestUrl($request->query())
        );
        view()->share(array_merge($this->preUsersService->getFormData(), $url));

        // 404
        $isEnableEdit = $this->preUsersService->isEnableEdit();
        if (! $isEnableEdit) {
            $action = $this->getActionName();
            if ($action != 'index') {
                abort(404);
            }
        }

        $this->guestPlayService = $guestPlayService;
    }

//*********************************************************************************************************************
    /**
     * 一覧
     * @param  Request $request
     * @return view
     */
    public function index(Request $request)
    {
        $pagerLinkNum  = config('forms.PreUsers.pagerLinkNum');
        $preUserList   = $this->preUsersService->getSearchList($request->all());
        $pagerView     = $this->preUsersService->getPagerView($preUserList, $pagerLinkNum);
        $selectAppData = $this->preUsersService->getSelectApplicationList();

        return view('PreUsers.index', compact('preUserList', 'pagerView', 'selectAppData'));
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  Request $request
     * @return view
     */
    public function create(Request $request)
    {
        $selectAppData = $this->preUsersService->getSelectApplicationList();

        return view('PreUsers.create', compact('selectAppData'));
    }

    /**
     * 登録確認
     * @param  PreUsersRequest $request
     * @return view
     */
    public function createConfirm(PreUsersRequest $request)
    {
        $preUserData   = $this->preUsersService->getMakeDetail($request->all());

        return view('PreUsers.createconfirm', compact('preUserData'));
    }

    /**
     * 登録完了
     * @param  PreUsersRequest $request
     * @return view
     */
    public function createStore(PreUsersRequest $request)
    {
        $title         = $this->preUsersService->getTitleResultPlain($this->screenName, '登録');
        $params = $request->all();
        unset($params['perPage'], $params['userSort'], $params['date_desc'], $params['page']);
        $result        = $this->preUsersService->createStore($params);

        if (! $result) {
            abort(400);
        }

        // ゲストプレイユーザであればキャッシュ削除する
        $this->deleteGuestPlayUserCache($params['user_id']);

        return view('PreUsers.store', compact('title'));
    }

//*********************************************************************************************************************
    /**
     * 削除
     * @param  Request $request
     * @return view
     */
    public function destroy(Request $request)
    {
        $result        = $this->preUsersService->destroy($request->all());

        if (! $result) {
            abort(400);
        }

        // ゲストプレイユーザであればキャッシュ削除する
        $this->deleteGuestPlayUserCache($request->input('user_id'));

        return redirect()->route(
            'PreUsers.index',
            ['search' => 'on'] + $this->preUsersService->requestUrl($request->query())
        );
    }

//*********************************************************************************************************************
    /**
     * ゲストプレイユーザのキャッシュを削除する
     * @param string|int $userId
     */
    protected function deleteGuestPlayUserCache($userId)
    {
        // ゲストテーブルからデータ取得
        $guest = $this->guestPlayService->getGuestData($userId);

        // ゲストテーブルにデータが存在し、なおかつ未アップグレードの場合はキャッシュ削除
        if (!is_null($guest) && !$guest->isUpgraded()) {
            $this->guestPlayService->deleteGuestPlayUserCache($guest['hashed_id']);
        }
    }
}
