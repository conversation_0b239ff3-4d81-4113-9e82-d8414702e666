<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\ChReportsDailyRequest;
use App\Services\ChReportsDailyService;

/**
 * CSVダウンロード（チャネリングゲーム）日別
 */
class ChReportsDailyController extends Controller
{
    protected $ChReportsDailyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(ChReportsDailyService $ChReportsDailyService)
    {
        parent::__construct();
        $this->ChReportsDailyService = $ChReportsDailyService;
        $this->config = config('forms.ChReportsDaily');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'ChReportsDaily.index'];

        view()->share(['formData'  => $this->config]);
    }

    /**
     * 一覧
     *
     */
    public function index()
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'periodType'   => config('forms.ChReportsDaily.periodType'),
            'appTitleType' => $this->ChReportsDailyService->getChApplicationList(),
            'reportType'   => config('forms.ChReportsDaily.reportType'),
            'deviceType'   => config('forms.ChReportsDaily.deviceType'),
        );

        return view('ChReportsDaily.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param ChReportsDailyRequest $request
     *
     */
    public function csvDownload(ChReportsDailyRequest $request)
    {
        $condition = $request->all();

        $fileName = $this->ChReportsDailyService->getCsvFileName($condition);
        $header = $this->ChReportsDailyService->getCsvHeader($condition);
        $list = $this->ChReportsDailyService->getCsvList($condition);
        $csvData[] = $header;
        $csvData = array_merge($csvData, $list);

        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename=' . $fileName);
        foreach ($csvData as $lineList) {
            echo mb_convert_encoding(implode(',', $lineList), 'sjis-win', 'UTF-8')."\n";
        }
    }
}
