<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\GuideIpRequest;
use App\Services\GuideIpService;
use Log;

class GuideIpController extends Controller
{
    protected $guideIpService;

    public function __construct(GuideIpService $guideIpService)
    {
        parent::__construct();

        $this->guideIpService = $guideIpService;
        // configの固定パラメタ
        view()->share([
            'formData' => $this->guideIpService->getFormData(),
        ]);
    }

    /**
     * Index page
     * @param Request $request
     * @param integer $guideAppId
     * @return view
     */
    public function index(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $guideIpList =  $this->guideIpService->getListOuterIp($guideAppId);

        $data = [
            'guideIpList' => $guideIpList,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideIp.index', $data);
    }


    /**
     * GuideIp create page
     * @param request $request
     * @param integer $guideAppId
     * @return view
     */
    public function create(Request $request, $guideAppId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideIp.create', $data);
    }

    /**
     * GuideIp create confirm page
     * @param request $request
     * @return view
     */
    public function createConfirm(GuideIpRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideIp.createconfirm', $data);
    }

    /**
     * GuideIp create store page
     * @param request $request
     * @return view
     */
    public function store(GuideIpRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideIpService->create($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '登録',
        ];
        return view('GuideIp.store', $data);
    }

    /**
     * GuideIp edit page
     * @param request $request
     * @param integer $guideAppId
     * @param integer $mainteId
     * @return view
     */
    public function edit(Request $request, $guideAppId, $mainteId)
    {
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $content = $this->guideIpService->getOneOuterIp($mainteId, $guideAppId);
        if (empty($content)) {
            Log::error('Not Found guide_outer_ip : id=' . $mainteId);
            abort(404);
        }
        $ips = explode('.', $content['ip_address']);
        $content['address1'] = $ips[0];
        $content['address2'] = $ips[1];
        $content['address3'] = $ips[2];
        $content['address4'] = $ips[3];

        if ($request->isMethod('post')) {
            $content = $request->all();
        } elseif ($request->isMethod('get') && $request->session()->has('errors')) {
            $content = $request->old();
        }

        $data = [
            'content' => $content,
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideIp.edit', $data);
    }

    /**
     * GuideIp edit confirm page
     * @param request $request
     * @return view
     */
    public function editConfirm(GuideIpRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $data = [
            'request' => $request,
            'guideAppTitle' => $guideAppTitle,
        ];
        return view('GuideIp.editconfirm', $data);
    }

    /**
     * GuideIp update page
     * @param request $request
     * @return view
     */
    public function update(GuideIpRequest $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $this->guideIpService->edit($request->all());

        $data = [
            'guideAppId' => $guideAppId,
            'guideAppTitle' => $guideAppTitle,
            'feature' => '編集',
        ];
        return view('GuideIp.store', $data);
    }

    /**
     * GuideIp destroy page
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        $guideAppId = $request->get('guideAppId');
        $guideAppTitle = $this->getAppTitleAndCheckId($guideAppId);
        if ($guideAppTitle === false) {
            return redirect()->route('GuideApplication.index');
        }

        $outerIpId = $request->get('outerIpId');
        $this->guideIpService->deleteContent($outerIpId, $guideAppId);

        return redirect()->route('GuideIp.index', ['id' => $guideAppId]);
    }

    private function getAppTitleAndCheckId($guideAppId)
    {
        if ($this->guideIpService->isEnableEdit($guideAppId) === false) {
            $loginId = auth_user_login_id();
            Log::error('Do not have permission : login_id=' . $loginId);
            return false;
        }
        $guideApplication = $this->guideIpService->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            abort(404);
        }
        return $guideApplication['name'];
    }
}
