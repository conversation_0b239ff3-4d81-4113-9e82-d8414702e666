<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ReceiptInquiryService;
use App\Http\Requests\ReceiptInquiry\BasicRequest;

class ReceiptInquiryController extends Controller
{
    /** @var ReceiptInquiryService */
    protected $receiptInquiryService;

    /**
     * ReceiptInquiryController constructor.
     *
     * @param ReceiptInquiryService $receiptInquiryService
     */
    public function __construct(
        ReceiptInquiryService $receiptInquiryService
    ) {
        parent::__construct();
        $this->middleware('check.developer');

        $this->receiptInquiryService = $receiptInquiryService;
        view()->share($this->receiptInquiryService->getFormData());
    }

    /**
     * 一覧
     *
     * @param  Request $request
     * @return view
     */
    public function index(Request $request)
    {
        // 参照用にGET
        $app_id = $request->get('app_id');
        $receiptId = $request->get('receiptId');
        $device = $request->get('device');

        // 初期化
        $appId = null;
        $receiptInfo[] = [
            'resultStatus'  => null,
            'resultMessage' => null,
            'receiptId'     => null,
            'sku'           => null,
            'title'         => null,
            'price'         => null,
            'status'        => null,
            'placedAt'      => null,
            'fulfilledAt'   => null,
            'payload'       => null
        ];
        $validationFlg = true;

        // アプリ権限のあるタイトルリストを取得
        $appTitleType = $this->receiptInquiryService->getApplicationTitleList();

        // GETしたアプリケーションIDとアプリ権限のあるタイトルリストを比較
        $appId = $this->checkApplicationId($app_id, $appTitleType);

        // 入力値にエラーがあった場合
        if ($request->session()->has('errors')) {
            $validationFlg = false;
        }

        // レシート情報を取得
        if ($appId && $receiptId && $validationFlg && $device) {
            $receiptInfo = $this->receiptInquiryService->getReceiptInfo($receiptId, $appId, $device);
        }
        return view('ReceiptInquiry.index', compact('appTitleType', 'receiptInfo'));
    }

    /**
     * 詳細
     *
     * @param BasicRequest $request
     * @return view
     */
    public function detail (BasicRequest $request)
    {
        // validation用にGET
        $app_id = $request->get('app_id');
        $receiptId = $request->get('receiptId');
        $device = $request->get('device');

        return redirect()->route('ReceiptInquiry.index', ['app_id' => $app_id, 'receiptId' => $receiptId, 'device' => $device]);
    }

    /**
     * GETしたアプリケーションIDとアプリ権限のあるタイトルリストを比較
     *
     * @param int $app_id
     * @param array $appTitleType
     * @return int
     */
    private function checkApplicationId ($app_id, $appTitleType)
    {
        // 初期化
        $appId = null;

        if (array_key_exists($app_id, $appTitleType)) {
            $appId = $app_id;
        }
        return $appId;
    }
}
