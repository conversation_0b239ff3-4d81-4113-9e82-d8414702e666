<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON><PERSON> extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * @var array
     */
    protected $middleware = [
        \App\Http\Middleware\CaptureRequests::class, // リクエストをキャプチャするためのミドルウェアを追加
        \App\Http\Middleware\CheckForMaintenanceMode::class,
        \App\Http\Middleware\EncryptCookies::class,
        \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
        \Illuminate\Session\Middleware\StartSession::class,
        \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        \App\Http\Middleware\VerifyCsrfToken::class,
        \App\Http\Middleware\UpdateCsrfTokenIfSaved::class,
        \App\Http\Middleware\RequestPaginationCheck::class,
        \App\Http\Middleware\ConvertRequests::class,
        \App\Http\Middleware\EncodeRequests::class,
        \App\Http\Middleware\FrameGuard::class,
        \App\Http\Middleware\CacheGuard::class,
        \App\Http\Middleware\RequireChangePassword::class,
        \App\Http\Middleware\TrimBlankSpaceFromInput::class,
    ];

    /**
     * The application's route middleware.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.gate' => \App\Http\Middleware\AuthenticateGate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'set.language' => \App\Http\Middleware\SetLanguage::class,
        'check.developer' => \App\Http\Middleware\CheckApplicationForLoginDeveloper::class,
    ];
}
