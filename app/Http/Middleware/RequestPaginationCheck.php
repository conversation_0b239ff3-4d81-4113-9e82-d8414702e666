<?php

namespace App\Http\Middleware;

use Closure;

class RequestPaginationCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->exists('perPage')) {
            $perPage = (int)$request->get('perPage', 0);
            $defaultPerPage = config('forms.common.pagination.perPage');
            $maxLimitPerPage = config('forms.common.maxLimitPerPage');
            if (1 > $perPage || $perPage > $maxLimitPerPage) {
                $request->merge(['perPage' => $defaultPerPage[0]]);
            }
        }

        return $next($request);
    }
}
