<?php

namespace App\Http\Middleware;

use App\Models\FreegameDeveloper\DeveloperApplication;
use Closure;

/**
 * ログインユーザーがリクエストされたアプリケーションを所持しているか確認します
 * @package App\Http\Middleware
 */
class CheckApplicationForLoginDeveloper
{
    protected $developerApplication;

    /**
     * CheckApplicationForLoginDeveloper constructor.
     * @param DeveloperApplication $developerApplication
     */
    public function __construct(DeveloperApplication $developerApplication)
    {
        $this->developerApplication = $developerApplication;
    }

    /**
     * handle.
     * @param $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // SAPではない場合は判定ナシ
        if (!$this->isSap()) {
            return $next($request);
        }

        // applicationIdの指定がなければ判定ナシ
        $applicationId = $request->get('app_id', '');
        if ($applicationId === '') {
            return $next($request);
        }

        // ログインしているユーザーIDから管理下にあるゲームを取得
        $developerId = $this->getDeveloperId();
        $applicationList = $this->developerApplication->getApplicationAppIdList(['developer_id' => $developerId]);

        $hasApplicationId = $applicationList->search(function ($item) use ($applicationId) {
            return $item->app_id == $applicationId;
        });

        if ($hasApplicationId === false) {
            // ログインユーザーが管理しているアプリケーションIDではないので404を返す
            abort(404);
        }

        return $next($request);
    }

    public function isSap()
    {
        return auth_is_sap();
    }

    public function getDeveloperId()
    {
        return auth_user_id();
    }
}
