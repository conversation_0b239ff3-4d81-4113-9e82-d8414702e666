<?php

namespace App\Http\Middleware;

use Closure;
use App\Services\MaintenanceModeService;

class CheckForMaintenanceMode
{
    /**
     * The MaintenanceModeService implementation.
     *
     * @var MaintenanceModeService
     */
    protected $service;

    /**
     * Create a new filter instance.
     *
     * @param  MaintenanceModeService  $service
     * @return void
     */
    public function __construct(MaintenanceModeService $service)
    {
        $this->service = $service;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $data = $this->service->getData();

        if ($data['exists'] && $data['status'] == 'active') {
            abort(503, $this->service->getMessage($data));
        }

        return $next($request);
    }
}
