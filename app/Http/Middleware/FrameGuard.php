<?php

namespace App\Http\Middleware;

use Closure;

class FrameGuard
{
    /**
     * Handle the given request and get the response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Illuminate\Http\Response
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        $origin = config('forms.FrameGuard.origin');

        $response->headers->set('content-security-policy', sprintf('frame-ancestors %s;', $origin), false);

        return $response;
    }
}