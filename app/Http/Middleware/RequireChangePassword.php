<?php

namespace App\Http\Middleware;

use Closure;
use Carbon\Carbon;

class RequireChangePassword
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $path          = parse_url($request->headers->get('referer'), PHP_URL_PATH);
        $passwordRoute = parse_url($request->url(), PHP_URL_PATH);
        $config        = config('forms.Users.requireChangePassword');

        if (env('PASSWORD_CHANGE_REQUEST', false) && auth()->check()) {
            $notification = $this->requestChangePassword();
            switch (true) {
                case ! $notification:
                    return $next($request);
                    break;
                case is_array($notification)
                    && $notification['content'] == $config['disableLogin']:
                    auth()->logout();
                    return redirect()->route('Users.login')
                        ->withErrors(['errors' => trans('validationmessage.MSG283')]);
                    break;
                case is_array($notification)
                    && $notification['content'] == $config['requireChangePassword']:
                    $auth = array_only(auth()->user()->toArray(), ['id', 'login_id']);
                    auth()->logout();
                    return redirect()->route('Users.login')->with(
                        'requestChangePassword',
                        ['content' => $config['requireChangePassword']]
                    )->withInput($auth);
                    break;
                default:
                    if ($passwordRoute == route('Password.update', [], false)) {
                        return $next($request);
                    }
                    if ($path === route('Users.login', [], false)) {
                        $request->session()->flash('requestChangePassword', $notification);
                        return $next($request);
                    }
                    $request->session()->forget('requestChangePassword');
                    return $next($request);
                    break;
            }
        }
        return $next($request);
    }

    /**
     * Request Change Password
     * @return boolean | array
     */
    protected function requestChangePassword()
    {
        switch (true) {
            case auth_is_user_admin():
            case auth_is_user_staff():
                return false;
                break;
            case auth_is_sap():
            case auth_is_user_kc():
                return $this->checkAuthRequestChangePassword();
                break;
            default:
                return false;
                break;
        }
    }

    /**
     * Check Auth Request Change Password
     * @return boolean | array
     */
    protected function checkAuthRequestChangePassword()
    {
        $passwordUpdateDate = auth()->user()->password_update_date;
        if (! $passwordUpdateDate) {
            return true;
        }
        $passwordUpdateDate = date('Y-m-d', strtotime($passwordUpdateDate));
        $expiredDays           = Carbon::parse($passwordUpdateDate)->diffInDays(
            Carbon::parse(Carbon::now()->format('Y-m-d'))
        );
        $config                = config('forms.Users.requireChangePassword');
        $passwordChangeAlert   = env('PASSWORD_CHANGE_ALERT', 30);
        $passwordChangeInvalid = env('PASSWORD_CHANGE_INVALID', 40);
        $passwordChangeLock    = env('PASSWORD_CHANGE_LOCK', 60);

        switch (true) {
            case ($expiredDays > $passwordChangeAlert
                && $expiredDays <= $passwordChangeInvalid):
                return [
                    'content'          => $config['alertMessage'],
                    'message'          => sprintf($config['messageAlarm'], $passwordChangeInvalid - $expiredDays + 1)
                ];
                break;
            case ($expiredDays > $passwordChangeInvalid 
                && $expiredDays <= $passwordChangeLock):
                return [
                    'content'          => $config['requireChangePassword']
                ];
                break;
            case ($expiredDays > $passwordChangeLock):
                return [
                    'content' => (auth_is_user_staff() || auth_is_user_kc()) ?
                        $config['requireChangePassword']
                        : $config['disableLogin']
                ];
                break;
            default:
                return false;
                break;
        }
    }
}
