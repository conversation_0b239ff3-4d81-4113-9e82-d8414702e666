<?php

namespace App\Http\Middleware;

use Closure;

class CacheGuard
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        $response->headers->set('Cache-Control', 'no-store', false);
        $response->headers->set('Pragma', 'no-cache', false);

        return $response;
    }
}
