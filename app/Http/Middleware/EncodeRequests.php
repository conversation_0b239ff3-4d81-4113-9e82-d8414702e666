<?php

namespace App\Http\Middleware;

use Closure;

class EncodeRequests
{
    /**
     * 除外したいURL
     * @var array
     */
    protected $except = [
        'inquiries/reply/store',
        'inquiries/reply/createconfirm',
        'games/basic/editconfirm',
        'games/basic/update',
        'games/device/pc/editconfirm',
        'games/device/pc/update',
        'games/device/sp/editconfirm',
        'games/device/sp/update',
        'games/device/mobile/editconfirm',
        'games/device/mobile/update',
        'games/device/android/app/editconfirm',
        'games/device/android/app/update',
        'subscription/createConfirm',
        'subscription/store',
        'subscription/editConfirm',
        'subscription/update',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($this->isExcluded($request)) return $next($request);
        $request->replace($this->encode($request->all()));
        return $next($request);
    }

    /**
     * Encode to character.
     *
     * @param  mixed $data
     * @return mixed
     */
    protected function encode($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $val) {
                $data[$key] = $this->encode($val);
            }
        } elseif (is_string($data)) {
            $substrchar = mb_substitute_character();
            mb_substitute_character('entity');
            $data = mb_convert_encoding($data, 'EUC-JP', 'UTF-8');
            mb_substitute_character($substrchar);
            $data = mb_convert_encoding($data, 'UTF-8', 'EUC-JP');
        }
        return $data;
    }

    /**
     * 除外したいURLであるかどうかをチェック
     * @param Request リクエスト
     * @return boolean 除外したいURLであればtrue
     */
    protected function isExcluded($request){
        foreach($this->except as $route){
            if ($request->is($route)) return true;
        }
    }

}
