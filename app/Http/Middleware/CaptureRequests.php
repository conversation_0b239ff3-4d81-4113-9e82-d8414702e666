<?php

namespace App\Http\Middleware;

use Closure;
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;

/**
 * リクエストキャプチャ用ミドルウェア
 *
 * 環境変数「CAPTURE_REQUESTS_ENABLED」が true の場合にのみ
 * リクエスト内容をキャプチャしてログに出力する
 *
 * ※本番環境のみ標準ログとキャプチャログを分離する
 *
 * <AUTHOR>
 */
class CaptureRequests
{
    const ENV_PRODUCTION = 'production';

    /**
     * CAPTURE_REQUESTS_ENABLED.
     *
     * @var bool
     */
    protected $enabled;

    /**
     * 新しいミドルウェアインスタンスを生成する
     *
     * @return void
     */
    public function __construct()
    {
        $this->enabled = env('CAPTURE_REQUESTS_ENABLED', false);
    }

    /**
     * リクエストをハンドリングする
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 終了処理ミドルウェアとして実装するためパススルー
        return $next($request);
    }

    /**
     * 終了処理でリクエストをキャプチャする
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Http\Response  $response
     * @return void
     */
    public function terminate($request, $response)
    {
        // リクエストキャプチャ時の例外を握りつぶして
        // 後続処理に影響を与えないようにする
        try {
            if ($this->enabled) {
                $this->capture($request);
            }
        } catch (Exception $e) {
            \Log::error($e->getMessage());
        }
    }

    /**
     * リクエストをキャプチャする
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function capture($request)
    {
        // ユーザー情報をキャプチャする
        // パスワードなどのセンシティブな情報は含めない
        $user = [];

        $loggedIn = $request->user();
        if ($loggedIn) {
            $user = array_only($loggedIn->toArray(), [
                'id',       // ユーザーID（ユニーク）
                'name',     // ユーザー名（非ユニーク）
                'login_id', // LoginID
            ]);
        }

        // ドキュメントサイトで使用しているトークンをキャプチャする
        // キャプチャ成功の場合はユーザー情報に加える
        $token = $request->cookie('SecureLinkToken');
        if ($token) {
            $user = array_add($user, 'secure_link_token', $token);
        }

        // パラメーターをキャプチャする
        // センシティブな情報やノイズになる情報は除去する
        $params = $request->except([
            'password',     // フォームでのパスワード識別子
            'password1',    // 同上
            'password2',    // 同上
            'password_now', // 同上
            '_token',       // フォームリクエスト時のトークン
        ]);

        // ユーザー情報・パラメーターのいずれかがキャプチャできた場合にはログに吐き出す
        if ($user || $params) {
            $captured = [];

            // サーバー情報をキャプチャする
            // 有益と思われる情報を含める
            $server = array_only($request->server(), [
                'HTTP_CLIENT_IP',
                'HTTP_X_FORWARDED_FOR',
                'REMOTE_ADDR',
                'REQUEST_URI',
            ]);
            $captured = array_add($captured, 'server', $server);

            if ($user) {
                $captured = array_add($captured, 'user', $user);
            }

            if ($params) {
                $captured = array_add($captured, 'params', $params);
            }

            // ログ出力時に固定文字を含める（grep用）
            $this->logger()->info('REQUEST_CAPTURED', $captured);
        }
    }

    /**
     * 適切なロガーを返す
     *
     * @return \Monolog\Logger
     */
    protected function logger()
    {
        // 本番環境のみカスタムロガーに出力する
        if (app()->environment(self::ENV_PRODUCTION)) {
            return $this->createCustomLogger();
        }

        return \Log::getMonolog();
    }

    /**
     * カスタムロガーを生成する
     *
     * @return \Monolog\Logger
     */
    protected function createCustomLogger()
    {
        // Laravelの標準的なログディレクトリの別名ログファイルに保存
        $fileName = storage_path() . '/logs/request.log';

        // ログローテーションは標準ログに合わせる
        // デフォルト値はLaravel標準の90
        $logMaxFiles = config('app.log_max_files', 90);

        // ログローテーションが有効なハンドラを生成する
        $handler = new RotatingFileHandler($fileName, $logMaxFiles);

        // ロガー名は標準ロガーと合わせる
        $logger = new Logger(self::ENV_PRODUCTION);
        $logger->pushHandler($handler);

        return $logger;
    }
}
