<?php 

namespace App\Http\Middleware;

use Closure;

class TrimBlankSpaceFromInput
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $request->replace($this->trimArrayRecursive($request->all()));
        return $next($request);
    }

    /**
     * Trim recursively.
     *
     * @param  array|string $input
     *
     * @return array|mixed
     */
    protected function trimArrayRecursive($input)
    {
        if (!is_array($input)) {
            return preg_replace("/(^\s+)|(\s+$)/us", "", $input);
        }

        return array_map([$this, 'trimArrayRecursive'], $input);
    }
}
