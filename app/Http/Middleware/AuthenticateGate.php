<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Guard;

class AuthenticateGate
{
    /**
     * The Guard implementation.
     *
     * @var Guard
     */
    protected $auth;

    /**
     * Create a new filter instance.
     *
     * @param  Guard  $auth
     * @return void
     */
    public function __construct(Guard $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $check = env('AUTH_GATE_REQUEST', false);

        if (! $check) {
            return $next($request);
        }

        if ($this->auth->check()) {
            if ($this->auth->user()->hasAuthRole($request->path())) {
                return $next($request);
            }
            abort(405);
        }

        return $next($request);
    }
}
