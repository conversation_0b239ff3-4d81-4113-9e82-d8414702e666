<?php

namespace App\Http\Middleware;

use Closure;

class UpdateCsrfTokenIfSaved
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $token = $request->session()->getToken();

        if ($request->isMethod('post')) {
            if ($request->is('*/store') ||
                $request->is('*/update') ||
                $request->is('*/destroy') ||
                $request->is('*/store/*') ||
                $request->is('*/update/*') ||
                $request->is('*/destroy/*') ||
                $request->is('*/createcomplete') ||
                $request->is('*/editcomplete')
            ) {
                $request->session()->regenerateToken();
            }
        }

        $response = $next($request);

        if ($request->isMethod('post')) {
            if ($request->session()->has('errors')) {
                $request->session()->put('_token', $token);
            }
        }

        return $response;
    }
}
