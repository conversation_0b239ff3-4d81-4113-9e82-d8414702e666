<?php

namespace App\Http\Middleware;

use Closure;

class SetLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $currentLanguageCode = config('forms.common.language.default');
        $languageSessionName = config('forms.common.language.sessionName');

        if ($request->session()->has($languageSessionName)) {
            $currentLanguageCode = session()->get($languageSessionName);
        }

        app()->setLocale($currentLanguageCode);
        return $next($request);
    }
}
