<?php

namespace App\Http\Middleware;

use Closure;

class ConvertRequests
{
    /**
     * The search character.
     */
    protected $search = [
        '①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩',
        '⑪', '⑫', '⑬', '⑭', '⑮', '⑯', '⑰', '⑱', '⑲', '⑳',
        '㉑', '㉒', '㉓', '㉔', '㉕', '㉖', '㉗', '㉘', '㉙', '㉚',
        '㉛', '㉜', '㉝', '㉞', '㉟', '㊱', '㊲', '㊳', '㊴', '㊵',
        '㊶', '㊷', '㊸', '㊹', '㊺', '㊻', '㊼', '㊽', '㊾', '㊿',
        'Ⅰ', 'Ⅱ', 'Ⅲ', 'Ⅳ', 'Ⅴ', 'Ⅵ', 'Ⅶ', 'Ⅷ', 'Ⅸ', 'Ⅹ', 'Ⅺ', 'Ⅻ',
        'ⅰ', 'ⅱ', 'ⅲ', 'ⅳ', 'ⅴ', 'ⅵ', 'ⅶ', 'ⅷ', 'ⅸ', 'ⅹ', 'ⅺ', 'ⅻ',
        '㈱', '㈲', '㈹', '㋿', '㍻', '㍼', '㍽', '㍾',
    ];

    /**
     * The replace character.
     */
    protected $replace = [
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
        '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
        '21', '22', '23', '24', '25', '26', '27', '28', '29', '30',
        '31', '32', '33', '34', '35', '36', '37', '38', '39', '40',
        '41', '42', '43', '44', '45', '46', '47', '48', '49', '50',
        'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII',
        'i', 'ii', 'iii', 'iv', 'v', 'vi', 'vii', 'viii', 'ix', 'x', 'xi', 'xii',
        '(株)', '(有)', '(代)', '令和', '平成', '昭和', '大正', '明治',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!$request->is('inquiries/*')) {
            $request->replace($this->convert($request->all()));
        }
        return $next($request);
    }

    /**
     * Convert to character.
     *
     * @param  mixed $data
     * @return mixed
     */
    protected function convert($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $val) {
                $data[$key] = $this->convert($val);
            }
        } elseif (is_string($data)) {
            $data = preg_replace("/\xef\xbc\x8d/", "\xe2\x88\x92", $data); // 全角ハイフン
            $data = preg_replace("/\xef\xbd\x9e/", "\xe3\x80\x9c", $data); // 全角チルダ
            $data = str_replace($this->search, $this->replace, $data); // 特殊文字・機種依存文字
        }
        return $data;
    }
}
