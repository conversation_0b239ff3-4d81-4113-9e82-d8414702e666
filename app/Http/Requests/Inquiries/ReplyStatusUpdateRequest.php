<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;

/**
 * お問い合わせ：返信状況更新
 */
class ReplyStatusUpdateRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'reply_status' => 'in:' . implode(',', array_keys(config('forms.Inquiries.replyStatusType')))
        ];
    }

    public function attributes()
    {
        return [
            'id' => '問合せID',
            'reply_status' => '返信状況'
        ];
    }

    public function customMessages()
    {
        return [
            'in' => $this->MSG012
        ];
    }
}
