<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;

/**
 * お問い合わせ：一括返信
 */
class ReplyAllRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'inquiry_id' => 'required|required_array:one',
            'status' => 'required|in:' . implode(',', array_merge([
                '0'
            ], array_keys(config('forms.Inquiries.statusType')))),
            'reply_status' => 'required|in:' . implode(',', array_merge([
                '0'
            ], array_keys(config('forms.Inquiries.replyStatusType')))),
            'staff_name' => 'max:10|picture_characters|special_characters',
            'body' => 'required|max:3000|picture_characters'
        ];
    }

    public function attributes()
    {
        return [
            'inquiry_id' => '問合せID',
            'status' => '対応状況',
            'reply_status' => '返信状況',
            'staff_name' => '担当者名',
            'body' => '返信内容'
        ];
    }

    public function customMessages()
    {
        return [
            'required_array'     => $this->MSG012,
            'in'                 => $this->MSG012,
            'picture_characters' => $this->MSG077,
            'special_characters' => $this->MSG232
        ];
    }

    protected function redirectRules()
    {
        return [
            'reply.all.store' => [
                'action' => 'reply.all.create'
            ]
        ];
    }
}
