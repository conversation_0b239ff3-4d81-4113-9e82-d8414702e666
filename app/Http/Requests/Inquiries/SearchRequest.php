<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;
use Validator;

/**
 * お問い合わせ：検索
 */
class SearchRequest extends Request
{
    protected $search = false;

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        if ($this->search) {

            Validator::extend('after_or_equal', function ($attribute, $value, $parameters, $validator) {
                $fromSendDate = $this->get('from_last_send_date');
                if (strtotime($value) >= strtotime($fromSendDate)) {
                    return true;
                }
                return false;
            });

            return [
                'from_last_send_date' => 'date|required',
                'to_last_send_date' => 'date|after_or_equal:from_last_send_date|required',
                'user_id' => 'integer'
            ];
        }
        return [];
    }

    public function attributes()
    {
        return [
            'from_last_send_date' => '開始日',
            'to_last_send_date' => '終了日',
            'user_id' => 'ユーザーID'
        ];
    }

    public function customMessages()
    {
        return [
            'date' => $this->MSG002,
            'to_last_send_date.after_or_equal' => $this->MSG005
        ];
    }

    /**
     * Get validator instance
     *
     * @return array
     */
    protected function getValidatorInstance()
    {
        if ($this->method() == 'GET' && $this->get('is_search')) {
            $this->search = true;
        }

        return parent::getValidatorInstance();
    }
}
