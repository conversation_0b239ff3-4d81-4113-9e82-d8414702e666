<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;

/**
 * お問い合わせ：対応状況更新
 */
class StatusUpdateRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'status' => 'in:' . implode(',', array_keys(config('forms.Inquiries.statusType')))
        ];
    }

    public function attributes()
    {
        return [
            'id' => '問合せID',
            'status' => '対応状況'
        ];
    }

    public function customMessages()
    {
        return [
            'in' => $this->MSG012
        ];
    }
}
