<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;
use App\Services\InquiriesService;
use Validator;

/**
 * お問い合わせ：カテゴリ更新
 */
class CategoryUpdateRequest extends Request
{
    protected $InquiriesService;

    public function __construct(InquiriesService $InquiriesService)
    {
        $this->InquiriesService = $InquiriesService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {

        Validator::extend('children', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                $not_set_large  = empty($value['large_id'])  || $value['large_id']  == 'not_set';
                $not_set_middle = empty($value['middle_id']) || $value['middle_id'] == 'not_set';
                $not_set_small  = empty($value['small_id'])  || $value['small_id']  == 'not_set';

                if ($not_set_large) {
                    //大カテゴリ未設定→中小も未設定かチェック
                    if ($not_set_middle && $not_set_small) return true;
                    return false;
                } else {
                    //設定した大カテゴリが存在するかチェック
                    $large_category = $this->InquiriesService->getCategory([
                        'app_id' => $value['app_id'],
                        'id' => $value['large_id'],
                        'relation' => 'large',
                    ]);
                    if ($large_category->isEmpty()) return false;
                }

                if ($not_set_middle) {
                    //中カテゴリ未設定→小も未設定かチェック
                    if ($not_set_small) return true;
                    return false;
                } else {
                    //設定した中カテゴリが存在するかチェック
                    $middle_category = $this->InquiriesService->getCategory([
                        'id' => $value['middle_id'],
                        'relation' => 'middle',
                        'parent_id' => $value['large_id'],
                    ]);
                    if ($middle_category->isEmpty()) return false;
                }

                if (!$not_set_small) {
                    //設定した小カテゴリが存在するかチェック
                    $small_category = $this->InquiriesService->getCategory([
                        'id' => $value['small_id'],
                        'relation' => 'small',
                        'parent_id' => $value['middle_id'],
                    ]);
                    if ($small_category->isEmpty()) return false;
                }
                return true;
            }
            return false;

        });

        return [
            'category' => 'children'
        ];
    }

    public function attributes()
    {
        return [
            'category' => 'カテゴリ'
        ];
    }

    public function customMessages()
    {
        return [
            'category.children' => $this->MSG264,
        ];
    }
}
