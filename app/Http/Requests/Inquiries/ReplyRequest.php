<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;

/**
 * お問い合わせ：返信
 */
class ReplyRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'staff_name' => 'max:10|picture_characters|special_characters',
            'body'       => 'required|max:3000|picture_characters|no_all_html_tag',
            'is_finish'  => 'boolean',
            'status'     => 'required|in:'.implode(',', array_merge(['0'], array_keys(config('forms.Inquiries.statusType')))),
            'reply_status' => 'required|in:'.implode(',', array_merge(['0'], array_keys(config('forms.Inquiries.replyStatusType')))),
        ];
    }

    public function attributes()
    {
        return [
            'staff_name' => '担当者名',
            'body' => '返信内容',
            'is_finish' => '返信確定後に返信済みにする',
            'select_status' => '対応状況',
            'reply_status' => '返信状況'
        ];
    }

    public function customMessages()
    {
        return [
            'picture_characters' => $this->MSG077,
            'special_characters' => $this->MSG232,
            'boolean'            => $this->MSG039,
            'in'                 => $this->MSG012,
        ];
    }

    protected function redirectRules()
    {
        return [
            'reply.store' => [
                'action' => 'reply.create',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
