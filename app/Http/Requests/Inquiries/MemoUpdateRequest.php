<?php
namespace App\Http\Requests\Inquiries;

use App\Http\Requests\Request;

/**
 * お問い合わせ：メモ更新
 */
class MemoUpdateRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'memo' => 'max:3000'
        ];
    }

    public function attributes()
    {
        return [
            'id' => '問合せID',
            'memo' => 'メモ'
        ];
    }
}
