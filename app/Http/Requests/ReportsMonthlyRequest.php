<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ReportsMonthlyService;
use Carbon\Carbon;
use Validator;

/**
 * レポート：月別レポート
 */
class ReportsMonthlyRequest extends Request
{
    protected $ReportsMonthlyService;

    public function __construct(ReportsMonthlyService $ReportsMonthlyService)
    {
        $this->ReportsMonthlyService = $ReportsMonthlyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // レポート共通バリデーション
        Validator::extend('aftermonth', function ($attribute, $value, $parameters) {
            $begin = request($parameters[0]);
            if (!empty($value) && !empty($begin)) {
                $begintime = strtotime($begin);
                $endtime   = strtotime($value);
                if ($begintime > $endtime) {
                    return false;
                }
            }
            return true;
        });

        $rules = [
            'begin'  => 'date',
            'end'    => 'date|aftermonth:begin',
            'app_id' => 'required|in:0,'.join(',', array_keys($this->ReportsMonthlyService->getAppTitleType())),
            'report' => 'required'
        ];
        // 利用状況レポート(定期購入)が選択されている場合のバリデーション
        if (Request::input('report') === 'Subscription_Usage_Report') {
            Validator::extend('appid_device_check', function ($attribute, $value, $parameters) {
                $device = Request::input('device');
                return (!empty($value) && !empty($device));
            });
            // 期間範囲チェック
            Validator::extend('is_range_less_than', function ($attribute, $value, $parameters, $validator) {
                $begin = $this->get($parameters[1]);
                $begin  = Carbon::parse($begin);
                $end    = Carbon::parse($value);
                $period = $end->diffInMonths($begin);
                if ($period < $parameters[0]) {
                    return true;
                }
                return false;
            });
            $maxMonthRange = config('forms.ReportsMonthly.subscriptionMaxMonthRange');
            Validator::replacer('is_range_less_than', function ($message, $attribute, $rule, $parameters) use ($maxMonthRange) {
                $replaceMaxAttribute = str_replace(':max', $maxMonthRange, $message);
                $start = $this->get($parameters[1]);
                return str_replace(':start', $start, $replaceMaxAttribute);
            });
            $rules['app_id'] = preg_replace('/\b(?:' . implode(array_keys(array_merge([config('forms.ReportsMonthly.reportGameAll')], config('forms.ReportsMonthly.reportDmmType'))), '|') . ')\b,?/', '', $rules['app_id']) . '|appid_device_check';
            $rules['begin'] = 'required|date';
            $rules['end'] = 'required|aftermonth:begin|is_range_less_than:' . $maxMonthRange . ',begin';
        }
        return $rules;
    }

    public function attributes()
    {
        return [
            'begin'  => '開始月',
            'end'    => '終了月',
            'app_id' => 'タイトル',
            'report' => '形式'
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.in'                 => $this->MSG012,
            'app_id.appid_device_check' => $this->MSG084,
            'begin.required'            => $this->MSG001,
            'begin.date'                => $this->MSG002,
            'end.required'              => $this->MSG001,
            'end.date'                  => $this->MSG002,
            'end.aftermonth'            => $this->MSG007,
            'date'                      => $this->MSG002,
            'is_range_less_than'        => $this->MSG334,            
        ];
    }
}
