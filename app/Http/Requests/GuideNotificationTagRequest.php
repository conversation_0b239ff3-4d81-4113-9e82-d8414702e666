<?php

namespace App\Http\Requests;

use App\Services\GuideNotificationService;
use Validator;

class GuideNotificationTagRequest extends Request
{
    protected $guideNotificationService;

    public function __construct(GuideNotificationService $guideNotificationService)
    {
        $this->guideNotificationService = $guideNotificationService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $guideAppId = $this->get('guideAppId');
        $id = $this->get('id');

        Validator::extend(
            'tag_name_unique',
            function ($attribute, $value, $parameters, $validator) use ($guideAppId, $id) {
                return $this->guideNotificationService->isUniqueTagName($guideAppId, $value, $id);
            }
        );

        Validator::extend(
            'tag_key_unique',
            function ($attribute, $value, $parameters, $validator) use ($guideAppId, $id) {
                return $this->guideNotificationService->isUniqueTagKey($guideAppId, $value, $id);
            }
        );

        $rules = [
            'name' => [
                'required',
                'max:100',
                'tag_name_unique:' . $guideAppId,
            ],
            'tag_key' => [
                'required',
                'max:100',
                'alfa_num_line_dot_only',
                'tag_key_unique:' . $guideAppId,
            ],
            'order_no' => [
                'required',
            ],
        ];
        return $rules;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        $attributes = [
            'name' => 'タグ名',
            'tag_key' => 'タグキー',
            'order_no' => '表示順',
        ];  
        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $message = [
            'name.tag_name_unique' => $this->MSG306,
            'tag_key.tag_key_unique' => $this->MSG306,
            'tag_key.alfa_num_line_dot_only' => $this->MSG308,
        ];
        return $message;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'noticid' => 'id',
                ],
            ]
        ];
    }
}
