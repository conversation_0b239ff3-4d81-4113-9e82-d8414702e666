<?php
namespace App\Http\Requests\Informations;
use App\Http\Requests\Request;
use Validator;

/**
 * お知らせ一覧：検索
 */
class SearchInfoRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $maxKeyword = 'max:'.config('forms.Informations.keywordLimit.length');

        //----------
        // word_count
        //----------
        Validator::extend(
            'word_count',
            function ($attribute, $value, $parameters, $validator) {
                // キーワードが許容数以下か判定
                $value = preg_replace('/　/', ' ', $value);
                $value = trim($value);
                $value = preg_replace('/\s+/', ' ', $value);

                if ( substr_count($value, ' ') < config('forms.Informations.keywordLimit.split') ) {
                    return true;
                } else {
                    return false;
                }
            }
        );

        return [
            'keyword' => 'platform_dependent|picture_characters|special_characters|word_count|'.$maxKeyword
        ];
    }

    public function attributes()
    {
        Validator::replacer('word_count', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':number', config('forms.Informations.keywordLimit.split'), $message);
        });
    
        return [
            'keyword' => '検索キーワード'
        ];
    }

    public function customMessages()
    {
        return [
            'keyword.max'                => $this->MSG021,
            'keyword.platform_dependent' => $this->MSG076,
            'keyword.picture_characters' => $this->MSG077,
            'keyword.special_characters' => $this->MSG232,
            'keyword.word_count'         => $this->MSG267
        ];
    }
}
