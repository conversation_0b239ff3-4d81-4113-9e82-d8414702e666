<?php

namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ApplyNotificationService;
use Illuminate\Http\JsonResponse;
use Validator;

class ApplyNotificationRequest extends Request
{

    /** @var ApplyNotificationService */
    protected $applyNotificationService;


    public function __construct(ApplyNotificationService $applyNotificationService)
    {
        $this->applyNotificationService = $applyNotificationService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {

        // 改行区切りのメールアドレス一覧のバリデーション
        Validator::extend('list_email', function ($attribute, $value, $parameters, $validator) {
            $addressList = array_filter(preg_split("/\r\n|\n|\r/", $value), 'strlen');
            foreach ($addressList as $address) {
                if(filter_var($address, FILTER_VALIDATE_EMAIL) === false){
                    return false;
                }
            }
            return true;
        });

        Validator::extend('list_max', function ($attribute, $value, $parameters, $validator) {
            $addressList = array_filter(preg_split("/\r\n|\n|\r/", $value), 'strlen');
            foreach ($addressList as $address) {
                if(strlen($address) > $parameters[0]){
                    return false;
                }
            }
            return true;
        });

        Validator::replacer('list_max', function ($message, $attribute, $rule, $parameters) {
            return str_replace([':max'], [$parameters[0]], $message);
        });

        return [
            
            /*
            email　：メールアドレス形式
            regex:/^[\x01-\x7E]+$/　 ：ASCII文字のみ許可
            */
            'addressList' => 'required|list_email|list_max:255'
        ];
    }

    public function attributes ()
    {
        return [
            'addressList' => '通知メールアドレス',
        ];
    }

    public function customMessages()
    {
        return [
            'addressList.list_email' => $this->MSG245,
            'addressList.list_max' => $this->MSG021,
        ];
    }

    /**
     * バリデーションエラー時のレスポンスを返す
     *
     * @param array $errors
     * @return JsonResponse
     */
    public function response(array $errors)
    {
        $response = [
            'status' => 422,
            'message' => 'Bad Request',
            'errors'  => $errors,
        ];
        return new JsonResponse( $response, 422 );
    }

    /**
     * リクエストのルート名を取得する
     *
     * @return void
     */
    public function getRouteName(){
        $routeName = $this->route()->getName();
        return $routeName;
    }
}