<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use App\Models\Freegame\User;
use App\Services\SubscriptionService;
use Validator;

class SubscriptionPaymentLogCsvDownloadRequest  extends Request
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // アプリとデバイスの組み合わせチェック
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return $this->subscriptionService->hasApplicationDevice($value, $device);
        });
        // 権限があるアプリケーションチェック
        Validator::extend('developer_check', function ($attribute, $value, $parameters) {
            return $this->subscriptionService->isDeveloperApplication($value);
        });
        Validator::extend('after_or_equal', function ($attribute, $value, $parameters, $validator) {
            if (strtotime($value) >= strtotime($parameters[0])) {
                return true;
            }
            return false;
        });
        // games_idチェック
        Validator::extend('check_games_id', function ($attribute, $value, $parameters, $validator) {
            $freeGame = new User;
            $user = $freeGame->getOne($value);
            return empty($user) ? false : true;
        });
        
        return [
            'app_id'            => 'required|device_check|developer_check',
            'request_date_from' => 'required|max:10|date',
            'request_date_to'   => 'required|max:10|date|after_or_equal:request_date_from',
            'games_id'          => 'required|restricted_characters|check_games_id',
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'app_id'            => 'タイトル',
            'request_date_from' => '期間の開始日',
            'request_date_to'   => '期間の終了日',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.required'                  => $this->MSG241,
            'app_id.device_check'              => $this->MSG241,
            'app_id.developer_check'           => $this->MSG241,
            'request_date_to.after'            => $this->MSG265,
            'games_id.restricted_characters'   => $this->MSG295,
            'games_id.check_games_id'          => $this->MSG011,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'csvDownload' => [
                'action' => 'index',
                'parameters' => [
                    'app_id'            => 'app_id',
                    'device'            => 'device',
                    'request_date_to'   => 'request_date_to',
                    'request_date_from' => 'request_date_from',
                    'games_id'          => 'games_id',
                ],
            ],
        ];
    }
}
