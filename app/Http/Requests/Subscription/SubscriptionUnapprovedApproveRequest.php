<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use App\Services\SubscriptionService;
use App\Services\SubscriptionUnapprovedListService;
use Validator;

class SubscriptionUnapprovedApproveRequest  extends Request
{
    protected $subscriptionService;
    protected $subscriptionUnapprovedListService;

    public function __construct(
        SubscriptionService $subscriptionService,
        SubscriptionUnapprovedListService $subscriptionUnapprovedListService
    )
    {
        $this->subscriptionService = $subscriptionService;
        $this->subscriptionUnapprovedListService = $subscriptionUnapprovedListService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // アプリとデバイスの組み合わせチェック
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return $this->subscriptionService->hasApplicationDevice($value, $device);
        });
        // 権限があるアプリケーションチェック
        Validator::extend('developer_check', function ($attribute, $value, $parameters) {
            return $this->subscriptionService->isDeveloperApplication($value);
        });
        // 未承認チェック
        Validator::extend('approved_check', function ($attribute, $value, $parameters) {
            // 承認済ならエラー
            return $this->subscriptionUnapprovedListService->isApproved(request()->all()) ? false : true;
        });
        // order_idのフォーマットチェック
        Validator::extend('order_id_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.SubscriptionUnapprovedList.constraints.orderId.regex'), $value);
        });
        // open_idのフォーマットチェック
        Validator::extend('open_id_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.SubscriptionUnapprovedList.constraints.openId.regex'), $value);
        });

        return [
            'app_id'   => 'required|device_check|developer_check|approved_check',
            'order_id' => 'required|order_id_check',
            'open_id'  => 'required|open_id_check',
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'app_id'   => 'タイトル',
            'order_id' => 'order_id',
            'open_id'  => 'open_id',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.required'         => $this->MSG241,
            'app_id.device_check'     => $this->MSG241,
            'app_id.developer_check'  => $this->MSG241,
            'app_id.approved_check'   => $this->MSG331,
            'order_id.order_id_check' => $this->MSG011,
            'open_id.open_id_check'   => $this->MSG011,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'index' => [
                'action' => 'index',
                'parameters'   => [
                    'app_id'   => 'app_id',
                    'device'   => 'device',
                    'order_id' => 'order_id',
                ],
            ],
        ];
    }
}
