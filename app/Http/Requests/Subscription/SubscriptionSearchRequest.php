<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use App\Services\SubscriptionService;
use Validator;

class SubscriptionSearchRequest extends Request
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // 左メニューから遷移してきた場合、バリデーションを行わない
        if (!$this->get('is_search')) {
            return [];
        }
        // 登録済みデバイス情報の存在チェック for app_id
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return $this->subscriptionService->hasApplicationDevice($value, $device);
        });

        $validate = [
            'app_id' => 'required|device_check',
        ];

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'app_id' => 'タイトル',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.device_check' => $this->MSG241,
        ];
    }
}
