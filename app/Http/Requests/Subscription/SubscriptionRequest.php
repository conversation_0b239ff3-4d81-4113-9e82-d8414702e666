<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use App\Services\SubscriptionService;
use Validator;

class SubscriptionRequest extends Request
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // アプリとデバイスの組み合わせチェック
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return $this->subscriptionService->hasApplicationDevice($value, $device);
        });
        // 権限があるアプリケーションチェック
        Validator::extend('developer_check', function ($attribute, $value, $parameters) {
            return $this->subscriptionService->isDeveloperApplication($value);
        });
        // 定期購入ID、基本プランIDに使用可能な文字列「半角英数字」「.」「_」の文字列バリデーション
        Validator::extend('alpha_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.Subscription.constraints.sku.regex'), $value);
        });
        // Maxバイト数チェック
        Validator::extend('max_byte_check', function ($attribute, $value, $parameters) {
            return strlen($value) <= $parameters[0];
        });
        // Maxバイト数チェックのメッセージ置換
        Validator::replacer('max_byte_check', function ($messages, $attribute, $rule, $parameters) {
            return str_replace(':max_byte', $parameters[0], $messages);
        });

        return [
            'app_id'        => 'required|device_check|developer_check',
            'sku'           => 'required|alpha_check|min:' . config('forms.Subscription.constraints.sku.minLength') . '|max:' . config('forms.Subscription.constraints.sku.maxLength'),
            'title'         => 'required|max_byte_check:' . config('forms.Subscription.constraints.title.maxLength') . '|restricted_characters',
            'description'   => 'max_byte_check:' . config('forms.Subscription.constraints.description.maxLength') . '|restricted_characters',
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'app_id'        => 'タイトル',
            'sku'           => 'アイテムID',
            'title'         => '商品名',
            'description'   => '内部説明',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.required'                   => $this->MSG241,
            'app_id.device_check'               => $this->MSG241,
            'app_id.developer_check'            => $this->MSG241,
            'sku.alpha_check'                   => $this->MSG329,
            'sku.min'                           => $this->MSG222,
            'sku.max'                           => $this->MSG021,
            'title.restricted_characters'       => $this->MSG295,
            'description.restricted_characters' => $this->MSG295,
            'platform_dependent'                => $this->MSG076,
            'max_byte_check'                    => $this->MSG330,
        ];
    }
}
