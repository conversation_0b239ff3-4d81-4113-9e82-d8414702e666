<?php
namespace App\Http\Requests\Subscription;

use App\Services\SubscriptionService;
use App\Http\Requests\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 定期購入詳細・基本プラン・特典一覧
 */
class DetailRequest extends Request
{
    /**
     * @var SubscriptionService
     */
    protected $subscriptionService;

    /**
     * コンストラクタ
     * 
     * @param SubscriptionService $subscriptionService
     */
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 定期購入ID、基本プランIDに使用可能な文字列「半角英数字」「.」「_」の文字列バリデーション
        Validator::extend('alpha_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.Subscription.constraints.sku.regex'), $value);
        });

        return [
            'app_id'  => 'required|numeric',
            'device'  => 'required|in:' . join(',', array_keys(config('forms.Subscription.device'))),
            'subsSku' => 'required|alpha_check|min:' . config('forms.Subscription.constraints.sku.minLength') . '|max:' . config('forms.Subscription.constraints.sku.maxLength'),
        ];
    }

    public function attributes()
    {
        return [
            'app_id'  => 'アプリID',
            'device'  => 'デバイス',
            'subsSku' => '定期購入ID',
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.required'     => $this->MSG012,
            'app_id.numeric'      => $this->MSG031,
            'device.required'     => $this->MSG012,
            'device.in'           => $this->MSG011,
            'subsSku.required'    => $this->MSG012,
            'subsSku.alpha_check' => $this->MSG329,
            'subsSku.min'         => $this->MSG222,
            'subsSku.max'         => $this->MSG021,
        ];
    }
}
