<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use App\Services\SubscriptionService;
use Validator;

class SubscriptionUnapprovedDetailRequest  extends Request
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // アプリとデバイスの組み合わせチェック
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return $this->subscriptionService->hasApplicationDevice($value, $device);
        });
        // 権限があるアプリケーションチェック
        Validator::extend('developer_check', function ($attribute, $value, $parameters) {
            return $this->subscriptionService->isDeveloperApplication($value);
        });
        // order_idのフォーマットチェック
        Validator::extend('order_id_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.SubscriptionUnapprovedList.constraints.orderId.regex'), $value);
        });
    
        $validate = [
            'app_id'   => 'required|device_check|developer_check',
            'order_id' => 'required|order_id_check',
        ];

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'app_id'   => 'タイトル',
            'order_id' => 'order_id',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.required'                 => $this->MSG241,
            'app_id.device_check'             => $this->MSG241,
            'app_id.developer_check'          => $this->MSG241,
            'order_id.required'               => $this->MSG012,
            'order_id.order_id_check'         => $this->MSG011,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'index' => [
                'action' => 'index',
                'parameters' => [
                    'app_id'   => 'app_id',
                    'device'   => 'device',
                    'order_id' => 'order_id',
                ]
            ],
        ];
    }
}
