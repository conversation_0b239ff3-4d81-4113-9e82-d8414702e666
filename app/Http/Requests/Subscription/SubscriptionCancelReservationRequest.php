<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;

/**
 * 定期購入：解約予約
 */
class SubscriptionCancelReservationRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        if ($this->method() == 'GET') {
            return [];
        }

        return [
            'app_id' => 'required|not_in:0',
            'id'     => 'required|numeric',
        ];
    }

    public function attributes()
    {
        return [
            'app_id' => 'タイトル',
            'id'     => 'ID',
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.required' => $this->MSG241,
            'app_id.not_in'   => $this->MSG241,
            'id.required'     => $this->MSG241,
            'id.numeric'      => $this->MSG241,
        ];
    }
}
