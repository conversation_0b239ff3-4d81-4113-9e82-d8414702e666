<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use App\Models\Freegame\User;
use App\Services\SubscriptionService;
use Validator;

class SubscriptionUnapprovedListSearchRequest  extends Request
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        if ($this->method() == 'GET' || request()->has('search')) {
            return [];
        }
        // アプリとデバイスの組み合わせチェック
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return $this->subscriptionService->hasApplicationDevice($value, $device);
        });
        // 権限があるアプリケーションチェック
        Validator::extend('developer_check', function ($attribute, $value, $parameters) {
            return $this->subscriptionService->isDeveloperApplication($value);
        });
        // order_idのフォーマットチェック
        Validator::extend('order_id_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.SubscriptionUnapprovedList.constraints.orderId.regex'), $value);
        });
        // games_idチェック
        Validator::extend('check_games_id', function ($attribute, $value, $parameters, $validator) {
            $freeGame = new User;
            $user = $freeGame->getOne($value);
            return empty($user) ? false : true;
        });
    
        $validate = [
            'app_id'   => 'required|device_check|developer_check',
            'order_id' => 'required_without:games_id|order_id_check',
            'games_id' => 'required_without:order_id|restricted_characters|check_games_id',
        ];

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'app_id'        => 'タイトル',
            'order_id'      => 'order_id',
            'games_id'      => 'games_id',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.required'                 => $this->MSG241,
            'app_id.device_check'             => $this->MSG241,
            'app_id.developer_check'          => $this->MSG241,
            'order_id.required'               => $this->MSG012,
            'order_id.order_id_check'         => $this->MSG011,
            'games_id.required'               => $this->MSG012,
            'games_id.restricted_characters'  => $this->MSG295,
            'games_id.check_games_id'         => $this->MSG011,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'index' => [
                'action' => 'index',
                'parameters' => [
                    'app_id'    => 'app_id',
                    'device'    => 'device',
                    'games_id'  => 'games_id',
                    'order_id'  => 'order_id',
                ],
            ],
        ];
    }
}
