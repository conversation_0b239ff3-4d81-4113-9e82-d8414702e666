<?php
namespace App\Http\Requests\Subscription;

use App\Http\Requests\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 定期購入：問い合わせ
 */
class SubscriptionInquiryRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('appid_device_check', function ($attribute, $value, $parameters) {
            $device = Request::input('device');
            return (!empty($value) && !empty($device));
        });
        // order_idのフォーマットチェック
        Validator::extend('order_id_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.SubscriptionInquiry.constraints.orderId.regex'), $value);
        });
        // inquiry_idのフォーマットチェック
        Validator::extend('inquiry_id_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.SubscriptionInquiry.constraints.inquiryId.regex'), $value);
        });

        return [
            'app_id' => 'required|appid_device_check',
            'order_id' => 'required_without:inquiry_id|order_id_check',
            'inquiry_id' => 'required_without:order_id|inquiry_id_check',
        ];
    }

    public function attributes()
    {
        return [
            'app_id'     => 'タイトル',
            'order_id'   => 'order_id',
            'inquiry_id' => '定期購入問い合わせID',
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.required'             => $this->MSG241,
            'app_id.appid_device_check'   => $this->MSG241,
            'order_id.order_id_check'     => $this->MSG011,
            'inquiry_id.inquiry_id_check' => $this->MSG011,
        ];
    }
}
