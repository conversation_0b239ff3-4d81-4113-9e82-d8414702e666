<?php
namespace App\Http\Requests\Subscription;

use App\Services\SubscriptionService;
use App\Http\Requests\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 定期購入：基本プラン登録
 */
class BasePlanRegistrationRequest extends Request
{
    /**
     * @var SubscriptionService
     */
    protected $subscriptionService;

    /**
     * コンストラクタ
     * 
     * @param SubscriptionService $subscriptionService
     */
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 登録済みデバイスか判定を行う
        Validator::extend('device_check', function ($attribute, $value, $parameters) {
            $appId = Request::input('app_id');
            return $this->subscriptionService->hasApplicationDevice($appId, $value);
        });
        // 定期購入ID、基本プランIDに使用可能な文字列「半角英数字」「.」「_」の文字列バリデーション
        Validator::extend('alpha_check', function ($attribute, $value, $parameters) {
            return preg_match(config('forms.Subscription.constraints.sku.regex'), $value);
        });
        // 権限があるアプリケーションチェック
        Validator::extend('developer_check', function ($attribute, $value, $parameters) {
            return $this->subscriptionService->isDeveloperApplication($value);
        });

        return [
            'app_id'   => 'required|numeric|developer_check',
            'device'   => 'required|in:' . join(',', array_keys(config('forms.Subscription.device'))) . '|device_check',
            'subsSku'  => 'required|alpha_check|min:' . config('forms.Subscription.constraints.sku.minLength') . '|max:' . config('forms.Subscription.constraints.sku.maxLength'),
            'planSku'  => 'required|alpha_check|min:' . config('forms.Subscription.constraints.sku.minLength') . '|max:' . config('forms.Subscription.constraints.sku.maxLength'),
            'status'   => 'required|in:' . join(',', array_keys(config('forms.Subscription.status.list'))),
            'price'    => 'required|numeric|integer|between:' . config('forms.Subscription.constraints.basePlanrPrice.min') . ',' . config('forms.Subscription.constraints.basePlanrPrice.max'),
            'duration' => 'required|in:' . join(',', array_keys(config('forms.Subscription.duration'))),
        ];
    }

    public function attributes()
    {
        return [
            'app_id'   => 'アプリID',
            'device'   => 'デバイス',
            'subsSku'  => '定期購入ID',
            'planSku'  => '基本プランID',
            'status'   => 'ステータス',
            'price'    => '価格',
            'duration' => '請求対象期間',
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.required'        => $this->MSG241,
            'app_id.numeric'         => $this->MSG031,
            'app_id.developer_check' => $this->MSG084,
            'device.required'        => $this->MSG241,
            'device.in'              => $this->MSG241,
            'device.device_check'    => $this->MSG084,
            'subsSku.required'       => $this->MSG012,
            'subsSku.alpha_check'    => $this->MSG329,
            'subsSku.min'            => $this->MSG222,
            'subsSku.max'            => $this->MSG021,
            'planSku.required'       => $this->MSG012,
            'planSku.alpha_check'    => $this->MSG329,
            'planSku.min'            => $this->MSG222,
            'planSku.max'            => $this->MSG021,
            'status.required'        => $this->MSG241,
            'status.in'              => $this->MSG241,
            'price.required'         => $this->MSG012,
            'price.numeric'          => $this->MSG242,
            'price.integer'          => $this->MSG242,
            'price.between'          => $this->MSG332,
            'duration.required'      => $this->MSG241,
            'duration.in'            => $this->MSG241,
        ];
    }
}
