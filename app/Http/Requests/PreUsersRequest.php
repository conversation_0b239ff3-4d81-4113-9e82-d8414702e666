<?php
namespace App\Http\Requests;

use App\Services\PreUsersService;
use Validator;

class PreUsersRequest extends Request
{
    private $preUsersService;

    public function __construct(
        PreUsersService $gameApplyService,
        array $query = array(),
        array $request = array(),
        array $attributes = array(),
        array $cookies = array(),
        array $files = array(),
        array $server = array(),
        $content = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
        $this->preUsersService = $gameApplyService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     * @return array
     */
    public function rules()
    {
        // 有効ユーザチェック
        Validator::extend('is_existing_user', function ($attribute, $value, $parameters, $validator) {
            return $this->preUsersService->isExistingUser($this->all());
        });

        // 優待ユーザチェック
        Validator::extend('is_courtesy_user', function ($attribute, $value, $parameters, $validator) {
            return $this->preUsersService->isCourtesyUser($this->all());
        });

        // 設定済み重複チェック
        Validator::extend('is_duplication_app', function ($attribute, $value, $parameters, $validator) {
            return $this->preUsersService->isDuplicationApp($this->all());
        });

        $rules = [
                'user_id' => 'required|numeric|is_existing_user|is_courtesy_user',
                'app_id'  => 'required|numeric|is_duplication_app',
        ];
        return $rules;
    }

    /**
     * Custom attributes list
     * @return array
     */
    public function attributes()
    {
        $attr  = [
                'user_id' => 'GAMES ID',
                'app_id'  => 'タイトル',
        ];
        return $attr;
    }

    /**
     * Custom validation message
     * @return array
     */
    public function customMessages()
    {
        $msg   = [
                'user_id.is_existing_user'  => $this->MSG078,
                'user_id.is_courtesy_user'  => $this->MSG251,
                'user_id.numeric'           => $this->MSG031,
                'app_id.is_duplication_app' => $this->MSG181,
                'app_id.numeric'            => $this->MSG031,
        ];
        return $msg;
    }

    /**
     * Redirect rules.
     * @return array
     */
    protected function redirectRules()
    {
        $rules = [
                'createStore'    => [
                        'action' => 'create',
                ],
        ];
        return $rules;
    }
}
