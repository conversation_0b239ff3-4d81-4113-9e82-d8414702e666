<?php

namespace App\Http\Requests;

use Validator;
use App\Models\FreegameGuide\GuideNotificationCategory;
use App\Models\FreegameGuide\GuideNotification;

class GuideNotificationRequest extends Request
{

    protected $guideNoticCategory;
    protected $guideNotic;

    public function __construct(
        GuideNotificationCategory $guideNoticCategory,
        GuideNotification $guideNotic
    ) {
        $this->guideNoticCategory = $guideNoticCategory;
        $this->guideNotic = $guideNotic;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        //----------
        // check_is_category
        //----------
        Validator::extend('check_is_category', function ($attribute, $value, $parameters) {
            if (empty($parameters[0])) {
                return false;
            }
            $guideAppId = $parameters[0];
            $guideNoticCategory = $this->guideNoticCategory->getOneById($value, $guideAppId);
            if (empty($guideNoticCategory)) {
                return false;
            }
            return true;
        });
        //----------

        //----------
        // check_is_priority
        //----------
        Validator::extend('check_is_priority', function ($attribute, $value, $parameters) {
            if (empty($parameters[0])) {
                return false;
            }
            return true;
        });
        //----------

        $validate = [
            'title'                          => 'required|script_tag',
            'guide_notification_category_id' => 'required|numeric|check_is_category:'.$this->get('guideAppId'),
            'is_priority'                    => 'check_is_priority:'.$this->get('guideAppId').','.$this->get('id'),
            'body'                           => 'required|script_tag',
            'device'                         => 'required',
            'published_datetime'             => 'required|date',
            'top_image.name'                  => ['regex:/(\.|\/)(gif|jpe?g|png)$/i'],
        ];

        $images = $this->get('images');
        if ($images) {
            foreach ($images as $intKey => $val) {
                $validateIndex = 'images.' . $intKey . '.image';
                $validate[$validateIndex] = ['regex:/(\.|\/)(gif|jpe?g|png)$/i'];
            }
        }

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        Validator::replacer('regex', function ($message, $attribute, $rule, $parameters) {
            $message = str_replace(':extension', 'gif、jpg、png', $message);

            if ($attribute == 'top_image.name') {
                $message = str_replace(
                    'ファイルの拡張子は',
                    'トップ表示用画像のファイルの拡張子は',
                    $message
                );
            } else {
                $images = $this->get('images');
                $id = explode('.', $attribute)[1];
                if (isset($images[$id])) {
                    $attribute = '画像「' . $images[$id]['image'] . '」';
                    $message = str_replace(
                        'ファイルの拡張子は',
                        $attribute . 'のファイルの拡張子は使えません。画像には',
                        $message
                    );
                }
            }

            return $message;
        });
        $attributes = [
            'title'                          => 'お知らせタイトル',
            'guide_notification_category_id' => 'カテゴリ',
            'is_priority'                    => 'トップ画面に表示',
            'body'                           => '本文',
            'device'                         => '掲載ページ',
            'published_datetime'             => '掲載日時',
        ];

        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $message = [
            'title.in'                                         => $this->MSG012,
            'title.script_tag'                                 => $this->MSG239,
            'guide_notification_category_id.in'                => $this->MSG241,
            'guide_notification_category_id.numeric'           => $this->MSG031,
            'guide_notification_category_id.check_is_category' => $this->MSG241,
            'is_priority.check_is_priority'                    => $this->MSG012,
            'body.in'                                          => $this->MSG012,
            'body.script_tag'                                  => $this->MSG239,
            'device.in'                                        => $this->MSG241,
            'published_datetime.in'                            => $this->MSG012,
            'published_datetime.date'                          => $this->MSG002,
            'top_image.name.regex'                              => $this->MSG237,
        ];
        $images = $this->get('images');
        if ($images) {
            foreach ($images as $intKey => $val) {
                $msgIndex = 'images.' . $intKey . '.image';
                $message[$msgIndex . '.regex'] = $this->MSG237;
            }
        }
        return $message;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'noticid' => 'id',
                ],
            ]
        ];
    }
}
