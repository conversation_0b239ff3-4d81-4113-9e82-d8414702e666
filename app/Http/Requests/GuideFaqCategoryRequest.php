<?php

namespace App\Http\Requests;

use Validator;
use App\Models\FreegameGuide\GuideFaq;

class GuideFaqCategoryRequest extends Request
{
    protected $guideFaqModels;

    public function __construct(GuideFaq $guideFaqModels) {
        $this->guideFaqModels = $guideFaqModels;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        //----------
        // check_faq_join
        //----------
        // 紐付いているデータがないかのチェック
        Validator::extend('check_faq_join', function ($attribute, $value, $parameters) {
            if (empty($parameters[0])) {
                return false;
            }
            $guideAppId = $parameters[0];
            $countFaq = $this->guideFaqModels->getCountByGuideAppIdAndCategoryId($guideAppId, $value);
            if ($countFaq === 0) {
                return true;
            }
            return false;
        });
        //----------


        $validate = array();
        if (strcmp($this->path(), 'guide_faq_category/destroy') === 0) {
            $validate = [
                'faqcatid' => 'required|check_faq_join:' . $this->get('guideAppId'),
            ];
        } else {
            $validate = [
                'name'     => 'required|max:20',
            ];
        }

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        Validator::replacer('check_faq_join', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':other', 'FAQ', $message);
        });

        return [
            'name'     => 'カテゴリ名',
            'faqcatid' => 'このカテゴリ',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'name.in'                 => $this->MSG012,
            'name.max'                => $this->MSG021,
            'faqcatid.check_faq_join' => $this->MSG260,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'faqcatid' => 'id',
                ],
            ]
        ];
    }
}
