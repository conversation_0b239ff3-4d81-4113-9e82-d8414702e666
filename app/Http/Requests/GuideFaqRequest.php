<?php
namespace App\Http\Requests;

use Validator;
use App\Models\FreegameGuide\GuideFaqCategory;

class GuideFaqRequest extends Request
{

    protected $guideFaqCategory;

    public function __construct(GuideFaqCategory $guideFaqCategory) {
        $this->guideFaqCategory = $guideFaqCategory;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        //----------
        // check_is_category
        //----------
        Validator::extend('check_is_category', function ($attribute, $value, $parameters) {
            if (empty($parameters[0])) {
                return false;
            }
            $guideAppId = $parameters[0];
            $guideFaqCategory = $this->guideFaqCategory->getOneById($value, $guideAppId);
            if (empty($guideFaqCategory)) {
                return false;
            }
            return true;
        });
        //----------

        $validate = [
            'question'              => 'required|max:255|script_tag',
            'guide_faq_category_id' => 'required|numeric|check_is_category:'.$this->get('guideAppId'),
            'answer'                => 'required|script_tag',
        ];
        $images = $this->get('images');
        if ($images) {
            foreach ($images as $intKey => $val) {
                $validateIndex = 'images.' . $intKey . '.image';
                $validate[$validateIndex] = ['regex:/(\.|\/)(gif|jpe?g|png)$/i'];
            }
        }

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        Validator::replacer('regex', function ($message, $attribute, $rule, $parameters) {
            $message = str_replace(':extension', 'gif、jpg、png', $message);
            $arrAttribute = explode('.', $attribute);
            if (isset($arrAttribute[1])) {
                $attribute = ((int)$arrAttribute[1] + 1) . 'つ目の画像';
                $message = str_replace(
                    'ファイルの拡張子は',
                    $attribute . 'のファイルの拡張子は',
                    $message
                );
            }
            return $message;
        });
        $attributes = [
            'question'              => '質問',
            'guide_faq_category_id' => 'カテゴリ',
            'answer'                => '答え',
        ];

        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $message = [
            'question.in'                             => $this->MSG012,
            'question.max'                            => $this->MSG021,
            'question.script_tag'                     => $this->MSG239,
            'guide_faq_category_id.in'                => $this->MSG241,
            'guide_faq_category_id.numeric'           => $this->MSG031,
            'guide_faq_category_id.check_is_category' => $this->MSG241,
            'answer.in'                               => $this->MSG012,
            'answer.script_tag'                       => $this->MSG239,
        ];
        $images = $this->get('images');
        if ($images) {
            foreach ($images as $intKey => $val) {
                $msgIndex = 'images.' . $intKey . '.image';
                $message[$msgIndex . '.regex'] = $this->MSG237;
            }
        }
        return $message;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'faqid' => 'id',
                ],
            ]
        ];
    }
}
