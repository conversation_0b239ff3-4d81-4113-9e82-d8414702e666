<?php

namespace App\Http\Requests;

use Validator;

class GuidePreregistrationRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $validate = [
            'start_at' => 'required|date',
            'end_at'   => 'required|date|after:start_at',
        ];
        if (strpos($this->path(), 'guide_preregistration/twitterapi') !== false) {
            $validate['is_use_twitter_application'] = 'required';
            $validate['twitter_api_key']            = 'required|single';
            $validate['twitter_secret_key']         = 'required|single';
            $validate['owner']                      = 'required|single';
            $validate['owner_id']                   = 'required|single';
        } elseif (strpos($this->path(), 'guide_preregistration/usercondition') !== false) {
            $validate['is_regist'] = 'required';
        }

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'start_at'                   => '開始日',
            'end_at'                     => '終了日',
            'is_use_twitter_application' => '設定',
            'twitter_api_key'            => 'API KEY',
            'twitter_secret_key'         => 'API SECRET',
            'owner'                      => 'Owner',
            'owner_id'                   => 'Owner ID',
            'is_regist'                  => '設定',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'start_at.in'                   => $this->MSG012,
            'start_at.date'                 => $this->MSG002,
            'end_at.in'                     => $this->MSG012,
            'end_at.date'                   => $this->MSG002,
            'end_at.after'                  => $this->MSG007,
            'is_use_twitter_application.in' => $this->MSG012,
            'twitter_api_key.in'            => $this->MSG012,
            'twitter_api_key.single'        => $this->MSG095,
            'twitter_secret_key.in'         => $this->MSG012,
            'twitter_secret_key.single'     => $this->MSG095,
            'owner.in'                      => $this->MSG012,
            'owner.single'                  => $this->MSG095,
            'owner_id.in'                   => $this->MSG012,
            'owner_id.single'               => $this->MSG095,
            'is_regist.in'                  => $this->MSG012,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'twitterapiupdate' => [
                'action' => 'twitterapiedit',
                'parameters' => [
                    'id' => 'id',
                ],
            ],
            'userconditionupdate' => [
                'action' => 'userconditionedit',
                'parameters' => [
                    'id' => 'id',
                ],
            ]
        ];
    }
}
