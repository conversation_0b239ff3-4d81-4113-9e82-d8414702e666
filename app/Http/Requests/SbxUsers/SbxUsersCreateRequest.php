<?php
namespace App\Http\Requests\SbxUsers;

use App\Http\Requests\Request;
use App\Services\SbxUsersService;
use Validator;

class SbxUsersCreateRequest extends Request
{
    protected $sbxUserService;

    public function __construct(SbxUsersService $sbxUsersService)
    {
        $this->sbxUsersService = $sbxUsersService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // cl_uidがcl_userテーブルに未登録か確認
        Validator::extend('check_cl_uid_exists', function ($attribute, $value, $parameters, $validator) {
            return $this->sbxUsersService->isNotExists($value);
        });

        // cl_uidがnetgame_cstool.test_accountに存在するか確認
        Validator::extend('check_test_account_exists', function ($attribute, $value, $parameters, $validator) {
            return $this->sbxUsersService->isTestAccountExists($value);
        });

        // 作成数が１の場合は最大12文字。２以上の場合は末尾に追加する文字列（_99）分短くした9文字とする。
        $nicknameMax = (intval($this->get('create_count')) <= 1) ? 12 : 9;

        $validate = [
            'password'      => 'required|alfa_num_line_only|min:4',
            'password2'     => 'required|same:password|alfa_num_line_only',
            'grade'         => 'required',
            'nickname'      => 'required|max:'.$nicknameMax.'|platform_dependent|picture_characters',
            'gender'        => 'required',
            'birth'         => 'required|date',
            'blood'         => 'required|in:' . implode(',', array_keys(config('forms.SbxUsers.blood'))),
            'point'         => 'required|in:' . implode(',', array_keys(config('forms.SbxUsers.point'))),
            'cl_uid'        => 'numeric|max:**********|check_cl_uid_exists|check_test_account_exists',
            'create_count'  => 'numeric|max:'.config('forms.SbxUsers.createCountMax'),
        ];

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'password'      => 'パスワード',
            'password2'     => 'パスワード確認',
            'grade'         => 'アカウント種別',
            'nickname'      => 'ニックネーム',
            'gender'        => '性別',
            'birth'         => '生年月日',
            'blood'         => '血液型',
            'point'         => 'ポイント',
            'cl_uid'        => 'クライアントゲームID',
            'create_count'  => '作成数',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'password.alfa_num_line_only'      => $this->MSG211,
            'password.min'                     => preg_replace('/以上/', '文字以上', $this->MSG222),
            'password2.alfa_num_line_only'     => $this->MSG211,
            'password2.same'                   => $this->MSG216,
            'grade.required'                   => $this->MSG012,
            'nickname.max'                     => $this->MSG021,
            'nickname.platform_dependent'      => $this->MSG076,
            'nickname.picture_characters'      => $this->MSG077,
            'gender.required'                  => $this->MSG012,
            'birth.date'                       => $this->MSG002,
            'blood.in'                         => $this->MSG012,
            'point.in'                         => $this->MSG012,
            'cl_uid.numeric'                   => $this->MSG031,
            'cl_uid.max'                       => $this->MSG093,
            'cl_uid.check_cl_uid_exists'       => $this->MSG181,
            'cl_uid.check_test_account_exists' => $this->MSG263,
            'create_count.max'                 => $this->MSG093,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
        ];
    }
}
