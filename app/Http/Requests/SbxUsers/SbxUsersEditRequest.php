<?php
namespace App\Http\Requests\SbxUsers;

use App\Http\Requests\Request;

use App\Services\SbxUsersService;
use Validator;

class SbxUsersEditRequest extends Request
{
    protected $sbxUsersService;

    public function __construct(SbxUsersService $sbxUsersService)
    {
        $this->sbxUsersService = $sbxUsersService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // cl_uidがcl_userテーブルに未登録か確認
        Validator::extend('check_cl_uid_exists', function ($attribute, $value, $parameters, $validator) {
            return $this->sbxUsersService->isNotExists($value);
        });

        // cl_uidがnetgame_cstool.test_accountに存在するか確認
        Validator::extend('check_test_account_exists', function ($attribute, $value, $parameters, $validator) {
            return $this->sbxUsersService->isTestAccountExists($value);
        });

        $validate = [
            'id'        => 'required',
            'grade'     => 'required',
            'nickname'  => 'required|max:12|platform_dependent|picture_characters',
            'point'     => 'required|in:' . implode(',', array_keys(config('forms.SbxUsers.point'))),
            'cl_uid'    => 'numeric|max:**********',
        ];

        // cl_uidを登録済みの場合はバリデートをしない
        if (! $this->get('isEntryClUid')) {
            $validate['cl_uid'] .= '|check_cl_uid_exists|check_test_account_exists';
        }

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id'        => 'id',
            'grade'     => 'アカウント種別',
            'nickname'  => 'ニックネーム',
            'cl_uid'    => 'クライアントゲームID',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'grade.required'                   => $this->MSG012,
            'nickname.max'                     => $this->MSG021,
            'nickname.platform_dependent'      => $this->MSG076,
            'nickname.picture_characters'      => $this->MSG077,
            'point.in'                         => $this->MSG012,
            'cl_uid.numeric'                   => $this->MSG031,
            'cl_uid.max'                       => $this->MSG093,
            'cl_uid.check_cl_uid_exists'       => $this->MSG181,
            'cl_uid.check_test_account_exists' => $this->MSG263,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id',
                ],
            ],
        ];
    }
}
