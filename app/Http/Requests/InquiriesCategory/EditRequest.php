<?php
namespace App\Http\Requests\InquiriesCategory;

use App\Http\Requests\Request;
use App\Services\InquiriesCategoryService;
use Validator;

/**
 * お問い合わせ：検索
 */
class EditRequest extends Request
{

    protected $InquiriesCategoryService;

    public function __construct(InquiriesCategoryService $InquiriesCategoryService)
    {
        $this->InquiriesCategoryService = $InquiriesCategoryService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 指定のapp_idが選択可能な値か確認(未選択か不正な値ならfalse)
        Validator::extend('permit_app_id', function ($attribute, $value, $parameters, $validator) {
            return $this->InquiriesCategoryService->checkPermitAppId(Request::all()['app_id']);
        });

        // 同層のカテゴリに同じ値が無いか確認
        Validator::extend('same_value', function ($attribute, $value, $parameters, $validator) {
            $request = Request::all();
            if (! $value || empty($request['app_id']) ) {
                // 自分が未入力なら確認不要。app_idが存在しないなら確認しない。
                return true;
            }
            $isNotSameValue = true;

            if (preg_match('#(.*)_(\d+)_(\d+)#', $attribute, $matchAttribute)) {
                switch ($matchAttribute[1]) {
                    case 'large':
                        $param = [
                            'value'   => $value,
                            'pattern' => '#(large)_('.$matchAttribute[2].')_(\d+)#',
                            'selfKey' => $attribute,
                            'app_id'  => $request['app_id'],
                            'mode'    => 'edit',
                        ];
                        $isNotSameValue = $this->InquiriesCategoryService->isNotSameValueLarge($param);
                        break;

                    case 'middle':
                        $param = [
                            'value'   => $value,
                            'pattern' => '#(middle)_('.$matchAttribute[2].')_(\d+)#',
                            'selfKey' => $attribute,
                        ];
                        $isNotSameValue = $this->InquiriesCategoryService->isNotSameValueMiddle($request, $param);
                        break;

                    case 'small':
                        preg_match('#\.(\d+)#', $attribute, $matchSmall);
                        if ($matchSmall[1]) {
                            $param = [
                                'value'   => $value,
                                'selfKey' => $matchSmall[1],
                            ];
                            $isNotSameValue = $this->InquiriesCategoryService->isNotSameValueSmall($request['small_'.$matchAttribute[2].'_'.$matchAttribute[3]], $param);
                        }
                        break;

                    default:
                        break;
                }
            }

            return $isNotSameValue;
        });

        // 親カテゴリが入力されていなければfalse
        Validator::extend('parent_empty', function ($attribute, $value, $parameters, $validator) {
            if (! $value ) {
                // 自分が未入力なら確認不要
                return true;
            }

            $request = Request::all();
            $isNotParentEmpty = true;

            if (preg_match('#(.*)_(\d+)_(\d+)#', $attribute, $matchAttribute)) {

                switch ($matchAttribute[1]) {
                    case 'middle':
                        if ( ! $request["large_".$matchAttribute[2]."_0"] ) {
                            $isNotParentEmpty = false;
                        }
                        break;

                    case 'small':
                        if ( ! $request["middle_".$matchAttribute[2]."_".$matchAttribute[3]] ) {
                            $isNotParentEmpty = false;
                        }
                        break;

                    default:
                        break;
                }
            }

            return $isNotParentEmpty;
        });

        $request = Request::all();

        // Laravel5.1用にプレースホルダを用いないバリデーションルールを作成
        $rules = [
            'app_id' => 'required|not_in:0|permit_app_id',
        ];
        if (! empty($request['app_id']) && ! empty($request['large_id'])){
            // 入力欄が新規なのか編集なのかを判定する為の配列を作成。
            $registCategory = $this->InquiriesCategoryService->getRegistCategory([$request['app_id']]);
            $categoryStructure = $this->InquiriesCategoryService->getCategoryStructureLarge([
                'registCategory'         => $registCategory,
                'appId'                  => $request['app_id'],
                'largeId'                => $request['large_id'],
                'categoryStructureParam' => ['smallKeyAscend' => true],
            ]);
            $this->InquiriesCategoryService->setEditData($categoryStructure, $request);

            $countInputTag = 0;
            foreach ($request as $keyRequest => $valRequest) {
                if (preg_match('#(.*)_(\d+)_(\d+)#', $keyRequest, $match)) {
                    switch ($match[1]) {
                        case 'large':
                            // 空欄の場合（編集画面では大カテゴリを新規追加しない）
                            if (! $request[$match[1].'_'.$match[2].'_'.$match[3]] ) {
                                $rules[ 'large_'.$match[2].'_'.$match[3] ] = 'required';
                                continue;
                            }
                            if ($categoryStructure[ $match[3] ]['editTypeLarge'] == 'update') {
                                // 文言を編集する場合
                                $rules[ 'large_'.$match[2].'_'.$match[3] ] = 'required|max:'.config('forms.InquiriesCategory.validate.max').'|same_value|platform_dependent|picture_characters|special_characters';
                            } else {
                                // 文言が変わらない場合
                                $rules[ 'large_'.$match[2].'_'.$match[3] ] = 'required';
                            }
                            $countInputTag++;
                            break;

                        case 'middle':
                            if ( empty($categoryStructure[ $match[3] ]['notBlank']) ) {
                                // 空欄を許可する欄（新規追加）のルール
                                if (! $request[$match[1].'_'.$match[2].'_'.$match[3]] ) {
                                    continue;    // 空欄はスキップ
                                }
                                $rules[ 'middle_'.$match[2].'_'.$match[3] ] = 'max:'.config('forms.InquiriesCategory.validate.max').'|same_value|parent_empty|platform_dependent|picture_characters|special_characters';
                            } else {
                                // 空欄を許可しない欄（登録済カテゴリ編集）のルール
                                $rules[ 'middle_'.$match[2].'_'.$match[3] ] = 'required|max:'.config('forms.InquiriesCategory.validate.max').'|same_value|platform_dependent|picture_characters|special_characters|parent_empty';
                            }
                            $countInputTag++;
                            break;

                        case 'small':
                            foreach ($valRequest as $keySmall => $valSmall) {
                                if ( empty($categoryStructure[ $match[3] ]['smallParam'][$keySmall]['notBlank']) ) {
                                    // 空欄を許可する欄（新規追加）のルール
                                    if (! $request[$match[1].'_'.$match[2].'_'.$match[3]][$keySmall] ) {
                                        continue;    // 空欄はスキップ
                                    }
                                    $rules[ 'small_'.$match[2].'_'.$match[3].'.'.$keySmall ] = 'max:'.config('forms.InquiriesCategory.validate.max').'|same_value|platform_dependent|picture_characters|special_characters|parent_empty';
                                } else {
                                    // 空欄を許可しない欄（登録済カテゴリ編集）のルール
                                    $rules[ 'small_'.$match[2].'_'.$match[3].'.'.$keySmall ] = 'required|max:'.config('forms.InquiriesCategory.validate.max').'|same_value|platform_dependent|picture_characters|special_characters|parent_empty';
                                }
                            }
                            $countInputTag++;
                            break;

                        default:
                            break;
                    }
                }
            }
            if ( $countInputTag > config('forms.InquiriesCategory.editCategory.limit') ) {
                $rules['input_tag_over'] = 'accepted';   // inputタグの数がポップアップで警告されるべき件数を超えている
            }
        }

        return $rules;
    }

    public function attributes()
    {

        $attributes = [
            'app_id'               => 'タイトル',
            'input_tag_over'       => 'カテゴリ数',
        ];
        foreach (Request::all() as $keyRequest => $valRequest) {
            if (preg_match('#(.*)_(\d+)_(\d+)#', $keyRequest, $match)) {

                switch ($match[1]) {
                    case 'large':
                        $attributes[ 'large_'.$match[2].'_'.$match[3] ] = config('forms.InquiriesCategory.categoryRelation.large');
                        break;

                    case 'middle':
                        $attributes[ 'middle_'.$match[2].'_'.$match[3] ] = config('forms.InquiriesCategory.categoryRelation.middle');
                        break;

                    case 'small':
                        foreach ($valRequest as $keySmall => $valSmall) {
                            $attributes[ 'small_'.$match[2].'_'.$match[3].'.'.$keySmall ] = config('forms.InquiriesCategory.categoryRelation.small');
                        }
                        break;

                    default:
                        break;
                }
            }
        }

        return $attributes;
    }

    public function customMessages()
    {
        return [
            'required'                => $this->MSG280,
            'app_id.required'         => $this->MSG241,
            'input_tag_over.accepted' => $this->MSG264,
            'parent_empty'            => $this->MSG274,
            'same_value'              => $this->MSG273,
            'not_in'                  => $this->MSG241,
            'permit_app_id'           => $this->MSG277,
            'under_required'          => $this->MSG278,
            'platform_dependent'      => $this->MSG076,
            'picture_characters'      => $this->MSG077,
            'special_characters'      => $this->MSG232,
        ];
    }
}
