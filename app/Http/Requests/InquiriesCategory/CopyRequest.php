<?php
namespace App\Http\Requests\InquiriesCategory;

use App\Http\Requests\Request;
use App\Services\InquiriesCategoryService;
use Validator;

/**
 * お問い合わせ：検索
 */
class CopyRequest extends Request
{

    protected $InquiriesCategoryService;

    public function __construct(InquiriesCategoryService $InquiriesCategoryService)
    {
        $this->InquiriesCategoryService = $InquiriesCategoryService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 指定のapp_idが選択可能な値か確認
        Validator::extend('permit_app_id', function ($attribute, $value, $parameters, $validator) {
            $index = (string)$attribute;
            return $this->InquiriesCategoryService->checkPermitAppId(Request::all()[$index], true);
        });

        // コピー先のタイトルの大カテゴリに同じ名前のカテゴリが存在しない事を確認
        Validator::extend('category_id_equal', function ($attribute, $value, $parameters, $validator) {
            $condition = Request::all();

            if (empty($condition['target_app_id']) || empty($condition['large_id']) || $condition['source_app_id'] == $condition['target_app_id']) {
                // 判定に必要な入力が存在しないか自分自身を判定なら、それぞれのエラーを優先。
                return true;
            }

            return $this->InquiriesCategoryService->isNotInquiryCategoryId($condition, 'large');
        });

        return [
            'source_app_id'          => 'required|not_in:0|permit_app_id',
            'target_app_id'          => 'required|not_in:0|permit_app_id|category_id_equal|different:source_app_id',
        ];
    }

    public function attributes()
    {
        return [
            'source_app_id'            => 'コピー元タイトル',    // 編集ページを表示した時点で固定
            'target_app_id'            => 'コピー先タイトル',
        ];
    }

    public function customMessages()
    {
        return [
            'required'          => $this->MSG241,
            'not_in'            => $this->MSG241,
            'permit_app_id'     => $this->MSG277,
            'different'         => $this->MSG275,
            'category_id_equal' => $this->MSG276,
        ];
    }
}
