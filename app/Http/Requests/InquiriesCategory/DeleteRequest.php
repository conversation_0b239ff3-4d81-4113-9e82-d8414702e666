<?php
namespace App\Http\Requests\InquiriesCategory;

use App\Http\Requests\Request;
use App\Services\InquiriesCategoryService;
use Validator;

/**
 * お問い合わせ：検索
 */
class DeleteRequest extends Request
{

    protected $InquiriesCategoryService;
    static $warned;

    public function __construct(InquiriesCategoryService $InquiriesCategoryService)
    {
        $this->InquiriesCategoryService = $InquiriesCategoryService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 指定のapp_idが選択可能な値か確認
        Validator::extend('permit_app_id', function ($attribute, $value, $parameters, $validator) {
            $index = (string)$attribute;
            return $this->InquiriesCategoryService->checkPermitAppId(Request::all()[$index], true);
        });

        // 削除指定の大・中カテゴリに対して、下層すべてが含まれているか確認
        Validator::extend('under_required', function ($attribute, $value, $parameters, $validator) {
            return $this->InquiriesCategoryService->checkUnderRequired(Request::all());
        });

        // Laravel5.1用にプレースホルダを用いないバリデーションルールを作成
        $rules = [
            'app_id'          => 'required|not_in:0|permit_app_id',
        ];
        foreach (Request::all() as $key => $value) {
            if (preg_match('#del-(\d+)#', $key, $match)) {
                $rules[ 'del-'.$match[1] ] = 'under_required';
                break;    // チェック用メソッドは1回だけ実行する
            }
        }
        if (count($rules) <= 1) {
            $rules['required_without_all'] = 'required';   // 全カテゴリにチェックが無い時、存在しないフォームに入力必須ルールを設けてバリデーションを通さない
        }

        return $rules;
    }

    public function attributes()
    {
        return [
            'app_id'               => 'タイトル',
            'required_without_all' => 'カテゴリ',
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.required'               => $this->MSG241,
            'required_without_all.required' => $this->MSG279,
            'not_in'                        => $this->MSG241,
            'permit_app_id'                 => $this->MSG277,
            'under_required'                => $this->MSG278,
        ];
    }
}
