<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ApplicationPolicyService;
use Validator;

class ApplicationPolicyRequest extends Request
{
    protected $PointLogsService;

    public function __construct(ApplicationPolicyService $applicationPolicyService)
    {
        $this->ApplicationPolicyService = $applicationPolicyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $policyBodyRule = 'required';

        // 申請の確認画面への遷移のみ拡張子チェックを実施
        $actionName = explode('.', \Route::current()->getAction()['as']);
        $actionName = $actionName[1];
        if ($actionName == 'createconfirm') {
            $policyBodyRule .= '|mimes:txt';
        }

        return [
            'app_id'      => 'required',
            'policy_body' => $policyBodyRule,
        ];
    }

    public function attributes()
    {
        return [
            'app_id'      => 'アプリID',
            'policy_body' => '規約文言',
        ];
    }

    public function customMessages()
    {
        return [
            'policy_body.required' => $this->MSG012,
            'policy_body.mimes' => str_replace(':extension', 'txt', $this->MSG237),
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'app_id' => 'app_id',
                ],
            ]
        ];
    }
}
