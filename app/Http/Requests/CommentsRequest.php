<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\CommentsService;
use Validator;

class CommentsRequest extends Request
{
    protected $CommentsService;

    public function __construct(CommentsService $CommentsService)
    {
        $this->CommentsService = $CommentsService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('developer_application_community', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                return $this->CommentsService->checkStillDeveloper($value);
            }
            return true;
        });

        Validator::extend('developer_application_topic', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                $dataTopic = $this->CommentsService->getTopicOne($value);
                if (count($dataTopic) == 0) {
                    return false;
                }
                return $this->CommentsService->checkStillDeveloper($dataTopic['community_id']);
            }
            return true;
        });

        return [
            'text'               => 'control_characters|max:10000|except_tag_html',
            'topic_community_id' => 'numeric|developer_application_community',
            'topic_id'           => 'required|numeric|developer_application_topic',
        ];
    }

    public function attributes()
    {
        return [
            'text'               => 'コメント内容',
            'topic_community_id' => 'コミュニティ',
            'topic_id'           => 'トピック',
        ];
    }

    public function customMessages()
    {
        return [
            'text.control_characters'                            => $this->MSG225,
            'topic_community_id.numeric'                         => $this->MSG031,
            'topic_community_id.developer_application_community' => $this->MSG264,
            'topic_id.numeric'                                   => $this->MSG031,
            'topic_id.developer_application_topic'               => $this->MSG264,
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'topic_id' => 'topic_id'
                ]
            ],
        ];
    }
}
