<?php
namespace App\Http\Requests\SandboxMonthlyService;

use App\Http\Requests\Request;

use Validator;

class CreateRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // 購入初月がありえる値かチェック
        Validator::extend('free_flg_exists', function ($attribute, $value, $parameters, $validator) {
            $freeFlagList = config('forms.SandboxMonthlyService.freeFlag');
            if (isset($freeFlagList[$value])) {
                return true;
            }
            return false;
        });

        // アクション次第でチェック内容を変える
        $validate = [
            'service_name' => 'required',
            'app_id' => 'required|numeric|integer',
            'callback_url' => 'required|url',
            'callback_url_withdrawal' => 'url',
            'price' => 'required|numeric|integer|min:1',
            'amount' => 'required|numeric|integer|min:1',
            'free_flg' => 'required|free_flg_exists',
            'term' => 'required|numeric|integer|max:365|min:1',
            'sell_begin_datetime' => 'date_time',
            'sell_end_datetime' => 'date_time|after:sell_begin_datetime',
        ];
        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'service_name' => 'サービス名',
            'app_id' => '対象アプリ',
            'callback_url' => 'コールバックURL',
            'callback_url_withdrawal' => 'コールバックURL(解約通知)',
            'price' => '値段(円)',
            'amount' => '販売個数',
            'free_flg' => '購入初月',
            'term' => '有効期間(日)',
            'sell_begin_datetime' => '販売開始日時',
            'sell_end_datetime' => '販売終了日時',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'app_id.numeric' => $this->MSG031,
            'price.numeric' => $this->MSG031,
            'amount.numeric' => $this->MSG031,
            'term.numeric' => $this->MSG031,
            'sell_end_datetime.after' => $this->MSG007,
            'free_flg.free_flg_exists' => $this->MSG011,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
        ];
    }
}
