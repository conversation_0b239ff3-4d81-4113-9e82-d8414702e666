<?php
namespace App\Http\Requests\SandboxMonthlyService;

use App\Http\Requests\Request;

use Validator;

class DestroyRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $validate['id'] = 'required|numeric|integer';
        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => 'サービスID',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'id.numeric' => $this->MSG031,
        ];
    }
}
