<?php
namespace App\Http\Requests\SandboxMonthlyService;

use App\Http\Requests\Request;

use Validator;

class EditRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $validate = [
            'id' => 'required|numeric|integer',
            'service_name' => 'required',
            'callback_url' => 'required|url',
            'callback_url_withdrawal' => 'url',
            'term' => 'required|numeric|integer|max:365|min:1',
            'sell_begin_datetime' => 'date_time',
            'sell_end_datetime' => 'date_time|after:sell_begin_datetime',
        ];
        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => 'サービスID',
            'service_name' => 'サービス名',
            'callback_url' => 'コールバックURL',
            'callback_url_withdrawal' => 'コールバックURL(解約通知)',
            'term' => '有効期間(日)',
            'sell_begin_datetime' => '販売開始日時',
            'sell_end_datetime' => '販売終了日時',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'id.numeric' => $this->MSG031,
            'term.numeric' => $this->MSG031,
            'sell_end_datetime.after' => $this->MSG007,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'update' => [
                'action' => 'edit',
            ],
        ];
    }
}
