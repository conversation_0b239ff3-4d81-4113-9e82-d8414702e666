<?php

namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\SandboxMonthlyUsersService;

class SandboxMonthlyUsersRequest extends Request
{
    public function __construct(SandboxMonthlyUsersService $sandboxMonthlyUsersService)
    {
        $this->sandboxMonthlyUsersService = $sandboxMonthlyUsersService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $validate = [
            'id'           => 'required|numeric|integer',
            'user_id'      => 'required|numeric|integer',
        ];
        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id'           => 'サービスID',
            'user_id'      => 'ユーザID',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'numeric' => $this->MSG031,
        ];
    }
}
