<?php

namespace App\Http\Requests;

use Validator;

class GuideBannerRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'           => 'required|max:255',
            'uploadImage'    => 'required_without_all:imagePath,uploadImage_encode|image',
            'imagePath'      => 'url',
            'link'           => 'required|max:255|url',
            'start_datetime' => 'required|date',
            'end_datetime'   => 'required|date|after:start_datetime'
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'           => 'バナー名称',
            'uploadImage'    => 'バナー画像',
            'imagePath'      => 'バナー画像',
            'link'           => 'リンク先',
            'start_datetime' => '開始日時',
            'end_datetime'   => '終了日時'
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'name.in'                  => $this->MSG012,
            'name.max'                 => $this->MSG021,
            'uploadImage.in'           => $this->MSG241,
            'uploadImage.image'        => str_replace(':extension', 'jpeg,png,gif,bmp,svg', $this->MSG237),
            'imagePath.in'             => $this->MSG241,
            'link.in'                  => $this->MSG012,
            'link.max'                 => $this->MSG021,
            'start_datetime.in'        => $this->MSG012,
            'start_datetime.date'      => $this->MSG002,
            'end_datetime.in'          => $this->MSG012,
            'end_datetime.date'        => $this->MSG002,
            'end_datetime.after'       => $this->MSG007,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'bannerid' => 'id',
                ],
            ]
        ];
    }
}
