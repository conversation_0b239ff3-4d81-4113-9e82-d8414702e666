<?php
namespace App\Http\Requests;

use App\Services\PasswordService;
use Validator;

class PasswordRequest extends Request
{
    protected $passwordService;

    public function __construct(PasswordService $passwordService)
    {
        $this->passwordService = $passwordService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        Validator::extend('check_user_exists', function ($attribute, $value, $parameters, $validator) {
            if ($this->ajax()) {
                return $this->passwordService->checkUserExist($value, $this->get('login_id'));
            }
            return $value == auth_user_id();
        });

        Validator::extend('check_pw_exists', function ($attribute, $value, $parameters, $validator) {
            return (bool) $this->passwordService->isPasswordSame($this->get('id'), $value);
        });

        Validator::extend('check_pw_now_exists', function ($attribute, $value, $parameters, $validator) {
            return ! $this->passwordService->isPasswordSame($this->get('id'), $value);
        });

        Validator::extend('check_pw_old_exists', function ($attribute, $value, $parameters, $validator) {
            return ! $this->passwordService->isOldPasswordSame($this->get('id'), $value);
        });

        $validate = [
            'id'           => 'required|check_user_exists',
            'login_id'     => 'required',
            'password_now' => 'required|alfa_num_line_only|check_pw_exists',
            'password1'    => 'required|alfa_num_line_only|check_pw_now_exists|check_pw_old_exists',
            'password2'    => 'required|alfa_num_line_only|same:password1',
        ];
        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id'           => 'ID',
            'login_id'     => 'ログインID',
            'password_now' => '現在のパスワード',
            'password1'    => '新しいパスワード',
            'password2'    => '新しいパスワード確認',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'id.check_user_exists'            => $this->MSG011,
            'password_now.alfa_num_line_only' => $this->MSG211,
            'password_now.check_pw_exists'    => $this->MSG011,
            'password1.alfa_num_line_only'    => $this->MSG211,
            'password1.check_pw_now_exists'   => $this->MSG281,
            'password1.check_pw_old_exists'   => $this->MSG282,
            'password2.alfa_num_line_only'    => $this->MSG211,
            'password2.same'                  => $this->MSG216,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'update' => [
                'action' => 'edit',
            ],
        ];
    }
}
