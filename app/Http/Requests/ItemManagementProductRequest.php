<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ItemManagementService;

class ItemManagementProductRequest extends Request
{
    protected $PointLogsService;

    public function __construct(ItemManagementService $itemManagementService)
    {
        $this->ItemManagementService = $itemManagementService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'sku'           => 'required|regex:/^[0-9a-z]{1}[0-9a-z_.]+$/|max:64',
            'title'         => 'required|max:55|restricted_characters',
            'description'   => 'required|max:80|restricted_characters',
            'status'        => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'sku'           => 'アイテムID',
            'title'         => '管理名',
            'description'   => '内部説明',
            'status'        => 'ステータス',
        ];
    }

    public function customMessages()
    {
        return [
            'sku.regex' => $this->MSG292,
            'title.restricted_characters' => $this->MSG295,
            'description.restricted_characters' => $this->MSG295,
        ];
    }
}
