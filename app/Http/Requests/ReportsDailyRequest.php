<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ReportsDailyService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

/**
 * レポート：日別レポート
 */
class ReportsDailyRequest extends Request
{
    protected $ReportsDailyService;

    public function __construct(ReportsDailyService $ReportsDailyService)
    {
        $this->ReportsDailyService = $ReportsDailyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'begin'  => 'date',
            'end'    => 'date',
            'app_id' => 'required|in:0,'.join(',', array_keys($this->ReportsDailyService->getAppTitleType())),
            'report' => 'required'
        ];
        // 利用状況レポート(定期購入)が選択されている場合のバリデーション
        if (Request::input('report') === 'Subscription_Usage_Report') {
            Validator::extend('appid_device_check', function ($attribute, $value, $parameters) {
                $device = Request::input('device');
                return (!empty($value) && !empty($device));
            });
            // after_or_equalが使用できないバージョン(Laravel 5.1)への対応
            Validator::extend('date_after_or_equal', function ($attribute, $value, $parameters, $validator) {
                $begin = $this->get($parameters[0]);
                if (strtotime($value) >= strtotime($begin)) {
                    return true;
                }
                return false;
            });
            // 期間範囲チェック
            Validator::extend('is_range_less_than', function ($attribute, $value, $parameters, $validator) {
                $begin = $this->get($parameters[1]);
                $begin  = Carbon::parse($begin);
                $end    = Carbon::parse($value);
                $period = $end->diffInDays($begin);
                if ($period < $parameters[0]) {
                    return true;
                }
                return false;
            });
            $maxDateRange = config('forms.ReportsDaily.subscriptionMaxDateRange');
            Validator::replacer('is_range_less_than', function ($message, $attribute, $rule, $parameters) use ($maxDateRange) {
                $replaceMaxAttribute = str_replace(':max', $maxDateRange, $message);
                $start = $this->get($parameters[1]);
                return str_replace(':start', $start, $replaceMaxAttribute);
            });
            $rules['app_id'] = preg_replace('/\b(?:' . implode(array_keys(array_merge([config('forms.ReportsDaily.reportGameAll')], config('forms.ReportsDaily.reportDmmType'))), '|') . ')\b,?/', '', $rules['app_id']) . '|appid_device_check';
            $rules['begin'] = 'required|date';
            $rules['end'] = 'required|date|date_after_or_equal:begin|is_range_less_than:' . $maxDateRange . ',begin';
        }
        return $rules;
    }

    public function attributes()
    {
        return [
            'begin'  => '開始日',
            'end'    => '終了日',
            'app_id' => 'タイトル',
            'report' => '形式'
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.in'                 => $this->MSG012,
            'app_id.appid_device_check' => $this->MSG084,
            'begin.required'            => $this->MSG001,
            'begin.date'                => $this->MSG002,
            'date'                      => $this->MSG002,
            'end.required'              => $this->MSG001,
            'end.date'                  => $this->MSG002,
            'date_after_or_equal'       => $this->MSG265,
            'is_range_less_than'        => $this->MSG272,
        ];
    }
}
