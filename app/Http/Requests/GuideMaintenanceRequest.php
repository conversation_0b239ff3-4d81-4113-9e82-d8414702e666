<?php
namespace App\Http\Requests;

use Validator;

class GuideMaintenanceRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        return [
            'guide_page_id'       => 'required|numeric',
            'is_internal_release' => 'required|numeric',
            'start_datetime'      => 'required|date',
            'end_datetime'        => 'required|date|after:start_datetime'
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'guide_page_id'       => '画面名',
            'is_internal_release' => '開放状態',
            'start_datetime'      => '開始日時',
            'end_datetime'        => '終了日時'
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'guide_page_id.in'            => $this->MSG241,
            'guide_page_id.numeric'       => $this->MSG031,
            'is_internal_release.in'      => $this->MSG241,
            'is_internal_release.numeric' => $this->MSG031,
            'start_datetime.in'           => $this->MSG012,
            'start_datetime.date'         => $this->MSG002,
            'end_datetime.in'             => $this->MSG012,
            'end_datetime.date'           => $this->MSG002,
            'end_datetime.after'          => $this->MSG007,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'mainteid' => 'id',
                ],
            ]
        ];
    }
}
