<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\GraphDailyService;

/**
 * レポート：日別表示・グラフ
 */
class GraphDailyRequest extends Request
{
    protected $GraphDailyService;

    public function __construct(GraphDailyService $GraphDailyService)
    {
        $this->GraphDailyService = $GraphDailyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'begin'  => 'date',
            'end'    => 'date',
            'app_id' => 'required|in:'.join(',', array_keys($this->GraphDailyService->getAppTitleType())),
            'device' => 'required',
            'report' => 'required'
        ];
    }

    public function attributes()
    {
        return [
            'begin'  => '開始日',
            'end'    => '終了日',
            'app_id' => 'タイトル',
            'device' => 'デバイス',
            'report' => '形式'
        ];
    }

    public function customMessages()
    {
        return [
            'date'      => $this->MSG002,
            'app_id.in' => $this->MSG012,
        ];
    }
}
