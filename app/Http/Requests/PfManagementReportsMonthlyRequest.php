<?php

namespace App\Http\Requests;

class PfManagementReportsMonthlyRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     * @return array
     */
    public function rules()
    {
        $rules = [
                'report_date_begin' => 'required|date',
                'report_date_end'   => 'required|date',
                'report_type'       => 'required|in:avatar,contents',
        ];
        return $rules;
    }

    /**
     * Custom attributes list
     * @return array
     */
    public function attributes()
    {
        $attr  = [
                'report_date_begin' => '開始月',
                'report_date_end'   => '終了月',
                'report_type'       => 'レポート種別',
        ];
        return $attr;
    }

    /**
     * Custom validation message
     * @return array
     */
    public function customMessages()
    {
        $msg   = [
                'date' => $this->MSG002,
        ];
        return $msg;
    }

    /**
     * Redirect rules.
     * @return array
     */
    protected function redirectRules()
    {
        $rules = [
                'csvDownload'    => [
                        'action' => 'index',
                ],
        ];
        return $rules;
    }
}
