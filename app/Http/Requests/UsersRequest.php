<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;

/**
 * ログイン認証
 */
class UsersRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'login_id' => 'required',
            'password' => 'required'
        ];
    }

    public function attributes()
    {
        return [
            'login_id' => 'ログインID',
            'password' => 'パスワード'
        ];
    }
}
