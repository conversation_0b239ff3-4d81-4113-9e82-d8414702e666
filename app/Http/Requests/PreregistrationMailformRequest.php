<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;

class PreregistrationMailformRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name'     => 'required|max:50',
            'title_id' => 'required|max:50|alfa_num_line_only',
            'start_at' => 'required|date',
            'end_at'   => 'required|date|after:start_at',
        ];
    }

    public function attributes()
    {
        return [
            'name'     => 'タイトル名',
            'title_id' => 'タイトルID',
            'start_at' => '受付開始日',
            'end_at'   => '受付終了日',
        ];
    }

    public function customMessages()
    {
        return [
            'title_id.alfa_num_line_only' => $this->MSG211,
            'start_at.date'               => $this->MSG002,
            'end_at.date'                 => $this->MSG002,
            'end_at.after'                => $this->MSG007,
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ],
        ];
    }
}
