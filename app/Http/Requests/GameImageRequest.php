<?php

namespace App\Http\Requests;

use App\Models\FreegameDeveloper\ApplicationImage;
use App\Services\GameImageInfoService;
use App\Services\GameImageService;
use Validator;

class GameImageRequest extends Request
{
    private $infoService;
    private $GameImageService;

    public function __construct(
        ApplicationImage     $imgService,
        GameImageInfoService $infoService,
        GameImageService     $GameImageService,
        array $query = array(),
        array $request = array(),
        array $attributes = array(),
        array $cookies = array(),
        array $files = array(),
        array $server = array(),
        $content = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);

        // 入力項目のサービス取得
        if (request('id')) {
            $imgId             = request('id');
            $imgData           = $imgService->getById($imgId);
            $this->infoService = $infoService->getDataByType(
                $imgData->app_id,
                $imgData->image_type,
                $imgData->image_size,
                $imgData->device
            );
        }
        $this->GameImageService = $GameImageService;
    }

//*********************************************************************************************************************
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     * @return array
     */
    public function rules()
    {
        $rules = [];

        // 掲載可能チェック
        Validator::extend('is_enable_up_post', function ($attribute, $value, $parameters, $validator) {
            if ($this->infoService) {
                return $this->infoService->isEnableUpPost();
            }
            return true;
        });

        if (preg_match('/index\/*\//', $this->path())) {
            $rules['id'] = 'in:'.join(',', array_keys($this->GameImageService->getSelectApplicationList()));
        } else {
            if ($this->infoService) {
                $rules['id'] = 'is_enable_up_post';
            }
        }

        return $rules;
    }

    /**
     * Custom attributes list
     * @return array
     */
    public function attributes()
    {
        $attr = [
            'id' => 'タイトル',
        ];
        return $attr;
    }

    /**
     * Custom validation message
     * @return array
     */
    public function customMessages()
    {
        $msg = [
            'id.is_enable_up_post' => $this->MSG258,
            'id.in'                => $this->MSG012,
        ];
        return $msg;
    }

    /**
     * Redirect rules.
     * @return array
     */
    protected function redirectRules()
    {
        $rules = [
            'poststore'         => [
                'action'        => 'index',
                'parameters'    => [
                    'id'        => 'app_id',
                    'imageType' => 'image_type',
                ],
            ],
        ];
        return $rules;
    }
}
