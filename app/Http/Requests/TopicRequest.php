<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\TopicService;
use Validator;

class TopicRequest extends Request
{
    protected $TopicService;

    public function __construct(TopicService $TopicService)
    {
        $this->TopicService = $TopicService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('developer', function ($attribute, $value, $parameters, $validator) {
            if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
                return true;
            }
            return $this->TopicService->checkStillDeveloper($value);
        });

        return [
            'community_id'      => 'required|developer|numeric',
            'title'             => 'required|max:100|platform_dependent|picture_characters|control_characters|except_tag_html',
            'description'       => 'required|max_emoji:10000|platform_dependent|picture_characters|control_characters|except_tag_html',
            'is_only_developer' => 'required|in:0,1',
        ];
    }

    public function attributes()
    {
        return [
            'community_id'      => 'コミュニティ',
            'title'             => 'トピック名',
            'description'       => 'トピック本文',
            'is_only_developer' => 'ユーザー書き込み',
        ];
    }

    public function customMessages()
    {
        return [
            'community_id.developer'         => $this->MSG264,
            'community_id.numeric'           => $this->MSG031,
            'title.platform_dependent'       => $this->MSG076,
            'title.picture_characters'       => $this->MSG077,
            'title.control_characters'       => $this->MSG225,
            'description.max_emoji'          => $this->MSG021,
            'description.platform_dependent' => $this->MSG076,
            'description.picture_characters' => $this->MSG077,
            'description.control_characters' => $this->MSG225,
            'is_only_developer.in'           => $this->MSG241,
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ],
        ];
    }
}
