<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ChReportsDailyService;
use Validator;

class ChReportsDailyRequest extends Request
{
    protected $ChReportsDailyService;

    public function __construct(ChReportsDailyService $ChReportsDailyService)
    {
        $this->ChReportsDailyService = $ChReportsDailyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // デバイス用チェックボックス配列に想定しない値が入力されていないか確認
        Validator::extend('device_diff', function ($attribute, $value, $parameters, $validator)
        {
            $deviceType = array_keys(config('forms.ChReportsDaily.deviceType'));

            if ( !empty($value) && is_array($value) && count(array_diff($value, $deviceType)) > 0 )
            {
                return false;
            }
            return true;
        });
    
        return [
            'begin'  => 'required|date',
            'end'    => 'required|date',
            'app_id' => 'in:0,'.join(',', array_keys($this->ChReportsDailyService->getChApplicationList())),
            'device' => 'required|array|device_diff',
        ];
    }

    public function attributes()
    {
        return [
            'begin'  => '開始日',
            'end'    => '終了日',
            'app_id' => 'タイトル',
            'device' => 'デバイス',
            'report' => '形式'
        ];
    }

    public function customMessages()
    {
        return [
            'date'               => $this->MSG002,
            'app_id.in'          => $this->MSG012,
            'device.array'       => $this->MSG264,
            'device.device_diff' => $this->MSG264,
        ];
    }

    protected function redirectRules()
    {
        return [
            'csvdownload' => [
                'action' => 'index',
            ],
        ];
    }
}
