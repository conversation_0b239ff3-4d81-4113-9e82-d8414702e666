<?php
namespace App\Http\Requests\ReceiptInquiry;

use App\Http\Requests\Request;
use Validator;

class BasicRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        validator::extend('permit_receipt_id', function ($attribute, $value, $parameters, $validator) {
            if (strlen($value) === 12) {
                if (preg_match('/[0-9a-f]{12}/', $value)) {
                    return true;
                }
            }
            if (strlen($value) === 36) {
                if (preg_match('/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/', $value)) {
                    return true;
                }
            }
            return false;
        });

        $rules = [
            'receiptId' => 'required|permit_receipt_id'
        ];

        return $rules;
    }

    public function attributes()
    {
        return [
            'receiptId' => 'レシートID'
        ];
    }

    public function customMessages()
    {
        $msg = [
            'receiptId.permit_receipt_id' => $this->MSG293
        ];

        return $msg;
    }

    public function redirectRules()
    {
        return [
            'detail' => [
                'action' => 'index',
                'parameters' => [
                    'app_id' => 'app_id',
                    'receiptId' => 'receiptId',
                    'device' => 'device',
                ]
            ]
    ];
    }
}
