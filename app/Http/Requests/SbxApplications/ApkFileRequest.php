<?php
namespace App\Http\Requests\SbxApplications;

use App\Http\Requests\Request;
use App\Models\FreegameSandbox\ApplicationDevice;
use Route;
use Validator;

/**
 * サンドボックスゲームAPKファイルアップロード
 */
class ApkFileRequest extends Request
{
    protected $applicationDevice;

    public function __construct(
        ApplicationDevice $applicationDevice,
        array $query = array(),
        array $request = array(),
        array $attributes = array(),
        array $cookies = array(),
        array $files = array(),
        array $server = array(),
        $content = null
    ) {
        $this->applicationDevice = $applicationDevice;
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        foreach (config('forms.SbxApplications.device') as $key => $val) {
            $validate['deviceList.' . $key] = 'in:' . $key;
        }

        $validate['apk_file'] = $this->getApkFileValidationRule();
        return $validate;
    }

    public function attributes()
    {
        return [
            'apk_file' => 'APKファイル'
        ];
    }

    public function customMessages()
    {
        return [
            'apk_file.apk_file_required' => $this->MSG012,
            'apk_file.mimes'             => str_replace(':extension', 'apk', $this->MSG237),
            'apk_file.max'               => str_replace(':max', 1, $this->MSG246)
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'createconfirm',
            ],
            'update' => [
                'action' => 'editconfirm',
                'parameters' => [
                    'id' => 'id',
                ],
            ],
        ];
    }

    private function getApkFileValidationRule()
    {
        // 対応デバイスにAndroidAppだけチェックされている＆APKファイルがチェックされている場合のみチェック
        Validator::extendImplicit('apk_file_required', function ($attribute, $value, $parameters, $validator) {
            $deviceList = $this->get('deviceList');
            if (count($deviceList) === 1 && in_array('android_app', $deviceList)) {
                // 対応デバイスに[AndroidApp]だけチェックされている場合
                if (!empty($this->get('require_apk_file')) ) {
                    // [APKファイル]がチェックされている場合、[APKファイル]入力チェック
                    return !empty(trim($value));
                }
            }

            return true;
        });

        $currentRoute = Route::getCurrentRoute()->getName();
        $currentAction = substr($currentRoute, strpos($currentRoute, '.') + 1);

        // 更新時のみ、application_deviceのandroid_appがactiveのレコードがないアプリは、apkのアップロードが必須になる
        if ($currentAction === 'update') {
            $applicationDeviceInfo = $this->applicationDevice->getOnetByAppIdAndDevice($this->get('id'), 'android_app', 'active');
            if (empty($applicationDeviceInfo) || empty($this->get('apk_name')) ) {
                return 'apk_file_required|mimes:apk,zip,jar|max:3145728';
            } else {
                return 'mimes:apk,zip,jar|max:3145728';
            }
        }

        return 'apk_file_required|mimes:apk,zip,jar|max:3145728';
    }

}
