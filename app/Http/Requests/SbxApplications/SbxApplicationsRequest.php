<?php
namespace App\Http\Requests\SbxApplications;

use App\Http\Requests\Request;
use Validator;

class SbxApplicationsRequest extends Request
{
    protected $sbxApplicationService;
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        // ApkFileRequestで既にエラーを保持している場合は、バリデーションを行わない
        $request = Request::instance();
        if ($request->session()->has('errors')) {
            return [];
        }
        
        // spとandroid_appを両方選択した場合はエラー(false)とする
        Validator::extend('sp_android_exists', function ($attribute, $value, $parameters, $validator) {
            if (count($this->get('deviceList')) > 1 && in_array('sp', $value) && in_array('android_app', $value)) {
                return false;
            }
            return true;
        });

        Validator::replacer('sp_android_exists', function ($message, $attribute, $rule, $parameters) {
            $deviceList = config('forms.SbxApplications.device');
            return str_replace([':other1', ':other2'], [$deviceList['sp'], $deviceList['android_app']], $message);
        });

        // spとemulatorを両方選択した場合はエラー(false)とする
        Validator::extend('sp_emulator_exists', function ($attribute, $value, $parameters, $validator) {
            if (count($this->get('deviceList')) > 1 && in_array('sp', $value) && in_array('emulator', $value)) {
                return false;
            }
            return true;
        });

        Validator::replacer('sp_emulator_exists', function ($message, $attribute, $rule, $parameters) {
            $deviceList = config('forms.SbxApplications.device');
            return str_replace([':other1', ':other2'], [$deviceList['sp'], $deviceList['emulator']], $message);
        });

        // 全角がある場合はエラー(false)とする
        Validator::extend('two_byte_exists', function ($attribute, $value, $parameters, $validator) {
            if (strlen($value) == mb_strlen($value)) {
                return true;
            }
            return false;
        });
        
        // (SP)PFメニュー非表示レイアウト選択時にメニュー位置が1 ~ 8の範囲にない場合はエラー(false)とする
        Validator::extend('position_not_selected', function ($attribute, $value, $parameters, $validator) {
            if (!empty($this->get('spLayout')) && $this->get('spLayout') == 2 && ($value < 1 || $value > 8)) {
                return false;
            }
            return true;
        });

        // PCまたはSPの場合、テストゲームSSL対応とURLのプロトコルを確認
        Validator::extend('is_available_protocol', function ($attribute, $value, $parameters, $validator) {
            if (in_array('sp', $parameters) || in_array('pc', $parameters)) {
                // SSL有効化かつガジェットURLがhttpsである場合
                if ($value === '1' && preg_match('/^https:\/\//', end($parameters)) === 1) {
                    return true;
                } else if ($value === '0') {
                    return true;
                }
                return false;
            }
            return true;
        });

        // 対応デバイスにAndroidAppだけチェックされている＆APKファイルがチェックされている場合のみチェック
        Validator::extendImplicit('package_name_required', function ($attribute, $value, $parameters, $validator) {
            $deviceList = $this->get('deviceList');
            if (count($deviceList) === 1 && in_array('android_app', $deviceList)) {
                // 対応デバイスに[AndroidApp]だけチェックされている場合
                if (!empty($this->get('require_apk_file')) ) {
                    // [APKファイル]がチェックされている場合、[APKファイルキー]入力チェック
                    return !empty(trim($value));
                }
            }

            return true;
        });

        $validate = [
            'title' => 'required|max:20|platform_dependent',
            'url' => 'required|url',
            'is_ssl' => 'required|in:0,1|is_available_protocol:'. implode(',', is_null($this->get('deviceList')) ? [] : $this->get('deviceList') ) . ',' . $this->get('url'),
            'type' => 'required|in:' . implode(',', array_keys(config('forms.SbxApplications.type'))),
            'deviceList' => 'required|array|sp_android_exists|sp_emulator_exists',
            'spLayout' => 'required|integer|in:1,2',
            'menu_position_portrait' => 'required|integer|between:0,8|position_not_selected',
            'menu_position_landscape' => 'required|integer|between:0,8|position_not_selected',
        ];
        foreach (config('forms.SbxApplications.device') as $key => $val) {
            $validate['deviceList.' . $key] = 'in:' . $key;
        }
        $validate['package_name'] = 'package_name_required|alpha_dash|two_byte_exists';
        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        $attributes = [
            'title' => 'タイトル',
            'url' => 'ガジェットURL',
            'is_ssl' => 'テストゲームSSL対応',
            'type' => 'タイプ',
            'deviceList' => '対応デバイス',
            'spLayout' => 'ゲーム画面レイアウト',
            'menu_position_portrait' => 'PFメニューボタン表示位置',
            'menu_position_landscape' => 'PFメニューボタン表示位置',
            'browser_sdk' => 'レシート課金対応',
            'package_name' => 'APKファイルキー',
        ];
        foreach (config('forms.SbxApplications.device') as $key => $val) {
            $attributes['deviceList.' . $key] = sprintf('対応デバイス[%s]', $val);
        }
        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $messages = [
            'title.max' => $this->MSG021,
            'title.platform_dependent' => $this->MSG076,
            'url.url' => $this->MSG034,
            'is_ssl.is_available_protocol' => $this->MSG312,
            'type.in' => $this->MSG012,
            'deviceList.array' => $this->MSG012,
            'deviceList.sp_android_exists' => $this->MSG262,
            'deviceList.sp_emulator_exists' => $this->MSG262,
            'package_name.required_with' => $this->MSG012,
            'package_name.alpha_dash' => $this->MSG211,
            'package_name.two_byte_exists' => $this->MSG211,
            'spLayout.required' => $this->MSG241,
            'spLayout.in' => $this->MSG241,
            'menu_position_portrait.required' => $this->MSG241,
            'menu_position_portrait.between' => $this->MSG241,
            'menu_position_portrait.position_not_selected' => $this->MSG241,
            'menu_position_landscape.required' => $this->MSG241,
            'menu_position_landscape.between' => $this->MSG241,
            'menu_position_landscape.position_not_selected' => $this->MSG241,
            'package_name.package_name_required' => $this->MSG012,
        ];
        foreach (config('forms.SbxApplications.device') as $key => $val) {
            $messages['deviceList.' . $key . '.in'] = $this->MSG012;
        }
        return $messages;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id',
                ],
            ],
        ];
    }
}
