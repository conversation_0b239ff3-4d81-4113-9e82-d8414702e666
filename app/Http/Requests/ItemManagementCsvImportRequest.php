<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ItemManagementService;
use Illuminate\Support\Facades\Validator;

class ItemManagementCsvImportRequest extends Request
{
    protected $PointLogsService;

    public function __construct(ItemManagementService $itemManagementService)
    {
        $this->ItemManagementService = $itemManagementService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::replacer('mimes', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':extension', $parameters[0], $message);
        });
        return [
            'csv_file' => 'required|mimes:csv,txt',
        ];
    }

    public function attributes()
    {
        return [
            'csv_file'    => 'CSVファイル',
        ];
    }

    public function customMessages()
    {
        return [
            'csv_file.required' => $this->MSG012,
            'csv_file.mimes' => $this->MSG237,
        ];
    }
}
