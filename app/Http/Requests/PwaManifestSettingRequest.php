<?php


namespace App\Http\Requests;

use Validator;

class PwaManifestSettingRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // regexなので配列で指定する #と小文字hex6桁
        Validator::extend('color', function ($attribute, $value, $parameters, $validator) {
            return !empty(preg_match('/^#[0-9a-f]{6}$/', $value));
        });
        return [
            'background_color' => ['required', 'color']
        ];
    }

    /**
     * Custom attributes list
     * @return array
     */
    public function attributes()
    {
        $attr = [
            'background_color' => '背景色',
        ];
        return $attr;
    }

    /**
     * Custom validation message
     * @return array
     */
    public function customMessages()
    {
        $msg = [
            'background_color.required' => $this->MSG012,
            'background_color.color' => $this->MSG011,
        ];
        return $msg;
    }
}