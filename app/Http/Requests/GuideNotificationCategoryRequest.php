<?php

namespace App\Http\Requests;

use Validator;
use App\Models\FreegameGuide\GuideNotification;

class GuideNotificationCategoryRequest extends Request
{
    protected $guideNoticModels;

    public function __construct(GuideNotification $guideNoticModels) {
        $this->guideNoticModels = $guideNoticModels;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        //----------
        // check_notic_join
        //----------
        // 紐付いているデータがないかのチェック
        Validator::extend('check_notic_join', function ($attribute, $value, $parameters) {
            if (empty($parameters[0])) {
                return false;
            }
            $guideAppId = $parameters[0];
            $countNotic = $this->guideNoticModels->getCountByGuideAppIdAndCategoryId($guideAppId, $value);
            if ($countNotic === 0) {
                return true;
            }
            return false;
        });
        //----------

        $validate = array();
        if (strcmp($this->path(), 'guide_notification_category/destroy') === 0) {
            $validate = [
                'noticcatid'       => 'required|check_notic_join:' . $this->get('guideAppId'),
            ];
        } else {
            $validate = [
                'name'             => 'required|max:20',
                'background_color' => 'required|max:6|regex:"^[a-fA-F0-9]+$"',
                'color'            => 'required|max:6|regex:"^[a-fA-F0-9]+$"',
            ];
        }

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        Validator::replacer('check_notic_join', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':other', 'お知らせ', $message);
        });

        return [
            'name'             => 'カテゴリ名',
            'background_color' => '背景色',
            'color'            => '文字色',
            'noticcatid'       => 'このカテゴリ',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'name.in'                     => $this->MSG012,
            'name.max'                    => $this->MSG021,
            'background_color.in'         => $this->MSG012,
            'background_color.max'        => $this->MSG021,
            'background_color.regex'      => $this->MSG011,
            'color.in'                    => $this->MSG012,
            'color.max'                   => $this->MSG021,
            'color.regex'                 => $this->MSG011,
            'noticcatid.check_notic_join' => $this->MSG260,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'noticcatid' => 'id',
                ],
            ]
        ];
    }
}
