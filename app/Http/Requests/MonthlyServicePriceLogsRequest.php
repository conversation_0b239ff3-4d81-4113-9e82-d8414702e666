<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\MonthlyServicePriceLogsService;
use Validator;

/**
 * レポート－月額課金サービス課金比較ツール
 */
class MonthlyServicePriceLogsRequest extends Request
{
    protected $priceLogsService;

    public function __construct(MonthlyServicePriceLogsService $priceLogsService)
    {
        $this->priceLogsService = $priceLogsService;
    }
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 期間が本日以前の日時であるか確認
        Validator::extend('before_today_exists', function ($attribute, $value, $parameters, $validator) {
            if (strtotime($value) > strtotime(date("Y/m/d", strtotime("-1 day")))) {
                return false;
            }
            return true;
        });

        // 期間終了が期間開始と同日以降か確認
        Validator::extend('equal_or_after_exists', function ($attribute, $value, $parameters, $validator) {
            if (strtotime($this->get('begin') . '00:00:00') <= strtotime($this->get('end') . '00:00:00')) {
                return true;
            }
            return false;
        });

        // セレクトボックスで選択できるapp_id
        $appTitleList = $this->priceLogsService->getMonthlyAppTitleList();
        // セレクトボックスで選択できる月額id
        $monthlyServiceList = $this->priceLogsService->getMonthlyServiceList();

        return [
            'begin' => 'required|date|before_today_exists',
            'end' => 'required|date|before_today_exists|equal_or_after_exists',
            'app_id' => 'required_without:monthly_service_id|integer|in:' . implode(',', array_keys($appTitleList)),
            // app_id / monthly_service_id 両方とも未入力の場合、両方ともエラーとしハイライトしたいためrequired_withoutを指定
            'monthly_service_id' => 'required_without:app_id|numeric|integer|in:' . implode(',', array_keys($monthlyServiceList)),
            'sap_log' => 'mimes:csv,txt',
        ];
    }

    public function attributes()
    {
        return [
            'begin' => '開始日',
            'end' => '終了日',
            'app_id' => 'タイトル',
            'monthly_service_id' => '月額サービス',
            'sap_log' => 'デベロッパー課金ログ',
        ];
    }

    public function customMessages()
    {
        return [
            'begin.date' => $this->MSG002,
            'end.date' => $this->MSG002,
            'numeric' => $this->MSG031,
            'in' => $this->MSG011,
            'before_today_exists' => $this->MSG005,
            'equal_or_after_exists' => $this->MSG265,
            'sap_log.mimes' => str_replace(':extension', 'csv', $this->MSG237),
            'app_id.required_without' => str_replace(':other', $this->attributes()['monthly_service_id'], $this->MSG335),
            // app_id / monthly_service_id 両方とも未入力の場合、両方をハイライトしたいがメッセージ重複するためスペースを設定（空文字の場合エラー判定されない）
            'monthly_service_id.required_without' => ' '
        ];
    }
}
