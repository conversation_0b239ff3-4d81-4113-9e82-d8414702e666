<?php

namespace App\Http\Requests;

use Validator;

class ApplicationImageTimerRequest extends Request
{
    protected $isUpdate = false;

//*********************************************************************************************************************
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     * @return array
     */
    public function rules()
    {
        $maxDayFromNow = config('forms.GameImage.timer.maxTimerSettingDateFromNow');
        // 本日より1年後
        $maxTimerDateFromNow = date('Y-m-d 23:59:00', strtotime("+".$maxDayFromNow." day"));

        $rules = [
            'app-id' => 'required|integer|min:1',
            'timer-thumbnail-200px' => 'integer|min:1|required_without_all:timer-thumbnail-80px,timer-thumbnail-60px',
            'timer-thumbnail-80px' => 'integer|min:1',
            'timer-thumbnail-60px' => 'integer|min:1',
            'timer-thumbnail-start' => 'required|date_format:"Y-m-d H:i"|after:"now"|before:'.$maxTimerDateFromNow,
            'timer-thumbnail-end' => 'required|date_format:"Y-m-d H:i"|after:"timer-thumbnail-start"|before:'.$maxTimerDateFromNow,
        ];

        if($this->isUpdate === true){
            $rules += ['timer-thumbnail-id' => 'required|string|min:36'];
        }

        return $rules;
    }

    protected function getValidatorInstance()
    {
        $routeName = $this->route()->getName();
        if($routeName === 'GameImage.updatetimer'){
            $this->isUpdate = true;
        }

        return parent::getValidatorInstance();
    }

    /**
     * Custom attributes list
     * @return array
     */
    public function attributes()
    {
        $attr = [
            'app-id' => 'ゲーム',
            'timer-thumbnail-id' => 'タイマー',
            'timer-thumbnail-start' => '掲載開始時間',
            'timer-thumbnail-end' => '掲載終了時間',
        ];
        return $attr;
    }

    /**
     * Custom validation message
     * @return array
     */
    public function customMessages()
    {
        $msg = [
            'app-id.required' => $this->MSG012,
            'timer-thumbnail-id.required' => $this->MSG012,
            'timer-thumbnail-start.required' => $this->MSG012,
            'timer-thumbnail-end.required' => $this->MSG012,
            'timer-thumbnail-start.date_time' => $this->MSG002,
            'timer-thumbnail-end.date_time' => $this->MSG002,
            'timer-thumbnail-start.after' => $this->MSG297,
            'timer-thumbnail-start.before' => $this->MSG296,
            'timer-thumbnail-end.after' => $this->MSG299,
            'timer-thumbnail-end.before' => $this->MSG296,
            'timer-thumbnail-200px.integer'                => $this->MSG031,
            'timer-thumbnail-80px.integer'                => $this->MSG031,
            'timer-thumbnail-60px.integer'                => $this->MSG031,
            'timer-thumbnail-200px.required_without_all' => $this->MSG301
        ];
        return $msg;
    }

    /**
     * Get the proper failed validation response for the request.
     * エラー時にsessionに値を入れてreturnする
     * オーバーライドしたのはその時のrouteを入れるため。updateとcreateで使う想定
     *
     * @param  array  $errors
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function response(array $errors)
    {
        $this->session()->flash('oldRoute', $this->route()->getName());

        return Request::response($errors);
    }
}