<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\GraphMonthlyService;

/**
 * レポート：月別表示・グラフ
 */
class GraphMonthlyRequest extends Request
{
    protected $GraphMonthlyService;

    public function __construct(GraphMonthlyService $GraphMonthlyService)
    {
        $this->GraphMonthlyService = $GraphMonthlyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'begin'  => 'date',
            'end'    => 'date',
            'app_id' => 'required|in:'.join(',', array_keys($this->GraphMonthlyService->getAppTitleType())),
            'device' => 'required',
            'report' => 'required'
        ];
    }

    public function attributes()
    {
        return [
            'begin'  => '開始月',
            'end'    => '終了月',
            'app_id' => 'タイトル',
            'device' => 'デバイス',
            'report' => '形式'
        ];
    }

    public function customMessages()
    {
        return [
            'date'      => $this->MSG002,
            'app_id.in' => $this->MSG012,
        ];
    }
}
