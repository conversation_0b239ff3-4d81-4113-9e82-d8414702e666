<?php
namespace App\Http\Requests;

use Illuminate\Support\Facades\Validator;

class ItemManagementSearchRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'status' => 'in:PUBLISHED,UNPUBLISHED',
        ];
    }

    public function getSearchCondition()
    {
        $currentPage = $this->request->get('page', config('forms.common.pagination.defaultPage'));
        $limitPage = $this->request->get('perPage', config('forms.common.pagination.defaultPerPage'));
        return [
            'app_id' => $this->request->get('app_id'),
            'device' => $this->request->get('device'),
            'item_id' => $this->request->get('item_id', ''),
            'item_name' => $this->request->get('item_name', ''),
            'status' => $this->request->get('status', ''),
            'limit' => $limitPage,
            'offset' => ($currentPage - 1) * $limitPage,
        ];
    }
}