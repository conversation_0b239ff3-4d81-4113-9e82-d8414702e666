<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ProfileService;
use Validator;

class ProfileRequest extends Request
{
    protected $ProfileService;

    public function __construct(ProfileService $ProfileService)
    {
        $this->ProfileService = $ProfileService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        if (preg_match('/update/', $this->path())) {
            Validator::extend('developer_user', function ($attribute, $value, $parameters, $validator) {
                if (empty($value)) {
                    return true;
                }
                $listCommunities = $this->ProfileService->getCommunities(['manager_user_id' => $value]);
                if ($listCommunities->count() == 0) {
                    return false;
                }
                $appId = '';
                foreach ($listCommunities as $val) {
                    $appId = $val->app_id;
                    break;
                }
                return $this->ProfileService->checkStillDeveloper($appId);
            });

            return [
                'user_id'  => 'required|numeric|developer_user',
                'image_id' => 'required',
                'number'   => 'required|numeric',
            ];
        } else {
            return [
                'file' => 'required|mimes:png,jpg,jpe,jpeg,gif|image_max:2',
            ];
        }
    }

    public function attributes()
    {
        return [
            'file'     => '画像ファイル',
            'user_id'  => 'ユーザーID',
            'image_id' => '画像ID',
            'number'   => '画像ナンバー',
        ];
    }

    public function customMessages()
    {
        return [
            'file.mimes'             => $this->MSG228,
            'file.image_max'         => $this->MSG029,
            'user_id.numeric'        => $this->MSG031,
            'user_id.developer_user' => $this->MSG061,
            'number.numeric'         => $this->MSG031,
        ];
    }

    protected function redirectRules()
    {
        return [
            'upload' => [
                'action' => 'index',
                'parameters' => [
                    'app_id' => 'app_id'
                ]
            ],
            'update' => [
                'action' => 'index',
                'parameters' => [
                    'app_id' => 'app_id'
                ]
            ],
        ];
    }
}
