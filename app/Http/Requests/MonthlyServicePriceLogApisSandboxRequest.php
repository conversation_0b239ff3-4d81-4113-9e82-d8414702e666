<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\MonthlyServicePriceLogApisSandboxService;
use Validator;

class MonthlyServicePriceLogApisSandboxRequest extends Request
{
    protected $MonthlyServicePriceLogApisSandboxService;

    public function __construct(MonthlyServicePriceLogApisSandboxService $MonthlyServicePriceLogApisSandboxService)
    {
        $this->MonthlyServicePriceLogApisSandboxService = $MonthlyServicePriceLogApisSandboxService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('after_same_day_is_ok', function ($attribute, $value, $parameters, $validator) {
            $begin = request($parameters[0]);
            if (!empty($value) && !empty($begin)) {
                $begintime = strtotime($begin);
                $endtime   = strtotime($value);
                if ($begintime > $endtime) {
                    return false;
                }
            }
            return true;
        });

        return [
            'begin'              => 'required|date',
            'end'                => 'required|date|after_same_day_is_ok:begin',
            'monthly_service_id' => 'required|in:'.join(',', array_keys($this->MonthlyServicePriceLogApisSandboxService->getServiceList())),
        ];
    }

    public function attributes()
    {
        return [
            'begin'              => '開始日',
            'end'                => '終了日',
            'monthly_service_id' => '月額サービス',
        ];
    }

    public function customMessages()
    {
        return [
            'date'                  => $this->MSG002,
            'after_same_day_is_ok'  => $this->MSG007,
            'monthly_service_id.in' => $this->MSG012,
        ];
    }

    protected function redirectRules()
    {
        return [
            'csvdownload' => [
                'action' => 'index',
            ],
        ];
    }
}
