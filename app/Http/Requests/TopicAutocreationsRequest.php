<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\TopicAutocreationsService;
use Validator;

class TopicAutocreationsRequest extends Request
{
    protected $TopicAutocreationsService;

    public function __construct(TopicAutocreationsService $TopicAutocreationsService)
    {
        $this->TopicAutocreationsService = $TopicAutocreationsService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('unique_data', function ($attribute, $value, $parameters, $validator) {
            $dataTopic = $this->TopicAutocreationsService->getTopicOne(Request::all()['last_topic_id']);
            if (count($dataTopic) == 0) {
                return true;
            }

            $params = array(
                'community_id'  => $dataTopic['community_id'],
                'last_topic_id' => $dataTopic['id'],
                'status'        => config('forms.TopicAutocreations.status'),
            );

            if (count($this->TopicAutocreationsService->getTopicAutocreationsOneByConditions($params)) > 0) {
                return false;
            }

            return true;
        });

        Validator::extend('developer', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                $dataTopic = $this->TopicAutocreationsService->getTopicOne($value);
                if (count($dataTopic) == 0) {
                    return false;
                }
                return $this->TopicAutocreationsService->checkStillDeveloper($dataTopic['community_id']);
            }
            return true;
        });

        if ($this->path() === 'topic_autocreations/index') {
            return[
                'topic_title' => 'special_characters',
            ];
        }

        return[
            'last_topic_id'        => 'required|unique_data|numeric|developer',
            'title_template'       => 'required|max:100|platform_dependent|picture_characters|control_characters',
            'next_number'          => 'required|integer|min:2|numeric',
            'topic_create_comment' => 'required|in:' . 
                                       implode(',', config('forms.TopicAutocreations.topicCreateComment')),
        ];
    }

    public function attributes()
    {
        return[
            'last_topic_id'        => '参照トピック',
            'title_template'       => 'トピック名',
            'next_number'          => '次のトピックNo.',
            'topic_title'          => 'トピック名',
            'topic_create_comment' => 'トピック生成コメント数',
        ];
    }

    public function customMessages()
    {
        return[
            'last_topic_id.unique_data'         => $this->MSG181,
            'last_topic_id.numeric'             => $this->MSG031,
            'last_topic_id.developer'           => $this->MSG264,
            'title_template.platform_dependent' => $this->MSG076,
            'title_template.picture_characters' => $this->MSG077,
            'title_template.control_characters' => $this->MSG225,
            'next_number.numeric'               => $this->MSG031,
            'topic_title.special_characters'    => $this->MSG232,
            'topic_create_comment.in'           => $this->MSG011,
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
        ];
    }
}
