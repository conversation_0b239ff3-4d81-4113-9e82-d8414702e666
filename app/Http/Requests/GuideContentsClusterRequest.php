<?php

namespace App\Http\Requests;

use App\Services\GuideContentsService;
use Validator;

class GuideContentsClusterRequest extends Request
{
    protected $guideContentsService;

    // text型の最大バイト数
    protected $maxTextByteSize = 65535;

    public function __construct(GuideContentsService $guideContentsService)
    {
        parent::__construct();
        $this->guideContentsService = $guideContentsService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $routeName = $this->route()->getName();

        switch ($routeName) {
            case 'GuideContents.createClusterConfirm':
            case 'GuideContents.storeCluster':
                $masterId = $this->get('master_id');

                Validator::extend(
                    'cluster_key_unique',
                    function ($attribute, $value, $parameters, $validator) use ($masterId) {
                        return $this->guideContentsService->isUniqueClusterKey($masterId, $value);
                    }
                );

                $rules = [
                    'cluster_key' => [
                        'required',
                        'max:45',
                        'alfa_num_line_dot_slash_only',
                        'cluster_key_unique:' . $masterId,
                    ],
                ];
                break;
            case 'GuideContents.editClusterConfirm':
            case 'GuideContents.updateCluster':
                $clusterId = $this->get('id');
                $contentsData = $this->guideContentsService->getContentsData($clusterId);
                $masterId = $contentsData['master_id'];
                $rules = [];
                break;
        }

        if (!$masterId) {
            App::abort(500);
        }

        $rules += [
            'name' => [
                'required',
                'max:100',
            ],
            'order_no' => [
                'required',
            ],
            'memo' => [
                'max:20000',
            ],
            'view_flag' => [
                'required',
                'in:0,1',
            ],
            'period_flag' => [
                'required',
                'in:0,1',
            ],
        ];

        if ($this->get('period_flag') == '1') {
            $rules += [
                'begin_time' => [
                    'required',
                    'date_time:Y/m/d H:i'
                ],
                'end_time' => [
                    'required',
                    'date_time:Y/m/d H:i',
                    'after:begin_time'
                ],
            ];  
        }   

        Validator::extend(
            'image_extension',
            function ($attribute, $value, $parameters, $validator) {
                return in_array(pathinfo($value, PATHINFO_EXTENSION), $parameters);
            }
        );

        Validator::replacer(
            'image_extension',
            function ($message, $attribute, $rule, $parameters) {
               return str_replace(':extension', implode(',', $parameters), $message);
            }
        );

        Validator::extend(
            'html_byte_extension',
            function ($attribute, $value, $parameters, $validator) {
                return strlen($value) <= $this->maxTextByteSize;
            }
        );

        Validator::replacer(
            'html_byte_extension',
            function ($message, $attribute, $rule, $parameters) {
               return str_replace(':byte', $this->maxTextByteSize, $message);
            }
        );

        $itemList = $this->guideContentsService->getTemplateList($masterId);
        foreach ($itemList as $itemId => $item) {
            $formName = 'values.' . $itemId . '.value';
            switch ($item['value_type']) {
                case 'alnum':
                    $rules[$formName] = [
                        'alfa_num_line_dot_only',
                        'max:20000',
                    ];
                    break;
                case 'text':
                    $rules[$formName] = [
                        'max:20000',
                    ];
                    break;
                case 'flag':
                    $rules[$formName] = [
                        'required',
                        'in:0,1',
                    ];
                    break;
                case 'digit':
                    $rules[$formName] = [
                        'integer',
                    ];
                    break;
                case 'image_url':
                    $rules[$formName] = [
                        'url',
                        'image_extension:jpg,jpeg,gif,png',
                    ];
                    break;
                case 'url':
                    $rules[$formName] = [
                        'url',
                        'max:20000',
                    ];
                    break;
                case 'html':
                    $rules[$formName] = [
                        'html_byte_extension',
                    ];
                    break;
                case 'date':
                    $rules[$formName] = [
                        'date_time:Y/m/d H:i',
                    ];
                    break;
            }
        }
        return $rules;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        $attributes = [
            'name' => 'データタイトル',
            'cluster_key' => 'データキー',
            'order_no' => '表示順',
            'view_flag' => '表示設定',
            'period_flag' => '表示期間',
            'begin_time' => '表示開始日時',
            'end_time' => '表示終了日時',
            'memo' => '備考',
        ];  

        $routeName = $this->route()->getName();

        switch ($routeName) {
            case  'GuideContents.createClusterConfirm':
            case  'GuideContents.storeCluster':
                $masterId = $this->get('master_id');
                break;
            case 'GuideContents.editClusterConfirm':
            case 'GuideContents.updateCluster':
                $clusterId = $this->get('id');
                $contentsData = $this->guideContentsService->getContentsData($clusterId);
                $masterId = $contentsData['master_id'];
                break;
        }

        $itemList = $this->guideContentsService->getTemplateList($masterId);
        foreach ($itemList as $itemId => $item) {
            $formName = 'values.' . $itemId . '.value';
            $attributes[$formName] = $item['template_key'];
        }

        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $message = [
            'cluster_key.cluster_key_unique' => $this->MSG306,
            'cluster_key.alfa_num_line_dot_slash_only' => $this->MSG311,
            'end_time.after' => $this->MSG310,
        ];

        $values = $this->request->get('values', []);
        if (!empty($values)) {
            foreach ($values as $key => $value) {
                $message['values.' . $key . '.value.alfa_num_line_dot_only'] = $this->MSG308;
                $message['values.' . $key . '.value.image_extension'] = $this->MSG309;
                $message['values.' . $key . '.value.html_byte_extension'] = $this->MSG121;
            }
        }
        return $message;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'masterStore' => [
                'action' => 'createMaster',
                'parameters' => [
                    'id' => 'guideAppId',
                ],  
            ],  
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppid',
                ],
            ]
        ];
    }
}
