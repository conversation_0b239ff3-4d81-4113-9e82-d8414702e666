<?php

namespace App\Http\Requests;

use Illuminate\Http\Exception\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Validator;
use Log;

class GameContentImageRegisterRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $imageInformation = $this->getImageInformation();
        return [
            'file_extension' => 'required|in:' . join(',', $imageInformation['extensions']),
            'file_size' => 'required|numeric|max:' . $imageInformation['maxFileSizeInBytes'],
        ];
    }

    public function attributes()
    {
        return [
            'file_extension' => 'ファイル',
            'file_size' => 'ファイル容量',
        ];
    }

    public function customMessages()
    {
        $imageInformation = $this->getImageInformation();
        return [
            'file_extension.required' => $this->MSG012,
            'file_extension.in' => str_replace(':extension', join(',', $imageInformation['extensions']), $this->MSG237),
            'file_size.max' => str_replace(':max', number_format($imageInformation['maxFileSizeInBytes'] / 1073741824, 0), $this->MSG246),
        ];
    }

    public function response(array $errors)
    {
        $response = [
            'status' => 500,
            'message' => 'Internal Server Error',
            'errors' => $errors,
        ];
        return new JsonResponse($response, 500);
    }

    /**
     * ファイル情報の取得
     * @return array
     */
    private function getImageInformation()
    {
        $imageType = $this->request->get('image_type');
        $routeName = $this->route()->getName();
        switch ($routeName) {
            case 'GameContentImage.register.upload.check':
                return config('forms.GameContentImage')['imageType'][$imageType];
            case 'ChGameContentImage.register.upload.check':
                return config('forms.ChGameContentImage')['imageType'][$imageType];
            case 'ClGameContentImage.register.upload.check':
                return config('forms.ClGameContentImage')['imageType'][$imageType];
            default:
                Log::error('不正なルートからのリクエストがありました。', ['route' => $routeName, 'image_type' => $imageType]);
                throw new HttpResponseException(response()->json(['success' => false, 'errors' => [$this->MSG337]], 422));
        }
    }
}