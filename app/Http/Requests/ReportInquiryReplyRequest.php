<?php
namespace App\Http\Requests;

use App\Services\ReportInquiryReplyService;
use Validator;
use Carbon\Carbon;

class ReportInquiryReplyRequest extends Request
{
    protected $reportInquiryReplyService;

    public function __construct(ReportInquiryReplyService $reportInquiryReplyService)
    {
        $this->reportInquiryReplyService = $reportInquiryReplyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $maxDateRange = config('forms.ReportInquiryReply.exportCsv.defaultValues.maxDateRange');
        $begin = $this->get('begin');
        $appTitleType = $this->reportInquiryReplyService->getAppTitleType();
        $appId = $this->get('app_id');
        
        Validator::extend('in_app_id', function ($attribute, $value, $parameters, $validator) {
            if ($parameters[0] == 0
                || $value == ''
                || $this->reportInquiryReplyService->isValidHuman($value, $parameters[0])
            ) {
                return true;
            }
            return false;
        });
        Validator::extend('after_or_equal', function ($attribute, $value, $parameters, $validator) {
            if (strtotime($value) >= strtotime($parameters[0])) {
                return true;
            }
            return false;
        });
        Validator::extend('is_range_less_than', function ($attribute, $value, $parameters, $validator) use ($begin) {
            $begin  = Carbon::parse($begin);
            $end    = Carbon::parse($value);
            $period = $end->diffInDays($begin);
            if ($period < $parameters[0]) {
                return true;
            }
            return false;
        });
        Validator::replacer('is_range_less_than', function ($message, $attribute, $rule, $parameters) {
            $replaceMaxAttribute = str_replace(':max', $parameters[0], $message);
            return str_replace(':start', $parameters[1], $replaceMaxAttribute);
        });

        return [
            'begin'           => 'required|date',
            'end'             => 'required|date|after_or_equal:' . $begin
                . '|is_range_less_than:' . $maxDateRange . ',' . $begin,
            'app_id'          => 'required|in:'
                . join(',', array_keys($appTitleType)),
            'human_in_charge' => 'in_app_id:' . $appId
        ];
    }

    public function attributes()
    {
        return [
            'begin'           => '開始日',
            'end'             => '終了日',
            'app_id'          => 'タイトル',
            'human_in_charge' => '担当者 ',
        ];
    }

    public function customMessages()
    {
        return [
            'begin.required'            => $this->MSG001,
            'begin.date'                => $this->MSG002,
            'end.required'              => $this->MSG001,
            'end.date'                  => $this->MSG002,
            'end.after_or_equal'        => $this->MSG007,
            'app_id.required'           => $this->MSG012,
            'app_id.in'                 => $this->MSG011,
            'human_in_charge.in_app_id' => $this->MSG011,
            'end.is_range_less_than'    => $this->MSG272,
        ];
    }
}
