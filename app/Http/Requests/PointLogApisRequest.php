<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\PointLogApisService;
use Validator;

/**
 * レポート－課金ログAPI比較
 */
class PointLogApisRequest extends Request
{
    protected $PointLogApisService;

    public function __construct(PointLogApisService $PointLogApisService)
    {
        $this->PointLogApisService = $PointLogApisService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('before_today_exists', function ($attribute, $value, $parameters, $validator) {
            if (strtotime($value) > strtotime(date("Y/m/d", strtotime("-1 day")))) {
                return false;
            }
            return true;
        });

        return [
            'begin'  => 'required|date',
            'end'    => 'required|date|before_today_exists',
            'app_id' => 'required|in:'.join(',', array_keys($this->PointLogApisService->getAppList($this->input('type')))),
            'device' => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'begin'  => '開始日',
            'end'    => '終了日',
            'app_id' => 'タイトル',
            'device' => 'デバイス',
        ];
    }

    public function customMessages()
    {
        return [
            'date'                => $this->MSG002,
            'before_today_exists' => $this->MSG005,
            'app_id.in'           => $this->MSG012,
        ];
    }
}
