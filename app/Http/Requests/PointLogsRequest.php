<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\PointLogsService;
use Validator;

class PointLogsRequest extends Request
{
    protected $PointLogsService;

    public function __construct(PointLogsService $PointLogsService)
    {
        $this->PointLogsService = $PointLogsService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('aftertime', function ($attribute, $value, $parameters, $validator) {
            $begin = request('begin');

            if (!empty($value) && !empty($begin)) {
                $date = explode('/', $value);
                if (count($date) != 3) {
                    return true;
                }
                if (!checkdate($date[1], $date[2], $date[0])) {
                    return true;
                }

                $date = explode('/', $begin);
                if (count($date) != 3) {
                    return true;
                }
                if (!checkdate($date[1], $date[2], $date[0])) {
                    return true;
                }

                $begintime = strtotime($begin.' 00:00:00');
                $endtime   = strtotime($value.' 23:59:59');

                if ($begintime >= $endtime) {
                    return false;
                }
            }

            return true;
        });

        return [
            'begin'     => 'required|date',
            'end'       => 'required|date|aftertime:begin',
            'app_id'    => 'required|in:'.join(',', array_keys($this->PointLogsService->getApplicationList())),
            'sap_point' => 'mimes:csv,txt',
            'device'    => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'begin'     => '開始日',
            'end'       => '終了日',
            'app_id'    => 'アプリID',
            'sap_point' => 'デベロッパー課金ログ',
            'device'    => 'デバイス',
        ];
    }

    public function customMessages()
    {
        return [
            'date'            => $this->MSG002,
            'aftertime'       => $this->MSG007,
            'app_id.in'       => $this->MSG012,
            'sap_point.mimes' => str_replace(':extension', 'csv', $this->MSG237),
        ];
    }

    protected function redirectRules()
    {
        return [
            'csvdownload' => ['action' => 'index'],
        ];
    }
}
