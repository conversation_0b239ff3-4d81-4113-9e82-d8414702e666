<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ItemManagementService;

class ItemManagementPriceRequest extends Request
{
    protected $PointLogsService;

    public function __construct(ItemManagementService $itemManagementService)
    {
        $this->ItemManagementService = $itemManagementService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'amount'        => 'required|integer|between:1,999999',
            'title'         => 'required|max:55|restricted_characters',
            'description'   => 'required|max:80|restricted_characters',
        ];
    }

    public function attributes()
    {
        return [
            'amount'        => '価格',
            'title'         => '名前',
            'description'   => '商品説明',
        ];
    }

    public function customMessages()
    {
        return [
            'amount.between' => $this->MSG291,
            'title.restricted_characters' => $this->MSG295,
            'description.restricted_characters' => $this->MSG295,
        ];
    }
}
