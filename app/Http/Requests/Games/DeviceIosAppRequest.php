<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;

/**
 * ゲーム登録：iOS App情報
 */
class DeviceIosAppRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'description' => 'required|max_emoji:300|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'description_middle' => 'required|max_emoji:20|platform_dependent|except_tag_html|restricted_characters',
            'how_to_play' => 'max:800|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'restrictions' => 'required|max:5000|platform_dependent|picture_characters|except_tag_html'
        ];
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'description' => '紹介文',
            'description_middle' => '紹介文（20文字）',
            'how_to_play' => 'ゲームの遊び方',
            'restrictions' => '対応機種'
        ];
    }

    public function customMessages()
    {
        return [
            'max_emoji' => $this->MSG021,
            'platform_dependent' => $this->MSG076,
            'control_characters' => $this->MSG261,
            'picture_characters' => $this->MSG077,
            'restricted_characters' => $this->MSG295
        ];
    }

    protected function redirectRules()
    {
        return [
            'device.android.app.update' => [
                'action' => 'device.android.app.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
