<?php
namespace App\Http\Requests\Games;

use Validator;
use App\Http\Requests\Request;

/**
 * ゲーム登録：APKクラウド情報
 */
class DeviceApkCloudRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $validate = [
            'id'                    => 'required',
            'description'           => 'required|max_emoji:5000|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'description_middle'    => 'required|max_emoji:20|platform_dependent|except_tag_html|restricted_characters',
            'how_to_play'           => 'max:2000|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'restrictions'          => 'required|max:5000|platform_dependent|picture_characters|except_tag_html',
            'maintenance_begin'     => 'date_time:"Y/m/d H:i"',
            'maintenance_end'       => 'date_time:"Y/m/d H:i"|after:maintenance_begin',
        ];

        Validator::replacer('after', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':other', 'メンテナンス終了日時', $message);
        });

        return $validate;
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'description' => '紹介文',
            'description_middle' => '紹介文（20文字）',
            'how_to_play' => 'ゲームの遊び方',
            'restrictions' => '対応機種',
            'maintenance_begin' => 'メンテナンス開始日時',
            'maintenance_end' => 'メンテナンス終了日時',
        ];
    }

    public function customMessages()
    {
        return [
            'max_emoji' => $this->MSG021,
            'platform_dependent' => $this->MSG076,
            'control_characters' => $this->MSG261,
            'picture_characters' => $this->MSG077,
            'restricted_characters' => $this->MSG295,
            'after'                       => $this->MSG225,
        ];
    }

    protected function redirectRules()
    {
        return [
            'device.apkcloud.update' => [
                'action' => 'device.apkcloud.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
