<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;

/**
 * ゲーム登録：FeaturePhone情報
 */
class DeviceMobileRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'description' => 'required|max_emoji:300|platform_dependent|special_characters|control_characters|except_tag_html',
            'description_middle' => 'required|max_emoji:20|platform_dependent|except_tag_html',
            'description_short' => 'required|max_emoji:10|platform_dependent|except_tag_html',
            'how_to_play' => 'max:800|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'restrictions' => 'required|max:5000|platform_dependent|picture_characters|except_tag_html'
        ];
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'description' => '紹介文',
            'description_middle' => '紹介文（20文字）',
            'description_short' => '紹介文（10文字）',
            'how_to_play' => 'ゲームの遊び方',
            'restrictions' => '対応機種'
        ];
    }

    public function customMessages()
    {
        return [
            'max_emoji' => $this->MSG021,
            'platform_dependent' => $this->MSG076,
            'special_characters' => $this->MSG232,
            'control_characters' => $this->MSG261,
            'picture_characters' => $this->MSG077,
        ];
    }

    protected function redirectRules()
    {
        return [
            'device.mobile.update' => [
                'action' => 'device.mobile.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
