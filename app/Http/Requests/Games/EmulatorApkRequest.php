<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;
use App\Services\GamesService;
use Validator;

/**
 * ゲーム登録：EmulatorAPK情報
 */
class EmulatorApkRequest extends Request
{

    protected $service;

    public function __construct(GamesService $service)
    {
        $this->service = $service;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id'       => 'required',
            'url'      => 'required|max:255|url',
            'apk_file' => 'required|mimes:apk,zip,jar',
        ];
    }

    public function attributes()
    {
        return [
            'id'       => 'アプリID',
            'url'      => '公式サイトURL',
            'apk_file' => 'APKファイル',
        ];
    }

    public function customMessages()
    {
        return [
            'url.url'        => $this->MSG327,
-           'url.max'        => $this->MSG021,
            'apk_file.mimes' => $this->MSG325,
        ];
    }

    protected function redirectRules()
    {
        return [
            'emulatorapk.update' => [
                'action' => 'emulatorapk.edit',
                'parameters' => [
                    'id' => 'id',
                ]
            ]
        ];
    }
}
