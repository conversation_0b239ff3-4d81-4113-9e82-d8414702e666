<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;
use Validator;
use App\Services\GamesService;

/**
 * ゲーム登録：タイトルグループ情報
 */
class TitleGroupRequest extends Request
{
    protected $gamesService;

    public function authorize()
    {
        return true;
    }

    public function __construct(GamesService $gamesService)
    {
        $this->gamesService = $gamesService;
    }

    public function rules()
    {
        // バリデーション拡張：タイトルグループ重複チェック
        Validator::extend('group_duplicate', function ($attribute, $value, $parameters, $validator) {
            $validArr = $this->gamesService->makeTitleGroupAppIds($this->get('app_id'));
            $id = $this->get('id') ? $this->get('id') : null;
            foreach ($validArr as $tableColumn => $appID) {
                if ($this->gamesService->isTitleGroupExists($tableColumn, $appID, $id) === true) {
                    return false;
                }
            }
            return true;
        });

        $configs = config('forms.ApplicationTitleGroup');
        return [
            'app_id'             => 'required|group_duplicate',
            'app_store_url'      => 'url|max:2048',
            'google_play_url'    => 'url|max:2048',
            'begin'              => 'required_with:end|date_time:"'. $configs['formatDate'].'"',
            'end'                => 'required_with:begin|date_time:"'. $configs['formatDate'].'"|after:begin',
        ];
    }

    public function attributes()
    {
        return [
            'app_store_url'         => 'App StoreのURL',
            'google_play_url'       => 'Google PlayのURL',
            'begin'                 => 'App Store・Google Playの公開開始日時',
            'end'                   => 'App Store・Google Playの公開終了日時',
        ];
    }

    public function customMessages()
    {
        return [
            'app_id.required'                => $this->MSG162,
            'app_id.group_duplicate'         => $this->MSG162,
            'app_store_url.url'              => $this->MSG034,
            'google_play_url.url'            => $this->MSG034,
            'begin.required_with'            => $this->MSG012,
            'end.required_with'              => $this->MSG012,
            'end.after'                      => $this->MSG007,
        ];
    }

    protected function redirectRules()
    {
        return [
            'titleGroup.update' => [
                'action' => 'titleGroup.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
