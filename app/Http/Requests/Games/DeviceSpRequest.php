<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;

/**
 * ゲーム登録：SmartPhone情報
 */
class DeviceSpRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $validate = [
            'id' => 'required',
            'description' => 'required|max_emoji:5000|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'description_middle' => 'required|max_emoji:20|platform_dependent|except_tag_html|restricted_characters',
            'how_to_play' => 'max:2000|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'restrictions' => 'required|max:5000|platform_dependent|picture_characters|except_tag_html',
            'spLayout' => 'required',
            'menu_position_portrait' => 'required',
            'menu_position_landscape' => 'required',
        ];

        return $validate;
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'description' => '紹介文',
            'description_middle' => '紹介文（20文字）',
            'how_to_play' => 'ゲームの遊び方',
            'restrictions' => '対応機種',
            'menu_position_portrait' => '縦向き',
            'menu_position_landscape' => '横向き',
        ];
    }

    public function customMessages()
    {
        return [
            'max_emoji' => $this->MSG021,
            'platform_dependent' => $this->MSG076,
            'control_characters' => $this->MSG261,
            'picture_characters' => $this->MSG077,
            'restricted_characters' => $this->MSG295,
            'menu_position_portrait' => $this->MSG314,
            'menu_position_landscape' => $this->MSG314,
        ];
    }

    protected function redirectRules()
    {
        return [
            'device.sp.update' => [
                'action' => 'device.sp.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
