<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;
use Validator;

/**
 * ゲーム登録：Emulatorキーマッピング設定ファイル
 */
class EmulatorKeyMappingFileRequest extends Request
{

    /**
     * 権限認証
     * ※使わない場合はtrueを返すようにする
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * リクエストパラメータのバリデーションルール
     *
     * @return array
     */
    public function rules()
    {
        // デフォルトのmimesバリデーションだとCFG拡張子がないためファイル名から確認
        Validator::extend('mimes_cfg', function ($attribute, $value, $parameters, $validator) {
            return pathinfo($value->getClientOriginalName(), PATHINFO_EXTENSION) === 'cfg';
        });
        return [
            'id' => 'required',
            'key_mapping_file' => 'mimes_cfg',
        ];
    }

    /**
     * リクエストパラメータ
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'key_mapping_file' => '設定ファイル',
        ];
    }

    /**
     * カスタムメッセージ
     *
     * @return array
     */
    public function customMessages()
    {
        return ['key_mapping_file.mimes_cfg' => str_replace(':extension', 'cfg', $this->MSG237)];
    }

    /**
     * バリデーションエラー時のリダイレクトルール
     *
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'emulatorkeymapping.update' => [
                'action' => 'emulatorkeymapping.editconfirm',
                'parameters' => [
                    'id' => 'id',
                    'is_enable' => 'is_enable',
                ],
            ],
        ];
    }
}
