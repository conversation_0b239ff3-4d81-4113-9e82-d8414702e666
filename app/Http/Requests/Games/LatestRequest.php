<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;

/**
 * ゲーム登録：新着情報
 */
class LatestRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'app_id' => 'required',
            'title' => 'required|max:45|platform_dependent|picture_characters|except_tag_html',
            'detail' => 'required|max:3000|platform_dependent|picture_characters|except_tag_html',
            'begin_date' => 'required|date',
            'end_date' => 'required|date|after:begin_date'
        ];
    }

    public function attributes()
    {
        return [
            'id' => '新着ID',
            'app_id' => 'アプリID',
            'title' => 'タイトル',
            'detail' => '詳細文',
            'begin_date' => '掲載開始日時',
            'end_date' => '掲載終了日時'
        ];
    }

    public function customMessages()
    {
        return [
            'platform_dependent' => $this->MSG076,
            'picture_characters' => $this->MSG077,
            'begin_date.date' => $this->MSG002,
            'end_date.date' => $this->MSG002,
            'end_date.after' => $this->MSG007,
        ];
    }

    protected function redirectRules()
    {
        return [
            'latest.store' => [
                'action' => 'latest.create',
                'parameters' => [
                    'id' => 'app_id'
                ]
            ],
            'latest.update' => [
                'action' => 'latest.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
