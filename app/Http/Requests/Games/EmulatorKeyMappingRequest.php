<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;
use App\Services\GamesService;
use Validator;

/**
 * ゲーム登録：Emulatorキーマッピング情報
 */
class EmulatorKeyMappingRequest extends Request
{

    /** @var GamesService */
    protected $service;

    /**
     * コンストラクタ
     *
     * @param GamesService $service
     */
    public function __construct(GamesService $service)
    {
        $this->service = $service;
    }

    /**
     * 権限認証
     * ※使わない場合はtrueを返すようにする
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * リクエストパラメータのバリデーションルール
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id' => 'required',
            'is_enable' => 'in:1,0',
        ];
    }

    /**
     * リクエストパラメータ
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'is_enable' => 'キーマッピング',
        ];
    }

    /**
     * カスタムメッセージ
     *
     * @return array
     */
    public function customMessages()
    {
        return ['is_enable.in' => $this->MSG081];
    }

    /**
     * バリデーションエラー時のリダイレクトルール
     *
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'emulatorkeymapping.update' => [
                'action' => 'emulatorkeymapping.edit',
                'parameters' => ['id' => 'id'],
            ],
        ];
    }
}
