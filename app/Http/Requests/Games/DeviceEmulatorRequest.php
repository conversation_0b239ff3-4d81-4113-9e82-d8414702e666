<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;

/**
 * ゲーム登録：Emulator情報
 */
class DeviceEmulatorRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'description_middle' => 'required|max_emoji:20|platform_dependent|except_tag_html|restricted_characters'
        ];
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'description_middle' => '紹介文（20文字）'
        ];
    }

    public function customMessages()
    {
        return [
            'max_emoji' => $this->MSG021,
            'platform_dependent' => $this->MSG076,
            'special_characters' => $this->MSG071,
            'control_characters' => $this->MSG225,
            'picture_characters' => $this->MSG077,
            'restricted_characters' => $this->MSG295
        ];
    }

    protected function redirectRules()
    {
        return [
            'device.emulator.update' => [
                'action' => 'device.emulator.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
