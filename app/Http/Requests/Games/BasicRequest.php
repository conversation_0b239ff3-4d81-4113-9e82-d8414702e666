<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;
use App\Services\GamesService;

/**
 * ゲーム登録：基本情報
 */
class BasicRequest extends Request
{

    protected $service;

    public function __construct(GamesService $service)
    {
        $this->service = $service;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id'                => 'required',
            'title_short'       => 'required|max:20|platform_dependent|picture_characters',
            'title_ruby'        => 'required|max:40|hiragana_only',
            'url'               => 'required|max:255|url|secure_url:'.$this->get('isSslDevice'),
            'genre_id'          => 'required|in:' . implode(',', array_keys($this->service->getGenreNameType())),
            'twitter_follow'    => 'in:' . implode(',', array_keys(config('forms.Games.twitterFollowType'))),
            'email'             => 'required|max:255|email',
            'inquiry_note'      => 'max:200|script_tag',
            'inquiry_signature' => 'max:1000|string|special_characters|picture_characters|no_all_html_tag',
            'meta_description'  => 'min:70|max:140|string|special_characters|picture_characters|no_all_html_tag',
        ];
    }

    public function attributes()
    {
        return [
            'id'                => 'アプリID',
            'title_short'       => 'タイトル（短縮用）',
            'title_ruby'        => 'タイトル（よみがな）',
            'url'               => 'ガジェットURL',
            'genre_id'          => 'ジャンル',
            'twitter_follow'    => 'フォローボタンの表示',
            'email'             => '問い合わせ先メールアドレス',
            'inquiry_note'      => '問い合わせ注意事項',
            'inquiry_signature' => '問い合わせ返信用署名',
            'meta_description'  => 'meta description',
        ];
    }

    public function customMessages()
    {
        return [
            'in'                 => $this->MSG012,
            'email'              => $this->MSG245,
            'hiragana_only'      => $this->MSG058,
            'secure_url'          => $this->MSG315,
            'platform_dependent' => $this->MSG076,
            'picture_characters' => $this->MSG077,
            'script_tag'         => $this->MSG239,
            'special_characters' => $this->MSG232,
            'picture_characters' => $this->MSG077,
        ];
    }

    protected function redirectRules()
    {
        return [
            'basic.update' => [
                'action' => 'basic.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }
}
