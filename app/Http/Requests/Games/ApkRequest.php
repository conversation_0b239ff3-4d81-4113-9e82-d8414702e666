<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;
use App\Services\GamesService;
use Validator;

/**
 * ゲーム登録：APK情報
 */
class ApkRequest extends Request
{

    protected $service;

    public function __construct(GamesService $service)
    {
        $this->service = $service;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id' => 'required',
            'apk_file' => 'required|mimes:apk,zip,jar',
        ];
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'apk_file' => 'APKファイル',
        ];
    }

    public function customMessages()
    {
        return [
            'apk_file.mimes' => $this->MSG325,
        ];
    }

    protected function redirectRules()
    {
        return [
            'apk.update' => [
                'action' => 'apk.edit',
                'parameters' => [
                    'id' => 'id',
                ]
            ]
        ];
    }
}
