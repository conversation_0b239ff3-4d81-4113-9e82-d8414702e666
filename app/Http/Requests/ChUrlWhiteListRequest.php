<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ChUrlWhiteListService;
use Validator;

class ChUrlWhiteListRequest extends Request
{
    protected $chUrlWhiteListService;

    public function __construct(ChUrlWhiteListService $chUrlWhiteListService)
    {
        $this->chUrlWhiteListService = $chUrlWhiteListService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('url_is_exist', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                $type = request()->get('type');
                $whiteListId = request()->get('white_list_id');
                return ! $this->chUrlWhiteListService->getDataExist($value, $type, $parameters[0], $whiteListId);
            }
            return true;
        });
        Validator::replacer('url_is_exist', function ($message, $attribute, $rule, $parameters) {
            $type = request()->get('type');
            return str_replace(':other', $type, $message);
        });

        $chAppData = request()->session()->get('stored_id');

        $rules = [
            'type' => 'required|in:'.join(',', array_keys(config('forms.ChUrlWhiteList.type'))),
            'url'  => 'required|url|url_is_exist:'.$chAppData['id']
        ];

        return $rules;
    }

    public function attributes()
    {
        return [
            'type' => '種別',
            'url'  => 'URL'
        ];
    }

    public function customMessages()
    {
        return [
            'type.in'          => $this->MSG012,
            'url.url_is_exist' => $this->MSG287,
            'url.url'          => $this->MSG034,
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'type' => 'type',
                    'url' => 'url',
                    'white_list_id' => 'white_list_id',
                ],
            ]
        ];
    }
}
