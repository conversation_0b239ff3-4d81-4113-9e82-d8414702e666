<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Libs\Validation\Message;
use Route;
use Input;

abstract class Request extends FormRequest
{
    /**
     * Overloading method get validation messsage by message code.
     *
     * @param string $messageCode
     * @return string
     */
    public function __get($messageCode)
    {
        if (substr($messageCode, 0, 3) == 'MSG' && ctype_digit(substr($messageCode, 3))) {
            return $this->getValidationMessage($messageCode);
        }
    
        return null;
    }

    /**
     * Get validation messsage by message code.
     *
     * @param string $messageCode
     * @return string
     */
    public function getValidationMessage($messageCode)
    {
        return Message::get($messageCode);
    }

    /**
     * Set custom messages for validator errors.
     *
     * @return array
     */
    public function customMessages()
    {
        return [];
    }

    /**
     * Set custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        $extendMessageList = $this->customMessages();

        $baseMessageList = [
            'date_time'  => $this->MSG002,
            'required'  => $this->MSG012,
            'required_if'  => $this->MSG012,
            'required_without'  => $this->MSG012,
            'required_with_all'  => $this->MSG012,
            'required_without_all'  => $this->MSG012,
            'max' => $this->MSG021,
            'integer' => $this->MSG031,
            'url' => $this->MSG034,
            'min' => $this->MSG222,
            'no_all_html_tag' => $this->MSG294,
            'except_tag_html' => $this->MSG294,
        ];

        return array_merge($baseMessageList, $extendMessageList);
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [];
    }

    /**
     * Get the URL to redirect to on a validation error.
     *
     * @param none
     * @return string
     */
    protected function getRedirectUrl()
    {
        $redirectRules = $this->redirectRules();

        if (count($redirectRules) == 0) {
            return parent::getRedirectUrl();
        }

        $currentRoute = Route::getCurrentRoute()->getName();
        $currentController = substr($currentRoute, 0, strpos($currentRoute, '.'));
        $currentAction = substr($currentRoute, strpos($currentRoute, '.') + 1);
        $actionData = array_merge(Input::get(), Route::current()->parameters());
        $url = $this->redirector->getUrlGenerator();

        if (! isset($redirectRules[$currentAction])) {
            return parent::getRedirectUrl();
        }

        $parameters = [];

        if (isset($redirectRules[$currentAction]['parameters'])) {
            foreach ($redirectRules[$currentAction]['parameters'] as $key => $value) {
                $parameters[$key] = $actionData[$value];
            }
        }

        return $url->route($currentController . '.' . $redirectRules[$currentAction]['action'], $parameters);
    }
}
