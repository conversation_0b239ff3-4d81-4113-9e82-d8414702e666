<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;

class PresentRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'present_name' => 'required|max:50',
        ];
    }

    public function attributes()
    {
        return [
            'present_name' => '景品名',
        ];
    }

    public function customMessages()
    {
        return [
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ],
            'csvdownload' => [
                'action' => 'index',
            ],
        ];
    }
}
