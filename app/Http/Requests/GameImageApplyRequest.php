<?php

namespace App\Http\Requests;

use App\Services\GameImageApplyService;
use App\Services\GameImageInfoService;
use Validator;

class GameImageApplyRequest extends Request
{
    private $applyService;
    private $infoService;

    public function __construct(
        GameImageApplyService $applyService,
        GameImageInfoService $infoService,
        array $query = array(),
        array $request = array(),
        array $attributes = array(),
        array $cookies = array(),
        array $files = array(),
        array $server = array(),
        $content = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
        $this->applyService = $applyService;
        // 入力項目のサービス取得
        if (request('type') && request('app_id')) {
            $appId             = request('app_id');
            $type              = explode('-', request('type'));
            $this->infoService = $infoService->getDataByType(
                $appId,
                $type[0],
                $type[1],
                $type[2]
            );
        }
    }

//*********************************************************************************************************************
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     * @return array
     */
    public function rules()
    {
        if ($this->path() == 'gameimageapply/review/index') {
            $rules = [];
            if ($this->get('image_type')) {
                $rules += ['device' => 'in:0,'.
                    join(',', array_keys($this->applyService->getSelectDevice()[$this->get('image_type')]))
                ];
            }
            $rules += [
                'app_id'                => 'numeric',
                'image_type'            => 'in:0,'.join(',', array_keys(config('forms.GameImage.namesImageType'))),
                'examination_situation' => 'in:0,'.join(',', array_keys(config('forms.GameImage.namesExamSituation'))),
                'examination_result'    => 'in:0,'.join(',', array_keys(config('forms.GameImage.namesExamResult'))),
            ];
        } elseif ($this->path() == 'gameimageapply/registermovie/store') {
            $rules = [
                'app_id'   => 'required|numeric',
                'type'     => 'required',
                'movie_id' => 'required',
            ];

            // 無効なIDかチェック
            Validator::extend('movie_id_check', function ($attribute, $value, $parameters, $validator) {
                $movie_id = request('movie_id');
                if (preg_match("/^[!-%\--\.0-9A-z]+$/", $movie_id)) {
                    return true;
                }
                return false;
            });
            $rules['movie_id']   = $rules['movie_id']  ."|movie_id_check";

            // 申請可能チェック
            Validator::extend('is_enable_exam_apply', function ($attribute, $value, $parameters, $validator) {
                if ($this->infoService) {
                    return $this->infoService->isEnableExamApply();
                }
                return true;
            });

            if ($this->infoService) {
                $rules['type']   = $rules['type']  ."|is_enable_exam_apply";
            }
        } else {
            $rules = [
                'app_id' => 'required|numeric',
                'type'   => 'required',
                'upfile' => 'required',
            ];

            // 申請可能チェック
            Validator::extend('is_enable_exam_apply', function ($attribute, $value, $parameters, $validator) {
                if ($this->infoService) {
                    return $this->infoService->isEnableExamApply();
                }
                return true;
            });

            if ($this->infoService) {
                $rules['type']   = $rules['type']  ."|is_enable_exam_apply";
                $rules['upfile'] = $rules['upfile']."|{$this->infoService->isEnableUpFile()}";
            }
        }
        return $rules;
    }


    /**
     * Custom attributes list
     * @return array
     */
    public function attributes()
    {
        $attr = [
            'type'                  => '種類',
            'upfile'                => '画像',
            'app_id'                => 'タイトル',
            'image_type'            => '種類',
            'device'                => 'デバイス',
            'examination_situation' => '審査状況',
            'examination_result'    => '審査結果',
            'movie_id'              => '動画ID',
        ];
        return $attr;
    }

    /**
     * Custom validation message
     * @return array
     */
    public function customMessages()
    {
        $msg = [
            'type.is_enable_exam_apply' => $this->MSG257,
            'upfile.mimes'              => $this->MSG228,
            'upfile.max'                => $this->MSG288,
            'upfile.image_size'         => $this->MSG229,
            'app_id.numeric'            => $this->MSG012,
            'image_type.in'             => $this->MSG012,
            'device.in'                 => $this->MSG012,
            'examination_situation.in'  => $this->MSG012,
            'examination_result.in'     => $this->MSG012,
            'movie_id.movie_id_check'   => $this->MSG011,
        ];
        return $msg;
    }

    /**
     * Redirect rules.
     * @return array
     */
    protected function redirectRules()
    {
        $rules = [
            'registerstore'  => [
                'action'     => 'register',
                'parameters' => [
                    'id'     => 'app_id',
                ],
            ],
            'review'  => [
                'action'     => 'review',
            ],
            'review/index'  => [
                'action'     => 'review/index',
            ],
        ];
        return $rules;
    }
}
