<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;

class TopicSearchRequest extends Request
{
    protected $TopicService;

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'community_id' => 'numeric',
            'id'           => 'numeric',
            'user_id'      => 'numeric',
        ];
    }

    public function attributes()
    {
        return [
            'community_id' => 'コミュニティ',
            'id'           => 'トピックID',
            'user_id'      => 'オンラインゲームID',
        ];
    }

    public function customMessages()
    {
        return [
            'community_id.numeric' => $this->MSG031,
            'id.numeric'           => $this->MSG031,
            'user_id.numeric'      => $this->MSG031,
        ];
    }

    protected function redirectRules()
    {
        return [
            'index' => [
                'action' => 'index',
            ],
        ];
    }
}
