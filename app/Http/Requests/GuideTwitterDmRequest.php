<?php

namespace App\Http\Requests;

use Validator;
use App\Models\FreegameGuide\GuideApplication;

class GuideTwitterDmRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        return [
            'body'    => 'required',
            'send_at' => 'required|date',
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'body'    => '本文',
            'send_at' => '送信日時',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'body.in'           => $this->MSG012,
            'send_at.in'        => $this->MSG012,
            'send_at.date'      => $this->MSG002,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'dmscheduleid' => 'id',
                ],
            ]
        ];
    }
}
