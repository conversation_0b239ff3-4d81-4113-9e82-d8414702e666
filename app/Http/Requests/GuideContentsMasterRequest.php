<?php

namespace App\Http\Requests;

use App\Services\GuideContentsService;
use Validator;

class GuideContentsMasterRequest extends Request
{
    protected $guideContentsService;

    public function __construct(GuideContentsService $guideContentsService)
    {
        parent::__construct();
        $this->guideContentsService = $guideContentsService;
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        $guideAppId = $this->get('guideAppId');
        $id = $this->get('id');
        $group = $this->get('group_select');
        if ($group == '') {
            $group = $this->get('group_name');
        }
        Validator::extend(
            'master_name_unique',
            function ($attribute, $value, $parameters, $validator) use ($guideAppId, $group, $id) {
                return $this->guideContentsService->isUniqueName($guideAppId, $value, $group, $id);
            }
        );

        Validator::extend(
            'master_key_unique',
            function ($attribute, $value, $parameters, $validator) use ($guideAppId) {
                return $this->guideContentsService->isUniqueMasterKey($guideAppId, $value);
            }
        );

        $rules = [
            'name' => [
                'required',
                'max:100',
                'master_name_unique',
            ],
            'group_name' => [
                'required_without:group_select',
                'max:100',
            ],
            'description' => [
                'required',
                'max:20000',
            ],
            'memo' => [
                'max:20000',
            ],
        ];

        $routeName = $this->route()->getName();
        if ($routeName == 'GuideContents.createMasterConfirm') {
            $rules['master_key'] = [
                'required',
                'max:45',
                'alfa_num_line_dot_only',
                'master_key_unique',
            ];
        }

        $templates = $this->request->get('templates', []);
        $templateKeys = array_pluck($templates, 'template_key');
        $templateKeyCount = array_count_values($templateKeys);
        Validator::extend(
            'template_key_different',
            function ($attribute, $value, $parameters, $validator) use ($templateKeyCount) {
                return $templateKeyCount[$value] === 1;
            }
        );

        foreach ($templates as $num => $value) {
            $rules['templates.' . $num . '.template_key'] = [
                'required',
                'alfa_num_line_dot_only',
                'max:100',
                'template_key_different',
            ];
            $rules['templates.' . $num . '.value_type'] = [
                'required',
                'in:' . implode(',', array_keys($this->guideContentsService->getDataTypeList())),
            ];
            $rules['templates.' . $num . '.memo'] = [
                'max:20000',
            ];
        }
        return $rules;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        $attributes = [
            'name' => 'コンテンツ名',
            'master_key' => 'マスターキー',
            'group_name' => 'グループ',
            'description' => '説明',
            'memo' => '詳細',
        ];  

        $templates = $this->request->get('templates', []);
        if (!empty($templates)) {
            $num = 1;
            foreach ($templates as $key => $value) {
                $attributeName = '管理項目' . $num . '行目:';
                $attributes['templates.' . $key . '.template_key'] = $attributeName . '項目名';
                $attributes['templates'][$key]['value_type'] = $attributeName . ' データ形式';
                $attributes['templates'][$key]['memo'] = $attributeName . ' 説明';
                $num++;
            }
        }
        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $message = [
            'name.master_name_unique' => $this->MSG306,
            'master_key.master_key_unique' => $this->MSG306,
            'master_key.alfa_num_line_dot_only' => $this->MSG308,
        ];

        $templates = $this->request->get('templates', []);
        if (!empty($templates)) {
            foreach ($templates as $key => $value) {
                $message['templates.' . $key . '.template_key.template_key_different'] = $this->MSG307;
                $message['templates.' . $key . '.template_key.alfa_num_line_dot_only'] = $this->MSG308;
            }
        }
        return $message;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'masterStore' => [
                'action' => 'createMaster',
                'parameters' => [
                    'id' => 'guideAppId',
                ],  
            ],  
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppid',
                ],
            ]
        ];
    }
}
