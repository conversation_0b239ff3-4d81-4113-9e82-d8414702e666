<?php
namespace App\Http\Requests;

class GuideNotificationSearchRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        return [
            'searchWord' => 'special_characters',
            'searchPriority' => 'in:0,1'
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'searchWord' => 'お知らせ内容',
            'searchPriority' => 'トップ画面に表示'
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'in' => $this->MSG012,
            'special_characters' => $this->MSG232
        ];
    }
}
