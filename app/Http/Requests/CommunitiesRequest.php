<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\CommunitiesService;
use Validator;

class CommunitiesRequest extends Request
{
    protected $CommunitiesService;

    public function __construct(CommunitiesService $CommunitiesService)
    {
        $this->CommunitiesService = $CommunitiesService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Validator::extend('kinsoku', function ($attribute, $value, $parameters, $validator) {
            return $this->CommunitiesService->checkKinsoku($value);
        });

        Validator::extend('developer', function ($attribute, $value, $parameters, $validator) {
            if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
                return true;
            }

            return $this->CommunitiesService->checkStillDeveloper($value);
        });

        Validator::extend('young_age', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                if (floor((date('Ymd') - mb_eregi_replace("/", "", $value)) / 10000) < $parameters[0]) {
                    return false;
                }
            }
            return true;
        });

        Validator::replacer('young_age', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':min', $parameters[0], $message);
        });

        Validator::extend('developer_application', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                if (in_array($value, $this->CommunitiesService->getDeveloperApplicationList(auth()->user()->id))) {
                    return true;
                } else {
                    return false;
                }
            }

            return true;
        });

        if (strpos($this->server->get('HTTP_REFERER'), 'create')) {
            return [
                'app_id'      => 'required|numeric|developer_application',
                'description' => 'required|max:1000|platform_dependent|picture_characters|control_characters|except_tag_html',
                'status'      => 'required|in:active,deactive',
                'nickname'    => 'required|max_emoji:1000|control_characters|kinsoku|except_tag_html',
                'gender'      => 'required|in:male,female',
                'birthday'    => 'required|date|young_age:18',
                'prefecture'  => 'numeric',
                'job'         => 'numeric',
                'blood_type'  => 'in:A,B,O,AB',
            ];
        } else {
            $listPrefectureJob = $this->CommunitiesService->getPrefectureListJobList();
            return [
                'id'          => 'developer',
                'app_id'      => 'required|numeric|developer_application',
                'description' => 'required|max:1000|platform_dependent|picture_characters|control_characters|except_tag_html',
                'status'      => 'required|in:active,deactive',
                'nickname'    => 'required|max_emoji:1000|control_characters|kinsoku|except_tag_html',
                'age'         => 'required|in:0,1',
                'birthday'    => 'required|in:0,1',
                'prefecture'  => 'in:' . implode(',', array_keys($listPrefectureJob['PrefectureName'])),
                'job'         => 'in:' . implode(',', array_keys($listPrefectureJob['JobName'])),
                'hobby'       => 'control_characters|except_tag_html',
                'about_me'    => 'max:1000|except_tag_html',
            ];
        }

    }

    public function attributes()
    {
        return [
            'id'          => 'コミュニティ',
            'app_id'      => 'コミュニティ名',
            'description' => '説明文',
            'status'      => '公開設定',
            'nickname'    => 'ニックネーム',
            'gender'      => '性別',
            'age'         => '年齢',
            'birthday'    => '誕生日',
            'prefecture'  => '地域',
            'job'         => '職業',
            'hobby'       => '趣味',
            'about_me'    => '自己紹介',
            'blood_type'  => '血液型',
        ];
    }

    public function customMessages()
    {
        return [
            'id.developer'                   => $this->MSG264,
            'app_id.numeric'                 => $this->MSG031,
            'app_id.developer_application'   => $this->MSG264,
            'description.platform_dependent' => $this->MSG076,
            'description.picture_characters' => $this->MSG077,
            'description.control_characters' => $this->MSG225,
            'description.script_tag'         => $this->MSG239,
            'status.in'                      => $this->MSG081,
            'nickname.max_emoji'             => $this->MSG021,
            'nickname.control_characters'    => $this->MSG225,
            'nickname.kinsoku'               => $this->MSG011,
            'nickname.script_tag'            => $this->MSG239,
            'about_me.script_tag'            => $this->MSG239,
            'gender.in'                      => $this->MSG081,
            'birthday.in'                    => $this->MSG081,
            'birthday.young_age'             => $this->MSG252,
            'prefecture.in'                  => $this->MSG012,
            'job.in'                         => $this->MSG012,
            'hobby.control_characters'       => $this->MSG225,
            'hobby.script_tag'               => $this->MSG239,
            'blood_type.in'                  => $this->MSG081,
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ],
        ];
    }
}
