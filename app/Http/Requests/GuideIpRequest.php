<?php

namespace App\Http\Requests;

use Validator;

class GuideIpRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'     => 'required',
            'address1' => 'required|integer|min:0',
            'address2' => 'required|integer|min:0',
            'address3' => 'required|integer|min:0',
            'address4' => 'required|integer|min:0',
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'     => '名称',
            'address1' => 'IPアドレスの1つ目の入力項目',
            'address2' => 'IPアドレスの2つ目の入力項目',
            'address3' => 'IPアドレスの3つ目の入力項目',
            'address4' => 'IPアドレスの4つ目の入力項目',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'name.in'          => $this->MSG012,
            'address1.in'      => $this->MSG012,
            'address1.integer' => $this->MSG031,
            'address1.min'     => $this->MSG222,
            'address2.in'      => $this->MSG012,
            'address2.integer' => $this->MSG031,
            'address2.min'     => $this->MSG222,
            'address3.in'      => $this->MSG012,
            'address3.integer' => $this->MSG031,
            'address3.min'     => $this->MSG222,
            'address4.in'      => $this->MSG012,
            'address4.integer' => $this->MSG031,
            'address4.min'     => $this->MSG222,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
                'parameters' => [
                    'id' => 'guideAppId',
                ],
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppId',
                    'outerIpid' => 'id',
                ],
            ]
        ];
    }
}
