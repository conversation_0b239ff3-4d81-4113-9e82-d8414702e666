<?php

namespace App\Http\Requests;

use Validator;

class GuideApplicationRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        //----------
        // not_included_http
        //----------
        Validator::extend(
            'not_included_http',
            function ($attribute, $value, $parameters, $validator) {
                // 型まで比較したいので
                if (strpos($value, 'http://') === false
                    && strpos($value, 'https://') === false) {
                    return true;
                }
                return false;
            }
        );
        //----------
        //----------
        // custom_format
        //----------
        Validator::extend(
            'custom_format',
            function ($attribute, $value, $parameters, $validator) {
                $pattern = '/^([A-Z0-9][A-Z0-9_-]*(?:\.[A-Z0-9][A-Z0-9_-]*)+):?(\d+)?\/?/i';
                if (preg_match($pattern, $value)) {
                    return true;
                }
                return false;
            }
        );
        //----------

        return [
            'name'   => 'required|max:255'
                . '|unique:freegame_db.guide_application,name,' . $this->get('id'),
            'domain' => 'required|max:255|not_included_http|custom_format'
                . '|unique:freegame_db.guide_application,domain,' . $this->get('id'),
            'integrated_inquiry' => 'required',
        ];
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'   => 'タイトル',
            'domain' => 'ドメイン',
            'integrated_inquiry' => '統合お問い合わせサイト',
        ];
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            'name.in'                  => $this->MSG012,
            'name.max'                 => $this->MSG021,
            'name.unique'              => $this->MSG181,
            'domain.in'                => $this->MSG012,
            'domain.max'               => $this->MSG021,
            'domain.not_included_http' => $this->MSG243,
            'domain.custom_format'     => $this->MSG244,
            'domain.unique'            => $this->MSG181,
        ];
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id',
                ],
            ]
        ];
    }
}
