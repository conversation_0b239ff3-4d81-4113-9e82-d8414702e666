<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\CommunityCommentBlackUserService;
use Validator;

class CommunityCommentBlackUserRequest extends Request
{
    protected $CommunityCommentBlackUserService;

    public function __construct(CommunityCommentBlackUserService $CommunityCommentBlackUserService)
    {
        $this->CommunityCommentBlackUserService = $CommunityCommentBlackUserService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {

        // 有効ユーザチェック
        Validator::extend('is_existing_user', function ($attribute, $value, $parameters, $validator) {
            return $this->CommunityCommentBlackUserService->isExistingUser($this->all());
        });

        // user_idがblack_userテーブルに未登録か確認
        Validator::extend('check_user_id_exists', function ($attribute, $value, $parameters, $validator) {
            return $this->CommunityCommentBlackUserService->isNotExists($this->all());
        });

        
        $rules = [
            'user_id' => 'required|numeric|is_existing_user',
        ];

        // user_idを登録済みの場合はバリデーション
        if ($this->get('user_id')) {
            $rules['user_id'] .= '|check_user_id_exists';
        }

        return $rules;

    }

    public function attributes()
    {
        return [
            'user_id'     => 'GAMES ID',
        ];
    }

    public function customMessages()
    {
        return [
            'user_id.numeric'              => $this->MSG031,
            'user_id.is_existing_user'     => $this->MSG078,
            'user_id.check_user_id_exists' => $this->MSG181,
        ];
    }

    /**
     * Redirect rules.
     * @return array
     */
    protected function redirectRules()
    {
        $rules = [
            'createStore'    => [
                'action' => 'create',
            ],
        ];
        return $rules;
    }
}
