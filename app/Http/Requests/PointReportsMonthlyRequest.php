<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\PointReportsMonthlyService;

class PointReportsMonthlyRequest extends Request
{
    protected $PointReportsMonthlyService;

    public function __construct(PointReportsMonthlyService $PointReportsMonthlyService)
    {
        $this->PointReportsMonthlyService = $PointReportsMonthlyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'date'   => 'required',
            'app_id' => 'required|in:'.join(',', array_keys($this->PointReportsMonthlyService->getApplicationList())),
        ];
    }

    public function attributes()
    {
        return [
            'date'   => '期間',
            'app_id' => 'タイトル',
        ];
    }

    public function customMessages()
    {
        return [
            'date'      => $this->MSG002,
            'app_id.in' => $this->MSG012,
        ];
    }

    protected function redirectRules()
    {
        return [
            'csvdownload' => [
                'action' => 'index',
            ],
        ];
    }
}
