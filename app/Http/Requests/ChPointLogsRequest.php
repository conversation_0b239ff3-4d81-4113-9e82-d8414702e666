<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\ChPointLogsService;
use Validator;

class ChPointLogsRequest extends Request
{
    protected $ChPointLogsService;

    public function __construct(ChPointLogsService $ChPointLogsService)
    {
        $this->ChPointLogsService = $ChPointLogsService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // デバイス用チェックボックス配列に想定しない値が入力されていないか確認
        Validator::extend('device_diff', function ($attribute, $value, $parameters, $validator)
        {
            $deviceType = array_keys(config('forms.ChPointLogs.deviceType'));

            if ( !empty($value) && is_array($value) && count(array_diff($value, $deviceType)) > 0 )
            {
                return false;
            }
            return true;
        });

        // 期間終了が期間開始と同日以降か確認
        Validator::extend('equal_or_after_exists', function ($attribute, $value, $parameters, $validator) {
            if (strtotime($this->get('begin') . '00:00:00') <= strtotime($this->get('end') . '00:00:00')) {
                return true;
            }
            return false;
        });

        return [
            'begin'     => 'required|date',
            'end'       => 'required|date|equal_or_after_exists',
            'app_id'    => 'required|in:'.join(',', array_keys($this->ChPointLogsService->getApplicationList())),
            'sap_point' => 'mimes:csv,txt',
            'device'    => 'required|array|device_diff',
        ];
    }

    public function attributes()
    {
        return [
            'begin'     => '開始日',
            'end'       => '終了日',
            'app_id'    => 'アプリID',
            'sap_point' => 'デベロッパー課金ログ',
            'device'    => 'デバイス',
        ];
    }

    public function customMessages()
    {
        return [
            'date'                  => $this->MSG002,
            'equal_or_after_exists' => $this->MSG265,
            'app_id.in'             => $this->MSG012,
            'sap_point.mimes'       => str_replace(':extension', 'csv', $this->MSG237),
            'device.array'          => $this->MSG264,
            'device.device_diff'    => $this->MSG264,
        ];
    }

    protected function redirectRules()
    {
        return [
            'csvdownload' => [
                'action' => 'index',
            ],
        ];
    }
}
