<?php

namespace App\Http\Requests;

use Validator;

class GuideFormRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    /**
     * Validation rules list
     *
     * @return array
     */
    public function rules()
    {
        //----------
        // check_is_from
        //----------
        // フォームでは固定値なので通常操作では発生しないエラー
        Validator::extend('check_is_from', function ($attribute, $value, $parameters) {
            $checkIsFrom = false;
            $cntIsFrom = 0;
            $detailList = $this->get('data');
            foreach ($detailList as $detail) {
                // 複数設定されていたら「未設定」とする
                if (intval($detail['is_from']) === 1) {
                    $cntIsFrom++;
                }
                // メールのFromに設定する条件
                if (intval($detail['is_from']) === 1
                    && strcmp($detail['type'], 'text') === 0) {
                    $checkIsFrom = true;
                }
            }
            if ($cntIsFrom > 1) {
                $checkIsFrom = false;
            }

            return $checkIsFrom;
        });
        //----------

        //----------
        // check_is_subject
        //----------
        // フォームでは固定値なので通常操作では発生しないエラー
        Validator::extend('check_is_subject', function ($attribute, $value, $parameters) {
            $checkIsSubject = false;
            $detailList = $this->get('data');
            foreach ($detailList as $detail) {
                if ($value != $detail['id']) {
                    continue;
                }

                // メールのSubjectに設定する条件
                if (strcmp($detail['type'], 'textarea') !== 0) {
                    $checkIsSubject = true;
                }
            }

            return $checkIsSubject;
        });
        //----------

        //----------
        // check_is_required
        //----------
        Validator::extend('check_required_from', function ($attribute, $value, $parameters) {
            $checkIsRequired = true;

            $detailList = $this->get('data');
            foreach ($detailList as $detail) {
                // メールのFromに設定する条件
                if (isset($detail['is_required']) == false
                    && intval($detail['is_from']) === 1) {
                    $checkIsRequired = false;
                    break;
                }
            }
            return $checkIsRequired;
        });
        //----------

        //----------
        // check_is_required_subject
        //----------
        Validator::extend('check_is_required_subject', function ($attribute, $value, $parameters) {
            $checkIsRequired = true;
            $detailList = $this->get('data');
            $isSubjectFormId = $this->get('is_subject');
            foreach ($detailList as $detail) {
                // バリデート確認中のフォームについて確認をする
                if ($value != $detail['id']) {
                    continue;
                }

                // メールのSubjectに設定する条件
                if (isset($detail['is_required']) == false
                    && $isSubjectFormId == $detail['id']) {
                    $checkIsRequired = false;
                    break;
                }
            }

            return $checkIsRequired;
        });
        //----------

        $typeList = config('forms.GuideForm.typeList');
        $strTypeList = implode(',', array_keys($typeList));
        $validate = [];
        $detailList = $this->get('data');
        foreach ($detailList as $detail) {
            $formId = $detail['id'];
            // 質問のタイトル
            $validateIndex = 'data.' . $formId . '.name';
            $validate[$validateIndex] = 'required|max:255';

            // 質問の形式
            $validateIndex = 'data.' . $formId . '.type';
            $validate[$validateIndex] = 'required|in:' . $strTypeList;

            // 質問の設定
            $type = '';
            $optionList = $detail['options'];
            if (isset($detail['options'][$detail['type']])) {
                $optionList = $detail['options'][$detail['type']];
                $type = '.' . $detail['type'];
            }
            // textarea はバリデートは無いので除く
            if (strcmp($detail['type'], 'text') === 0) {
                $validateIndex = 'data.' . $formId . '.options' . $type . '.0.value';
                $validate[$validateIndex] = 'max:255';
            } elseif (strcmp($detail['type'], 'textarea') !== 0) {
                foreach ($optionList as $optKey => $option) {
                    $optId = $optKey;
                    $validateIndex = 'data.' . $formId . '.options'
                        . $type . '.' . $optId . '.value';
                    $validate[$validateIndex] = 'required|max:20';
                }
            }

            // 最大文字数
            if (in_array($detail['type'], ['text', 'textarea'])) {
                $validateIndex = 'data.' . $formId . '.max_length';
                $validate[$validateIndex] = 'integer';
            }

            // 必須の質問 と設定の組み合わせ
            $validateIndex = 'data.' . $formId . '.is_required_formid';
            $validate[$validateIndex] = 'check_is_required_subject';
        }

        // 問い合わせメールのFROMにする設定
        $validate['check_is_from'] = 'required|check_is_from|check_required_from';

        // 問い合わせメールの件名にする設定
        $validate['is_subject'] = 'required|check_is_subject';

        return $validate;
    }

    /**
     * Custom attributes list
     *
     * @return array
     */
    public function attributes()
    {
        $attributes = [];
        $detailList = $this->get('data');
        $index = 1;
        foreach ($detailList as $detail) {
            $qTitle = '項目' . $index;
            $formId = $detail['id'];
            // 質問のタイトル
            $attrIndex = 'data.' . $formId . '.name';
            $attributes[$attrIndex] = $qTitle . 'の質問のタイトル';

            // 質問の形式
            $attrIndex = 'data.' . $formId . '.type';
            $attributes[$attrIndex] = $qTitle . 'の質問の形式';

            // 質問の設定
            $type = '';
            $optionList = $detail['options'];
            if (isset($detail['options'][$detail['type']])) {
                $optionList = $detail['options'][$detail['type']];
                $type = '.' . $detail['type'];
            }
            foreach ($optionList as $optKey => $option) {
                $optId = $optKey;
                $attrIndex = 'data.' . $formId . '.options'
                    . $type . '.' . $optId . '.value';
                $attributes[$attrIndex] = $qTitle . 'の質問の設定';
            }

            // 最大文字数
            $attrIndex = 'data.' . $formId . '.max_length';
            $attributes[$attrIndex] = $qTitle . 'の質問の設定の最大文字数';

            // 必須の質問 と設定の組み合わせ
            $attrIndex = 'data.' . $formId . '.is_required_formid';
            $attributes[$attrIndex] =
                '(' . $qTitle . ')問合せのメール件名に設定しているときは、必須の質問';

            $index++;
        }

        // 問い合わせメールのFROMにする設定
        $attributes['check_is_from'] = 'メールアドレスの形式';
        Validator::replacer('check_is_from', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':other', '固定の組み合わせ', $message);
        });
        Validator::replacer('check_required_from', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':other', '固定の組み合わせ', $message);
        });

        // 問合せのメール件名に設定する
        $attributes['is_subject'] = '問い合わせメールの件名にする設定';

        // プライバシーポリシー
        $attributes['policy'] = 'プライバシーポリシー';

        return $attributes;
    }

    /**
     * Custom validation message
     *
     * @return array
     */
    public function customMessages()
    {
        $message = [];
        $detailList = $this->get('data');
        foreach ($detailList as $detail) {
            $formId = $detail['id'];
            // 質問のタイトル
            $msgIndex = 'data.' . $formId . '.name';
            $message[$msgIndex . '.in']  = $this->MSG012;
            $message[$msgIndex . '.max'] = $this->MSG021;

            // 質問の形式
            $msgIndex = 'data.' . $formId . '.type';
            $message[$msgIndex . '.in'] = $this->MSG012;

            // 質問の設定
            $type = '';
            $optionList = $detail['options'];
            if (isset($detail['options'][$detail['type']])) {
                $optionList = $detail['options'][$detail['type']];
                $type = '.' . $detail['type'];
            }
            foreach ($optionList as $optKey => $option) {
                $optId = $optKey;
                $msgIndex = 'data.' . $formId . '.options'
                    . $type . '.' . $optId . '.value';
                $message[$msgIndex . '.in']  = $this->MSG012;
                $message[$msgIndex . '.max'] = $this->MSG021;
            }

            // 最大文字数
            $msgIndex = 'data.' . $formId . '.max_length';
            $message[$msgIndex . '.integer'] = $this->MSG242;

            // 必須の質問 と設定の組み合わせ
            $msgIndex = 'data.' . $formId . '.is_required_formid';
            $message[$msgIndex . '.check_is_required_subject'] = $this->MSG241;
        }

        // 問い合わせメールのFROMにする設定
        $message['check_is_from.in']                  = $this->MSG241;
        $message['check_is_from.check_is_from']       = $this->MSG216;
        $message['check_is_from.check_required_from'] = $this->MSG216;

        // 問合せのメール件名に設定する
        $message['is_subject.in']               = $this->MSG241;
        $message['is_subject.check_is_subject'] = $this->MSG241;

        return $message;
    }

    /**
     * Redirect rules.
     *
     * @param none
     * @return array
     */
    protected function redirectRules()
    {
        return [
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'guideAppid',
                ],
            ]
        ];
    }
}
