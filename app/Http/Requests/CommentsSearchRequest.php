<?php

namespace App\Http\Requests;

use App\Services\CommentsSearchService;

class CommentsSearchRequest extends Request
{
    protected $commentsSearchService;
    protected $authAdmin;

    public function __construct(CommentsSearchService $commentsSearchService)
    {
        $this->commentsSearchService = $commentsSearchService;
        if (auth_is_user_admin() || auth_is_user_adminforpoint() || auth_is_user_staff()) {
            $this->authAdmin = 1;
        } else {
            $this->authAdmin = 0;
        }
    }

    public function authorize()
    {
        return true;
    }

    /**
     * Set rules for validator
     *
     * @return array
     */
    public function rules()
    {
        $community = [];
        if ($this->authAdmin) {
            $community    = ["" => ""] + $this->commentsSearchService
                ->getCommunitiesList()
                ->lists('id')->toArray();
        } else {
            $devAppIds = $this->commentsSearchService->getDeveloperApplicationList(auth()->user()->id);
            if (count($devAppIds) > 0) {
                $community = ["" => ""] + $this->commentsSearchService
                    ->getCommunitiesList($devAppIds)
                    ->lists('id')->toArray();
            }
        }
        return [
            's_community_id' => 'in:'.implode(',', $community),
            's_topic_id'     => 'integer|min:1',
            's_start_date'   => 'date',
            's_end_date'     => 'date|after:s_start_date',
            's_dmm_game_id'  => 'integer|min:1',
        ];
    }

    /**
     * Set attribute for validator
     *
     * @return array
     */
    public function attributes()
    {
        return [
            's_community_id' => 'コミュニティ',
            's_topic_id'     => 'トピックID',
            's_start_date'   => '開始日',
            's_end_date'     => '終了日',
            's_dmm_game_id'  => 'DMM GAMES ID',
        ];
    }

    /**
     * Set custom messages for validator errors.
     *
     * @return array
     */
    public function customMessages()
    {
        return [
            's_community_id.in' => $this->MSG012,
            's_start_date.date' => $this->MSG002,
            's_end_date.date'   => $this->MSG002,
            's_end_date.after'  => $this->MSG005
        ];
    }
}
