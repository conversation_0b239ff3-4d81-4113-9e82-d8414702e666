<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Services\PointReportsDailyService;
use Validator;

class PointReportsDailyRequest extends Request
{
    protected $PointReportsDailyService;

    public function __construct(PointReportsDailyService $PointReportsDailyService)
    {
        $this->PointReportsDailyService = $PointReportsDailyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // 当日または30日以前はNG
        Validator::extend('ngday', function ($attribute, $value, $parameters, $validator) {
            if (!empty($value)) {
                $dates = explode('/', $value);
                if (count($dates) == 3) {
                    if (checkdate($dates[1], $dates[2], $dates[0])) {
                        $today = date('Y/m/d');
                        if ($value == $today) {
                            return false;
                        }
                        if ((int)strtotime($value) < (int)strtotime($today.' -31 day')) {
                            return false;
                        }
                    }
                }
            }
            return true;
        });

        return [
            'date'   => 'required|date|ngday',
            'app_id' => 'required|in:'.join(',', array_keys($this->PointReportsDailyService->getApplicationList())),
        ];
    }

    public function attributes()
    {
        return [
            'date'   => '期間',
            'app_id' => 'タイトル',
        ];
    }

    public function customMessages()
    {
        return [
            'date'      => $this->MSG002,
            'ngday'     => $this->MSG011,
            'app_id.in' => $this->MSG012,
        ];
    }

    protected function redirectRules()
    {
        return [
            'csvdownload' => [
                'action' => 'index',
            ],
        ];
    }
}
