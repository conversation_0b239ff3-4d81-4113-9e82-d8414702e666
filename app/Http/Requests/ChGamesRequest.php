<?php
namespace App\Http\Requests;

use App\Services\ChGamesService;

class ChGamesRequest extends Request
{
    protected $chGamesService;

    public function __construct(ChGamesService $chGamesService)
    {
        $this->chGamesService = $chGamesService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'id'                 => 'required',
            'description_middle' => 'required|max:20|platform_dependent',
            'genre_id'           => 'required|in:' .
                                    implode(',', array_keys($this->chGamesService->getSelectGenreList()))
        ];
    }

    public function attributes()
    {
        return [
            'id'                 => 'アプリID',
            'description_middle' => '紹介枠（20文字）',
            'genre_id'           => 'ジャンル'
        ];
    }

    public function customMessages()
    {
        return [
            'in'                 => $this->MSG012,
            'platform_dependent' => $this->MSG076,
        ];
    }

    protected function redirectRules()
    {
        return [
            'update'         => [
                'action'     => 'edit',
                'parameters' => [
                    'id'     => 'id'
                ]
            ]
        ];
    }
}
