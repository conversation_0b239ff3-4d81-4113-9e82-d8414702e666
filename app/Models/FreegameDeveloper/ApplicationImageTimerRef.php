<?php

namespace App\Models\FreegameDeveloper;

class ApplicationImageTimerRef extends FreegameDeveloper
{
    protected $table = 'application_image_timer_ref';

    protected $fillable = ['timer_id', 'application_image_id'];

    protected $primaryKey = ['timer_id', 'application_image_id'];

    public $incrementing = false;

//*********************************************************************************************************************

    /**
     * timerIdで取得
     * @param string $timerId
     * @return bool|array
     */
    public function getByTimerId($timerId)
    {
        if (empty($timerId)) {
            return false;
        }
        return self::where('timer_id', $timerId)->get();
    }

    /**
     * 追加メソッド
     * @param string $timerId
     * @param integer $imageId
     * @return bool| void
     */
    public function add($timerId, $imageId)
    {
        if (empty($timerId) || empty($imageId)) {
            return false;
        }

        self::create(['timer_id' => $timerId, 'application_image_id' => $imageId]);
    }

    /**
     * 更新
     * @param $timerId
     * @param $oldImageId
     * @param $newImageId
     * @return bool
     */
    public function edit($timerId, $oldImageId, $newImageId)
    {
        if (empty($timerId) || empty($oldImageId) || empty($newImageId)) {
            return false;
        }

        return self::where('timer_id', $timerId)
            ->where('application_image_id', $oldImageId)
            ->update(['application_image_id' => $newImageId]);
    }

    /**
     * タイマーid, イメージidで該当する一つのローを消す
     * @param string $timerId
     * @param integer $imageId
     * @return bool
     */
    public function deleteByTimerIdAndImageId($timerId, $imageId)
    {
        if (empty($timerId) || empty($imageId)) {
            return false;
        }

        return self::where('timer_id', $timerId)
        ->where('application_image_id', $imageId)
        ->delete();
    }
}