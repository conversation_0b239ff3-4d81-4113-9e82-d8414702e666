<?php
namespace App\Models\FreegameDeveloper;

/**
 * デベロッパー と 事前登録キャンペーンの紐付けテーブル
 */
class DeveloperMailformCampaign extends FreegameDeveloper
{
    protected $table = 'developer_mailform_campaign';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * select by mailform_campaign_id
     * @param int $mailformCampaignId
     * @return Collection
     */
    public function getByMailformCampaignId($mailformCampaignId)
    {
        if (empty($mailformCampaignId)) {
            return false;
        }
        $query = self::where('mailform_campaign_id', $mailformCampaignId);
        return $query->get();
    }

    /**
     * insert
     * @param  array  $param
     * @return boolean
     */
    public function add($param)
    {
        return self::insert($param);
    }

    /**
     * delete by mailform_campaign_id
     * @param int $mailformCampaignId
     * @return boolean | int
     */
    public function deleteByMailformCampaignId($mailformCampaignId)
    {
        if (empty($mailformCampaignId)) {
            return false;
        }
        return self::where('mailform_campaign_id', $mailformCampaignId)->delete();
    }

    /**
     * returns true if it exists
     * @param array $conditions
     * @return boolean
     */
    public function existsRecord($conditions)
    {
        if (empty($conditions)) {
            return false;
        }
        return self::where($conditions)->exists();
    }
}
