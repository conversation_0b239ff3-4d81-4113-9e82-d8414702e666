<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * 利用状況レポート
 */
class ReportUtilization extends FreegameDeveloper
{

    protected $table = 'report_utilization';

    protected $dates = [
        'date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        $query = $query->exceptApp(); // 特定のアプリを除外
        return $query->orderBy('app_id', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS date",
            'app_id',
            'device',
            'active_user',
            'download_user',
            'regist_user',
            'suspend_user',
            'pv',
            'dau',
            'use_point_user',
            'use_point',
            'use_point_pay',
            'use_point_free',
            'use_point_staff',
            'discount_price'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $subQuery = $subQuery->whereIn('app_id', $condition['app_id']);
            } else {
                $subQuery = $subQuery->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        $subQuery = $subQuery->exceptApp(); // 特定のアプリを除外
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'date' => 'date',
            'app_id' => 'app_id',
            'device' => 'device',
            'active_user' => 'MAX(active_user) AS active_user',
            'active_user_all' => 'MAX(active_user) AS active_user_all',
            'download_user' => 'SUM(download_user) AS download_user',
            'regist_user' => 'SUM(regist_user) AS regist_user',
            'suspend_user' => 'SUM(suspend_user) AS suspend_user',
            'pv' => 'SUM(pv) AS pv',
            'dau' => 'SUM(dau) AS mau',
            'mau' => 'SUM(dau) AS mau',
            'use_point_user' => 'SUM(use_point_user) AS use_point_user',
            'use_point' => 'SUM(use_point) AS use_point',
            'use_point_pay' => 'SUM(use_point_pay) AS use_point_pay',
            'use_point_free' => 'SUM(use_point_free) AS use_point_free',
            'use_point_staff' => 'SUM(use_point_staff) AS use_point_staff',
            'discount_price' => 'SUM(discount_price) AS discount_price'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('date')
            ->groupBy('app_id')
            ->groupBy('device')
            ->orderBy('app_id', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }
}
