<?php
namespace App\Models\FreegameDeveloper;

/**
 * デベロッパー起源テーブル
 */
class DeveloperParentage extends FreegameDeveloper
{
    protected $table = 'developer_parentage';

    public $timestamps = false;

    /**
     * get list
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (!empty($condition['parent_id'])) {
            $query = $query->where('parent_id', '=', $condition['parent_id']);
        }

        return $query->get();
    }

    /**
     * 親アカウントのdeveloper_idをキーにして、子のdeveloper_idのリストを取得
     * @param int $parent_id
     *
     * @param int $parent_id
     *
     * @return object
     */
    public function getDeveloperIdList($parent_id)
    {
        return self::where('parent_id', $parent_id)->lists('developer_id', 'developer_id');
    }

    /**
     * 有効な親子関係のdeveloper_idかを確認
     * @params int $parent_id 親のdeveloper_id
     * @params $developer_id 子のdeveloper_id
     * @return boolean true:有効 / false:無効
     */
    public function isParentAndChild($parent_id, $developer_id)
    {
        if (empty($parent_id) || empty($developer_id)) {
            return false;
        }
        $query = self::where('parent_id', $parent_id)->where('developer_id', $developer_id);
        if (! empty($query->count())) {
            return true;
        }
        return false;
    }

}
