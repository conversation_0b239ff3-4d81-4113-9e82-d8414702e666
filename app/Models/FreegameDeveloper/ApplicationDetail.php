<?php
namespace App\Models\FreegameDeveloper;

/**
 * アプリケーション詳細テーブル
 */
class ApplicationDetail extends FreegameDeveloper
{

    protected $table = 'application_detail';

    protected $primaryKey = 'application_detail_id';

    protected $guarded = [
        'application_detail_id'
    ];

    public $timestamps = false;

    /**
     * 詳細取得
     * @param  string $appId
     * @return object
     */
    public function getApplicationDetail($appId)
    {
        return self::where('app_id', $appId)->first();
    }

    public function getLastById($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->orderBy('application_detail_id', 'desc')->first();
    }

    public function editApp($app_id, $data)
    {
        if (empty($app_id) || empty($data)) {
            return false;
        }
        $attr = $data;
        $attr['app_id'] = $app_id;
        return self::updateOrCreate([
            'app_id' => $app_id
        ], $attr);
    }
}
