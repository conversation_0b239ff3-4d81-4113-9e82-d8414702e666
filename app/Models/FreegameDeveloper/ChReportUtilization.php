<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * チャネリングレポート利用
 */
class ChReportUtilization extends FreegameDeveloper
{
    protected $table = 'ch_report_utilization';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (!empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (!empty($condition['device'])) {
            $query = $query->whereIn('device', $condition['device']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('report_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('report_date', '<=', $condition['end']);
        }
        if (!empty($condition['report_date_ym'])) {
            $query = $query->where(DB::raw('DATE_FORMAT(report_date, "%Y-%m")'), '=', $condition['report_date_ym']);
        }

        // ch_applicationのレコード登録順で並べ替える
        if (! empty($condition['orderAppId']) && count($condition['orderAppId']) > 0) {
            $orderAppId = $condition['orderAppId'];
            $field = 'FIELD(app_id';
            for ($i = 0; $i < count($orderAppId); ++$i) {
                $field .= ', ?';
            }
            $field .= ')';
            $query = $query->orderByRaw($field)->addBinding($orderAppId);
        }

        if (!empty($condition['order'])) {
            foreach ($condition['order'] as $order) {
                $query = $query->orderBy($order[0], $order[1]);
            }
        }
        return $query->get();
    }

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getListDailyMonthly($condition = [])
    {
        $query = self::select(
            'ch_report_utilization.report_date AS report_date',
            'ch_report_utilization.app_id AS app_id',
            'ch_report_utilization.device',
            'ch_report_utilization.active_user AS active_user',
            'ch_report_utilization.regist_user AS regist_user',
            'ch_report_utilization.suspend_user AS suspend_user',
            'ch_report_utilization.use_point_user AS use_point_user',
            'ch_report_utilization.use_point AS use_point',
            'ch_report_utilization.use_point_pay AS use_point_pay',
            'ch_report_utilization.use_point_free AS use_point_free',
            'ch_report_utilization.use_point_staff AS use_point_staff',
            'cmru.price AS monthly_price',
            'cmru.regist_user AS monthly_regist_user',
            'cmru.continue_user AS monthly_continue_user',
            'cmru.active_user AS monthly_active_user',
            'cmru.withdrawal_user AS monthly_withdrawal_user',
            'cmru.withdrawing_user AS monthly_withdrawing_user',
            'cmru.eviction_user AS monthly_eviction_user'
        )
        ->leftjoin('ch_monthly_report_utilization AS cmru', function ($q) use ($condition) {
            $q->on('ch_report_utilization.report_date', '=', 'cmru.report_date')
            ->on('ch_report_utilization.app_id', '=', 'cmru.app_id')
            ->on('ch_report_utilization.device', '=', 'cmru.device');
        });

        if (!empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('ch_report_utilization.app_id', $condition['app_id']);
            } else {
                $query = $query->where('ch_report_utilization.app_id', $condition['app_id']);
            }
        }
        if (!empty($condition['device'])) {
            $query = $query->whereIn('ch_report_utilization.device', $condition['device']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('ch_report_utilization.report_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('ch_report_utilization.report_date', '<=', $condition['end']);
        }

        // ch_applicationのレコード登録順で並べ替える
        if (! empty($condition['orderAppId']) && count($condition['orderAppId']) > 0) {
            $orderAppId = $condition['orderAppId'];
            $field = 'FIELD(ch_report_utilization.app_id';
            for ($i = 0; $i < count($orderAppId); ++$i) {
                $field .= ', ?';
            }
            $field .= ')';
            $query = $query->orderByRaw($field)->addBinding($orderAppId);
        }

        if (!empty($condition['order'])) {
            foreach ($condition['order'] as $order) {
                $query = $query->orderBy($order[0], $order[1]);
            }
        }

        return $query->get();
    }

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getListMonthly($condition = [])
    {
        $query = self::select(
            'report_date',
            'device',
            'app_id',
            DB::raw('SUM(regist_user) AS regist_user'),
            DB::raw('SUM(suspend_user) AS suspend_user'),
            DB::raw('SUM(use_point) AS use_point'),
            DB::raw('SUM(use_point_pay) AS use_point_pay'),
            DB::raw('SUM(use_point_free) AS use_point_free'),
            DB::raw('SUM(use_point_staff) AS use_point_staff')
        );

        if (!empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (!empty($condition['device'])) {
            $query = $query->whereIn('device', $condition['device']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('report_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('report_date', '<=', $condition['end']);
        }
        if (!empty($condition['group'])) {
            $query = $query->groupBy(DB::raw($condition['group']));
        }

        // ch_applicationのレコード登録順で並べ替える
        if (! empty($condition['orderAppId']) && count($condition['orderAppId']) > 0) {
            $orderAppId = $condition['orderAppId'];
            $field = 'FIELD(app_id';
            for ($i = 0; $i < count($orderAppId); ++$i) {
                $field .= ', ?';
            }
            $field .= ')';
            $query = $query->orderByRaw($field)->addBinding($orderAppId);
        }

        if (!empty($condition['order'])) {
            foreach ($condition['order'] as $order) {
                $query = $query->orderBy($order[0], $order[1]);
            }
        }

        return $query->get();

    }

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getListMonthlyMonthly($condition = [])
    {
        $query = self::select(
            'ch_report_utilization.report_date',
            'ch_report_utilization.device',
            'ch_report_utilization.app_id',
            DB::raw('SUM(ch_report_utilization.regist_user) AS regist_user'),
            DB::raw('SUM(ch_report_utilization.suspend_user) AS suspend_user'),
            DB::raw('SUM(ch_report_utilization.use_point) AS use_point'),
            DB::raw('SUM(ch_report_utilization.use_point_pay) AS use_point_pay'),
            DB::raw('SUM(ch_report_utilization.use_point_free) AS use_point_free'),
            DB::raw('SUM(ch_report_utilization.use_point_staff) AS use_point_staff'),
            DB::raw('SUM(cmru.price) AS monthly_price'),
            DB::raw('SUM(cmru.regist_user) AS monthly_regist_user'),
            DB::raw('SUM(cmru.continue_user) AS monthly_continue_user'),
            DB::raw('SUM(cmru.withdrawal_user) AS monthly_withdrawal_user'),
            DB::raw('SUM(cmru.eviction_user) AS monthly_eviction_user')
        )
        ->leftjoin('ch_monthly_report_utilization AS cmru', function ($q) use ($condition) {
            $q->on('ch_report_utilization.report_date', '=', 'cmru.report_date')
            ->on('ch_report_utilization.app_id', '=', 'cmru.app_id')
            ->on('ch_report_utilization.device', '=', 'cmru.device');
        });

        if (!empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('ch_report_utilization.app_id', $condition['app_id']);
            } else {
                $query = $query->where('ch_report_utilization.app_id', $condition['app_id']);
            }
        }
        if (!empty($condition['device'])) {
            $query = $query->whereIn('ch_report_utilization.device', $condition['device']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('ch_report_utilization.report_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('ch_report_utilization.report_date', '<=', $condition['end']);
        }
        if (!empty($condition['group'])) {
            $query = $query->groupBy(DB::raw($condition['group']));
        }

        // ch_applicationのレコード登録順で並べ替える
        if (! empty($condition['orderAppId']) && count($condition['orderAppId']) > 0) {
            $orderAppId = $condition['orderAppId'];
            $field = 'FIELD(ch_report_utilization.app_id';
            for ($i = 0; $i < count($orderAppId); ++$i) {
                $field .= ', ?';
            }
            $field .= ')';
            $query = $query->orderByRaw($field)->addBinding($orderAppId);
        }

        if (!empty($condition['order'])) {
            foreach ($condition['order'] as $order) {
                $query = $query->orderBy($order[0], $order[1]);
            }
        }

        return $query->get();
    }
}
