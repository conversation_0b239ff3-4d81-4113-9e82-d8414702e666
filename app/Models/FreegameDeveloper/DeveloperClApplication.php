<?php

namespace App\Models\FreegameDeveloper;

class DeveloperClApplication extends FreegameDeveloper
{
    protected $table = 'developer_cl_application';

    public $timestamps = false;

    /**
     * Get by developer_id
     * @param  integer $developerId
     * @return array
     */
    public function getListAppIdByDeveloperId($developerId)
    {
        return self::where('developer_id', $developerId)->lists('cl_app_id', 'id');
    }

    /**
     * Check exists by developer_id and ch_app_id
     * @param integer $developerId
     * @param integer $clAppId
     * @return array
     */
    public function getListAppIdByDeveloperIdAndChAppId($developerId, $clAppId)
    {
        if (empty($developerId)) {
            return self::where('cl_app_id', $clAppId)->lists('cl_app_id', 'id');
        } else {
            return self::where('developer_id', $developerId)->where('cl_app_id', $clAppId)->lists('cl_app_id', 'id');
        }
    }

    /**
     * Check exists by developer_id and cl_app_id
     * @param integer $developerId
     * @param integer $clAppId
     * @return boolean
     */
    public function existsByDeveloperIdAndClAppId($developerId, $clAppId)
    {
        return self::where('developer_id', $developerId)->where('cl_app_id', $clAppId)->exists();
    }

    public function getApplicationAppIdList($condition = [])
    {
        $query = self::select([
            'cl_app_id'
        ]);
        if (!empty($condition['developer_id'])) {
            $query = $query->where('developer_id', $condition['developer_id']);
        }
        return $query->get();
    }
}
