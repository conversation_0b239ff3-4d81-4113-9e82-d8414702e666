<?php
namespace App\Models\FreegameDeveloper;

/**
 * 税率テーブル
 */
class TaxRate extends FreegameDeveloper
{

    protected $table = 'tax_rate';

    protected $dates = [
        'begin_date',
        'end_date'
    ];

    public $timestamps = false;

    public function getList()
    {
        return self::orderBy('begin_date', 'asc')->get();
    }

    public function getOne($date)
    {
        if (empty($date)) {
            return false;
        }
        return self::where('begin_date', '<=', $date)->where('end_date', '>=', $date)->first();
    }
}
