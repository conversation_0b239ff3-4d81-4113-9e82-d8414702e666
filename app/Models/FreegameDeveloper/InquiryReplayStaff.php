<?php
namespace App\Models\FreegameDeveloper;

/**
 * お問い合わせ返信担当者テーブル
 */
class InquiryReplayStaff extends FreegameDeveloper
{

    protected $table = 'inquiry_reply_staff';

    protected $guarded = [
        'stamp'
    ];

    public $timestamps = false;

    public function add($data)
    {
        if (empty($data)) {
            return false;
        }
        return self::create($data);
    }

    public function findByStaffNameAndAppId($staffName, $appId)
    {
        return self::where('staff_name', $staffName)
            ->where('inquiry_message.app_id', $appId)
            ->join('inquiry_message', 'inquiry_reply_staff.inquiry_message_id', '=', 'inquiry_message.id')
            ->first();
    }

    public function countByAppId($appId)
    {
        return self::where('app_id', $appId)
            ->join('inquiry_message', 'inquiry_reply_staff.inquiry_message_id', '=', 'inquiry_message.id')
            ->count('inquiry_message_id');
    }
}
