<?php
namespace App\Models\FreegameDeveloper;

/**
 * 問い合わせカテゴライズリファレンステーブル
 */
class InquiryCategorizedRef extends FreegameDeveloper
{

    protected $table = 'inquiry_categorized_ref';
    protected $primaryKey = 'inquiry_id';
    protected $fillable = array('inquiry_id','large_id','middle_id','small_id');

    public $timestamps = false;

    /**
     * カテゴリの更新
     * @param $inquiry_id
     * @param $data
     * @return bool
     */
    public function updateCategory($inquiry_id, $data)
    {
        if (empty($inquiry_id) || empty($data)) {
            return false;
        }
        return self::updateOrCreate(['inquiry_id' => $inquiry_id], $data);
    }


}
