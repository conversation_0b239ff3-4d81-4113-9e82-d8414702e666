<?php
namespace App\Models\FreegameDeveloper;

/**
 * アプリケーション画像テーブル
 */
class ChApplicationImage extends FreegameDeveloper
{

    protected $table   = 'ch_application_image';

    protected $guarded = ['id'];

    protected $dates   = ['request_date', 'examination_date'];

    public $timestamps = false;

    /**
     * ID指定取得
     * @param  integer $id
     * @return boolean|object
     */
    public function getById($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->first();
    }

    /**
     * ID・デベロッパ指定取得
     * @param  integer $id
     * @param  integer $devId
     * @return boolean|object
     */
    public function getByIdAndDeveloper($id, $devId)
    {
        if (empty($id) || empty($devId)) {
            return false;
        }
        return self::where('id', $id)->where('developer_id', $devId)->first();
    }

    /**
     * 一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getList($param, $order = 'id asc')
    {
        $perPage = isset($param['perPage']) ? $param['perPage'] : null;
        $query = $this->getWhere($param);

        if ($perPage) {
            if (!empty($order) && $order != 'id asc') {
                return $query->orderByRaw($order)->paginate($perPage);
            } else {
                return $query->orderByRaw('id desc')->paginate($perPage);
            }
        } else {
            return $query->orderByRaw($order)->get();
        }
    }

    /**
     * 掲載画像一覧取得
     * @param int $chAppId
     * @param int $imageType
     * @return array
     */
    public function getPostImageList($chAppId, $imageType)
    {
        return self::where('ch_app_id', '=', $chAppId)
            ->where('image_type', '=', $imageType)
            ->where('examination_situation', '=', 3)
            ->where('post_id', '=', 1)->get();
    }

    /**
     * 審査OK一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getExamOkList($param, $order = 'sort_num asc')
    {
        $param['examination_result'] = config('forms.ChGameImage.ok');
        $param['examination_situation'] = config('forms.ChGameImage.completed');
        return $this->getList($param, $order);
    }

    /**
     * 審査中一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getExamReviewList($param, $order = 'sort_num asc')
    {
        $param['examination_result'] = config('forms.ChGameImage.none');
        $param['examination_situation'] = config('forms.ChGameImage.review');
        return $this->getList($param, $order);
    }

    /**
     * 審査前登録一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getExamRegisterList($param, $order = 'sort_num asc')
    {
        $param['examination_situation'] = config('forms.ChGameImage.register');
        return $this->getList($param, $order);
    }

    /**
     * 掲載画像存在確認
     * @param int $appId
     * @param int $imageType
     * @return array
     */
    public function postImageExist($appId, $imageType)
    {
        return self::where('ch_app_id', '=', $appId)
            ->where('image_type', '=', $imageType)
            ->where('examination_situation', '=', 3)
            ->where('post_id', '=', 1)
            ->first();
    }

    /**
     * 登録
     * @param  array   $param
     * @return boolean|object
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::create($param);
    }

    /**
     * 更新
     * @param  integer $id
     * @param  array   $param
     * @return boolean|object
     */
    public function edit($id, $param)
    {
        if (empty($id) || empty($param)) {
            return false;
        }
        return self::where('id', $id)->update($param);
    }

    /**
     * 削除
     * @param  integer $id
     * @return boolean|object
     */
    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

    /**
     * 該当種別の掲載ID初期化
     * @param  array $imgData
     * @return object
     */
    public function initPostIdByType($imgData)
    {
        $param = ['post_id' => config('forms.ChGameImage.none')];
        $where = [
            'ch_app_id' => $imgData['ch_app_id'],
            'image_size' => $imgData['image_size'],
        ];
        $query = $this->getWhere($where);

        return $query->update($param);
    }

    /**
     * 該当以降の審査OKデータ取得
     * @param  array $imgData
     * @return array
     */
    public function getAfterDataByType($imgData)
    {
        $where = [
            'ch_app_id' => $imgData['ch_app_id'],
            'image_size' => $imgData['image_size'],
            'examination_result' => config('forms.ChGameImage.ok'),
            'examination_situation' => config('forms.ChGameImage.completed'),
        ];
        $query = $this->getWhere($where);
        $query->where('sort_num', '>', $imgData['sort_num']);

        return $query->orderByRaw('id asc')->get();
    }

    /**
     * 変更対象となる画像のidのリストを返す
     * @param  array $imgData ch_application_imageから取得した詳細データ
     * @return array
     */
    public function getUpdateIdList($imgData)
    {
        return self::where('ch_app_id', $imgData['ch_app_id'])
            ->where('image_type', $imgData['image_type'])
            ->where('image_size', $imgData['image_size'])
            ->where('device', $imgData['device'])
            ->lists('id', 'id');
    }

    /**
     * 検索条件取得
     * @param  array $param
     * @return array
     */
    private function getWhere($param = [])
    {
        $query = self::from($this->table);

        $imgId = isset($param['id']) ? $param['id'] : null;
        $devId = isset($param['developer_id']) ? $param['developer_id'] : null;
        $chAppId = isset($param['ch_app_id']) ? $param['ch_app_id'] : null;
        $imageType = isset($param['image_type']) ? $param['image_type'] : null;
        $imageSize = isset($param['image_size']) ? $param['image_size'] : null;
        $device = isset($param['device']) ? $param['device'] : null;
        $examSituation = isset($param['examination_situation']) ? $param['examination_situation'] : null;
        $examResult = isset($param['examination_result']) ? $param['examination_result'] : null;
        $examDeveloperId = isset($param['examination_developer_id']) ? $param['examination_developer_id'] : null;

        $reqDateFrom = isset($param['request_date_from']) ? $param['request_date_from'] : null;
        $reqDateTo = isset($param['request_date_to']) ? $param['request_date_to'] : null;
        $examDateFrom = isset($param['examination_date_from']) ? $param['examination_date_from'] : null;
        $examDateTo = isset($param['examination_date_to']) ? $param['examination_date_to'] : null;

        if (! empty($imgId)) {
            $query->where('id', $imgId);
        }

        if (! empty($devId)) {
            $query->where('developer_id', $devId);
        }

        if (! empty($chAppId)) {
            $query->where('ch_app_id', $chAppId);
        }

        if (! empty($imageType)) {
            $query->where('image_type', $imageType);
        }

        if (! empty($imageSize)) {
            $query->where('image_size', $imageSize);
        }

        if (! empty($imageType) && ! empty($device)) {
            if ($imageType == config('forms.ChGameImage.thumbnail')) {
                $query->where('image_size', $device);
            } else {
                $query->where('device', $device);
            }
        }

        if (! empty($examSituation)) {
            $query->where('examination_situation', $examSituation);
        } else {
            $query->where('examination_situation', '<>', config('forms.ChGameImage.register'));
        }

        if (! empty($examResult)) {
            $query->where('examination_result', $examResult);
        }

        if (! empty($examDeveloperId)) {
            $query->where('examination_developer_id', $examDeveloperId);
        }


        if (! empty($reqDateFrom)) {
            $query->where('request_date', '>=', date('Y-m-d 00:00:00', strtotime($reqDateFrom)));
        }

        if (! empty($reqDateTo)) {
            $query->where('request_date', '<=', date('Y-m-d 23:59:59', strtotime($reqDateTo)));
        }

        if (! empty($examDateFrom)) {
            $query->where('examination_date', '>=', date('Y-m-d 00:00:00', strtotime($examDateFrom)));
        }

        if (! empty($examDateTo)) {
            $query->where('examination_date', '<=', date('Y-m-d 23:59:59', strtotime($examDateTo)));
        }

        return $query;
    }

    /**
     * Get Post Thumbnail List
     * @param  integer $chAppId
     * @return array
     */
    public function getPostThumbnailList($chAppId)
    {
        if (empty($chAppId)) {
            return false;
        }
        return self::where('ch_app_id', $chAppId)
            ->where('post_id', 1)
            ->where('examination_situation', 3)
            ->where('examination_result', 1)
            ->orderBy('image_size', 'asc')
            ->get();
    }
}
