<?php

namespace App\Models\FreegameDeveloper;

class DeveloperChApplication extends FreegameDeveloper
{
    protected $table = 'developer_ch_application';

    public $timestamps = false;

    /**
     * Get by developer_id
     * @param  integer $developerId
     * @return array
     */
    public function getListAppIdByDeveloperId($developerId)
    {
        return self::where('developer_id', $developerId)->lists('ch_app_id', 'id');
    }

    /**
     * Check exists by developer_id and ch_app_id
     * @param integer $developerId
     * @param integer $chAppId
     * @return array
     */
    public function getListAppIdByDeveloperIdAndChAppId($developerId, $chAppId)
    {
        if(empty($developerId)){
            return self::where('id', $chAppId)->lists('ch_app_id', 'id');
        }else{
            return self::where('developer_id', $developerId)->where('id', $chAppId)->lists('ch_app_id', 'id');
        }
    }

    /**
     * Check exists by developer_id and ch_app_id
     * @param integer $developerId
     * @param integer $chAppId
     * @return boolean
     */
    public function existsByDeveloperIdAndChAppId($developerId, $chAppId)
    {
        return self::where('developer_id', $developerId)->where('ch_app_id', $chAppId)->exists();
    }

    public function getApplicationAppIdList($condition = [])
    {
        $query = self::select([
            'ch_app_id'
        ]);
        if (! empty($condition['developer_id'])) {
            $query = $query->where('developer_id', $condition['developer_id']);
        }
        return $query->get();
    }
}
