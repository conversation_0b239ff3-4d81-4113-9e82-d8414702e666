<?php
namespace App\Models\FreegameDeveloper;

class MismatchedPaymentDataSandbox extends FreegameDeveloper
{

    protected $table = 'mismatched_payment_data_sandbox';

    public $timestamps = false;

    /**
     * 課金ログ比較ツール　期間とapp_id、デバイスをキーにして対象レコードを抽出
     * @param array $params
     * @return object
     */
    public function getPointLogApisList($params)
    {
        $query = self::select([
            'app_id',
            'device',
            'compare_status',
            'game_payment_id',
            'game_unit_price',
            'game_quantity',
            'game_total_point',
            'dmm_payment_id',
            'dmm_unit_price',
            'dmm_quantity',
            'dmm_total_point'
        ])
            ->where('app_id', $params['app_id'])
            ->whereIn('device', $params['device'])
            ->whereBetween('target_date', [$params['begin'], $params['end']]);

        if (! empty($params['chunk'])) {
            return $query->chunk(10000, $params['chunk']);
        } else {
            return $query->get();
        }
    }
}
