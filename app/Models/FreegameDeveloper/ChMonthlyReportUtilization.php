<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * チャネリングレポート利用
 */
class ChMonthlyReportUtilization extends FreegameDeveloper
{
    protected $table = 'ch_monthly_report_utilization';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (!empty($condition['app_id'])) {
            $query = $query->where('app_id', $condition['app_id']);
        }
        if (!empty($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
        }
        if (!empty($condition['report_date_ym'])) {
            $query = $query->where(DB::raw('DATE_FORMAT(report_date, "%Y-%m")'), '=', $condition['report_date_ym']);
        }
        if (!empty($condition['order'])) {
            foreach ($condition['order'] as $order) {
                $query = $query->orderBy($order[0], $order[1]);
            }
        }

        return $query->get();
    }
}
