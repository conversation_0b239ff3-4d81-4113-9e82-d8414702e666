<?php
namespace App\Models\FreegameDeveloper;

class MonthlyServiceReportDmmUtilization extends FreegameDeveloper
{
    protected $table   = 'monthly_service_report_dmm_utilization';

    protected $dates   = ['date'];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $query = $query->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        if (! empty($condition['kind'])) {
            $query = $query->where('kind', $condition['kind']);
        }
        return $query->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }
}
