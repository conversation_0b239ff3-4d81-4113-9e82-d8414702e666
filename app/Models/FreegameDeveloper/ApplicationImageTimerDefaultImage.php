<?php


namespace App\Models\FreegameDeveloper;


class ApplicationImageTimerDefaultImage extends FreegameDeveloper
{
    protected $table = 'application_image_timer_default_image';

    protected $fillable = ['app_id', 'application_image_id'];

    public $incrementing = false;

    protected $primaryKey = ['app_id', 'application_image_id'];

//*********************************************************************************************************************

    /**
     * appIdで取得
     * @param string $appId
     * @return bool|array
     */
    public function getByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }
        return self::where('app_id', $appId)->get();
    }

    /**
     * 追加
     * @param integer $appId
     * @param integer $imageId
     * @return bool
     */
    public function add($appId, $imageId)
    {
        if (empty($appId || $imageId)) {
            return false;
        }

        self::create(['app_id' => $appId, 'application_image_id' => $imageId]);
    }

    /**
     * 更新
     * @param $appId
     * @param $oldImageId
     * @param $newImageId
     * @return bool
     */
    public function edit($appId, $oldImageId, $newImageId)
    {
        if (empty($appId || $oldImageId || $newImageId)) {
            return false;
        }

        self::where('app_id', $appId)
            ->where('application_image_id', $oldImageId)
            ->update(['application_image_id' => $newImageId]);
    }
}