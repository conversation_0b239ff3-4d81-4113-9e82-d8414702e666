<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * ポイント合計
 */
class TotalPaymentPoint extends FreegameDeveloper
{

    protected $table = 'total_payment_point';

    public $timestamps = false;

    /**
     * 課金ログ比較ツール　期間とapp_id、デバイスをキーにして合計を算出
     * @param array $params
     * @return object
     */
    public function getPointLogApisList($params)
    {
        $query = self::select(
            DB::raw('SUM(game_total_point) as game_total_point
                , SUM(dmm_total_point) as dmm_total_point
                , SUM(matched_total_point) as matched_total_point')
        );
        $query = $query->where('app_id', $params['app_id']);
        $query = $query->whereIn('device', $params['device']);
        $query = $query->whereBetween('target_date', [$params['begin'], $params['end']]);
        return $query->get();
    }
}
