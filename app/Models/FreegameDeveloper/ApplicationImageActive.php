<?php
namespace App\Models\FreegameDeveloper;

/**
 */
class ApplicationImageActive extends FreegameDeveloper
{
    const UPDATED_AT = null;

    protected $table   = 'application_image_active';

    protected $guarded = ['id'];

//*********************************************************************************************************************
    /**
     * Application Image ID指定取得
     * @param  integer $appImageId
     * @return array
     */
    public function getByAppImgId($appImageId)
    {
        return self::where('application_image_id', $appImageId)->first();
    }

    /**
     * Application Id指定取得
     * @param integer $appId
     * @return array
     */
    public function getByAppIdAndIsMain($appId)
    {
        return self::join('application_image', function($join) use ($appId) {
            $join->on('application_image_active.application_image_id', '=', 'application_image.id')
                ->where('application_image.app_id', '=', $appId)
                ->where('application_image.image_type', '=', 1)
                ->where('application_image.examination_situation', '=', 3)
                ->where('application_image_active.is_main', '=', 1);
        })->first();
    }

    /**
     * Application Image Id指定取得
     * @param integer $appImageId
     * @return array
     */
    public function getByAppImgIdAndIsMain($appImageId)
    {
        return self::join('application_image', function($join) use ($appImageId) {
            $join->on('application_image_active.application_image_id', '=', 'application_image.id')
                ->where('application_image_id', '=', $appImageId)
                ->where('application_image_active.is_main', '=', 1);
        })->first();
    }

    /**
     * Application Image Id指定取得
     * @param integer $appId
     * @return array
     */
    public function getRecommendImageListByAppId($appId)
    {
        return self::join('application_image', function($join) use ($appId) {
            $join->on('application_image_active.application_image_id', '=', 'application_image.id')
                ->where('application_image.app_id', '=', $appId)
                ->where('application_image_active.is_main', '=', false);
        })->get();
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param array $param
     * @return boolean
     */
    public function edit($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::updateOrCreate(
            ['application_image_id' => $param['application_image_id']],
            $param
        );
    }

    /**
     * 削除
     * @param  integer $appImageId
     * @return boolean
     */
    public function del($appImageId)
    {
        if (empty($appImageId)) {
            return false;
        }
        return self::where('application_image_id', $appImageId)->delete();
    }
}
