<?php
namespace App\Models\FreegameDeveloper;

class MaintenanceInformation extends FreegameDeveloper
{
    protected $table = 'maintenance_information';

    protected $guarded = [
        'id'
    ];

    protected $dates = [
        'notice_date', 'notice_start_date', 'notice_end_date',
    ];

    public $timestamps = false;

    /**
     * Get one by ID
     * @param integer $id
     * @return object|boolean
     */
    public function getOne($id)
    {
        if (empty($id)) {
            return false;
        }

        return self::find($id);
    }

    /**
     * Get list by condition list
     * @param array $condition
     * @return array
     */
    public function getList($condition)
    {
        $query = $this
            ->where('is_delete', 0)
            ->orderBy('notice_date', 'desc')
            ->orderBy('id', 'desc');

        $this->addKeywordCondition($query, $condition);

        if (isset($condition['take'])) {
            $query
                ->where(function ($groupedQuery) use ($condition) {
                    $groupedQuery
                        ->where('notice_end_date', '>=', $condition['today'])
                        ->orWhere('notice_end_date', '=', '0000-00-00')
                        ->where('notice_start_date', '>', $condition['today']);
                });
            return $query->take($condition['take'])->get();
        }

        if (isset($condition['perPage'])) {
            return $query->paginate($condition['perPage']);
        }

        return $query->get();
    }

    /**
     * Add keyword condition
     * @param array $condition
     * @return Illuminate\Database\Eloquent\Builder
     */
    protected function addKeywordCondition($query, $condition)
    {
        if (empty($condition['keywordArray'])) {
            return $query;
        }

        foreach ($condition['keywordArray'] as $v) {
            $query->where(function ($groupedQuery) use ($v) {
                $groupedQuery
                    ->where('title', 'like', '%' . $v . '%')
                    ->orWhere('description', 'like', '%' . $v . '%');
            });
        }

        return $query;
    }
}
