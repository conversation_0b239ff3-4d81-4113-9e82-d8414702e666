<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * 男女別利用状況レポート
 */
class ReportGender extends FreegameDeveloper
{

    protected $table = 'report_gender';

    protected $dates = [
        'date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        if (! empty($condition['type'])) {
            $query = $query->where('type', $condition['type']);
        }
        $query = $query->exceptApp(); // 特定のアプリを除外
        return $query->orderBy('app_id', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS date",
            'app_id',
            'device',
            'type',
            'male_18_19',
            'male_20_24',
            'male_25_29',
            'male_30_34',
            'male_35_39',
            'male_40_49',
            'male_50',
            'female_18_19',
            'female_20_24',
            'female_25_29',
            'female_30_34',
            'female_35_39',
            'female_40_49',
            'female_50'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $subQuery = $subQuery->whereIn('app_id', $condition['app_id']);
            } else {
                $subQuery = $subQuery->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        if (! empty($condition['type'])) {
            $subQuery = $subQuery->where('type', $condition['type']);
        }
        $subQuery = $subQuery->exceptApp(); // 特定のアプリを除外
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'date' => 'date',
            'app_id' => 'app_id',
            'device' => 'device',
            'type' => 'type',
            'male_18_19' => 'SUM(male_18_19) AS male_18_19',
            'male_20_24' => 'SUM(male_20_24) AS male_20_24',
            'male_25_29' => 'SUM(male_25_29) AS male_25_29',
            'male_30_34' => 'SUM(male_30_34) AS male_30_34',
            'male_35_39' => 'SUM(male_35_39) AS male_35_39',
            'male_40_49' => 'SUM(male_40_49) AS male_40_49',
            'male_50' => 'SUM(male_50) AS male_50',
            'female_18_19' => 'SUM(female_18_19) AS female_18_19',
            'female_20_24' => 'SUM(female_20_24) AS female_20_24',
            'female_25_29' => 'SUM(female_25_29) AS female_25_29',
            'female_30_34' => 'SUM(female_30_34) AS female_30_34',
            'female_35_39' => 'SUM(female_35_39) AS female_35_39',
            'female_40_49' => 'SUM(female_40_49) AS female_40_49',
            'female_50' => 'SUM(female_50) AS female_50'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('date')
            ->groupBy('app_id')
            ->groupBy('device')
            ->groupBy('type')
            ->orderBy('app_id', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }
}
