<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * ポイント注文履歴
 */
class PointOrderHistory extends FreegameDeveloper
{
    protected $table = 'point_order_history';

    /**
     * ポイント注文履歴データ取得
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getList($condition)
    {
        return $this->getPointOrderHistory($condition);
    }

    /**
     * ポイント注文履歴データ または データ取得SQLを取得
     *
     * @params array  $condition
     * @params boolean $toSql
     * @params array  $bindings
     *
     * @return object or SQL text
     */
    public function getPointOrderHistory($condition, $toSql = false, &$bindings = [])
    {
        config()->set('database.default', $this->connection);

        $query = self::select();

        if (!empty($condition['partition'])) {
            $query->from(DB::raw('point_order_history PARTITION ('.$condition['partition'].')'));
        }
        if (isset($condition['app_id']) && $condition['app_id']) {
            $query->where('app_id', '=', $condition['app_id']);
            $bindings[] = $condition['app_id'];
        }
        if (!empty($condition['begin'])) {
            $query->where('entry_date', '>=', $condition['begin']);
            $bindings[] = $condition['begin'];
        }
        if (!empty($condition['end'])) {
            $query->where('entry_date', '<=', $condition['end']);
            $bindings[] = $condition['end'];
        }
        if (!empty($condition['user_id'])) {
            $query->whereRaw('SUBSTR(payment_id, 8, 10) = ?', [str_pad($condition['user_id'], 10, 0, STR_PAD_LEFT)]);
        }
        if (!empty($condition['order'])) {
            $query->orderBy($condition['order'][0], $condition['order'][1]);
        }

        if ($toSql) {
            return $query->toSql();
        }
        return $query->get();
    }

    /**
     * CSV出力用データを取得
     * @param  string $sql
     * @param  array $bindings
     * @return object
     */
    public function getExportCsvData($sql, $bindings = [])
    {
        return $this->getSelectStmt($sql, $bindings);
    }
}
