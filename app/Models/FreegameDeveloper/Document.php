<?php
namespace App\Models\FreegameDeveloper;

/**
 * ドキュメントテーブル
 */
class Document extends FreegameDeveloper
{

    protected $table = 'document';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['type'])) {
            $query = $query->where('type', $condition['type']);
        }
        return $query->get();
    }
}
