<?php
namespace App\Models\FreegameDeveloper;

/**
 * アプリケーション画像テーブル
 */
class ApplicationImage extends FreegameDeveloper
{

    protected $table   = 'application_image';

    protected $guarded = ['id'];

    protected $dates   = ['request_date', 'examination_date'];

    public $timestamps = false;

//*********************************************************************************************************************
    /**
     * ID指定取得
     * @param  integer $id
     * @return array
     */
    public function getById($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->first();
    }

    /**
     * ID・デベロッパ指定取得
     * @param  integer $id
     * @param  integer $devId
     * @return array
     */
    public function getByIdAndDeveloper($id, $devId)
    {
        if (empty($id) || empty($devId)) {
            return false;
        }
        return self::where('id', $id)->where('developer_id', $devId)->first();
    }

    /**
     * 一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getList($param, $order = 'id asc')
    {
        $perPage = isset($param['perPage']) ? $param['perPage'] : null;
        $query   = $this->getWhere($param);

        if ($perPage) {
            if (!empty($order) && $order != 'id asc') {
                return $query->orderByRaw($order)->paginate($perPage);
            } else {
                return $query->orderByRaw('id desc')->paginate($perPage);
            }
        } else {
            return $query->orderByRaw($order)->get();
        }
    }

    /**
     * 審査OK一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getExamOkList($param, $order = 'sort_num asc')
    {
        $param['examination_result']    = config('forms.GameImage.ok');
        $param['examination_situation'] = config('forms.GameImage.completed');
        return $this->getList($param, $order);
    }

    /**
     * 審査中一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getExamReviewList($param, $order = 'sort_num asc')
    {
        $param['examination_result']    = config('forms.GameImage.none');
        $param['examination_situation'] = config('forms.GameImage.review');
        return $this->getList($param, $order);
    }

    /**
     * 審査前登録一覧取得
     * @param  array  $param
     * @param  string $order
     * @return array
     */
    public function getExamRegisterList($param, $order = 'sort_num asc')
    {
        $param['examination_situation'] = config('forms.GameImage.register');
        return $this->getList($param, $order);
    }

    /**
     * 掲載画像存在確認
     * @param integer $appId
     * @param string $imageType
     * @return array
     */
    public function postImageExist($appId, $imageType)
    {
        return self::where('app_id', '=', $appId)
            ->where('image_type', '=', $imageType)
            ->where('examination_situation', '=', 3)
            ->where('post_id', '=', 1)
            ->first();
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  integer $id
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::create($param);
    }

    /**
     * 更新
     * @param  integer $id
     * @param  array   $param
     * @return boolean
     */
    public function edit($id, $param)
    {
        if (empty($id) || empty($param)) {
            return false;
        }
        return self::where('id', $id)->update($param);
    }

    /**
     * 削除
     * @param  integer $id
     * @return boolean
     */
    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

    /**
     * 該当種別の掲載ID初期化
     * @param  array $imgData
     * @return boolean
     */
    public function initPostIdByType($imgData)
    {
        $param = ['post_id' => config('forms.GameImage.none')];
        $where = [
            'app_id'     => $imgData['app_id'],
            'image_type' => $imgData['image_type'],
            'image_size' => $imgData['image_size'],
            'device'     => $imgData['device'],
        ];
        $query = $this->getWhere($where);

        return $query->update($param);
    }

    /**
     * 該当以降の審査OKデータ取得
     * @param  array $imgData
     * @return array
     */
    public function getAfterDataByType($imgData)
    {
        $where = [
            'app_id'                => $imgData['app_id'],
            'image_type'            => $imgData['image_type'],
            'image_size'            => $imgData['image_size'],
            'device'                => $imgData['device'],
            'examination_result'    => config('forms.GameImage.ok'),
            'examination_situation' => config('forms.GameImage.completed'),
        ];
        $query = $this->getWhere($where);
        $query->where('sort_num', '>', $imgData['sort_num']);

        return $query->orderByRaw('id asc')->get();
    }

    /**
     * 変更対象となる画像のidのリストを返す
     * @param  array $imgData application_imageから取得した詳細データ
     * @return array
     */
    public function getUpdateIdList($imgData)
    {
        return self::where('app_id', $imgData['app_id'])
            ->where('image_type', $imgData['image_type'])
            ->where('image_size', $imgData['image_size'])
            ->where('device', $imgData['device'])
            ->lists('id', 'id');
    }

//*********************************************************************************************************************
    /**
     * 検索条件取得
     * @param  array $param
     * @return array
     */
    private function getWhere($param = [])
    {
        $query           = self::from($this->table);

        $imgId           = isset($param['id'])                       ? $param['id']                       : null;
        $devId           = isset($param['developer_id'])             ? $param['developer_id']             : null;
        $appId           = isset($param['app_id'])                   ? $param['app_id']                   : null;
        $imageType       = isset($param['image_type'])               ? $param['image_type']               : null;
        $imageSize       = isset($param['image_size'])               ? $param['image_size']               : null;
        $device          = isset($param['device'])                   ? $param['device']                   : null;
        $examSituation   = isset($param['examination_situation'])    ? $param['examination_situation']    : null;
        $examResult      = isset($param['examination_result'])       ? $param['examination_result']       : null;
        $examDeveloperId = isset($param['exmaination_developer_id']) ? $param['exmaination_developer_id'] : null;

        $reqDateFrom     = isset($param['request_date_from'])        ? $param['request_date_from']        : null;
        $reqDateTo       = isset($param['request_date_to'])          ? $param['request_date_to']          : null;
        $examDateFrom    = isset($param['examination_date_from'])    ? $param['examination_date_from']    : null;
        $examDateTo      = isset($param['examination_date_to'])      ? $param['examination_date_to']      : null;

        if (! empty($imgId)) {
            $query->where('id', $imgId);
        }

        if (! empty($devId)) {
            $query->where('developer_id', $devId);
        }

        if (! empty($appId)) {
            $query->where('app_id', $appId);
        }

        if (! empty($imageType)) {
            $query->where('image_type', $imageType);
        }

        if (! empty($imageSize)) {
            $query->where('image_size', $imageSize);
        }

        if (! empty($imageType) && ! empty($device)) {
            if ($imageType == config('forms.GameImage.thumbnail')) {
                $query->where('image_size', $device);
            } else {
                $query->where('device', $device);
            }
        }

        if (! empty($examSituation)) {
            $query->where('examination_situation', $examSituation);
        } else {
            $query->where('examination_situation', '<>', config('forms.GameImage.register'));
        }

        if (! empty($examResult)) {
            $query->where('examination_result', $examResult);
        }

        if (! empty($examDeveloperId)) {
            $query->where('exmaination_developer_id', $examDeveloperId);
        }


        if (! empty($reqDateFrom)) {
            $query->where('request_date', '>=', date('Y-m-d 00:00:00', strtotime($reqDateFrom)));
        }

        if (! empty($reqDateTo)) {
            $query->where('request_date', '<=', date('Y-m-d 23:59:59', strtotime($reqDateTo)));
        }

        if (! empty($examDateFrom)) {
            $query->where('examination_date', '>=', date('Y-m-d 00:00:00', strtotime($examDateFrom)));
        }

        if (! empty($examDateTo)) {
            $query->where('examination_date', '<=', date('Y-m-d 23:59:59', strtotime($examDateTo)));
        }

        return $query;
    }

//*********************************************************************************************************************
    public function getPostThumbnailList($app_id, $imageType = 1)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->where('image_type', $imageType)
            ->where('post_id', 1)
            ->where('examination_situation', 3)
            ->where('examination_result', 1)
            ->orderBy('image_size', 'asc')
            ->get();
    }

    public function getPostGameImageList($app_id, $device)
    {
        if (empty($app_id) || empty($device)) {
            return false;
        }
        return self::where('app_id', $app_id)->where('image_type', 2)
            ->where('device', $device)
            ->where('post_id', '>', 0)
            ->where('examination_situation', 3)
            ->where('examination_result', 1)
            ->orderBy('post_id', 'asc')
            ->get();
    }

    /**
     * picsにアップロードされているmanifestIconを探す
     * @param string|int $app_id アプリid
     * @return false|array manifestIcon
     */
    public function getPostedManifestIcon($app_id)
    {
        if (empty($app_id)) {
            return false;
        }

        return self::where('app_id', $app_id)
            ->where('image_type', config('forms.GameImage.manifestIcon'))
            ->where('post_id', '=', 1)
            ->get();
    }
}
