<?php
namespace App\Models\FreegameDeveloper;

/**
 * お知らせテーブル
 */
class Information extends FreegameDeveloper
{

    protected $table = 'information';

    protected $guarded = [
        'id'
    ];

    protected $dates = [
        'notice_date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();

        if (! empty($condition['category'])) {
            $query = $query->whereIn('category', $condition['category']);
        }
        if (! empty($condition['from_notice_date'])) {
            $query = $query->where('notice_date', '>=', $condition['from_notice_date']);
        }

        $query = $query->where('public_status', 1)
            ->orderBy('notice_date', 'desc')
            ->orderBy('id', 'desc');

        // キーワード指定があれば、タイトルか本文を検索
        if (! empty($condition['keywordArray'])) {
            foreach ($condition['keywordArray'] as $v) {
                $query->where(function ($concatQuery) use ($v) {
                    $concatQuery->Where('title', 'like', '%'.$v.'%')
                        ->orWhere('description', 'like', '%'.$v.'%');
                });
            }
        }

        if (! empty($condition['perPage'])) {
            return $query->paginate($condition['perPage']);
        } elseif (! empty($condition['take'])) {
            return $query->take($condition['take'])->get();
        } else {
            return $query->get();
        }
    }

    public function getOne($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::find($id);
    }
}
