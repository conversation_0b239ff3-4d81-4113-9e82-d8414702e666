<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * DMM利用状況レポート
 */
class ReportDmmUtilizationByFloor extends FreegameDeveloper
{

    protected $table = 'report_dmm_utilization_by_floor';

    protected $dates = [
        'date'
    ];

    protected $primaryKey = ['date', 'app_id', 'device', 'publisher_floor_id'];

    public $incrementing = false;

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $query = $query->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        return $query->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->orderBy('publisher_floor_id', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS date",
            'attr',
            'device',
            'publisher_floor_id',
            'use_point',
            'use_point_pay',
            'use_point_free',
            'discount_price',
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $subQuery = $subQuery->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'date' => 'date',
            'attr' => 'attr',
            'device' => 'device',
            'publisher_floor_id' => 'publisher_floor_id',
            'use_point' => 'SUM(use_point) AS use_point',
            'use_point_pay' => 'SUM(use_point_pay) AS use_point_pay',
            'use_point_free' => 'SUM(use_point_free) AS use_point_free',
            'discount_price' => 'SUM(discount_price) AS discount_price',
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('date')
            ->groupBy('attr')
            ->groupBy('device')
            ->groupBy('publisher_floor_id')
            ->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->orderBy('publisher_floor_id', 'asc')
            ->get();
    }
}
