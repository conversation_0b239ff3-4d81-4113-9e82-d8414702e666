<?php
namespace App\Models\FreegameDeveloper;

use DB;

class MonthlyServiceReportMonthlyUtilization extends FreegameDeveloper
{
    protected $table   = 'monthly_service_report_monthly_utilization';

    protected $dates   = ['date', 'subdate'];

    public $timestamps = false;

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS subdate",
            'app_id',
            'device',
            'regist_user AS m_regist_user',
            'continue_user AS m_continue_user',
            'active_user AS m_active_user',
            'price AS m_price',
            'credit_price AS m_credit_price',
            'point_price AS m_point_price',
            'withdrawal_user AS m_withdrawal_user',
            'eviction_user AS m_eviction_user',
            'use_monthly_service_user AS m_use_monthly_service_user',
            'use_duplicate_user AS m_use_duplicate_user',
            'monthly_service_id'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $subQuery = $subQuery->whereIn('app_id', $condition['app_id']);
            } else {
                $subQuery = $subQuery->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        if (! empty($condition['kind'])) {
            $subQuery = $subQuery->where('kind', $condition['kind']);
        }
        if (! empty($condition['monthly_service_id'])) {
            if (is_array($condition['monthly_service_id'])) {
                $subQuery = $subQuery->whereIn('monthly_service_id', $condition['monthly_service_id']);
            } else {
                $subQuery = $subQuery->where('monthly_service_id', $condition['monthly_service_id']);
            }
        }
        $subQuery = $subQuery->exceptApp(); // 特定のアプリを除外
        return $subQuery->groupBy('device', 'subdate')
            ->orderBy('app_id', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('subdate', 'asc')
            ->get();
    }
}
