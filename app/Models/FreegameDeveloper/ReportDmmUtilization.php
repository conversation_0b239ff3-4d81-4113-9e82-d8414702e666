<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * DMM利用状況レポート
 */
class ReportDmmUtilization extends FreegameDeveloper
{

    protected $table = 'report_dmm_utilization';

    protected $dates = [
        'date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $query = $query->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        return $query->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS date",
            'attr',
            'device',
            'active_user',
            'download_user',
            'regist_user',
            'suspend_user',
            'pv',
            'dau',
            'use_point_user',
            'use_point',
            'use_point_pay',
            'use_point_free'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $subQuery = $subQuery->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            $subQuery = $subQuery->where('device', $condition['device']);
        }
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'date' => 'date',
            'attr' => 'attr',
            'device' => 'device',
            'active_user' => 'MAX(active_user) AS active_user',
            'active_user_all' => 'MAX(active_user) AS active_user_all',
            'download_user' => 'SUM(download_user) AS download_user',
            'regist_user' => 'SUM(regist_user) AS regist_user',
            'suspend_user' => 'SUM(suspend_user) AS suspend_user',
            'pv' => 'SUM(pv) AS pv',
            'dau' => 'SUM(dau) AS dau',
            'mau' => 'SUM(dau) AS mau',
            'use_point_user' => 'SUM(use_point_user) AS use_point_user',
            'use_point' => 'SUM(use_point) AS use_point',
            'use_point_pay' => 'SUM(use_point_pay) AS use_point_pay',
            'use_point_free' => 'SUM(use_point_free) AS use_point_free'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('date')
            ->groupBy('attr')
            ->groupBy('device')
            ->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }
}
