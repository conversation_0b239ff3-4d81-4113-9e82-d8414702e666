<?php
namespace App\Models\FreegameDeveloper;

/**
 * デベロッパーアプリケーション
 */
class DeveloperApplication extends FreegameDeveloper
{
    protected $table = 'developer_application';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get list by developer_id
     *
     * @param  integer $developerId
     *
     * @return object
     */
    public function getListByDeveloperId($developerId)
    {
        return self::select()->where('developer_id', $developerId)->get();
    }

    public function getApplicationAppIdList($condition = [])
    {
        $query = self::select([
            'app_id'
        ]);
        if (! empty($condition['developer_id'])) {
            $query = $query->where('developer_id', $condition['developer_id']);
        }
        return $query->get();
    }

    /**
     * ユーザタイトル取得
     * @param  integer $devId
     * @param  integer $appId
     * @return array
     */
    public function getApplication($appId, $devId)
    {
        return self::select('*')->where('app_id', $appId)->where('developer_id', $devId)->first();
    }

    /**
     * Check exists by developer_id and app_id
     * @param integer $developerId
     * @param integer $appId
     * @return boolean
     */
    public function existsByDeveloperIdAndAppId($developerId, $appId)
    {
        return self::where('developer_id', $developerId)->where('app_id', $appId)->exists();
    }
}
