<?php
namespace App\Models\FreegameDeveloper;

use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use App\Auth\DeveloperUserAuthRole;
use DB;

/**
 * デベロッパーテーブル
 */
class Developer extends FreegameDeveloper implements AuthenticatableContract
{
    use Authenticatable, DeveloperUserAuthRole;

    protected $table = 'developer';

    protected $guarded = [
        'id'
    ];

    protected $hidden = [
        'password'
    ];

    public $timestamps = false;

    /**
     * パスワードをハッシュ化して格納する
     * @param string $value パスワード値
     */
    public function setPasswordAttribute($value)
    {
        // SHA1ハッシュ化（ソルト値＋パスワード値）
        $this->attributes['password'] = hash('sha1', env('AUTH_PASSWORD_SALT', 'SomeRandomString') . $value);
    }

    /**
     * ログインidからレコードを1件取得
     * @param int $login_id login_id
     * @return object
     */
    public function getOneByLoginId($login_id)
    {
        return self::where('login_id', $login_id)->first();
    }

    /**
     * 指定したログインidのパスワードが一致した場合のみレコード取得
     * @param int $login_id ログインid
     * @param int $password パスワード
     * @return object
     */
    public function getOneForAuth($login_id, $password)
    {
        // パスワードハッシュ化のため[setPasswordAttribute()]関数を通す
        $this->password = $password;
        return self::where('login_id', $login_id)->where('password', $this->password)->first();
    }

    /**
     * 指定したidのパスワードが一致した場合のみレコード取得
     * @param int $id d
     * @param int $password パスワード
     * @return object
     */
    public function getOneForAuthById($id, $password)
    {
        // パスワードハッシュ化のため[setPasswordAttribute()]関数を通す
        $this->password = $password;
        return self::where('id', $id)->where('password', $this->password)->first();
    }

    /**
     * idで指定したレコードを変更
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        if (isset($updateData['password'])) {
            // hash値を格納
            $updateData['old_password'] = DB::raw('password');

            // パスワードハッシュ化のため[setPasswordAttribute()]関数を通す
            $this->password = $updateData['password'];
            // hash値を格納
            $updateData['password'] = $this->password;
        }
        return self::where('id', $id)
            ->update($updateData);
    }

    public function getOneAuthByOldPassword($id, $password)
    {
        $this->password = $password;
        return self::where('id', $id)->where('old_password', $this->password)->first();
    }

    /**
     * Check User Exist By Id And Login Id
     * @param integer $id
     * @param string $loginId
     * @return object
     */
    public function checkUserExistByIdAndLoginId($id, $loginId)
    {
        return self::where('id', $id)->where('login_id', $loginId)->first();
    }

    /**
     * xhr Update Password
     * @param array $params
     * @return boolean
     */
    public function xhrUpdatePassword($params)
    {
        if (!$params) {
            return false;
        }
        if (isset($params['password1'])) {
            // パスワードハッシュ化のため[setPasswordAttribute()]関数を通す
            $this->password         = $params['password1'];
            // hash値を格納
            $params['password1']    = $this->password;
            $this->password         = $params['password_now'];
            $params['password_now'] = $this->password;
        }
        return self::where('id', $params['id'])
            ->where('login_id', $params['login_id'])
            ->where('password', '=', $params['password_now'])
            ->update([
                'old_password'         => DB::raw('password'),
                'password'             => $params['password1'],
                'password_update_date' => $params['password_update_date']
            ]);
    }
}
