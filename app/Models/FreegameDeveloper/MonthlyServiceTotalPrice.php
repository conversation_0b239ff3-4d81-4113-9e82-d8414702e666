<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * ゲーム課金ログ・DMM課金ログ合計値保存テーブル
 */
class MonthlyServiceTotalPrice extends FreegameDeveloper
{
    protected $table = 'monthly_service_total_price';

    /**
     * get total
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getTotal($condition = [])
    {
        $query = self::select(
            DB::raw('SUM(game_unit_price)    AS game_total_price'),
            DB::raw('SUM(dmm_unit_price)     AS dmm_total_price'),
            DB::raw('SUM(matched_unit_price) AS matched_total_price')
        );

        if (!empty($condition['monthly_service_id'])) {
            $query = $query->where('monthly_service_id', '=', $condition['monthly_service_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('target_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('target_date', '<=', $condition['end']);
        }

        return $query->first();
    }
}
