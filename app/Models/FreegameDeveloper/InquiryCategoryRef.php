<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * 問合わせ管理カテゴリテーブル
 */
class InquiryCategoryRef extends FreegameDeveloper
{

    protected $table = 'inquiry_category_ref';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    public function store($condition = [])
    {
        if ( empty($condition['name_id']) && empty($condition['app_id']) ) {
            return false;
        }

        return $this->insertGetId($condition);
    }

    // inquiry_category_idをUPDATE（他の値のUPDATEは想定していない）
    public function updateToInquiryCategoryId($condition = [])
    {
        if ( empty($condition['id']) && empty($condition['app_id']) && empty($condition['inquiry_category_id']) ) {
            return false;
        }

        $this->where('id', $condition['id'])->where('app_id', $condition['app_id'])->update(['inquiry_category_id' => $condition['inquiry_category_id']]);

        return true;
    }

    public function delete($condition=[])
    {
        if ( empty($condition)) {
            return false;
        }

        $this->whereIn('id', $condition)->delete();

        return true;
    }

    /**
     * カテゴリリファレンスとカテゴリを取得する
     * 各条件に対応
     * @param array $condition
     * @return mixed
     */
    public function getCategory ($condition = [])
    {
        $query = self::select([
            'inquiry_category_ref.id',
            'inquiry_category_id',
            'app_id',
            'relation',
            'parent_id',
            'inquiry_category_ref.stamp',
            'category_name',
        ]);
        $query->join('inquiry_category', 'inquiry_category_ref.inquiry_category_id', '=', 'inquiry_category.id');

        //問合わせカテゴリリファレンスID
        if (!empty($condition['id'])) {
            $id = is_array ($condition['id']) ? $condition['id'] : array($condition['id']);
            $query->whereIn('inquiry_category_ref.id', $id);
        }
        //アプリID
        if (!empty($condition['app_id'])) {
            $app_id = is_array ($condition['app_id']) ? $condition['app_id'] : array($condition['app_id']);
            $query = $query->whereIn('app_id', $app_id);
        }
        //親ID
        if (!empty($condition['parent_id'])) {
            $query->where('parent_id', $condition['parent_id']);
        }
        //続柄
        if (!empty($condition['relation'])) {
            $query->where('relation', $condition['relation']);
        }
        // カテゴリ名
        if (!empty($condition['category_name'])) {
            $query->where('inquiry_category.category_name', $condition['category_name']);
        }
        // 並び順
        if (!empty($condition['ordey_by'])) {
            foreach ($condition['ordey_by'] as $ordeBy) {
                switch ($ordeBy) {
                    case 'refid_asc':
                        $query->orderBy('inquiry_category_ref.id','asc');
                        break;

                    case 'relation_desc':
                        $query->orderBy(DB::raw("field(relation,'large','middle','small')"));
                        break;
                    
                    default:
                        break;
                }
            }
        }

        return $query->get();
    }

    // 指定したapp_idに紐づいたカテゴリを取得（app_id単体、文言とjoinしない）
    public function getAppIdCategory($condition=0)
    {
        if ( empty($condition) ) {
            return false;
        }

        return $this->where('app_id', $condition)->get();
    }

    // 指定idの持つinquiry_category_idが、指定タイトルに存在するか確認。
    public function getExistInquiryCategoryId($condition) {
        if ( empty($condition) ) {
            return false;
        }

        $firstQuery = $this->select('inquiry_category_id')->
        where('id', $condition['large_id'])
        ->first();

        $query = $this->select('id')
        ->where('app_id', $condition['app_id'])
        ->Where('relation', $condition['relation'])
        ->where('inquiry_category_id' , $firstQuery->inquiry_category_id);

        return $query->first();
    }

    // カテゴリidを取得
    public function getCategoryRefID($id, $relation=null){
        $query = $this->select('id')->where('id', $id);
        if (! empty($relation)) {
            $query->where('relation', $relation);
        }

        return $query->first();
    }

    // 1レコードしか存在しないinquiry_category_idと、それを持つidを全て取得 
    public function getOnlyInquiryCategoryId() {

        $query = $this->select('id','inquiry_category_id')
        ->groupBy('inquiry_category_id')
        ->havingRaw("COUNT(inquiry_category_id) = 1");

        return $query->get();
    }

    // 指定したinquiry_category_idが使われているか取得
    public function getUseInquiryCategoryId($inquiryCategoryId) {

        $query = $this->select('id')
        ->where('inquiry_category_id', $inquiryCategoryId);

        return $query->first();
    }
}
