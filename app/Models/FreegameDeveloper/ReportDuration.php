<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * 経過日数別利用状況レポート
 */
class ReportDuration extends FreegameDeveloper
{

    protected $table = 'report_duration';

    protected $dates = [
        'report_date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('report_date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('report_date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        if (! empty($condition['type'])) {
            $query = $query->where('type', $condition['type']);
        }
        $query = $query->exceptApp(); // 特定のアプリを除外
        return $query->orderBy('report_date', 'asc')
            ->orderBy('app_id', 'asc')
            ->orderBy('device', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(report_date, '%Y-%m-01') AS report_date",
            'app_id',
            'device',
            'type',
            'install_count',
            'day_1st',
            'day_2nd',
            'day_3rd',
            'day_4th',
            'day_5th',
            'day_6th',
            'day_7th',
            'day_14th',
            'day_30th'
        ]));
        if (! empty($condition['select'])) {
            $subQuery = $subQuery->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('report_date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('report_date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $subQuery = $subQuery->whereIn('app_id', $condition['app_id']);
            } else {
                $subQuery = $subQuery->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        if (! empty($condition['type'])) {
            $subQuery = $subQuery->where('type', $condition['type']);
        }
        $subQuery = $subQuery->exceptApp(); // 特定のアプリを除外
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'report_date' => 'report_date',
            'app_id' => 'app_id',
            'device' => 'device',
            'type' => 'type',
            'install_count' => 'SUM(install_count) AS install_count',
            'day_1st' => 'MAX(day_1st) AS day_1st',
            'day_2nd' => 'MAX(day_2nd) AS day_2nd',
            'day_3rd' => 'MAX(day_3rd) AS day_3rd',
            'day_4th' => 'MAX(day_4th) AS day_4th',
            'day_5th' => 'MAX(day_5th) AS day_5th',
            'day_6th' => 'MAX(day_6th) AS day_6th',
            'day_7th' => 'MAX(day_7th) AS day_7th',
            'day_14th' => 'MAX(day_14th) AS day_14th',
            'day_30th' => 'MAX(day_30th) AS day_30th'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('report_date')
            ->groupBy('app_id')
            ->groupBy('device')
            ->groupBy('type')
            ->orderBy('report_date', 'asc')
            ->orderBy('app_id', 'asc')
            ->orderBy('device', 'asc')
            ->get();
    }
}
