<?php
namespace App\Models\FreegameDeveloper;

/**
 * 事前登録キャンペーン
 */
class MailformCampaign extends FreegameDeveloper
{
    protected $table = 'mailform_campaign';

    protected $guarded = ['id'];

    public $timestamps = false;

    /**
     * select by condition
     *
     * @params array  $condition
     * @return LengthAwarePaginator | Collection
     */
    public function getList($condition = [])
    {
        $query = self::select('mailform_campaign.*');

        if (!empty($condition['developer_id'])) {
            $query = $query->join('developer_mailform_campaign AS dmc', 'mailform_campaign.id', '=', 'dmc.mailform_campaign_id');
            $query = $query->where('dmc.developer_id', $condition['developer_id']);
        }

        $query = $query->orderBy('mailform_campaign.id');

        if (isset($condition['perPage'])) {
            $result = $query->paginate($condition['perPage']);
        } else {
            $result = $query->get();
        }

        return $result;
    }

    /**
     * get one by id
     * @param int $id
     * @return MailformCampaign
     */
    public function getOneById($id)
    {
        if (empty($id)) {
            return false;
        }
        $query = self::where('id', $id);
        return $query->first();
    }


    /**
     * insert and get id
     * @param array $param
     * @return boolean | int
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        $id = self::insertGetId($param);
        return $id;
    }

    /**
     * update
     * @param int $id
     * @param array $param
     * @return boolean | int
     */
    public function edit($id, $param)
    {
        if (empty($id) || empty($param)) {
            return false;
        }
        return self::where('id', $id)->update($param);
    }

    /**
     * delete by id
     * @param int $id
     * @return boolean | int
     */
    public function deleteById($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

}
