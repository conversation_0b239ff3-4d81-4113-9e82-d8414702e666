<?php
namespace App\Models\FreegameDeveloper;

use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use App\Auth\DeveloperUserAuthRole;

/**
 * デベロッパーパスワード履歴テーブル
 */
class DeveloperPasswordHistory extends FreegameDeveloper implements AuthenticatableContract
{
    use Authenticatable, DeveloperUserAuthRole;

    protected $table = 'developer_password_history';

    protected $fillable = [
        'developer_id',
        'password',
        'password_update_date',
    ];

    protected $hidden = [
        'password'
    ];

    public $timestamps = false;

    /**
     * パスワードをハッシュ化して格納する
     * @param string $value パスワード値
     */
    public function setPasswordAttribute($value)
    {
        // SHA1ハッシュ化（ソルト値＋パスワード値）
        $this->attributes['password'] = hash('sha1', env('AUTH_PASSWORD_SALT', 'SomeRandomString') . $value);
    }

    /**
     * 指定したidとパスワードが一致するレコードを取得
     * パスワードが以前利用されていた場合はレコードが返る
     * @param int $developerId デベロッパID
     * @param int $password パスワード平文
     * @return object|null
     */
    public function getHistoryByIdAndPass($developerId, $password)
    {
        // パスワードハッシュ化のため[setPasswordAttribute()]関数を通す
        $this->password = $password;
        return self::where('developer_id', $developerId)->where('password', $this->password)->first();
    }

    /**
     * 新規のパスワードを格納する
     * @param integer $developerId デベロッパID
     * @param string  $password パスワード平文
     * @param Carbon  $date 設定日時(Optional)
     * @return boolean
     */
    public function addHistory($developerId, $password, $date = null)
    {
        if (isset($developerId) && isset($password)) {
            $data['developer_id'] = $developerId;
            $data['password_update_date'] = ($date !== null ? $date : null);
            $data['password'] = $password;
            return self::create($data);
        }
        return false;
    }
}
