<?php
namespace App\Models\FreegameDeveloper;

/**
 * チャネリングアプリケーション区分
 */
class ChApplicationDivision extends FreegameDeveloper
{
    protected $table = 'ch_application_division';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        return self::select()->get();
    }

/**
     * Get One by ch_app_id
     * @return object
     */
    public function getOneByChAppId($chAppId)
    {
        return self::select('ch_app_id', 'game_id')
               ->where('ch_app_id', $chAppId)->first();
    }
}
