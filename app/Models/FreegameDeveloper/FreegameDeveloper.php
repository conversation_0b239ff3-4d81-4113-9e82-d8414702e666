<?php

namespace App\Models\FreegameDeveloper;

use App\Models\CustomModel;

class FreegameDeveloper extends CustomModel
{
    protected $connection = 'freegame_developer_db';

    /**
     * Get database connection name
     * @return string
     */
    public function getDbConnection()
    {
        return $this->connection;
    }

    /**
     * utfmb4で取得するようDB接続設定を切り替える
     * @param $on
     */
    protected function changeModeToUtf8mb4($on)
    {
        $this->connection = $on ? 'freegame_developer_db_utf8mb4' : 'freegame_developer_db';
    }

    /**
     * 文字化け修正
     * EUCの文字をUTF-8に変換し、JISの文字コードから対応する文字に修正する
     * 対象外の文字コードは?に変換する
     * (character corruption = 文字化け)
     * @param $str
     * @return mixed
     */
    protected function fixCharCorruption($str)
    {
        $convert_data = mb_convert_encoding($str, 'UTF-8', 'EUC-JP');
        $fixed_str = preg_replace_callback("/JIS2?+\+\w{4}+/", function($matches) {
            $replacements = [
                'JIS+2D21' => '①',
                'JIS+2D22' => '②',
                'JIS+2D23' => '③',
                'JIS+2D24' => '④',
                'JIS+2D25' => '⑤',
                'JIS+2D26' => '⑥',
                'JIS+2D27' => '⑦',
                'JIS+2D28' => '⑧',
                'JIS+2D29' => '⑨',
                'JIS+2D2A' => '⑩',
                'JIS+2D2B' => '⑪',
                'JIS+2D2C' => '⑫',
                'JIS+2D2D' => '⑬',
                'JIS+2D2E' => '⑭',
                'JIS+2D2F' => '⑮',
                'JIS+2D30' => '⑯',
                'JIS+2D31' => '⑰',
                'JIS+2D32' => '⑱',
                'JIS+2D33' => '⑲',
                'JIS+2D34' => '⑳',
                'JIS+2D35' => 'Ⅰ',
                'JIS+2D36' => 'Ⅱ',
                'JIS+2D37' => 'Ⅲ',
                'JIS+2D38' => 'Ⅳ',
                'JIS+2D39' => 'Ⅴ',
                'JIS+2D3A' => 'Ⅵ',
                'JIS+2D3B' => 'Ⅶ',
                'JIS+2D3C' => 'Ⅷ',
                'JIS+2D3D' => 'Ⅸ',
                'JIS+2D3E' => 'Ⅹ',
                'JIS+2D40' => '㍉',
                'JIS+2D41' => '㌔',
                'JIS+2D42' => '㌢',
                'JIS+2D43' => '㍍',
                'JIS+2D44' => '㌘',
                'JIS+2D45' => '㌧',
                'JIS+2D46' => '㌃',
                'JIS+2D47' => '㌶',
                'JIS+2D48' => '㍑',
                'JIS+2D49' => '㍗',
                'JIS+2D4A' => '㌍',
                'JIS+2D4B' => '㌦',
                'JIS+2D4C' => '㌣',
                'JIS+2D4D' => '㌫',
                'JIS+2D4E' => '㍊',
                'JIS+2D4F' => '㌻',
                'JIS+2D50' => '㎜',
                'JIS+2D51' => '㎝',
                'JIS+2D52' => '㎞',
                'JIS+2D53' => '㎎',
                'JIS+2D54' => '㎏',
                'JIS+2D55' => '㏄',
                'JIS+2D56' => '㎡',
                'JIS+2D5F' => '㍻',
                'JIS+2D60' => '〝',
                'JIS+2D61' => '〟',
                'JIS+2D62' => '№',
                'JIS+2D63' => '㏍',
                'JIS+2D64' => '℡',
                'JIS+2D65' => '㊤',
                'JIS+2D66' => '㊥',
                'JIS+2D67' => '㊦',
                'JIS+2D68' => '㊧',
                'JIS+2D69' => '㊨',
                'JIS+2D6A' => '㈱',
                'JIS+2D6B' => '㈲',
                'JIS+2D6C' => '㈹',
                'JIS+2D6D' => '㍾',
                'JIS+2D6E' => '㍽',
                'JIS+2D6F' => '㍼',
            ];
            return !empty($replacements[$matches[0]]) ? $replacements[$matches[0]] : "?";
        }, $convert_data);
        return $fixed_str;
    }

    /**
     * select 文を実行し、PDOStatement を返却します。
     * <em>必ず select 文のみに使用してください</em>
     * @param $sql
     * @param array $bindings
     * @return \PDOStatement
     */
    protected function getSelectStmt($sql, $bindings = [])
    {
        return $this->getSelectStmtByConnection(\DB::connection('freegame_developer_db'), $sql, $bindings);
    }

    /**
     * select 文を実行し、PDOStatement を返却します。
     * <em>必ず select 文のみに使用してください</em>
     * @param Connection $connection
     * @param $sql
     * @param array $bindings
     * @return \PDOStatement
     */
    protected function getSelectStmtByConnection($connection, $sql, $bindings = [])
    {
        if (!preg_match('!^\s*select[\s\r\n]!i', $sql) && !preg_match('!^\s*\(select[\s\r\n]!i', $sql)) {
            throw new \BadMethodCallException("illegal sql : $sql");
        }
        if ($connection->pretending()) {
            return array();
        }

        for ($i = 0; $i < 2; $i++) {
            try {
                if (is_null($connection->getPdo()) || is_null($connection->getReadPdo())) {
                    $connection->reconnect();
                }
                $connection->getReadPdo()->setAttribute(\PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, false);
                $stmt = $connection->getReadPdo()->prepare($sql);
                $stmt->execute($connection->prepareBindings($bindings));
                break;
            } catch (\Exception $e) {
                if ($i <= 0 && str_contains($e->getMessage(), 'server has gone away')) {
                    $connection->reconnect();
                    continue;
                }
                throw new \Illuminate\Database\QueryException(
                    $sql, $connection->prepareBindings($bindings), $e
                );
            }
        }

        $connection->logQuery($sql, $bindings);

        // General error: No fetch class specified 対策
        $stmt->setFetchMode(\PDO::FETCH_CLASS, '\stdClass');

        return $stmt;
    }
}
