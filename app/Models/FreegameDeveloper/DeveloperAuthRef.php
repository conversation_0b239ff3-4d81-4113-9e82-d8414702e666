<?php
namespace App\Models\FreegameDeveloper;

/**
 * デベロッパー閲覧権限関連テーブル
 */
class DeveloperAuthRef extends FreegameDeveloper
{

    protected $table = 'developer_auth_ref';

    protected $primaryKey = [
        'developer_id',
        'role_id'
    ];

    protected $fillable = [
        'developer_id',
        'role_id'
    ];

    public $timestamps = false;

    public function getListForAuth($condition = [])
    {
        $query = self::query();
        $query = $query->join(
            'developer_auth_role',
            'developer_auth_ref.role_id',
            '=',
            'developer_auth_role.id'
        );
        $query = $query->join(
            'developer_auth_role_ref',
            'developer_auth_role.id',
            '=',
            'developer_auth_role_ref.role_id'
        );
        $query = $query->join(
            'developer_auth_permission',
            'developer_auth_role_ref.permission_id',
            '=',
            'developer_auth_permission.id'
        );
        $query = $query->join(
            'developer_auth_permission_ref',
            'developer_auth_permission.id',
            '=',
            'developer_auth_permission_ref.permission_id'
        );
        $query = $query->join(
            'developer_auth_action',
            'developer_auth_permission_ref.action_id',
            '=',
            'developer_auth_action.id'
        );
        if (! empty($condition['developer_id'])) {
            $query = $query->where('developer_auth_ref.developer_id', $condition['developer_id']);
        }
        if (! empty($condition['role_id'])) {
            $query = $query->where('developer_auth_ref.role_id', $condition['role_id']);
        }
        $query = $query->where('developer_auth_role.deleted', 0)
            ->where('developer_auth_permission.deleted', 0)
            ->where('developer_auth_action.site', 'developer')
            ->where('developer_auth_action.deleted', 0);
        return $query->select('developer_auth_action.url')->get();
    }
}
