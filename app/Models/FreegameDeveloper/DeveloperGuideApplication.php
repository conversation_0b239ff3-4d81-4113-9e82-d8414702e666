<?php

namespace App\Models\FreegameDeveloper;

class DeveloperGuideApplication extends FreegameDeveloper
{
    protected $table = 'developer_guide_application';

    public $timestamps = false;

    /**
     * Get guide_application_id by DeveloperGuideApplication
     * @param integer $userId
     * @return array
     */
    public function getListGuideAppIdByDevId($userId)
    {
        return self::where('developer_id', $userId)->lists('guide_application_id');
    }
}
