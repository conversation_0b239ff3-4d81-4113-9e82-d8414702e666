<?php
namespace App\Models\FreegameDeveloper;

class MonthlyServiceReportUtilization extends FreegameDeveloper
{
    protected $table   = 'monthly_service_report_utilization';

    protected $dates   = ['date'];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        if (! empty($condition['kind'])) {
            $query = $query->where('kind', $condition['kind']);
        }
        if (! empty($condition['monthly_service_id'])) {
            if (is_array($condition['monthly_service_id'])) {
                $query = $query->whereIn('monthly_service_id', $condition['monthly_service_id']);
            } else {
                $query = $query->where('monthly_service_id', $condition['monthly_service_id']);
            }
        }
        $query = $query->exceptApp(); // 特定のアプリを除外
        return $query->orderBy('app_id', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }
}
