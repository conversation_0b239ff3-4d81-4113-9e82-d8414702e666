<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * ゲストDMM月別利用状況レポート
 */
class GuestReportDmmMonthlyUtilization extends FreegameDeveloper
{

    protected $table = 'guest_report_dmm_monthly_utilization';

    protected $dates = [
        'date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $query = $query->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        return $query->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS date",
            'attr',
            'device',
            'active_user',
            'active_user_all',
            'regist_user',
            'upgrade_user',
            'pv',
            'mau',
            'use_point_user',
            'use_point_pay'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $subQuery = $subQuery->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'date' => 'date',
            'attr' => 'attr',
            'device' => 'device',
            'active_user' => 'MAX(active_user) AS active_user',
            'active_user_all' => 'MAX(active_user_all) AS active_user_all',
            'regist_user' => 'SUM(regist_user) AS regist_user',
            'upgrade_user' => 'SUM(upgrade_user) AS upgrade_user',
            'pv' => 'SUM(pv) AS pv',
            'mau' => 'SUM(mau) AS mau',
            'use_point_user' => 'SUM(use_point_user) AS use_point_user',
            'use_point_pay' => 'SUM(use_point_pay) AS use_point_pay'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('date')
            ->groupBy('attr')
            ->groupBy('device')
            ->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }
}
