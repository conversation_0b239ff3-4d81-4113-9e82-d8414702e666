<?php
namespace App\Models\FreegameDeveloper;

use DB;

/**
 * DMM月別利用状況レポート
 */
class ReportDmmMonthlyUtilization extends FreegameDeveloper
{

    protected $table = 'report_dmm_monthly_utilization';

    protected $dates = [
        'date'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $query = $query->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('device', $condition['device']);
            } else {
                $query = $query->where('device', $condition['device']);
            }
        }
        return $query->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(date, '%Y-%m-01') AS date",
            'attr',
            'device',
            'active_user',
            'active_user_all',
            'download_user',
            'regist_user',
            'suspend_user',
            'pv',
            'mau',
            'use_point_user',
            'use_point',
            'use_point_pay',
            'use_point_free',
            'discount_price'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('date', '<=', $condition['end']);
        }
        if (! empty($condition['attr'])) {
            $subQuery = $subQuery->where('attr', $condition['attr']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $subQuery = $subQuery->whereIn('device', $condition['device']);
            } else {
                $subQuery = $subQuery->where('device', $condition['device']);
            }
        }
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'date' => 'date',
            'attr' => 'attr',
            'device' => 'device',
            'active_user' => 'MAX(active_user) AS active_user',
            'active_user_all' => 'MAX(active_user_all) AS active_user_all',
            'download_user' => 'SUM(download_user) AS download_user',
            'regist_user' => 'SUM(regist_user) AS regist_user',
            'suspend_user' => 'SUM(suspend_user) AS suspend_user',
            'pv' => 'SUM(pv) AS pv',
            'mau' => 'SUM(mau) AS mau',
            'use_point_user' => 'SUM(use_point_user) AS use_point_user',
            'use_point' => 'SUM(use_point) AS use_point',
            'use_point_pay' => 'SUM(use_point_pay) AS use_point_pay',
            'use_point_free' => 'SUM(use_point_free) AS use_point_free',
            'discount_price' => 'SUM(discount_price) AS discount_price'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('date')
            ->groupBy('attr')
            ->groupBy('device')
            ->orderBy('attr', 'asc')
            ->orderBy('device', 'desc')
            ->orderBy('date', 'asc')
            ->get();
    }

    public function getMonthlyServiceList($condition = [])
    {
        $select = [
            'date'                       => 'report_dmm_monthly_utilization.date',
            'attr'                       => 'report_dmm_monthly_utilization.attr',
            'device'                     => 'report_dmm_monthly_utilization.device',
            'date_detail'                => 'DATE_FORMAT(report_dmm_monthly_utilization.date, \'%Y-%m\') AS date_detail',
            'active_user'                => 'MAX(report_dmm_monthly_utilization.active_user) AS active_user',
            'active_user_all'            => 'MAX(report_dmm_monthly_utilization.active_user_all) AS active_user_all',
            'download_user'              => 'SUM(report_dmm_monthly_utilization.download_user) AS download_user',
            'regist_user'                => 'SUM(report_dmm_monthly_utilization.regist_user) AS regist_user',
            'suspend_user'               => 'SUM(report_dmm_monthly_utilization.suspend_user) AS suspend_user',
            'pv'                         => 'SUM(report_dmm_monthly_utilization.pv) AS pv',
            'use_point_user'             => 'SUM(report_dmm_monthly_utilization.use_point_user) AS use_point_user',
            'use_point'                  => 'SUM(report_dmm_monthly_utilization.use_point) AS use_point',
            'use_point_pay'              => 'SUM(report_dmm_monthly_utilization.use_point_pay) AS use_point_pay',
            'use_point_free'             => 'SUM(report_dmm_monthly_utilization.use_point_free) AS use_point_free',
            'upgrade_user'               => 'SUM(g.upgrade_user) AS upgrade_user',
            'm_regist_user'              => 'SUM(m.regist_user) AS m_regist_user',
            'm_continue_user'            => 'SUM(m.continue_user) AS m_continue_user',
            'm_active_user'              => 'MAX(m.active_user) AS m_active_user',
            'm_price'                    => 'SUM(m.price) AS m_price',
            'm_withdrawal_user'          => 'SUM(m.withdrawal_user) AS m_withdrawal_user',
            'm_eviction_user'            => 'SUM(m.eviction_user) AS m_eviction_user',
            'm_use_monthly_service_user' => 'SUM(m.use_monthly_service_user) AS m_use_monthly_service_user',
            'm_use_duplicate_user'       => 'SUM(m.use_duplicate_user) AS m_use_duplicate_user',
            'm_credit_price'             => 'SUM(m.credit_price) AS m_credit_price',
            'm_point_price'              => 'SUM(m.point_price) AS m_point_price',
        ];
        $query = self::selectRaw(implode(', ', $select))
                ->leftJoin('guest_report_dmm_monthly_utilization AS g', function ($q) use ($condition) {
                    $q->on('report_dmm_monthly_utilization.date', '=', 'g.date')
                    ->on('report_dmm_monthly_utilization.device', '=', 'g.device')
                    ->on('report_dmm_monthly_utilization.attr', '=', 'g.attr');
                })
                ->leftJoin('monthly_service_report_dmm_monthly_utilization AS m', function ($q) use ($condition) {
                    $q->on('report_dmm_monthly_utilization.date', '=', 'm.date')
                    ->on('report_dmm_monthly_utilization.device', '=', 'm.device')
                    ->on('m.kind', '=', DB::raw('"social"'));
                    if ($condition['attr'] == 'spt') {
                        $q = $q->on('m.attr', '=', DB::raw('"all"'));
                    } else {
                        $q = $q->on('m.attr', '=', 'report_dmm_monthly_utilization.attr');
                    }
                });

        if (! empty($condition['begin'])) {
            $query = $query->where('report_dmm_monthly_utilization.date', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('report_dmm_monthly_utilization.date', '<=', $condition['end']);
        }
        if (! empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query = $query->whereIn('report_dmm_monthly_utilization.device', $condition['device']);
            } else {
                $query = $query->where('report_dmm_monthly_utilization.device', $condition['device']);
            }
        }
        if (! empty($condition['attr'])) {
            if ($condition['attr'] == 'spt') {
                // 月額サービスの場合、DMM全体サポート調整含(spt)がなくDMM全体(all)と同じデータを登録するため
                $query = $query->where('report_dmm_monthly_utilization.attr', '=', 'all');
            } else {
                $query = $query->where('report_dmm_monthly_utilization.attr', '=', $condition['attr']);
            }
        }

        $query = $query->groupBy('device', DB::raw('DATE_FORMAT(report_dmm_monthly_utilization.date, \'%Y-%m\')'));
        $query = $query->orderBy('report_dmm_monthly_utilization.date', 'asc');

        return $query->get();
    }
}
