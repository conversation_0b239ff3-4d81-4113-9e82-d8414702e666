<?php
namespace App\Models\FreegameDeveloper;

/**
 * 利用規約申請テーブル
 */
class ApplicationPolicyApply extends FreegameDeveloper
{
    protected $table = 'application_policy_apply';

    public $timestamps = false;

    /**
     * app_idをキーにして、同アプリで最後に申請した規約を取得する
     * @return int uid
     */
    public function getRecencyOne($app_id)
    {
        $query = self::select()
                ->where('app_id', $app_id)
                ->where('is_delete', 0)
                ->orderBy('id', 'desc')
                ->first();
        return $query;
    }

    /**
     * 登録
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::insert($param);
    }

    /**
     * 更新
     * @param  array   $param
     * @param  int   $id
     * @return boolean
     */
    public function edit($param, $id)
    {
        return self::where('id', $id)
            ->update($param);
    }

    /**
     * 公開済みの規約（過去分含めた履歴、公開日時昇順）を取得
     * @return int uid
     */
    public function getReleasedList($app_id)
    {
        $query = self::select()
                ->where('app_id', $app_id)
                ->where('examination_status', 1)
                ->whereNotNull('release_date')
                ->where('is_delete', 0)
                ->orderBy('release_date')
                ->get();
        return $query;
    }

}
