<?php
namespace App\Models\FreegameDeveloper;

/**
 */
class ClApplicationImageActive extends FreegameDeveloper
{
    const UPDATED_AT = null;

    protected $table   = 'cl_application_image_active';

    protected $guarded = ['id'];

//*********************************************************************************************************************
    /**
     * @param  integer $appImageId
     * @return array
     */
    public function getByAppImgId($appImageId)
    {
        return self::where('cl_application_image_id', $appImageId)->first();
    }

    /**
     * @param integer $appImageId
     * @return array
     */
    public function getByAppImgIdAndIsMain($appImageId)
    {
        return self::join('cl_application_image', function($join) use ($appImageId) {
            $join->on('cl_application_image_active.cl_application_image_id', '=', 'cl_application_image.id')
                ->where('cl_application_image_id', '=', $appImageId)
                ->where('cl_application_image_active.is_main', '=', 1);
        })->first();
    }

    /**
     * Application Image Id指定取得
     * @param integer $clAppId
     * @return array
     */
    public function getRecommendImageListByClAppId($clAppId)
    {
        return self::join('cl_application_image', function($join) use ($clAppId) {
            $join->on('cl_application_image_active.cl_application_image_id', '=', 'cl_application_image.id')
                ->where('cl_application_image.cl_app_id', '=', $clAppId)
                ->where('cl_application_image_active.is_main', '=', false);
        })->get();
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param array $param
     * @return boolean
     */
    public function edit($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::updateOrCreate(
            ['cl_application_image_id' => $param['cl_application_image_id']],
            $param
        );
    }

    /**
     * 削除
     * @param  integer $appImageId
     * @return boolean
     */
    public function del($appImageId)
    {
        if (empty($appImageId)) {
            return false;
        }
        return self::where('cl_application_image_id', $appImageId)->delete();
    }
}
