<?php
namespace App\Models\FreegameDeveloper;

/**
 * 問合わせ管理カテゴリテーブル
 */
class InquiryCategory extends FreegameDeveloper
{

    protected $table = 'inquiry_category';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    public function store($categoryName)
    {
        if (empty($categoryName)) {
            return false;
        }

        $param = [
            'category_name' => $categoryName,
        ];

        return $this->insertGetId($param);
    }

    // 配列でidを指定して削除
    public function deleteById($id)
    {
        if (empty($id)) {
            return false;
        }

        $this->whereIn('id', $id)->delete();

        return true;
    }

    public function getId($categoryName)
    {
        if (empty($categoryName)) {
            return false;
        }

        return $this->select('id')->where('category_name', $categoryName)->first();
    }
}
