<?php
namespace App\Models\FreegameDeveloper;

/**
 * イメージユーザ検閲ログ
 */
class ImageUserInspectionLog extends FreegameDeveloper
{
    protected $table = 'image_user_inspection_log';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insertGetId($data);
    }

    /**
     * update
     *
     * @param  string $id
     * @param  array  $data
     *
     * @return object
     */
    public function edit($id, $data)
    {
        return self::where('id', $id)->update($data);
    }
}
