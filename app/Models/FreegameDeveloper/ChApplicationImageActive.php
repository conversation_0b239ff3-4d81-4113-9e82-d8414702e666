<?php
namespace App\Models\FreegameDeveloper;

/**
 */
class ChApplicationImageActive extends FreegameDeveloper
{
    const UPDATED_AT = null;

    protected $table   = 'ch_application_image_active';

    protected $guarded = ['id'];

//*********************************************************************************************************************
    /**
     * @param  integer $appImageId
     * @return array
     */
    public function getByAppImgId($appImageId)
    {
        return self::where('ch_application_image_id', $appImageId)->first();
    }

    /**
     * @param integer $appImageId
     * @return array
     */
    public function getByAppImgIdAndIsMain($appImageId)
    {
        return self::join('ch_application_image', function($join) use ($appImageId) {
            $join->on('ch_application_image_active.ch_application_image_id', '=', 'ch_application_image.id')
                ->where('ch_application_image_id', '=', $appImageId)
                ->where('ch_application_image_active.is_main', '=', 1);
        })->first();
    }

    /**
     * Application Image Id指定取得
     * @param integer $chAppId
     * @return array
     */
    public function getRecommendImageListByChAppId($chAppId)
    {
        return self::join('ch_application_image', function($join) use ($chAppId) {
            $join->on('ch_application_image_active.ch_application_image_id', '=', 'ch_application_image.id')
                ->where('ch_application_image.ch_app_id', '=', $chAppId)
                ->where('ch_application_image_active.is_main', '=', false);
        })->get();
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param array $param
     * @return boolean
     */
    public function edit($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::updateOrCreate(
            ['ch_application_image_id' => $param['ch_application_image_id']],
            $param
        );
    }

    /**
     * 削除
     * @param  integer $appImageId
     * @return boolean
     */
    public function del($appImageId)
    {
        if (empty($appImageId)) {
            return false;
        }
        return self::where('ch_application_image_id', $appImageId)->delete();
    }
}
