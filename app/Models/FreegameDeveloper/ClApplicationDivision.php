<?php

namespace App\Models\FreegameDeveloper;

class ClApplicationDivision extends FreegameDeveloper
{

    protected $table    = 'cl_application_division';
    public $timestamps  = false;

    /**
     * Get One by cl_app_id
     * @return object
     */
    public function getOneByClAppId($clAppId)
    {
        return self::select('cl_app_id', 'game_id')
               ->where('cl_app_id', $clAppId)->first();
    }
}
