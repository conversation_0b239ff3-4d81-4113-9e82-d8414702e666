<?php

namespace App\Models\FreegameDeveloper;

use DB;

class ApplicationImageTimer extends FreegameDeveloper
{
    protected $table = 'application_image_timer';

    protected $fillable = ['id', 'start_time', 'end_time'];

    protected $dates = ['start_time', 'end_time', 'created_at', 'updated_at'];

    public $incrementing = false;

//*********************************************************************************************************************

    /**
     * ID指定取得
     * @param integer $id
     * @return bool|ApplicationImageTimer
     */
    public function getById($id)
    {
        if (empty($id)) {
            return false;
        }

        return self::where('id', $id)->where('deleted', 0)->first();
    }

    /**
     * app_id指定取得
     * @param integer $appId
     * @return array|bool
     */
    public function getListByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }

        return self::select(DB::raw('t.id, t.start_time, t.end_time'))
            ->distinct('t.id')
            ->from('application_image AS i')
            ->join('application_image_timer_ref AS r', 'r.application_image_id', '=', 'i.id')
            ->join('application_image_timer as t', 't.id', '=', 'r.timer_id')
            ->where('i.app_id', $appId)
            ->where('t.deleted', 0)->get();
    }

    /**
     * タイマーとイメージ情報をとってくる. イメージ情報から取るのでイメージが複数になる可能性がある
     * @param integer $appId
     * @return boolean|array
     */
    public function getListAndImageDataByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }

        return self::select(DB::raw('t.id, t.start_time, t.end_time, i.id AS image_id, i.image_url, i.image_type, i.image_size'))
            ->from('application_image AS i')
            ->join('application_image_timer_ref AS r', 'r.application_image_id', '=', 'i.id')
            ->join('application_image_timer AS t', 't.id', '=', 'r.timer_id')
            ->where('i.app_id', $appId)
            ->where('t.deleted', 0)
            ->orderBy('i.image_size')
            ->get();
    }

    /**
     * id指定削除
     * @param string $id
     * @return bool
     */
    public function deleteById($id)
    {
        if (empty($id)) {
            return false;
        }

        return self::where('id', $id)->update(['deleted' => 1]);
    }

    /**
     * アプリidで現在使われているタイマーを探す
     * @param integer $appId
     * @return bool|array
     */
    public function getActiveTimerByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }

        return self::select(DB::raw('t.id, t.start_time, t.end_time'))
            ->from('application_image AS i')
            ->join('application_image_timer_ref AS r', 'r.application_image_id', '=', 'i.id')
            ->join('application_image_timer AS t', 't.id', '=', 'r.timer_id')
            ->where('i.app_id', $appId)
            ->where('t.deleted', 0)
            ->where('t.start_time', '<=', DB::raw('NOW()'))
            ->where('t.end_time', '>', DB::raw('NOW()'))
            ->first();
    }

    /**
     * アプリidで現在使われているタイマーのイメージリストを探す
     * @param integer $appId
     * @return bool|array
     */
    public function getActiveTimerImageListByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }

        return self::select(DB::raw('i.id AS image_id, i.image_url, i.image_type, i.image_size'))
            ->from('application_image AS i')
            ->join('application_image_timer_ref AS r', 'r.application_image_id', '=', 'i.id')
            ->join('application_image_timer AS t', 't.id', '=', 'r.timer_id')
            ->where('i.app_id', $appId)
            ->where('t.deleted', 0)
            ->where('t.start_time', '<=', DB::raw('NOW()'))
            ->where('t.end_time', '>', DB::raw('NOW()'))
            ->get();
    }

    /**
     * 追加メソッド
     * @param string $id
     * @param $startTime
     * @param $endTime
     * @return bool
     */
    public function add($id, $startTime, $endTime)
    {
        if (empty($id) || empty($startTime) || empty($endTime)) {
            return false;
        }

        self::create(['id' => $id, 'start_time' => $startTime, 'end_time' => $endTime]);
    }

    /**
     * タイマー時間をupdateする
     * @param $id
     * @param $startTime
     * @param $endTime
     * @return bool
     */
    public function updateStartAndEndTime($id, $startTime, $endTime)
    {
        if (empty($id) || empty($startTime) || empty($endTime)) {
            return false;
        }

        return self::where('id', $id)->update(['start_time' => $startTime, 'end_time' => $endTime]);
    }
}