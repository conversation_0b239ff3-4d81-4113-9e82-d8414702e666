<?php
namespace App\Models\FreegameDeveloper;

/**
 * ゲーム課金ログ・DMM課金ログ差分値保存テーブル
 */
class MonthlyServiceMismatchedPrice extends FreegameDeveloper
{
    protected $table = 'monthly_service_mismatched_price';

    /**
     * get list
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (!empty($condition['monthly_service_id'])) {
            $query = $query->where('monthly_service_id', '=', $condition['monthly_service_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('target_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('target_date', '<=', $condition['end']);
        }

        return $query->get();
    }
}
