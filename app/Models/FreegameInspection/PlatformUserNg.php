<?php
namespace App\Models\FreegameInspection;

/**
 * プラットフォームユーザNG
 */
class PlatformUserNg extends FreegameInspection
{
    protected $table = 'platform_user_ng';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['category_id'])) {
            $query = $query->where('category_id', $params['category_id']);
        }
        if (!empty($params['category'])) {
            $query = $query->where('category', $params['category']);
        }

        return $query->get();
    }
}
