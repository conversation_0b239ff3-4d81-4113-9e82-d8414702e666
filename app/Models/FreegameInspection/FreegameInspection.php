<?php

namespace App\Models\FreegameInspection;

use App\Models\CustomModel;

class FreegameInspection extends CustomModel
{
    protected $connection = 'freegame_inspection_db';

    /**
     * 最もレコードの少ない監査テーブルの番号を取得する
     * @return integer
     */
    public function getInspectionTableNumber()
    {
        $data   = \DB::connection($this->connection)
            ->select("SHOW TABLE STATUS FROM freegame_inspection like 'application_user_text%'");

        foreach ($data as $val) {
            $temp[$val->Name] = $val->Rows;
        }
        $name   = array_keys($temp, min($temp));
        $number = preg_replace('/[^0-9]/', '', $name[0]);

        return $number;
    }

    /**
     * MySQLのujisに変換できない文字を「?」に置換した文字列を返します。
     * 
     * MySQL8.0化により、freegame_inspection DBにujisで扱えない文字を投入するとクエリエラーになるので
     * 必要な箇所で本関数を使ってsanitizeする
     *
     * @param string $string 処理対象のUTF-8文字列
     * @return string 変換後の安全なUTF-8文字列
     */
    public function sanitizeForMysqlUjis($string)
    {
        // 〜などの文字がエラーとなってしまうため変換しない
        // $string = $this->sanitizeForUjis($string);

        // PHPとMySQLで変換に差異があると思われるものは個別に変換する
        $string = str_replace('＼', '?', $string);

        return $string;
    }

    /**
     * 文字列をujis(SJIS-win)に変換できない文字を「?」に置換して、安全なUTF-8文字列を返します。
     * 
     * @param string $string 処理対象のUTF-8文字列
     * @return string 変換後の安全なUTF-8文字列
     */
    public function sanitizeForUjis($string)
    {
        // 1. 現在の代替文字設定を記憶しておく
        $originalSubstituteChar = mb_substitute_character();

        try {
            // 2. 代替文字を強制的に「?」に設定する
            // '?' のUnicodeコードポイントは 0x3F
            mb_substitute_character(0x3F);

            $fromEncoding = 'UTF-8';
            $toEncoding = 'SJIS-win';

            // 3. 文字コード変換を実行
            $sjisString = mb_convert_encoding($string, $toEncoding, $fromEncoding);
            $safeUtf8String = mb_convert_encoding($sjisString, $fromEncoding, $toEncoding);

            return $safeUtf8String;

        } finally {
            // 4. 処理が終わったら、必ず代替文字設定を元に戻す
            mb_substitute_character($originalSubstituteChar);
        }
    }
}
