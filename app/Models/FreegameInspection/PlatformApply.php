<?php
namespace App\Models\FreegameInspection;

/**
 * プラットフォーム許可
 */
class PlatformApply extends FreegameInspection
{
    protected $table = 'platform_apply';

    public $timestamps = false;

    /**
     * insert
     *
     * @param String $month
     * @param array $data
     *
     * @return object
     */
    public function add($month, $data)
    {
        self::setTable("platform_apply_". $month);

        if (!empty($data['data'])) {
            $data['data'] = $this->sanitizeForMysqlUjis($data['data']);
        }

        return self::insert($data);
    }
}
