<?php
namespace App\Models\FreegameInspection;

/**
 * プラットフォームログ
 */
class PlatformImageLog extends FreegameInspection
{
    protected $table = 'platform_image_log';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  string $month
     * @param  array  $params
     *
     * @return object
     */
    public function getList($month, $params)
    {
        $query = self::select()->from('platform_image_log_'.$month);

        if (!empty($params['category_id'])) {
            $query = $query->where('category_id', $params['category_id']);
        }

        return $query->get();
    }
}
