<?php
namespace App\Models\FreegameInspection;

/**
 * プラットフォームログ
 */
class PlatformLog extends FreegameInspection
{
    protected $table = 'platform_log';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  string $month
     * @param  array  $params
     *
     * @return object
     */
    public function getList($month, $params)
    {
        $query = self::select()->from('platform_log_'.$month);

        if (!empty($params['category_id'])) {
            $query = $query->where('category_id', $params['category_id']);
        }
        if (!empty($params['category'])) {
            $query = $query->where('category', $params['category']);
        }

        return $query->get();
    }
}
