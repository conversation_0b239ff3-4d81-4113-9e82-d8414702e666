<?php
namespace App\Models\FreegameInspection;

/**
 * 監視中コメント画像
 */
class PlatformImageUnderProcessing extends FreegameInspection
{
    protected $table = 'platform_image_under_processing';
    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['category_id'])) {
            $query = $query->where('category_id', $params['category_id']);
        }

        return $query->get();
    }
}
