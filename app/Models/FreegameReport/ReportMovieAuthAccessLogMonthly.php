<?php
namespace App\Models\FreegameReport;

use DB;

/**
 * 月別動画視聴状況レポート
 */
class ReportMovieAuthAccessLogMonthly extends FreegameReport
{

    protected $table = 'report_movie_auth_access_log_monthly';

    protected $dates = [
        'report_month'
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['select'])) {
            $query = $query->select($condition['select']);
        }
        if (! empty($condition['begin'])) {
            $query = $query->where('report_month', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $query = $query->where('report_month', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['movie_id'])) {
            $query = $query->where('movie_id', $condition['device']);
        }
        return $query->orderBy('app_id', 'asc')
            ->orderBy('movie_id', 'asc')
            ->orderBy('report_month', 'asc')
            ->get();
    }

    public function getMonthlyList($condition = [])
    {
        $subQuery = self::query();
        $subQuery = $subQuery->selectRaw(implode(', ', [
            "DATE_FORMAT(report_month, '%Y-%m-01') AS report_month",
            'app_id',
            'movie_id',
            'auth_count',
            'unique_user'
        ]));
        if (! empty($condition['begin'])) {
            $subQuery = $subQuery->where('report_month', '>=', $condition['begin']);
        }
        if (! empty($condition['end'])) {
            $subQuery = $subQuery->where('report_month', '<=', $condition['end']);
        }
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $subQuery = $subQuery->whereIn('app_id', $condition['app_id']);
            } else {
                $subQuery = $subQuery->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['movie_id'])) {
            $subQuery = $subQuery->where('movie_id', $condition['device']);
        }
        $query = self::from(DB::raw("({$subQuery->toSql()}) as T"))->mergeBindings($subQuery->getQuery());
        $select = [
            'report_month' => 'report_month',
            'app_id' => 'app_id',
            'movie_id' => 'movie_id',
            'auth_count' => 'SUM(auth_count) AS auth_count',
            'unique_user' => 'SUM(unique_user) AS unique_user'
        ];
        if (! empty($condition['select'])) {
            $select = array_only($select, $condition['select']);
            $query = $query->selectRaw(implode(', ', $select));
        } else {
            $query = $query->selectRaw(implode(', ', $select));
        }
        return $query->groupBy('report_month')
            ->groupBy('app_id')
            ->groupBy('movie_id')
            ->orderBy('app_id', 'asc')
            ->orderBy('movie_id', 'asc')
            ->orderBy('report_month', 'asc')
            ->get();
    }
}
