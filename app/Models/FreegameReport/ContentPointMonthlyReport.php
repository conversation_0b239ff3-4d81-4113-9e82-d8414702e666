<?php
namespace App\Models\FreegameReport;

class ContentPointMonthlyReport extends FreegameReport
{
    protected $table   = 'content_point_monthly_report';

    protected $guarded = ['id'];

    public $timestamps = false;

    const PF_CONTENT_UNIT_APP_ID = '0';

//*********************************************************************************************************************
    /**
     * ID指定取得
     * @param  integer $id
     * @return array
     */
    public function getById($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->first();
    }

    /**
     * ID・デベロッパ指定取得
     * @param  integer $id
     * @param  integer $devId
     * @return array
     */
    public function getByIdAndDeveloper($id, $devId)
    {
        if (empty($id) || empty($devId)) {
            return false;
        }
        return self::where('id', $id)->where('developer_id', $devId)->first();
    }

    /**
     * 一覧取得
     * @param  array  $where
     * @param  string $order
     * @return array
     */
    public function getList($where = [], $order = 'id ASC')
    {
        $perPage = isset($where['perPage']) ? $where['perPage'] : null;
        $query   = $this->getWhere($where);

        if ($perPage) {
            return $query->orderByRaw('id DESC')->paginate($perPage);
        } else {
            return $query->orderByRaw($order)->get();
        }
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::create($param);
    }

    /**
     * 更新
     * @param  integer $id
     * @param  array   $param
     * @return boolean
     */
    public function edit($id, $param)
    {
        if (empty($id) || empty($param)) {
            return false;
        }
        return self::where('id', $id)->update($param);
    }

    /**
     * 削除
     * @param  integer $id
     * @return boolean
     */
    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

//*********************************************************************************************************************
    /**
     * 検索条件取得
     * @param  array $param
     * @return object
     */
    private function getWhere($param = [])
    {
        $query           = self::from($this->table);

        $reportId        = isset($param['id'])                ? $param['id']                : null;
        $devId           = isset($param['developer_id'])      ? $param['developer_id']      : null;
        $ownerId         = isset($param['owner_id'])          ? $param['owner_id']          : null;
        $makerId         = isset($param['maker_id'])          ? $param['maker_id']          : null;
        $productId       = isset($param['product_id'])        ? $param['product_id']        : null;

        $site            = isset($param['site'])              ? $param['site']              : null;
        $device          = isset($param['device'])            ? $param['device']            : null;
        $ownerIds        = isset($param['owner_ids'])         ? $param['owner_ids']         : null;

        $reportDateBegin = isset($param['report_date_begin']) ? $param['report_date_begin'] : null;
        $reportDateEnd   = isset($param['report_date_end'])   ? $param['report_date_end']   : null;

        if (! empty($reportId)) {
            $query->where('id', $reportId);
        }

        if (! empty($devId)) {
            $query->where('developer_id', $devId);
        }

        if (!empty($ownerId) || $ownerId === self::PF_CONTENT_UNIT_APP_ID) {
            $query->where('owner_id', $ownerId);
        }

        if (! empty($makerId)) {
            $query->where('maker_id', $makerId);
        }

        if (! empty($productId)) {
            $query->where('product_id', $productId);
        }


        if (! empty($site)) {
            $query->where('site', $site);
        }

        if (! empty($device)) {
            $query->whereIn('device', $device);
        }

        if (! empty($ownerIds)) {
            $query->whereIn('owner_id', $ownerIds);
        }


        if (! empty($reportDateBegin)) {
            $query->where('report_date', '>=', date('Y-m-d 00:00:00', strtotime($reportDateBegin)));
        }

        if (! empty($reportDateEnd)) {
            $query->where('report_date', '<=', date('Y-m-d 23:59:59', strtotime($reportDateEnd)));
        }
        return $query;
    }
}
