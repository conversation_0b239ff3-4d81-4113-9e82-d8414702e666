<?php

namespace App\Models\NetgameCstool;

class TestAccount extends NetgameCstool
{
    protected $table = 'test_account';

    public $timestamps = false;

    /**
     * Get record by test_account_id & delete_flg
     * @param integer $id
     * @return boolean
     */
    public function getOneByIdAndDeleteFlag($id)
    {
        return self::where('test_account_id', $id)->where('delete_flg', 0)->first();
    }
}
