<?php

namespace App\Models;

use DB;
use PDO;
use Closure;
use Illuminate\Database\Eloquent\Model;
use App\Models\Accessory\AppQueryScope;

abstract class CustomModel extends Model
{
    use AppQueryScope;

    /**
     * Call transaction method
     *
     * 上位にある BaseCollection, QueryBuilder, Model に Transaction 関係のものが無いので
     * Illuminate\Database\Eloquent\Model を参考に getConnection() で接続情報を得て利用します
     *
     * @param  string  $method
     * @return mixed
    */
    protected static function transaction(Closure $callback)
    {
        $instance = new static;
        $result = $instance->getConnection()->transaction($callback);
        return $result;
    }

    /**
     * Call beginTransaction method
     *
     * 上位にある BaseCollection, QueryBuilder, Model に Transaction 関係のものが無いので
     * Illuminate\Database\Eloquent\Model を参考に getConnection() で接続情報を得て利用します
     *
     * @return void
    */
    protected static function beginTransaction()
    {
        $instance = new static;
        $instance->getConnection()->beginTransaction();
    }

    /**
     * Call commit method
     *
     * 上位にある BaseCollection, QueryBuilder, Model に Transaction 関係のものが無いので
     * Illuminate\Database\Eloquent\Model を参考に getConnection() で接続情報を得て利用します
     *
     * @return void
    */
    protected static function commit()
    {
        $instance = new static;
        $instance->getConnection()->commit();
    }

    /**
     * Call rollback method
     *
     * 上位にある BaseCollection, QueryBuilder, Model に Transaction 関係のものが無いので
     * Illuminate\Database\Eloquent\Model を参考に getConnection() で接続情報を得て利用します
     *
     * @return void
    */
    protected static function rollback()
    {
        $instance = new static;
        $instance->getConnection()->rollback();
    }

    /**
     * Call queryAssoc method
     *
     * 実行結果を配列・連想配列に格納する QueryBuilder を返却します
     */
    protected function queryAssoc()
    {
        $query = DB::connection($this->connection)->table($this->table);
        $query->getConnection()->setFetchMode(PDO::FETCH_ASSOC);
        return $query;
    }
}
