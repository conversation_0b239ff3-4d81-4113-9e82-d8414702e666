<?php
namespace App\Models\Freegame;

/**
 * ユーザープロフィール画像
 */
class UserImage extends Freegame
{
    protected $table = 'user_image';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * get list
     *
     * @param  array   $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['user_id'])) {
            $query = $query->where('user_id', $params['user_id']);
        }
        if (!empty($params['number'])) {
            $query = $query->where('number', $params['number']);
        }
        if (!empty($params['priority'])) {
            $query = $query->where('priority', $params['priority']);
        }

        return $query->get();
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insertGetId($data);
    }

    /**
     * update
     *
     * @param  array   $params
     * @param  array   $data
     *
     * @return object
     */
    public function editByConditions($params, $data)
    {
        if (!empty($params['id'])) {
            if (isset($query)) {
                $query = $query->where('id', $params['id']);
            } else {
                $query = self::where('id', $params['id']);
            }
        }
        if (!empty($params['user_id'])) {
            if (isset($query)) {
                $query = $query->where('user_id', $params['user_id']);
            } else {
                $query = self::where('user_id', $params['user_id']);
            }
        }

        return $query->update($data);
    }

    /**
     * delete
     *
     * @param  integer $id
     *
     * @return object
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
