<?php


namespace App\Models\Freegame;

/**
 * アプリケーションのPWA設定用
 */
class ApplicationPwaSettings extends Freegame
{
    protected $table = 'application_pwa_settings';

    protected $primaryKey = 'app_id';

    protected $fillable = [
        'app_id',
        'manifest_background_color',
    ];

    /**
     * app_idからデータを取得
     *
     * @param string|int $id
     * @return array|null
     */
    public function getOneByAppId($app_id)
    {
        return self::where('app_id', $app_id)->first();
    }


    /**
     * なければレコード作る、あれば背景色を変える
     * @param string|int $appId アプリのid
     * @param string $backgroundColor '#ffffff'#と6桁hex文字
     * @return boolean
     */
    public function edit($appId, $backgroundColor)
    {
        if (empty($appId) || empty($backgroundColor)) {
            return false;
        }

        $attr = [
            'app_id' => $appId,
            'manifest_background_color' => $backgroundColor,
        ];

        return self::updateOrCreate(['app_id' => $appId], $attr);
    }
}