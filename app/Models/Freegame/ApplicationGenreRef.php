<?php
namespace App\Models\Freegame;

/**
 * アプリケーションジャンル関連テーブル
 */
class ApplicationGenreRef extends Freegame
{

    protected $table = 'application_genre_ref';

    protected $primaryKey = [
        'kind',
        'app_id',
        'main_genre_id'
    ];

    protected $fillable = [
        'kind',
        'app_id',
        'main_genre_id',
        'sub_genre_id'
    ];

    public $incrementing = false;

    public $timestamps = false;

    /**
     * ジャンル名取得
     * @param  string $appId
     * @return object
     */
    public function getApplicationGenreName($appId)
    {
        return self::select('m.name AS main_name', 's.name AS sub_name')
            ->from('application_genre_ref AS g')
            ->join('application_main_genre AS m', 'g.main_genre_id', '=', 'm.id')
            ->leftjoin('application_sub_genre AS s', 'g.sub_genre_id', '=', 's.id')
            ->where('g.app_id', $appId)
            ->first();
    }

    public function getSocialById($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('kind', 'social')->where('app_id', $app_id)
            ->orderBy('stamp', 'desc')
            ->first();
    }

    public function addSocial($app_id, $data)
    {
        if (empty($app_id) || empty($data)) {
            return false;
        }
        $attr = $data;
        $attr['kind'] = 'social';
        $attr['app_id'] = $app_id;
        return self::create($attr);
    }

    public function delSocial($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('kind', 'social')->where('app_id', $app_id)->delete();
    }

    public function getChannelById($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('kind', 'channel')->where('app_id', $app_id)
            ->orderBy('stamp', 'desc')
            ->first();
    }

    public function addChannel($app_id, $data)
    {
        if (empty($app_id) || empty($data)) {
            return false;
        }
        $attr = $data;
        $attr['kind'] = 'channel';
        $attr['app_id'] = $app_id;
        return self::create($attr);
    }

    public function delChannel($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('kind', 'channel')->where('app_id', $app_id)->delete();
    }
}
