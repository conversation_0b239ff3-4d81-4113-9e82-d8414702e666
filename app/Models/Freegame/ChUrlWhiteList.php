<?php
namespace App\Models\Freegame;

/**
 * チャネリングアプリケーションテーブル
 */
class ChUrlWhiteList extends Freegame
{
    protected $table = 'ch_url_whitelist';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getList($params)
    {
        return self::select()->where('ch_app_id', $params['ch_app_id'])->orderBy('id')->get();
    }

    /**
     * get data exist by ch_app_id, type and url
     *
     * @param  array  $params
     * @param  string  $type
     * @param  integer  $chAppId
     * @param  integer  $whiteListId
     *
     * @return object
     */
    public function getDataExist($url, $type, $chAppId, $whiteListId)
    {
        $query = self::select()
            ->where('ch_app_id', $chAppId)
            ->where('type', $type)
            ->where('url', $url);

        if ($whiteListId) {
            $query->where('id', '<>', $whiteListId);
        }

        return $query->first();
    }

    /**
     * edit
     *
     * @param  array  $params
     *
     * @return boolean
     */
    public function edit($params)
    {
        if (empty($params['id']) || empty($params['ch_app_id'])) {
            return false;
        }
        return self::where('id', $params['id'])
            ->where('ch_app_id', $params['ch_app_id'])
            ->update($params);
    }


    /**
     * delete
     * @param  array $params
     * @return boolean
     */
    public function del($params)
    {
        if (empty($params['id'])) {
            return false;
        }
        return self::where('id', $params['id'])
            ->where('ch_app_id', $params['ch_app_id'])
            ->delete();
    }
}
