<?php

namespace App\Models\Freegame;

/**
 * コンテントオーナーテーブル
 *
 */
class ContentOwner extends Freegame
{
    protected $table = 'content_owner';

     protected $guarded = [
        'id'
    ];

    /**
    * Get one by id
    * @param integer $id
    * @return ContentOwner
    */
    public function getOneById($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * Get All
     * @return Collection
     */
    public function getList($condition = [], $order = 'id')
    {
        $query = self::where($condition);
        return $query->orderByRaw($order)->get();
    }

    /**
    * Get one by unit_application_id
    * @param integer $id
    * @return ContentOwner
    */
    public function getOneByUnitAppId($id)
    {
        return self::where('unit_application_id', $id)->first();
    }


}