<?php
namespace App\Models\Freegame;

/**
 * アプリケーションメインジャンルテーブル
 */
class ApplicationMainGenre extends Freegame
{

    protected $table = 'application_main_genre';

    public $timestamps = false;

    public function getNameList()
    {
        return self::select([
            'id',
            'name'
        ])
        ->where('is_disabled', '=', 0)
        ->orderBy('sort_order')
        ->get();
    }
}
