<?php

namespace App\Models\Freegame;

/**
 * アプリケーションメタデータテーブル
 * memo:
 *  dev管理サイト: SEO設定 > meta title設定で titleカラムにデータを追加しているため注意が必要
 */
class ApplicationMetaData extends Freegame
{
    protected $table = 'application_meta_data';

    public $timestamps = false;

    /**
     * @param $appId
     * @return object
     */
    public function getOne($appId)
    {
        return self::where('app_id', $appId)->first();
    }

    /**
     * @param $appId
     * @param $metaDescription
     * @return mixed
     */
    public function upsert($appId, $metaDescription)
    {
        if (self::where('app_id', $appId)->exists()) {
            return self::where('app_id', $appId)->update(['description' => $metaDescription]);
        }

        return self::insert([
            'app_id' => $appId,
            'description' => $metaDescription,
        ]);
    }
}
