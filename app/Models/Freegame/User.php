<?php
namespace App\Models\Freegame;

/**
 * ユーザー
 */
class User extends Freegame
{
    protected $table = 'user';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * get list by ids
     *
     * @param  array  $ids
     *
     * @return object
     */
    public function getListByIds($ids)
    {
        return self::select()->whereIn('id', $ids)->get();
    }

    /**
     * insert
     *
     * @param  array   $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * update
     *
     * @param  integer $id
     * @param  array   $data
     *
     * @return object
     */
    public function edit($id, $data)
    {
        return self::where('id', $id)->update($data);
    }
}
