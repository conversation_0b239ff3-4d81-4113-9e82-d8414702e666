<?php
namespace App\Models\Freegame;

use DB;

/**
 * 月額サービス注文履歴
 */
class MonthlyPaymentOrderLog extends Freegame
{
    protected $table = 'monthly_payment_order_log';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * 指定期間の最大、あるいは最小のidを取得する
     * @param array $condition
     * @param int id
     */
    public function getLimitId($isMax, $condition = [])
    {
        $query = self::select('mpol.id')
            ->from(DB::raw($this->table . ' AS mpol FORCE INDEX(settlement_datetime)'))
            ->join('monthly_service AS ms', 'ms.id', '=', 'mpol.monthly_service_id')
            ->where('mpol.status', 'enabled')
            ->whereBetween('mpol.settlement_datetime', [$condition['begin'], $condition['end']]);

        if (!empty($condition['monthly_service_id'])) {
            $query->where('mpol.monthly_service_id', $condition['monthly_service_id']);
        }

        if (!empty($condition['app_id'])) {
            $query->where('ms.app_id', $condition['app_id']);
        }

        if ($isMax) {
            $query = $query->orderBy('mpol.id', 'desc');
        }
        return $query->value('id');
    }

    /**
     * 指定したidをキーにしてリストを取得
     * @param array $condition
     * @param object
     */
    public function getMatchPaymentList($condition = [])
    {
        // 初回
        $queryFirst = self::select([
            'mpol.payment_id AS payment_id',
            'mpol.price AS price',
            'mpol.tax AS tax',
            'mpol.monthly_service_id AS monthly_service_id',
            'mpcl.regist_device AS regist_device',
            'mpcl.pay_type AS pay_type',
        ])
        ->from($this->table . ' AS mpol')
        ->join('monthly_payment_cashier_log AS mpcl', 'mpcl.cashier_id', '=', 'mpol.cashier_id')
        ->join('monthly_service AS ms', 'ms.id', '=', 'mpol.monthly_service_id')
        ->where('mpol.status', 'enabled')
        ->whereBetween('mpol.id', [$condition['fromId'], $condition['toId'] ])
        ->where('mpol.member_id', 'NOT LIKE', 'D500%');
    
        if (!empty($condition['monthly_service_id'])) {
            $queryFirst->where('mpol.monthly_service_id', $condition['monthly_service_id']);
        }

        if (!empty($condition['app_id'])) {
            $queryFirst->where('ms.app_id', $condition['app_id']);
        }

        // 継続
        $queryContinue = self::select([
            'mpol.payment_id AS payment_id',
            'mpol.price AS price',
            'mpol.tax AS tax',
            'mpol.monthly_service_id AS monthly_service_id',
            'msu.regist_device AS regist_device',
            'msu.pay_type AS pay_type',
        ])
        ->from($this->table . ' AS mpol')
        ->join('monthly_service_user AS msu', function ($query) {
            $query
                ->on('mpol.monthly_service_id', '=', 'msu.monthly_service_id')
                ->on('mpol.member_id', '=', 'msu.member_id')
                ->on('msu.expire_datetime', '>', 'mpol.settlement_datetime')
                ->on('msu.begin_datetime', '<=', 'mpol.settlement_datetime');
        })
        ->join('monthly_service AS ms', 'ms.id', '=', 'mpol.monthly_service_id')
        ->whereNull('mpol.cashier_id')
        ->where('mpol.status', 'enabled')
        ->whereBetween('mpol.id', [$condition['fromId'], $condition['toId'] ])
        ->where('mpol.member_id', 'NOT LIKE', 'D500%');

        if (!empty($condition['monthly_service_id'])) {
            $queryContinue->where('mpol.monthly_service_id', $condition['monthly_service_id']);
        }

        if (!empty($condition['app_id'])) {
            $queryContinue->where('ms.app_id', $condition['app_id']);
        }

        // 結合
        $query = $queryFirst->unionAll($queryContinue)
        ->orderBy('payment_id');

        return $query->get();
    }
}
