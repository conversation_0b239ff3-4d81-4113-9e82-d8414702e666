<?php

namespace App\Models\Freegame;

/**
 * アプリケーションデバイステーブル
 */
class ApplicationDevice extends Freegame
{

    protected $table = 'application_device';

    protected $guarded = [
        'app_id',
        'device'
    ];

    public $timestamps = false;

    public function getOne($app_id, $device)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->where('device', $device)->first();
    }

    public function getListById($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->get();
    }

    public function edit($app_id, $device, $data)
    {
        if (empty($app_id) || empty($device) || empty($data)) {
            return false;
        }
        return self::where('app_id', $app_id)->where('device', $device)->update($data);
    }

    /**
     * 取得：アプリケーションID＆デバイス指定
     * @param  integer $appId
     * @param  array   $device
     * @return \App\Models\Freegame\ApplicationDevice
     */
    public function getByApplicationIdAndDevice($appId, $device)
    {
        return self::where('app_id', $appId)->whereIn('device', $device)->get();
    }
}
