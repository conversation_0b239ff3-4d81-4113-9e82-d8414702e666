<?php
namespace App\Models\Freegame;

/**
 * チャネリングアプリケーションテーブル
 */
class ChApplication extends Freegame
{
    protected $table = 'ch_application';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * Get data by parameters
     *
     * @param  array $params
     *
     * @return object
     */
    public function getOneByParameters($params)
    {
        $query = self::select()->where('id', $params['ch_app_id']);

        if (!empty($params['developer_id'])) {
            $query = $query->where(function ($subConditions) use ($params) {
                $subConditions = $subConditions->where('developer_id', $params['developer_id']);

                if (!empty($params['ch_app_id_list'])) {
                    $subConditions = $subConditions->orWhereIn('id', $params['ch_app_id_list']);
                }
            });
        }

        return $query->first();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['developer_id'])) {
            $query = $query->where('developer_id', $params['developer_id']);

            if (!empty($params['ch_app_id_list'])) {
                $query = $query->orWhereIn('id', $params['ch_app_id_list']);
            }
        }

        return $query->get();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getListOrderTitle($params)
    {
        $query = self::select();

        if (!empty($params['developer_id'])) {
            $query = $query->where('developer_id', $params['developer_id']);

            if (!empty($params['ch_app_id_list'])) {
                $query = $query->orWhereIn('id', $params['ch_app_id_list']);
            }
        }
        elseif (!empty($params['ch_app_id_list'])) {
            $query = $query->whereIn('id', $params['ch_app_id_list']);
        }
        return $query->orderByRaw('BINARY title')->get();
    }

    /**
     * Get list by developer_id
     *
     * @param integer $developerId
     *
     * @return array
     */
    public function getListTitleSortByBinaryTitle($developerId)
    {
        return self::where('developer_id', $developerId)->orderByRaw('BINARY title')->lists('title', 'id');
    }

    /**
     * Get list title by developer_id and ch_app_id list
     *
     * @param integer $developerId
     * @param array $chApplicationIdList
     *
     * @return array
     */
    public function getListTitleByDeveloperIdAndChAppIdList($developerId, $chApplicationIdList)
    {
        $query = self::where('developer_id', $developerId);

        if (!empty($chApplicationIdList)) {
            $query = $query->orWhereIn('id', $chApplicationIdList);
        }

        return $query->orderByRaw('BINARY title')->lists('title', 'id');
    }

    public function edit($id, $data)
    {
        if (empty($id) || empty($data)) {
            return false;
        }
        return self::where('id', $id)->update($data);
    }

    public function getApplicationTitleList($condition = [])
    {
        $query = self::select([
            'id',
            'title'
        ]);
        if (! empty($condition['id'])) {
            $query = $query->whereIn('id', $condition['id']);
        }
        if (! empty($condition['developer_id'])) {
            $query = $query->where('developer_id', $condition['developer_id']);
        }

        return $query->orderBy('title', 'asc')->get();
    }
}
