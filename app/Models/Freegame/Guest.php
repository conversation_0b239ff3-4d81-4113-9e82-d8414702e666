<?php

namespace App\Models\Freegame;

use Carbon\Carbon;

/**
 * ゲスト
 */
class Guest extends Freegame
{
    protected $table = 'guest';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * hashed_idからゲストユーザーIDを取得
     *
     * @param string $hashedId cookieからのハッシュされたID
     * @return int|null guestUserId
     */
    public function getIdByHashedId($hashedId)
    {
        return self::select('id')
            ->where('hashed_id', $hashedId)
            ->value('id');
    }

    /**
     * idからデータを取得
     *
     * @param string|int $id
     * @return Guest|null
     */
    public function getOneById($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 取得データがアップグレード済みか判定
     *
     * @return boolean
     */
    public function isUpgraded()
    {
        // 取得データが存在しない場合はfalse返却
        if (!$this->exists) {
            return false;
        }

        // アップグレード日時をCarbonに変換
        $dateUpgrade = Carbon::parse($this->attributes['upgrade_date']);
        $dateDefault = Carbon::parse('0000-00-00 00:00:00');

        // アップグレード日時の値がデフォルトより大きい(更新されてる)場合にtrueを返却
        return ($dateUpgrade->gt($dateDefault));
    }
}
