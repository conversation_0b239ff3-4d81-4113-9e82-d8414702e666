<?php
namespace App\Models\Freegame;

/**
 * チャネリング月ポイント注文エントリー
 */
class ChMonthlyPointOrderEntry extends Freegame
{
    protected $table = 'ch_monthly_point_order_entry';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get range id
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getRangeId($condition = [])
    {
        $query = self::selectRaw('MIN(id) AS min_id, MAX(id) AS max_id');

        if (!empty($condition['ch_app_id'])) {
            $query = $query->where('ch_app_id', $condition['ch_app_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }

        return $query->first();
    }

    /**
     * get list with point_order_item
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getListWithPointOrderItem($condition = [])
    {
        if (! empty($condition['assoc'])) {
            $query = self::queryAssoc();
        } else {
            $query = self::query();
        }

        $query = $query->select(
            'poe.payment_id',
            'poi.unit_price',
            'poi.quantity'
        )
        ->from('ch_monthly_point_order_entry AS poe')
        ->join('ch_monthly_point_order_item AS poi', 'poe.payment_id', '=', 'poi.payment_id')
        ->where('poe.member_id', 'NOT LIKE', 'D500%%')
        ->where('poe.status', 'purchase')
        ;

        if (!empty($condition['min_point_order_entry_id'])) {
            $query = $query->where('poe.id', '>=', $condition['min_point_order_entry_id']);
        }
        if (!empty($condition['max_point_order_entry_id'])) {
            $query = $query->where('poe.id', '<=', $condition['max_point_order_entry_id']);
        }
        if (!empty($condition['ch_app_id'])) {
            $query = $query->where('poe.ch_app_id', '=', $condition['ch_app_id']);
        }

        $query = $query->orderBy('poe.payment_id', 'asc');

        if (! empty($condition['chunk'])) {
            return $query->chunk(20000, $condition['chunk']);
        } else {
            return $query->get();
        }
    }
}
