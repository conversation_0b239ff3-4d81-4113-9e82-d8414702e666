<?php
namespace App\Models\Freegame;

/**
 * ソーシャルゲーム用通信プロトコル管理テーブル
 */
class ApplicationProtocol extends Freegame
{

    protected $table = 'application_protocol';

    protected $primaryKey = 'app_id';

    protected $fillable = [
        'app_id',
        'is_ssl',
    ];

    public $timestamps = false;

    public function getActiveById($appId)
    {
        if (empty($appId)) {
            return false;
        }

        return self::where('app_id', $appId)->first();
    }

    public function edit($appId, $isSsl)
    {
        if (empty($appId) || is_null($isSsl)) {
            return false;
        }
        $attr = [
            'app_id' => $appId,
            'is_ssl' => (int)$isSsl,
        ];

        return self::updateOrCreate(['app_id' => $appId], $attr);
    }
}
