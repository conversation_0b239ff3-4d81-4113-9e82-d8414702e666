<?php
namespace App\Models\Freegame;

/**
 * アプリケーションTwitterテーブル
 */
class ApplicationTwitter extends Freegame
{

    protected $table = 'application_twitter';

    protected $primaryKey = [
        'app_id'
    ];

    protected $fillable = [
        'app_id',
        'twitter_account',
        'widget_id',
        'inspection_status',
        'twitter_follow'
    ];

    public $incrementing = false;

    public $timestamps = false;

    public function getActiveById($app_id)
    {
        return self::where('app_id', $app_id)->where('inspection_status', 'active')->first();
    }

    public function editActive($app_id, $data)
    {
        if (empty($app_id) || empty($data)) {
            return false;
        }
        return self::where('app_id', $app_id)->where('inspection_status', 'active')->update($data);
    }
}
