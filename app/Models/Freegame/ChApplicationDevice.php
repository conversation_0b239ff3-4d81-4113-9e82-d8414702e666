<?php

namespace App\Models\Freegame;

class ChApplicationDevice extends Freegame
{
    protected $table = 'ch_application_device';

    protected $guarded = [
        'app_id',
        'device'
    ];

    public $timestamps = false;

    public function getListById($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->get();
    }

    /**
     * 取得：アプリケーションID＆デバイス指定
     * @param  integer $appId
     * @param  array   $device
     * @return \App\Models\Freegame\ApplicationDevice
     */
    public function getByApplicationIdAndDevice($appId, $device)
    {
        return self::where('app_id', $appId)->whereIn('device', $device)->get();
    }
}
