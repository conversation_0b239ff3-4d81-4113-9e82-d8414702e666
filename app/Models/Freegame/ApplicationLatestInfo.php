<?php
namespace App\Models\Freegame;

/**
 * アプリケーション新着テーブル
 */
class ApplicationLatestInfo extends Freegame
{

    protected $table = 'application_latest_info';

    protected $guarded = [
        'id'
    ];

    protected $dates = [
        'begin_date',
        'end_date',
        'initial_begin_date',
    ];

    public $timestamps = false;

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['app_id'])) {
            $query = $query->where('app_id', $condition['app_id']);
        }
        $query = $query->where('public_status', 'active')
            ->orderBy('initial_begin_date', 'desc')
            ->orderBy('id', 'desc');
        if (! empty($condition['perPage'])) {
            return $query->paginate($condition['perPage']);
        } else {
            return $query->get();
        }
    }

    public function getActiveById($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->where('public_status', 'active')->first();
    }

    public function add($data)
    {
        if (empty($data)) {
            return false;
        }

        $attr = $data;
        $attr['begin_date']         = date('Y-m-d H:i:00', strtotime($attr['begin_date']));
        $attr['end_date']           = date('Y-m-d H:i:00', strtotime($attr['end_date']));
        $attr['initial_begin_date'] = date('Y-m-d H:i:00', strtotime($attr['initial_begin_date']));
        $attr['public_status']      = 'active';
        $attr['create_date']        = date('Y-m-d H:i:s');
        $attr['update_date']        = date('Y-m-d H:i:s');
        return self::create($attr);
    }

    public function edit($id, $data)
    {
        if (empty($id) || empty($data)) {
            return false;
        }
        $attr = $data;
        $attr['public_status'] = 'active';
        $attr['update_date'] = date('Y-m-d H:i:s');
        return self::where('id', $id)->update($attr);
    }

    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        $attr['public_status'] = 'delete';
        $attr['update_date'] = date('Y-m-d H:i:s');
        return self::where('id', $id)->update($attr);
    }
}
