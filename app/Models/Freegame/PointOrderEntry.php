<?php
namespace App\Models\Freegame;

use DB;

/**
 * ポイント注文エントリー
 */
class PointOrderEntry extends Freegame
{
    protected $table = 'point_order_entry';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get range id
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getRangeId($condition = [])
    {
        $query = self::selectRaw('MIN(id) AS min_id, MAX(id) AS max_id');

        if (!empty($condition['app_id'])) {
            $query = $query->where('app_id', '=', $condition['app_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }

        return $query->first();
    }

    /**
     * get list with point_log
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getListWithPointLog($condition = [])
    {
        if (! empty($condition['assoc'])) {
            $query = self::queryAssoc();
        } else {
            $query = self::query();
        }

        $query = $query->select(
            'poe.payment_id',
            'poe.passport_id',
            'poi.unit_price',
            'poi.quantity',
            'poe.model',
            'p.date'
        )
        ->from('point_order_entry AS poe')
        ->join('point_log AS p', function ($q) use ($condition) {
            $q->on('poe.id', '=', 'p.account_id')
            ->on('poe.member_id', '=', 'p.member_id');
        })
        ->join('point_order_item AS poi', 'poe.payment_id', '=', 'poi.payment_id')
        ->where('poe.member_id', 'NOT LIKE', 'D500%')
        ->where('poe.status', 'purchase')
        ->where('p.command', 'use')
        ->where('p.description', 'NOT LIKE', '%ch')
        ->where('p.description', 'NOT LIKE', '%cl')
        ->where('p.balance', '>=', '0')
        ->whereNotExists(function ($query) {
            $query->select("payment_id")
                ->from('item_purchase_order_log AS ipol')
                ->whereRaw('poe.payment_id = ipol.payment_id');
        })
        ;

        if (!empty($condition['min_point_order_entry_id'])) {
            $query = $query->where('poe.id', '>=', $condition['min_point_order_entry_id']);
        }
        if (!empty($condition['max_point_order_entry_id'])) {
            $query = $query->where('poe.id', '<=', $condition['max_point_order_entry_id']);
        }
        if (!empty($condition['min_point_pog_id'])) {
            $query = $query->where('p.id', '>=', $condition['min_point_pog_id']);
        }
        if (!empty($condition['max_point_pog_id'])) {
            $query = $query->where('p.id', '<=', $condition['max_point_pog_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('p.date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('p.date', '<=', $condition['end']);
        }
        if (!empty($condition['app_id'])) {
            $query = $query->where('poe.app_id', '=', $condition['app_id']);
        }
        if (!empty($condition['device'])) {
            if (!is_array($condition['device'])) {
                $condition['device'] = [$condition['device']];
            }
            $query = $query->where(function ($q) use ($condition) {
                foreach ($condition['device'] as $device) {
                    if ($device == 'pc') {
                        $q = $q->orWhereNull('poe.passport_id');
                    } elseif ($device == 'sp') {
                        $q = $q->orWhereIn('poe.passport_id', ['iphone', 'android', 'ios'])
                        ->where('poe.model', '<>', 'emulator');
                    } elseif ($device == 'emulator') {
                        $q = $q->orWhere('poe.passport_id', '=', 'android')
                        ->where('poe.model', '=', 'emulator');
                    } else {
                        $q = $q->orWhere('poe.passport_id', $device);
                    }
                }
            });
        }

        $query = $query->groupBy('poe.id');
        $query = $query->orderBy('poe.payment_id', 'asc');

        if (! empty($condition['chunk'])) {
            return $query->chunk(20000, $condition['chunk']);
        } else {
            return $query->get();
        }
    }

    /**
     * get list with item_purchase_order_log
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getListWithItemPurchaseOrderLog($condition = [])
    {
        if (! empty($condition['assoc'])) {
            $query = self::queryAssoc();
        } else {
            $query = self::query();
        }

        $query = $query->select(
            'poe.payment_id',
            'poe.passport_id',
            'poe.model',
            'poi.unit_price',
            'poi.quantity',
            'ipol.coupon_id AS coupon_id',
            'ipol.pay_amount_coupon AS discount_price'
            )
        ->from('point_order_entry AS poe')
        ->join('point_order_item AS poi', 'poe.payment_id', '=', 'poi.payment_id')
        ->join('item_purchase_order_log AS ipol', 'poe.payment_id', '=', 'ipol.payment_id')
        ->join('purchase_order_reference AS por', 'poe.payment_id', '=', 'por.payment_id')
        ->where('poe.member_id', 'NOT LIKE', 'D500%')
        ->where('poe.status', 'purchase')
        ;

        if (!empty($condition['min_point_order_entry_id'])) {
            $query = $query->where('poe.id', '>=', $condition['min_point_order_entry_id']);
        }
        if (!empty($condition['max_point_order_entry_id'])) {
            $query = $query->where('poe.id', '<=', $condition['max_point_order_entry_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('por.exec_date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('por.exec_date', '<=', $condition['end']);
        }
        if (!empty($condition['app_id'])) {
            $query = $query->where('poe.app_id', '=', $condition['app_id']);
        }
        if (!empty($condition['device'])) {
            if (!is_array($condition['device'])) {
                $condition['device'] = [$condition['device']];
            }
            $query = $query->where(function ($q) use ($condition) {
                foreach ($condition['device'] as $device) {
                    if ($device == 'pc') {
                        $q = $q->orWhereNull('poe.passport_id');
                    } elseif ($device == 'sp') {
                        $q = $q->orWhereIn('poe.passport_id', ['iphone', 'android', 'ios'])
                        ->where('poe.model', '<>', 'emulator');
                    } elseif ($device == 'emulator') {
                        $q = $q->orWhere('poe.passport_id', '=', 'android')
                        ->where('poe.model', '=', 'emulator');
                    } else {
                        $q = $q->orWhere('poe.passport_id', $device);
                    }
                }
            });
        }

        $query = $query->groupBy('poe.id');
        $query = $query->orderBy('poe.payment_id', 'asc');

        if (! empty($condition['chunk'])) {
            return $query->chunk(20000, $condition['chunk']);
        } else {
            return $query->get();
        }
    }
}
