<?php
namespace App\Models\Freegame;

/**
 * クライアントアプリケーションテーブル
 */
class ClApplication extends Freegame
{
    protected $table = 'cl_application';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['developer_id'])) {
            $query = $query->where('developer_id', $params['developer_id']);
        }

        return $query->get();
    }

    /**
     * get list
     *
     * @param array $params
     *
     * @return object
     */
    public function getListOrderTitle($params)
    {
        $query = self::select();

        if (!empty($params['developer_id'])) {
            $query = $query->where('developer_id', $params['developer_id']);

            if (!empty($params['cl_app_id_list'])) {
                $query = $query->orWhereIn('id', $params['cl_app_id_list']);
            }
        } elseif (!empty($params['cl_app_id_list'])) {
            $query = $query->whereIn('id', $params['cl_app_id_list']);
        }
        return $query->orderByRaw('BINARY title')->get();
    }

    /**
     * クライアントIDに紐づく、画像アップロードPrefix(ソーシャル・チャネリングの titleIdに相当)を取得する
     * 
     * @param integer $clAppId
     * 
     * @return string
     */
    public function getUploadImageDirNameByClAppId($clAppId)
    {
        return self::where('id', $clAppId)->first()->upload_image_dirname;
    }

    /**
     * 表示権限のあるクライアントタイトルの一覧を取得
     *
     * @param integer $developerId
     * @param array $clApplicationIdList
     *
     * @return array
     */
    public function getListTitleByDeveloperIdAndClAppIdList($developerId, $clApplicationIdList)
    {
        $query = self::where('developer_id', $developerId);

        if (!empty($clApplicationIdList)) {
            $query = $query->orWhereIn('id', $clApplicationIdList);
        }

        return $query->orderByRaw('BINARY title')->lists('title', 'id');
    }

    public function getApplicationTitleList($condition = [])
    {
        $query = self::select([
            'id',
            'title'
        ]);
        if (!empty($condition['id'])) {
            $query = $query->whereIn('id', $condition['id']);
        }
        if (!empty($condition['developer_id'])) {
            $query = $query->where('developer_id', $condition['developer_id']);
        }

        return $query->orderBy('title', 'asc')->get();
    }
}
