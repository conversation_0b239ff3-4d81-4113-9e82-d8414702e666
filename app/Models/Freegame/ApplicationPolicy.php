<?php
namespace App\Models\Freegame;

/**
 * 利用規約テーブル
 */
class ApplicationPolicy extends Freegame
{

    protected $table = 'application_policy';

    public $timestamps = false;

    protected $fillable = ['app_id', 'policy_body', 'release_info', 'stamp'];

    protected $primaryKey = 'app_id';

    /**
     * app_idをキーにして1レコード取得します
     * @param  int   $app_id
     * @return collecion
     */
    public function getOne($app_id)
    {
        return self::select()->where('app_id', $app_id)->first();
    }

    /**
     * 登録(REPLACE INTOします)
     * @param  array   $params
     * @param  int   $app_id
     * @return boolean
     */
    public function insertUpdoadData($params, $app_id)
    {
        return self::firstOrNew(['app_id' => $app_id])->fill($params)->save();
    }
}
