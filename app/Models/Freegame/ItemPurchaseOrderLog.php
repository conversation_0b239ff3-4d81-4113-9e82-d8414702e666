<?php
namespace App\Models\Freegame;

/**
 * アイテム購入の決済情報
 */
class ItemPurchaseOrderLog extends Freegame
{
    protected $table = 'item_purchase_order_log';

    protected $primaryKey = 'payment_id';

    public $incrementing = false;

    public $timestamps = false;

    /**
     * ペイメントID一覧から決済情報の一覧を取得する
     * 
     * @param array  $ids   ペイメントIDの一覧
     * @param string $order 並び順
     * 
     * @return object 抽出結果
     */
    public function getListByPaymentIds($ids, $order = '')
    {
        $query = self::select()->whereIn('payment_id', $ids);
        if (!empty($order)) {
            $query = $query->orderByRaw($order);
        }

        return $query->get();
    }
}
