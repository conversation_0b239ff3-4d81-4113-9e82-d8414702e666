<?php
namespace App\Models\Freegame;

/**
 * アプリケーションテーブル
 */
class Application extends Freegame
{
    protected $table = 'application';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * get list by ids
     *
     * @param  array  $ids
     * @param  string $order
     *
     * @return object
     */
    public function getListByIds($ids, $order = '')
    {
        $query = self::select()->whereIn('id', $ids);
        if (!empty($order)) {
            $query = $query->orderByRaw($order);
        }
        return $query->get();
    }

    /**
     * get list all
     *
     * @return object
     */
    public function getListAll()
    {
        return self::orderByRaw('BINARY title')->get();
    }

    public function getApplicationTitleList($condition = [])
    {
        $query = self::select([
            'id',
            'title'
        ]);
        if (! empty($condition['id'])) {
            $query = $query->whereIn('id', $condition['id']);
        }
        if (! empty($condition['developer_id'])) {
            $query = $query->where('developer_id', $condition['developer_id']);
        }

        return $query->orderBy('title', 'asc')->get();
    }

    public function getApplicationTitleListByDevice($deviceList)
    {
        $result = self::select(['id', 'title', 'device'])
            ->join('application_device', 'application.id', '=', 'application_device.app_id')
            ->whereIn('application_device.device', $deviceList)
            ->orderBy('title', 'asc')->orderBy('device', 'asc')->get();
        return $result;
    }

    public function getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList)
    {
        $result = self::select(['id', 'title', 'device'])
            ->join('application_device', 'application.id', '=', 'application_device.app_id')
            ->whereIn('id', $applicationIdList)
            ->whereIn('application_device.device', $deviceList)
            ->orderBy('title', 'asc')->orderBy('device', 'asc')->get();
        return $result;
    }

    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('id', $condition['app_id']);
            } else {
                $query = $query->where('id', $condition['app_id']);
            }
        }
        $query = $query->orderBy('title', 'asc');
        if (! empty($condition['perPage'])) {
            return $query->paginate($condition['perPage']);
        } else {
            return $query->get();
        }
    }

    public function edit($id, $data)
    {
        if (empty($id) || empty($data)) {
            return false;
        }
        return self::where('id', $id)->update($data);
    }

    /**
     * Get application List by ids
     * @param  array $idList
     * @return array
     */
    public function getListTitleSortByBinaryTitle($idList)
    {
        return self::whereIn('id', $idList)->orderByRaw('BINARY title')->lists('title', 'id');
    }

    /**
     * Get application And device List by applicationId And device
     * @param integer $applicationId
     * @param string $device
     * @return array
     */
    public function getApplicationAndDeviceByApplicationIdAndDevice($applicationId, $device)
    {
        return self::join('application_device', 'application.id', '=', 'application_device.app_id')
            ->where('application.id', $applicationId)
            ->where('application_device.device', $device)
            ->first();
    }
}
