<?php
namespace App\Models\Freegame;

/**
 * アプリケーションIPAテーブル
 */
class ApplicationIpa extends Freegame
{

    protected $table = 'application_ipa';

    protected $guarded = [
        'app_id'
    ];

    public $timestamps = false;

    public function getOne($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->first();
    }

    public function edit($app_id, $data)
    {
        if (empty($app_id) || empty($data)) {
            return false;
        }
        return self::where('app_id', $app_id)->update($data);
    }
}
