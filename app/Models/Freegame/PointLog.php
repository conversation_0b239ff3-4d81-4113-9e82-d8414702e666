<?php
namespace App\Models\Freegame;

use DB;

/**
 * ポイントログ
 */
class PointLog extends Freegame
{
    protected $table = 'point_log';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get range id
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getRangeId($condition = [])
    {
        $query = self::selectRaw('MIN(id) AS min_id, MAX(id) AS max_id');

        if (!empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }

        return $query->first();
    }
}
