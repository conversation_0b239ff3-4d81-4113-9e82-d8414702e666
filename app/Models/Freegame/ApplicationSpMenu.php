<?php
namespace App\Models\Freegame;

/**
 * ソーシャルゲーム用spメニューボタン初期位置管理テーブル
 */
class ApplicationSpMenu extends Freegame
{

    protected $table = 'application_sp_menu';

    protected $primaryKey = 'app_id';

    protected $fillable = [
        'app_id',
        'portrait_position',
        'landscape_position',
    ];

    public $timestamps = false;

    public function getById($appId)
    {
        if (empty($appId)) {
            return null;
        }

        return self::where('app_id', $appId)->first();
    }

    public function edit($appId, $portraitPosition, $landscapePosition)
    {
        if (empty($appId) || is_null($portraitPosition) || is_null($landscapePosition)) {
            return false;
        }
        $attr = [
            'app_id' => $appId,
            'portrait_position' => (int)$portraitPosition,
            'landscape_position' => (int)$landscapePosition,
        ];

        return self::updateOrCreate(['app_id' => $appId], $attr);
    }
}
