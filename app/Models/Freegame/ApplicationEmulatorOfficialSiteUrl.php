<?php
namespace App\Models\Freegame;

/**
 * アプリケーションAPKテーブル
 */
class ApplicationEmulatorOfficialSiteUrl extends Freegame
{

    protected $table = 'application_emulator_official_site_url';

    protected $guarded = [
        'app_id'
    ];

    public $timestamps = false;

    /**
     * add
     * @param  array $data
     * @return boolean
     */
    public function add($data)
    {
        if (empty($data)) {
            return false;
        }
        return self::insert($data);
    }

    public function getOne($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('app_id', $app_id)->pluck('url');
    }

    public function edit($app_id, $data)
    {
        if (empty($app_id) || empty($data)) {
            return false;
        }
        return self::where('app_id', $app_id)->update($data);
    }
}
