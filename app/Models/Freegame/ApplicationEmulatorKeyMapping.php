<?php
namespace App\Models\Freegame;

/**
 * アプリケーションEmulatorキーマッピング情報テーブル
 */
class ApplicationEmulatorKeyMapping extends Freegame
{

    /** @var string */
    protected $table = 'application_emulator_key_mapping';

    /** @var array */
    protected $guarded = ['app_id'];

    /** @var bool */
    public $timestamps = false;

    /**
     * Emulatorキーマッピング情報を１レコード取得
     *
     * @param int $appId
     * @return stdClass|null
     */
    public function getOne($appId)
    {
        if (empty($appId)) {
            return null;
        }
        return self::where('app_id', $appId)->first();
    }

    /**
     * Emulatorキーマッピング情報更新
     *
     * @param int $appId
     * @param array $data
     * @return integer|bool
     */
    public function edit($appId, $data)
    {
        if (empty($appId) || empty($data)) {
            return false;
        }
        return self::where('app_id', $appId)->update($data);
    }

    /**
     * CFGファイルバージョンコードカウントアップ
     *
     * @param int $appId
     * @return int
     */
    public function editCountUpCfgVersionCode($appId)
    {
        return self::where('app_id', $appId)->where('is_enable', 1)->increment('cfg_version_code');
    }
}
