<?php
namespace App\Models\Freegame;

use DB;

class Present extends Freegame
{
    protected $table = 'present';

    public $timestamps = false;

    /**
     * get one by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * get list
     *
     * @param  array $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (isset($condition['unit_app_id'])) {
            $query = $query->where('present.unit_app_id', '=', $condition['unit_app_id']);
        }

        if (isset($condition['unit_app_id_list'])) {
            $query = $query->whereIn('present.unit_app_id', $condition['unit_app_id_list']);
        }

        if (isset($condition['status_not'])) {
            $query = $query->where('present.status', '!=', $condition['status_not']);
        }

        $query = $query->orderBy('present.id', 'desc');

        if (isset($condition['perPage'])) {
            $result = $query->paginate($condition['perPage']);
        } else {
            $result = $query->get();
        }

        return $result;
    }

    /**
     * get unit_app_id
     *
     * @param  array $condition
     *
     * @return object
     */
    public function getUnitAppIds($condition = [])
    {
        $query = self::select(DB::raw('DISTINCT unit_app_id'));

        if (isset($condition['status_not'])) {
            $query = $query->where('status', '!=', $condition['status_not']);
        }

        $result = $query->get();

        return $result;
    }

    /**
     * update
     *
     * @param  integer $id
     * @param  array   $data
     *
     * @return object
     */
    public function edit($id, $data)
    {
        return self::where('id', $id)->update($data);
    }
}
