<?php

namespace App\Models\Freegame;

use App\Models\FreegameDeveloper\DeveloperApplication;

class ApplicationExaminationUser extends Freegame
{
    protected $table   = 'application_examination_user';

    protected $guarded = ['id'];

    public $timestamps = false;

//*********************************************************************************************************************
    /**
     * ID指定取得
     * @param  integer $appId
     * @param  integer $userId
     * @return array
     */
    public function getById($appId, $userId)
    {
        if (empty($appId) || empty($userId)) {
            return false;
        }
        $where = [
                'app_id'  => $appId,
                'user_id' => $userId,
        ];
        return $this->getSelect($where)->first();
    }

    /**
     * ID・デベロッパ指定取得
     * @param  integer $appId
     * @param  integer $userId
     * @param  integer $devId
     * @return array
     */
    public function getByIdAndDeveloper($appId, $userId, $devId)
    {
        if (empty($appId) || empty($userId) || empty($devId)) {
            return false;
        }
        $where = [
                'app_id'       => $appId,
                'user_id'      => $userId,
                'developer_id' => $devId,
        ];
        return $this->getSelect($where)->first();
    }

    /**
     * 一覧取得
     * @param  array  $where
     * @param  string $order
     * @return array
     */
    public function getList($where, $order = 'p.stamp ASC')
    {
        $perPage = isset($where['perPage']) ? $where['perPage'] : null;
        $query   = $this->getSelect($where);

        if ($perPage) {
            return $query->orderByRaw($order)->paginate($perPage);
        } else {
            return $query->orderByRaw($order)->get();
        }
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::create($param);
    }

    /**
     * 更新
     * @param  integer $appId
     * @param  integer $userId
     * @param  array   $param
     * @return boolean
     */
    public function edit($appId, $userId, $param)
    {
        if (empty($appId) || empty($userId) || empty($param)) {
            return false;
        }
        return self::where('app_id', $appId)->where('user_id', $userId)->update($param);
    }

    /**
     * 削除
     * @param  integer $appId
     * @param  integer $userId
     * @return boolean
     */
    public function del($appId, $userId)
    {
        if (empty($appId) || empty($userId)) {
            return false;
        }
        return self::where('app_id', $appId)->where('user_id', $userId)->delete();
    }

//*********************************************************************************************************************
    /**
     * セレクト値設定
     * @param  array $param
     * @return object
     */
    private function getSelect($param = [])
    {
        $query = $this->getWhere($param)->select(
            'p.*',
            'a.title',
            'u.nickname',
            'u.prefecture',
            'u.gender',
            'u.birth_date',
            'u.blood_type',
            'u.job',
            'u.hobby'
        );
        return $query;
    }

    /**
     * 検索条件取得
     * @param  array $param
     * @return object
     */
    private function getWhere($param = [])
    {
        $query = self::from('application_examination_user AS p')
            ->join('user AS u', 'p.user_id', '=', 'u.id')
            ->join('application AS a', 'p.app_id', '=', 'a.id');

        $appId    = isset($param['app_id'])       ? $param['app_id']       : null;
        $userId   = isset($param['user_id'])      ? $param['user_id']      : null;
        $status   = isset($param['status'])       ? $param['status']       : null;
        $nickname = isset($param['nickname'])     ? $param['nickname']     : null;

        $devId    = isset($param['developer_id']) ? $param['developer_id'] : null;

        if (! empty($appId)) {
            $query->where('a.id', $appId);
        }

        if (! empty($userId)) {
            $query->where('u.id', $userId);
        }

        if (! empty($status)) {
            $query->where('u.status', $status);
        }

        if (! empty($nickname)) {
            $query->where('u.nickname', 'LIKE', "%{$nickname}%");
        }

        if (! empty($devId)) {
            $devApp  = new DeveloperApplication();
            $devList = $devApp->getApplicationAppIdList(['developer_id' => $devId]);
            $appIds  = [];

            foreach ($devList as $devData) {
                $appIds[] = $devData->app_id;
            }
            $query->whereIn('a.id', $appIds);
        }
        return $query;
    }
}
