<?php
namespace App\Models\Freegame;

/**
 * アプリケーションサブジャンルテーブル
 */
class ApplicationSubGenre extends Freegame
{

    protected $table = 'application_sub_genre';

    public $timestamps = false;

    public function getNameList()
    {
        $joinTable = 'application_main_genre';
        $mainGenreTable = $joinTable . '.';
        $subGenreTable = $this->table . '.';
        return self::select([
            $subGenreTable . 'id',
            $subGenreTable . 'main_genre_id',
            $subGenreTable . 'name',
        ])
        ->join($joinTable, $mainGenreTable . 'id', '=', $subGenreTable . 'main_genre_id')
        ->where($subGenreTable . 'is_disabled', 0)
        ->orderBy($mainGenreTable . 'sort_order')
        ->orderBy($subGenreTable . 'sort_order')
        ->get();
    }
}
