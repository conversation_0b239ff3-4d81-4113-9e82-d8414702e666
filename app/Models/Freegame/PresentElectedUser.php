<?php
namespace App\Models\Freegame;

use DB;

class PresentElectedUser extends Freegame
{
    protected $table = 'present_elected_user';

    /**
     * get list
     *
     * @param  array $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (isset($condition['present_id'])) {
            $query = $query->where('present_id', $condition['present_id']);
        }

        return $query->orderBy('id', 'asc')->get();
    }

    /**
     * get count group by present_id
     *
     * @param  array $condition
     *
     * @return object
     */
    public function getCount($condition = [])
    {
        $query = self::select(DB::raw('COUNT(present_id) AS cnt'));

        if (isset($condition['present_id'])) {
            $query = $query->where('present_id', $condition['present_id']);
        }
        if (isset($condition['user_id_big'])) {
            $query = $query->where('user_id', '>', $condition['user_id_big']);
        }

        return $query->groupBy('present_id')->first();
    }
}
