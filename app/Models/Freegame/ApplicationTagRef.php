<?php
namespace App\Models\Freegame;

/**
 * アプリケーションタグ関連テーブル
 */
class ApplicationTagRef extends Freegame
{

    protected $table = 'application_tag_ref';

    public $timestamps = false;

    /**
     * タグ名一覧取得
     * @param  string $appId
     * @return object
     */
    public function getApplicationTagNameList($appId)
    {
        return self::select('t.name')
            ->from('application_tag_ref AS r')
            ->join('application_tag AS t', 'r.tag_id', '=', 't.id')
            ->where('r.app_id', $appId)
            ->get();
    }

    public function getSocialListById($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return self::where('kind', 'social')->where('app_id', $app_id)->get();
    }

    /**
     * 女性向け取得：アプリケーションID指定
     * @param  integer $appId
     * @param  array   $device
     * @return \App\Models\Freegame\ApplicationTagRef
     */
    public function getGirlsByApplicationId($appId)
    {
        if (empty($appId)) {
            return false;
        }
        return self::where('app_id', $appId)->where('tag_id', 67)->first();
    }

    /**
     * Get app id list by tag id
     * @param integer $tagId
     * @return array
     */
    public function getAppIdListByTagId($tagId)
    {
        return self::where('tag_id', $tagId)->lists('app_id');
    }
}
