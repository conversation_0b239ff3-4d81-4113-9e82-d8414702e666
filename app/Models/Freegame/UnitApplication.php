<?php
namespace App\Models\Freegame;

use DB;

class UnitApplication extends Freegame
{
    protected $table = 'unit_application';

    public $timestamps = false;

    /**
     * get one by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * get list
     *
     * @param  array  $conditions
     *
     * @return object $query
     */
    public static function getList($conditions = [])
    {
        $query = self::select();

        if (!empty($conditions['id'])) {
            $query = $query->whereIn('id', $conditions['id']);
        }

        if (!empty($conditions['app_id'])) {
            $query = $query->whereIn('app_id', $conditions['app_id']);
        }

        if (!empty($conditions['app_type'])) {
            $query = $query->where('app_type', $conditions['app_type']);
        }

        return $query->get();
    }

    /**
     * get list with application
     *
     * @param  array  $conditions
     *
     * @return object $query
     */
    public static function getListWithApplication($conditions = [])
    {
        $query = self::select('unit_application.id AS unit_app_id', 'app.title AS title', 'app.id AS app_id')
        ->join('application AS app', 'unit_application.app_id', '=', 'app.id')
        ->join('application_device AS d', 'app.id', '=', 'd.app_id')
        ->whereRaw('CURRENT_TIMESTAMP <= d.end');

        if (!empty($conditions['id'])) {
            $query = $query->whereIn('unit_application.id', $conditions['id']);
        }

        if (!empty($conditions['app_type'])) {
            $query = $query->where('unit_application.app_type', $conditions['app_type']);
        }

        if (!empty($conditions['join'])) {
            $join = $conditions['join'];
            $query->join($join['table'], $join['col1'], $join['operator'], $join['col2']);
        }

        if (!empty($conditions['add_select'])) {
            foreach ((array)$conditions['add_select'] as $add) {
                $query->addSelect($add);
            }
        }

        return $query->get();
    }

    /**
     * get list with ch_application
     *
     * @param  array  $conditions
     *
     * @return object $query
     */
    public static function getListWithChApplication($conditions = [])
    {
        $query = self::select('unit_application.id AS unit_app_id', 'app.title AS title', 'app.id AS app_id')
        ->join('ch_application AS app', 'unit_application.app_id', '=', 'app.id')
        ->whereRaw('CURRENT_TIMESTAMP <= app.end');

        if (!empty($conditions['id'])) {
            $query = $query->whereIn('unit_application.id', $conditions['id']);
        }

        if (!empty($conditions['app_type'])) {
            $query = $query->where('unit_application.app_type', $conditions['app_type']);
        }

        if (!empty($conditions['join'])) {
            $join = $conditions['join'];
            $query->join($join['table'], $join['col1'], $join['operator'], $join['col2']);
        }

        if (!empty($conditions['add_select'])) {
            foreach ((array)$conditions['add_select'] as $add) {
                $query->addSelect($add);
            }
        }

        return $query->get();
    }

    /**
     * get list with cl_application
     *
     * @param  array  $conditions
     *
     * @return object $query
     */
    public static function getListWithClApplication($conditions = [])
    {
        $query = self::select('unit_application.id AS unit_app_id', 'app.title AS title', 'app.id AS app_id')
        ->join('cl_application AS app', 'unit_application.app_id', '=', 'app.id')
        ->whereRaw('CURRENT_TIMESTAMP <= app.end');

        if (!empty($conditions['id'])) {
            $query = $query->whereIn('unit_application.id', $conditions['id']);
        }

        if (!empty($conditions['app_type'])) {
            $query = $query->where('unit_application.app_type', $conditions['app_type']);
        }

        if (!empty($conditions['join'])) {
            $join = $conditions['join'];
            $query->join($join['table'], $join['col1'], $join['operator'], $join['col2']);
        }

        if (!empty($conditions['add_select'])) {
            foreach ((array)$conditions['add_select'] as $add) {
                $query->addSelect($add);
            }
        }

        return $query->get();
    }

    /**
     * unit_application.idをキーにアプリ情報を取得する
     * クローズ済みのタイトルも含む
     *
     * @param  array  $unitAppIds
     * @return object $query
     */
    public static function getListWithApplicationByUnitAppIds($unitAppIds = [])
    {
        $query = self::select('unit_application.id AS unit_app_id', 'app.title AS title', 'app.id AS app_id')
            ->join('application AS app', 'unit_application.app_id', '=', 'app.id')
            ->join('application_device AS d', 'app.id', '=', 'd.app_id')
            ->where('unit_application.app_type', 'application')
            ->whereIn('unit_application.id', $unitAppIds);
        return $query->get();
    }

    /**
     * unit_application.idをキーにチャネリングアプリ情報を取得する
     * クローズ済みのタイトルも含む
     *
     * @param  array  $unitAppIds
     * @return object $query
     */
    public static function getListWithChApplicationByUnitAppIds($unitAppIds = [])
    {
        $query = self::select('unit_application.id AS unit_app_id', 'app.title AS title', 'app.id AS app_id')
            ->join('ch_application AS app', 'unit_application.app_id', '=', 'app.id')
            ->where('unit_application.app_type', 'ch_application')
            ->whereIn('unit_application.id', $unitAppIds);
        return $query->get();
    }

    /**
     * unit_application.idをキーにクライアントアプリ情報を取得する
     * クローズ済みのタイトルも含む
     *
     * @param  array  $unitAppIds
     * @return object $query
     */
    public static function getListWithClApplicationByUnitAppIds($unitAppIds = [])
    {
        $query = self::select('unit_application.id AS unit_app_id', 'app.title AS title', 'app.id AS app_id')
            ->join('cl_application AS app', 'unit_application.app_id', '=', 'app.id')
            ->where('unit_application.app_type', 'cl_application')
            ->whereIn('unit_application.id', $unitAppIds);
        return $query->get();
    }
}
