<?php

namespace App\Models\Freegame;

class ApplicationInstallReport extends Freegame
{
    protected $table = 'application_install_report';

    public $timestamps = false;

    /**
     * 一覧取得。
     *
     * @param array    $condition  抽出条件
     * @return array
     */
    public function getList($condition = [])
    {
        $query = self::select(
                'application_install_report.id',
                'application_install_report.user_id',
                'application_install_report.app_id',
                'application_install_report.device',
                'application_install_report.begin_date',
                'application_install_report.end_date',
                'application.title',
                'application_device.prebegin',
                'application_device.preend',
                'user.regist_date',
                'user.nickname'
            )
            ->join('application', 'application_install_report.app_id', '=', 'application.id')
            ->join('application_device', function ($queque) {
                $queque->on('application_install_report.app_id', '=', 'application_device.app_id');
                $queque->on('application_install_report.device', '=', 'application_device.device');
            })
            ->join('user', 'application_install_report.user_id', '=', 'user.id')
            ->where('application_install_report.status', 'active')
            ->where('user.status', 'active');

        if (!empty($condition['app_id'])) {
            $query->where('application_install_report.app_id', $condition['app_id']);
        }

        if (!empty($condition['device'])) {
            if (is_array($condition['device'])) {
                $query->whereIn('application_install_report.device', $condition['device']);
            } else {
                $query->where('application_install_report.device', $condition['device']);
            }
        }

        if (!empty($condition['begin'])) {
            if (strlen($condition['begin']) <= 10) {
                $condition['begin'] .= ' 00:00:00';
            }
            $query->where('application_install_report.begin_date', '>=', $condition['begin']);
        }

        if (!empty($condition['end'])) {
            if (strlen($condition['end']) <= 10) {
                $condition['end'] .= ' 23:59:59';
            }
            $query->where('application_install_report.begin_date', '<=', $condition['end']);
        }

        $query->orderBy('application_install_report.app_id')
            ->orderBy('device', 'asc')
            ->orderBy('begin_date', 'asc');

        return $query->get();
    }
}
