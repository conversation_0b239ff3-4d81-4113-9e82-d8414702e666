<?php
namespace App\Models\Freegame;

/**
 * サイト別タイトルグループテーブル
 */
class ApplicationTitleGroupBySite extends Freegame
{
    protected $table = 'application_title_group_by_site';

    public $timestamps = false;

    /**
     * Edit
     * Insert
     *
     * @param array $params
     * @return boolean
     */
    public function add($params)
    {
        if (empty($params)) {
            return false;
        }
        return self::insert($params);
    }

    /**
     * Edit
     *
     * @param array $conditions
     * @param array $params
     * @return boolean
     */
    public function edit($conditions, $params)
    {
        return self::where($conditions)->update($params);
    }

    /**
     * Get group by app id
     * @param $tableColumn
     * @param $appId
     * @param $id
     * @param $idExclusion
     * @return array
     */
    public function getGroupByAppId($tableColumn, $appId, $id = null, $idExclusion = true)
    {
        $applicationTitleGroupBySite = self::from($this->table);

        $applicationTitleGroupBySite->where($tableColumn, $appId);
        if ($id) {
            if ($idExclusion) {
                // 存在確認の際、自分のレコードを対象外にする
                $applicationTitleGroupBySite->where('id', '<>', $id);
            } else {
                // 組み合わせ整合性確認の場合
                $applicationTitleGroupBySite->where('id', $id);
            }
        }

        return $applicationTitleGroupBySite->first();
    }

    /**
     * Get group by id
     * @param $id
     * @return array
     */
    public function getGroupById($id)
    {
        return self::where('id', $id)->first();
    }
}
