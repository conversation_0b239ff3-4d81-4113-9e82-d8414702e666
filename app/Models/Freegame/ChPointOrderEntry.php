<?php
namespace App\Models\Freegame;

use DB;

/**
 * チャネリングポイント注文エントリー
 */
class ChPointOrderEntry extends Freegame
{
    protected $table = 'ch_point_order_entry';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getCount($condition = [])
    {
        $query = self::select(DB::raw('COUNT(DISTINCT(ch_point_order_entry.member_id)) AS point_user_user'))
                 ->join('ch_user', 'ch_point_order_entry.member_id', '=', 'ch_user.member_id');

        if (!empty($condition['ch_app_id'])) {
            $query = $query->where('ch_point_order_entry.ch_app_id', $condition['ch_app_id']);
        }
        if (!empty($condition['date'])) {
            $query = $query->where('ch_point_order_entry.date', $condition['date']);
        }
        if (!empty($condition['date_ym'])) {
            $query = $query->where(
                DB::raw('DATE_FORMAT(ch_point_order_entry.date, "%Y-%m")'),
                '=',
                $condition['date_ym']
            );
        }
        if (!empty($condition['status'])) {
            $query = $query->where('ch_point_order_entry.status', $condition['status']);
        }
        if (!empty($condition['member_id_not'])) {
            $query = $query->where('ch_point_order_entry.member_id', 'not like', $condition['member_id_not'].'%%');
        }

        return $query->get();
    }

    /**
     * get range id
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getRangeId($condition = [])
    {
        $query = self::selectRaw('MIN(id) AS min_id, MAX(id) AS max_id');

        if (!empty($condition['ch_app_id'])) {
            $query = $query->where('ch_app_id', $condition['ch_app_id']);
        }
        if (!empty($condition['begin'])) {
            $query = $query->where('date', '>=', $condition['begin']);
        }
        if (!empty($condition['end'])) {
            $query = $query->where('date', '<=', $condition['end']);
        }

        return $query->first();
    }

    /**
     * get list with point_order_item
     *
     * @params array  $condition
     *
     * @return object
     */
    public function getListWithPointOrderItem($condition = [])
    {
        //conditionチェック
        if (empty($condition['min_point_order_entry_id']) || empty($condition['max_point_order_entry_id'])
            || empty($condition['min_point_pog_id']) || empty($condition['max_point_pog_id'])
            || empty($condition['ch_app_id']) || empty($condition['device']) || !is_array($condition['device'])) {
            return;
        }

        /*
         * 取得対象のpayment_idを取得する
         */
        $entryQuery = self::select('poe.payment_id')
            ->from('ch_point_order_entry AS poe')
            ->join('point_log AS p', function ($q) use ($condition) {
                $q->on('poe.id', '=', 'p.account_id')
                    ->on('poe.member_id', '=', 'p.member_id');
            })
            ->where('poe.member_id', 'NOT LIKE', 'D500%')
            ->where('poe.status', '=', 'purchase')
            ->where('p.command', '=', 'use')
            ->where('p.description', 'LIKE', '%ch')
            ->where('p.balance', '>=', '0');

        $entryQuery = $entryQuery->where('poe.id', '>=', $condition['min_point_order_entry_id'])
            ->where('poe.id', '<=', $condition['max_point_order_entry_id'])
            ->where('p.id', '>=', $condition['min_point_pog_id'])
            ->where('p.id', '<=', $condition['max_point_pog_id'])
            ->where('poe.ch_app_id', '=', $condition['ch_app_id'])
            ->whereIn('poe.device', $condition['device'])
            ->groupBy('poe.payment_id');

        //クエリ実行(5000件ずつ取得)
        $result_chunk = array();
        $entryQuery->chunk(5000, function ($result) use(&$result_chunk) {
            $tmp_chunk = array();
            $result->each(function ($item, $key) use (&$tmp_chunk) {
                $tmp_chunk[] = $item['payment_id'];
            });
            $result_chunk[] = $tmp_chunk;
        });

        /*
         * payment_id5000ずつ、point_order_itemとデバイスを取得
         */
        if (! empty($condition['assoc'])) {
            $itemQueryInit = self::queryAssoc();
        } else {
            $itemQueryInit = self::query();
        }

        foreach ($result_chunk as $payment_id) {
            $itemQuery = clone $itemQueryInit;
            $itemQuery = $itemQuery->select(
                'poe.payment_id',
                'poe.device',
                'p.date',
                DB::raw('poi.unit_price * poi.quantity AS unit_price'),
                DB::raw('1 AS quantity')
            )
                ->from('ch_point_order_entry AS poe')
                ->join('point_log AS p', function ($q) use ($condition) {
                    $q->on('poe.id', '=', 'p.account_id')
                        ->on('poe.member_id', '=', 'p.member_id')
                        ->where('p.id', '>=', $condition['min_point_pog_id'])
                        ->where('p.id', '<=', $condition['max_point_pog_id'])
                        ->where('p.command', '=', 'use')
                        ->where('p.description', 'LIKE', '%ch')
                        ->where('p.balance', '>=', '0');
                })
                ->join('ch_point_order_item AS poi', 'poe.payment_id', '=', 'poi.payment_id')
                ->whereIn('poe.payment_id', $payment_id)
                ->groupBy('poe.payment_id')
                ->orderBy('poe.payment_id');

            if (! empty($condition['chunk'])) {
                $itemQuery->chunk(20000, $condition['chunk']);
            } else {
                $itemQuery->get();
            }
        }
    }
}