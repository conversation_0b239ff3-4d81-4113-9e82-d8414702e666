<?php
namespace App\Models\Freegame;

/**
 * アプリケーション言語設定グループテーブル
 */
class ApplicationLanguageGroup extends Freegame
{
    protected $table = 'application_language_group';

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::select();
        
        if (!empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        return $query->get();
    }
}
