<?php
namespace App\Models\Freegame;

class ApplicationCdnGameStore extends Freegame
{
    protected $table = 'application_cdn_gamestore';

    public $timestamps = false;

    /**
     * Get data by app_id
     *
     * @param integer $appId
     * @return boolean
     */
    public function getDataByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }
        return self::where('app_id', $appId)->first();
    }
}
