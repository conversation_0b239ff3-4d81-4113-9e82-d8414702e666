<?php
namespace App\Models\Freegame;

/**
 * 末端掲載動画テーブル
 */
class ApplicationMovie extends Freegame
{

    protected $table = 'application_movie';

    protected $primaryKey = [
        'app_id',
        'device'
    ];

    public $timestamps = false;

    /**
     * 登録
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::insert($param);
    }

    /**
     * 削除
     * @param  array   $param
     * @return boolean
     */
    public function del($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::where('app_id', $param['app_id'] )->where('device', $param['device'] )->delete();
    }

}
