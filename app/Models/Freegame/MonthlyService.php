<?php
namespace App\Models\Freegame;

/**
 * 月額サービステーブル
 */
class MonthlyService extends Freegame
{
    protected $table   = 'monthly_service';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * 一覧取得
     * @param array $condition
     */
    public function getList($condition = [])
    {
        $query = self::query();
        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('app_id', $condition['app_id']);
            } else {
                $query = $query->where('app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['kind'])) {
            $query = $query->where('kind', $condition['kind']);
        }
        return $query->orderBy('app_id', 'asc')->get();
    }

    /**
     * get list with application
     *
     * @param  array $conditions
     *
     * @return array $list
     *
     */
    public function getListWithApplication($conditions = [])
    {
        $query = self::select(
            'monthly_service.id',
            'monthly_service.service_name',
            'monthly_service.app_id',
            'application.title'
        )->join('application', 'monthly_service.app_id', '=', 'application.id');

        if (!empty($conditions['app_id'])) {
            if (is_array($conditions['app_id'])) {
                $query = $query->whereIn('monthly_service.app_id', $conditions['app_id']);
            } else {
                $query = $query->where('monthly_service.app_id', $conditions['app_id']);
            }
        }

        if (! empty($condition['kind'])) {
            $query = $query->where('monthly_service.kind', $condition['kind']);
        }

        return $query->get();
    }
}
