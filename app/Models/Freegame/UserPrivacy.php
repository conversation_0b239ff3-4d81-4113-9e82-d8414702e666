<?php
namespace App\Models\Freegame;

/**
 * ユーザープライバシー
 */
class UserPrivacy extends Freegame
{
    protected $table = 'user_privacy';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by user_id
     *
     * @param  integer $userId
     *
     * @return object
     */
    public function getOneByUserId($userId)
    {
        return self::select()->where('user_id', $userId)->first();
    }

    /**
     * insert
     *
     * @param  array   $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * update
     *
     * @param  integer $userId
     * @param  array   $data
     *
     * @return object
     */
    public function editByUserId($userId, $data)
    {
        return self::where('user_id', $userId)->update($data);
    }
}
