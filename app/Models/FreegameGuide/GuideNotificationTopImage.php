<?php

namespace App\Models\FreegameGuide;

use Carbon\Carbon;

class GuideNotificationTopImage extends FreegameGuide
{
    protected $table = 'guide_notification_top_image';

    public $timestamps = false;

    /**
     * Get one by guide_notification_top_image
     * @param integer $id
     * @return array
     */
    public function getOneById($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * Get content by guide_notification_top_image
     * @param integer $guideNotificationId
     * @return array
     */
    public function getOneByNoticId($guideNotificationId)
    {
        return self::where('guide_notification_id', $guideNotificationId)->first();
    }

    /**
     * Get content by guide_notification_top_image after hour
     * @return array
     */
    public function getListByAfterHour()
    {
        $anHourAgo = Carbon::now()->subHour();
        return self::where('guide_notification_id', 0)
            ->where('updated_at', '<=', $anHourAgo)
            ->get();
    }

    /**
     * Update guide_notification_top_image
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_notification_top_image by guide_notification_id
     * @param integer $notificationId
     * @return boolean
     */
    public function deleteByNotificationId($notificationId)
    {
        return self::where('guide_notification_id', $notificationId)->delete();
    }

    /**
     * Delete temporary guide_notification_top_image
     * @param integer $id
     * @return boolean
     */
    public function deleteTmpById($id)
    {
        return self::where('id', $id)->where('guide_notification_id', 0)->delete();
    }

    /**
     * Delete guide_notification_top_image By idlist
     * @param array $idList
     * @return array
     */
    public function deleteByIdList($idList)
    {
        return self::whereIn('id', $idList)->delete();
    }
}
