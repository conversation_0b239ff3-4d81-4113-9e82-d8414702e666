<?php

namespace App\Models\FreegameGuide;

class GuideNotificationImage extends FreegameGuide
{
    protected $table = 'guide_notification_image';

    public $timestamps = false;

    /**
     * Get content by guide_notification_image
     * @param integer $noticId
     * @return array
     */
    public function getListByNoticId($noticId)
    {
        return self::where('guide_notification_id', $noticId)->get();
    }

    /**
     * Get content by guide_notification_image  after hour
     *
     * @return array
     */
    public function getListByAfterHour()
    {
        return self::where('guide_notification_id', 0)
            ->where('stamp', '<=', timestamp_to_sqldate(now_stamp() - 3600))
            ->get();
    }

    /**
     * Get content by guide_notification_image
     * @param integer $guideAppId
     * @param integer $guideNoticId
     * @return array
     */
    public function getListByWithInHour($guideAppId, $guideNoticId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('guide_notification_id', $guideNoticId)
            ->where('stamp', '>', timestamp_to_sqldate(now_stamp() - 3600))
            ->get();
    }

    /**
     * Update guide_notification_image
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_notification_image
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * Delete guide_notification_image By guide_application_id AND guide_notification_id
     * @param integer $guideAppId
     * @param integer $guideNoticId
     * @return boolean
     */
    public function delByGuideAppIdAndGuideNoticId($guideAppId, $guideNoticId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('guide_notification_id', $guideNoticId)->delete();
    }

    /**
     * Delete guide_notification_image By idlist
     * @param array $idList
     * @return array
     */
    public function delByIdList($idList)
    {
        return self::whereIn('id', $idList)->delete();
    }
}
