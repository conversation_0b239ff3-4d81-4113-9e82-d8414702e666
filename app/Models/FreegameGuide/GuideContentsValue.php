<?php

namespace App\Models\FreegameGuide;

class GuideContentsValue extends FreegameGuide
{
    protected $table = 'guide_contents_value';

    protected $fillable = [
        'cluster_id',
        'template_id',
        'value',
    ];

    public $timestamps = true;

    /**
     * 入力値削除(データID指定)
     * @param integer $clusterId 削除対象データID
     * @return boolean
     */
    public function deleteByClusterId($clusterId)
    {
        return self::where('cluster_id', $clusterId)
            ->delete();
    }

    /**
     * 入力値削除(管理項目ID指定)
     * @param integer $templateId 削除対象管理項目ID
     * @return boolean
     */
    public function deleteByTemplateId($templateId)
    {
        return self::where('template_id', $templateId)
            ->delete();
    }

    /**
     * 入力値削除(管理項目ID複数指定)
     * @param array $templateIds 削除対象管理項目ID配列
     * @return boolean
     */
    public function deleteByMultiTemplateIds($templateIds)
    {
        return self::whereIn('template_id', $templateIds)
            ->delete();
    }

    /**
     * 入力値新規登録・更新
     * @param array $updateData 登録する値
     * @param integer $clusterId 登録対象データID
     * @param integer $templateId 登録対象管理項目ID
     * @return boolean
     */
    public function upsert($upsertData, $clusterId, $templateId)
    {
        $keys = [
            'cluster_id' => $clusterId,
            'template_id' => $templateId,
        ];
        return self::updateOrCreate(
            $keys,
            array_merge($keys, $upsertData)
        );
    }
}
