<?php

namespace App\Models\FreegameGuide;

class GuideFormOptions extends FreegameGuide
{
    protected $table = 'guide_form_options';

    public $timestamps = false;

    /**
     * Get content by guide_form_options
     * @param integer $guideFormId
     * @return array
     */
    public function getListByGuideFormId($guideFormId)
    {
        return self::where('guide_form_id', $guideFormId)
            ->orderBy('priority', 'asc')->get();
    }

    /**
     * Get count by guide_form_options
     * @param integer $optionId
     * @param integer $guideFormId
     * @return array
     */
    public function isEmpty($optionId, $guideFormId)
    {
        $query = self::where('id', $optionId)->where('guide_form_id', $guideFormId);
        return empty($query->first());
    }

    /**
     * Update guide_application
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_form_options
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
