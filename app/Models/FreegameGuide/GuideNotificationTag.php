<?php

namespace App\Models\FreegameGuide;

class GuideNotificationTag extends FreegameGuide
{
    protected $table = 'guide_notification_tag';

    public $timestamps = true;

    /**
     * お知らせタグ取得
     * @param integer $tagId ID
     * @return array
     */
    public function getOne($tagId)
    {
        return self::where('id', $tagId)
            ->first();
    }

    /**
     * お知らせタグ一覧取得
     * @param integer $tagId ID
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->orderBy('order_no')
            ->get();
    }

    /**
     * お知らせタグ検索
     * @param array $params 検索条件
     * @return array
     */
    public function search($params)
    {
        $data = self::select('*')
            ->where('guide_application_id', $params['guide_application_id']);

        if ($this->existsParam($params, 'name')) {
            $data->where('name', $params['name']);
        }

        if ($this->existsParam($params, 'tag_key')) {
            $data->where('tag_key', $params['tag_key']);
        }

        return $data->orderBy('order_no')
            ->paginate($params['perPage']);
    }

    /**
     * 検索条件の検出
     * @param array $params
     * @param string $key
     * @return boolean
     */
    protected function existsParam($params, $key)
    {
        return isset($params[$key]) && $params[$key] != '';
    }

    /**
     * お知らせタグ更新
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * タグ名存在確認
     * @param integer $guideAppId アプリID
     * @param string $name タグ名
     * @return boolean
     */
    public function existsName($guideAppId, $name, $ignoreId = null)
    {
        $query =  self::where('guide_application_id', $guideAppId)
            ->where('name', $name);
        if ($ignoreId) {
            $query->where('id', '<>', $ignoreId);
        }
        return $query->exists();
    }

    /**
     * タグキー存在確認
     * @param integer $guideAppId アプリID
     * @param string $tagKey タグキー
     * @return boolean
     */
    public function existsTagKey($guideAppId, $tagKey, $ignoreId = null)
    {
        $query =  self::where('guide_application_id', $guideAppId)
            ->where('tag_key', $tagKey);
        if ($ignoreId) {
            $query->where('id', '<>', $ignoreId);
        }
        return $query->exists();
    }

    /**
     * 表示順リスト取得
     * @param integer $guideAppID 取得対象マスターID
     * @return array
     */
    public function getOrderList($guideAppId)
    {
        return self::select(['id', 'order_no', 'name'])
            ->where('guide_application_id', $guideAppId)
            ->orderBy('order_no')
            ->get();
    }

    /**
     * 表示順設定
     * @param integer $masterId 更新対象データID
     * @param integer $orderNo 表示順
     * @return boolean
     */
    public function setOrderNo($id, $orderNo)
    {
        return self::where('id', $id)
            ->update(['order_no' => $orderNo]);
    }

    /**
     * 表示順繰り下げ
     * @param integer $guideAppID アプリID
     * @param integer $from 開始位置
     * @param integer $to 終了位置
     * @return boolean
     */
    public function incrementOrder($guideAppID, $from, $to = null)
    {
        $query = self::where('guide_application_id', $guideAppID)
            ->where('order_no', '>=', $from);

        if (!is_null($to)) {
            $query->where('order_no', '<', $to);
        }

        return $query->orderBy('order_no', 'desc')
            ->increment('order_no');
    }

    /**
     * 表示順繰り上げ
     * @param integer $guideAppID アプリID
     * @param integer $from 開始位置
     * @param integer $to 終了位置
     * @return boolean
     */
    public function decrementOrder($guideAppID, $from, $to = null)
    {
        $query = self::where('guide_application_id', $guideAppID)
            ->where('order_no', '>=', $from);

        if (!is_null($to)) {
            $query->where('order_no', '<', $to);
        }

        return $query->orderBy('order_no')
            ->decrement('order_no');
    }

    /**
     * 表示順最後尾取得
     * @param integer $guideAppID
     * @return integer
     */
    public function getLastOrder($guideAppID)
    {
        return self::where('guide_application_id', $guideAppID)
            ->max('order_no');
    }

}
