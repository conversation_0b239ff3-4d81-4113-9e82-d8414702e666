<?php

namespace App\Models\FreegameGuide;

class GuideFaqTmp extends FreegameGuide
{
    protected $table = 'guide_faq_tmp';

    public $timestamps = false;

    /**
     * Update guide_faq_tmp
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_faq_tmp
     * @param integer $guideAppId
     * @param string $previewHash
     * @return boolean
     */
    public function delByPreviewHash($guideAppId, $previewHash)
    {
        return self::where('guide_application_id', $guideAppId)
                ->where('preview_hash', $previewHash)->delete();
    }
}
