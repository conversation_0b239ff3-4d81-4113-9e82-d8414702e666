<?php

namespace App\Models\FreegameGuide;

class GuideContentsImage extends FreegameGuide
{
    protected $table = 'guide_contents_image';

    public $timestamps = true;

    /**
     * データID更新
     * @param array $imageIds
     * @param integer $clusterId
     * @return boolean
     */
    public function updateClusterId($imageIds, $clusterId)
    {
        return self::whereIn('id', $imageIds)
            ->update(['cluster_id' => $clusterId]);
    }

    /**
     * 削除(master_id抽出)
     * @param integer $masterId
     * @return boolean
     */
    public function deleteByMasterId($masterId)
    {
        return self::join('guide_contents_cluster as cluster', 'cluster.id', '=', 'cluster_id')
            ->where('cluster.master_id', $masterId)
            ->delete();
    }

    /**
     * 削除(cluster_id抽出)
     * @param mixed(array|integer) $clusterId
     * @return boolean
     */
    public function deleteByClusterId($clusterId)
    {
        if (is_array($clusterId)) {
            $query = self::whereIn('cluster_id', $clusterId);
        } else {
            $query = self::where('cluster_id', $clusterId);
        }
        return $query->delete();
    }

    /**
     * 取得(master_id抽出)
     * @param integer $masterId
     * @return boolean
     */
    public function getByMasterId($masterId)
    {
        return self::join('guide_contents_cluster as cluster', 'cluster.id', '=', 'cluster_id')
            ->join('guide_contents_master as master', 'master.id', '=', 'cluster.master_id')
            ->select([
                'guide_contents_image.id',
                'image',
                'master.guide_application_id',
            ])
            ->where('cluster.master_id', $masterId)
            ->get();
    }

    /**
     * 取得(cluster_id抽出)
     * @param mixed(array|integer) $clusterId
     * @return boolean
     */
    public function getByClusterId($clusterId)
    {
        $query = self::join('guide_contents_cluster as cluster', 'cluster.id', '=', 'cluster_id')
            ->join('guide_contents_master as master', 'master.id', '=', 'cluster.master_id')
            ->select([
                'guide_contents_image.id',
                'image',
                'master.guide_application_id',
            ]);

        if (is_array($clusterId)) {
            $query = $query->whereIn('cluster_id', $clusterId);
        } else {
            $query = $query->where('cluster_id', $clusterId);
        }
        return $query->get();
    }
}
