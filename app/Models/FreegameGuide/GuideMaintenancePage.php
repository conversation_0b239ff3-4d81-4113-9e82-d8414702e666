<?php

namespace App\Models\FreegameGuide;

class GuideMaintenancePage extends FreegameGuide
{
    protected $table = 'guide_maintenance_page';

    public $timestamps = false;

    /**
     * Get content by guide_maintenance_page with guide_page
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        $query =  self::from('guide_maintenance_page AS mente')
            ->join('guide_page AS page', 'mente.guide_page_id', '=', 'page.id')
            ->select(
                'mente.id AS id',
                'mente.guide_application_id AS guide_application_id',
                'mente.guide_page_id AS guide_page_id',
                'page.page_name AS page_name',
                'mente.is_internal_release AS is_internal_release',
                'mente.start_datetime AS start_datetime',
                'mente.end_datetime AS end_datetime'
            )
            ->where('end_datetime', '>', timestamp_to_sqldate(now_stamp()))
            ->where('mente.guide_application_id', $guideAppId);

        return $query->orderBy('start_datetime', 'asc')->get();
    }

    /**
     * Get content by guide_maintenance_page with guide_page
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneByid($id, $guideAppId)
    {
        $query =  self::from('guide_maintenance_page AS mente')
            ->join('guide_page AS page', 'mente.guide_page_id', '=', 'page.id')
            ->select(
                'mente.id AS id',
                'mente.guide_application_id AS guide_application_id',
                'mente.guide_page_id AS guide_page_id',
                'page.page_name AS page_name',
                'mente.is_internal_release AS is_internal_release',
                'mente.start_datetime AS start_datetime',
                'mente.end_datetime AS end_datetime'
            );

        return $query->where('mente.id', $id)->where('mente.guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_maintenance_page
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_maintenance_page
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
