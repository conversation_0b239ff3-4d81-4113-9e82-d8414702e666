<?php

namespace App\Models\FreegameGuide;

class GuideRotatingBanner extends FreegameGuide
{
    protected $table = 'guide_rotating_banner';

    public $timestamps = false;

    /**
     * Get content by guide_rotating_banner
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->get();
    }

    /**
     * Get content by guide_rotating_banner with guide_banner
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppIdJoinGuideBanner($guideAppId)
    {
        $query = self::from('guide_rotating_banner AS rotating')
            ->join('guide_banner AS banner', 'rotating.guide_banner_id', '=', 'banner.id')
            ->select(
                'rotating.id AS id',
                'rotating.guide_banner_id AS guide_banner_id',
                'rotating.priority AS priority',
                'banner.name AS name',
                'banner.image AS image',
                'banner.link AS link',
                'banner.start_datetime AS start_datetime',
                'banner.end_datetime AS end_datetime'
            );

        return $query->where('rotating.guide_application_id', $guideAppId)->get();
    }

    /**
     * Update guide_rotating_banner
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_rotating_banner
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * Delete guide_rotating_banner
     * @param integer $bannerId
     * @return boolean
     */
    public function delByGuideBannerId($bannerId)
    {
        return self::where('guide_banner_id', $bannerId)->delete();
    }
}
