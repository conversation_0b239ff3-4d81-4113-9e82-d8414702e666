<?php

namespace App\Models\FreegameGuide;

class GuidePrivacyPolicy extends FreegameGuide
{
    protected $table = 'guide_privacy_policy';

    protected $fillable = [
        'guide_application_id',
        'policy',
    ];

    public $timestamps = false;

    /**
     * Get guide_privacy_policy by guide_application_id
     * @param integer $guideAppId
     * @return GuidePrivacyPolicy
     */
    public function getOneByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->first();
    }
}
