<?php
namespace App\Models\FreegameGuide;

/**
 * FAQカテゴリグループテーブル
 */
class GuideFaqCategoryGroup extends FreegameGuide
{
    protected $table = 'guide_faq_category_group';

    protected $guarded = ['id'];

    public $timestamps = false;

    /**
     * Get guide_faq_category_group List
     * @param  array  $condition
     * @return object
     */
    public function getList($condition = [])
    {
        $query = self::query();

        if (! empty($condition['guide_application_id'])) {
            $query = $query->where('guide_application_id', $condition['guide_application_id']);
        }

        $query = $query->orderBy('id', 'asc');

        return $query->get();
    }
}
