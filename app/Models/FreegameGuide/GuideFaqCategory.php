<?php

namespace App\Models\FreegameGuide;

class GuideFaqCategory extends FreegameGuide
{
    protected $table = 'guide_faq_category';

    public $timestamps = false;

    /**
     * Get content by guide_faq_category
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
            return self::where('guide_application_id', $guideAppId)
                ->orderBy('priority', 'asc')->orderBy('id', 'asc')->get();
    }

    /**
     * Get content
     * @param  array $condition
     * @return array
     */
    public function getList($condition = [])
    {
        $query = self::select();

        if (!empty($condition['guide_application_id'])) {
            $query = $query->where('guide_application_id', $condition['guide_application_id']);
        }
        if (!empty($condition['guide_faq_category_group_id'])) {
            $query = $query->where('guide_faq_category_group_id', $condition['guide_faq_category_group_id']);
        }

        return $query->orderBy('priority', 'asc')->orderBy('id', 'asc')->get();
    }

    /**
     * Get content by guide_faq_category
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        return self::where('id', $id)->where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_faq_category
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_faq_category
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
