<?php

namespace App\Models\FreegameGuide;

class GuideNotificationCategory extends FreegameGuide
{
    protected $table = 'guide_notification_category';

    public $timestamps = false;

    /**
     * Get content by guide_notification_category
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
            return self::where('guide_application_id', $guideAppId)
                ->orderBy('id', 'asc')->get();
    }

    /**
     * Get content by guide_notification_category
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        return self::where('id', $id)->where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_notification_category
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_notification_category
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
