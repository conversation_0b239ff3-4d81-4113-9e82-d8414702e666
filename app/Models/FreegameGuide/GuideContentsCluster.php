<?php

namespace App\Models\FreegameGuide;

use DB;

class GuideContentsCluster extends FreegameGuide
{
    protected $table = 'guide_contents_cluster';

    public $timestamps = true;

    public function master()
    {
        return $this->belongsTo('App\Models\FreegameGuide\GuideContentsMaster', 'master_id');
    }

    public function template()
    {
        return $this->belongsTo('App\Models\FreegameGuide\GuideContentsTemplate', 'template_id');
    }

    public function values()
    {
        return $this->hasMany('App\Models\FreegameGuide\GuideContentsValue', 'cluster_id');
    }

    public function images()
    {
        return $this->hasMany('App\Models\FreegameGuide\GuideContentsImage', 'cluster_id');
    }

    /** 
     * コンテンツデータ更新
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {   
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * コンテンツデータ取得
     * @param integer $clusterId データID
     * @return array
     */
    public function getContentsCluster($clusterId)
    {
        return self::with('values')
            ->with('images')
            ->where('id', $clusterId)
            ->first();
    }

    /**
     * コンテンツデータ一覧取得
     * @param integer $masterId 取得対象マスターID
     * @param integer $limit 最大取得件数
     * @return LengthAwarePaginator
     */
    public function getByMasterId($masterId, $limit)
    {
        return self::with('master')
            ->with('template')
            ->with('values')
            ->where('master_id', $masterId)
            ->orderBy('order_no')
            ->paginate($limit);
    }

    /**
     * コンテンツデータ全削除
     * @param integer $masterId 削除対象マスターID
     * @return array
     */
    public function deleteByMasterId($masterId)
    {
        return self::where('master_id', $masterId)
            ->delete();
    }

    /**
     * 表示順リスト取得
     * @param integer $masterId 取得対象マスターID
     * @return array
     */
    public function getOrderList($masterId)
    {
        return self::select(['id', 'order_no', 'name'])
            ->where('master_id', $masterId)
            ->orderBy('order_no')
            ->get();
    }

    /**
     * 表示順繰り下げ
     * @param integer $masterId 更新対象マスターID
     * @param integer $from 開始位置
     * @param integer $to 終了位置
     * @return boolean
     */
    public function incrementOrder($masterId, $from, $to = null)
    {
        $query = self::where('master_id', $masterId)
            ->where('order_no', '>=', $from);

        if (!is_null($to)) {
            $query->where('order_no', '<', $to);
        }

        return $query->orderBy('order_no', 'desc')
            ->increment('order_no');
    }

    /**
     * 表示順繰り上げ
     * @param integer $masterId 更新対象マスターID
     * @param integer $from 開始位置
     * @param integer $to 終了位置
     * @return boolean
     */
    public function decrementOrder($masterId, $from, $to = null)
    {
        $query = self::where('master_id', $masterId)
            ->where('order_no', '>=', $from);

        if (!is_null($to)) {
            $query->where('order_no', '<', $to);
        }

        return $query->orderBy('order_no')
            ->decrement('order_no');
    }

    /**
     * 表示順設定
     * @param integer $masterId 更新対象データID
     * @param integer $orderNo 表示順
     * @return boolean
     */
    public function setOrderNo($id, $orderNo)
    {
        return self::where('id', $id)
            ->update(['order_no' => $orderNo]);
    }

    /**
     * 表示順最後尾取得
     * @param integer $masterId 取得対象マスターID
     * @return integer
     */
    public function getLastOrder($masterId)
    {
        return self::where('master_id', $masterId)
            ->max('order_no');
    }

    /**
     * データキーの存在確認
     * @param integer $masterId 検索対象マスターID
     * @param string $clusterKey データキー
     * @return boolean
     */
    public function existsClusterKey($masterId, $clusterKey)
    {
        return self::where('master_id', $masterId)
            ->where('cluster_key', $clusterKey)
            ->exists();
    }

    /**
     * 表示ステータス切替
     * @param integer $id 更新対象データID
     * @return boolean
     */
    public function switchViewStatus($id)
    {
        return self::where('id', $id)
            ->update(['view_flag' => DB::raw("(CASE view_flag WHEN 0 THEN 1 ELSE 0 END)")]);
    }
}
