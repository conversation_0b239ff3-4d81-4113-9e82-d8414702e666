<?php

namespace App\Models\FreegameGuide;

class GuideForm extends FreegameGuide
{
    protected $table = 'guide_form';

    public $timestamps = false;

    /**
     * Get content by guide_form
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->orderBy('priority', 'asc')->get();
    }

    /**
     * Check empty by guide_form
     * @param  integer $formId
     * @param  integer $guideAppId
     * @return array
     */
    public function isEmpty($formId, $guideAppId)
    {
        $query = self::where('id', $formId)->where('guide_application_id', $guideAppId);
        return empty($query->first());
    }

    /**
     * Update guide_form
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_form
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
