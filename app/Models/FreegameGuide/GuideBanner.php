<?php

namespace App\Models\FreegameGuide;

class GuideBanner extends FreegameGuide
{
    protected $table = 'guide_banner';

    public $timestamps = false;

    /**
     * Get content by guide_banner
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->get();
    }

    /**
     * Get guide_banner by id
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOne($id, $guideAppId)
    {
        return self::where('id', $id)->where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_banner
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_banner
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
