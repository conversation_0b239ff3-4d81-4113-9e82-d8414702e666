<?php

namespace App\Models\FreegameGuide;

class GuideApplication extends FreegameGuide
{
    protected $table = 'guide_application';

    public $timestamps = false;

    /**
     * Get content by guide_application
     * @return array
     */
    public function getListAll()
    {
        return self::orderBy('id', 'asc')->get();
    }

    /**
     * Get content by guide_application
     * @param array $idLists
     * @return array
     */
    public function getListByIdLists($idLists)
    {
        return self::whereIn('id', $idLists)
            ->orderBy('id', 'asc')->get();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * Update guide_application
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }
}
