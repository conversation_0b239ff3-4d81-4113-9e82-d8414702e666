<?php

namespace App\Models\FreegameGuide;

class GuideNotificationTagRef extends FreegameGuide
{
    protected $table = 'guide_notification_tag_ref';

    public $timestamps = true;

    /**
     * お知らせ削除
     * @param integer $notificationId 削除対象お知らせID
     * @return boolean
     */
    public function deleteByNotificationId($notificationId)
    {
        return self::where('notification_id', $notificationId)->delete();
    }

    /**
     * お知らせタグ削除
     * @param integer $tagId 削除対象タグID
     * @return boolean
     */
    public function deleteByTagId($tagId)
    {
        return self::where('tag_id', $tagId)->delete();
    }
}
