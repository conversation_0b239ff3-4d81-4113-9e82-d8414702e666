<?php

namespace App\Models\FreegameGuide;

class GuideFaq extends FreegameGuide
{
    protected $table = 'guide_faq';

    public $timestamps = false;

    /**
     * Get content by guide_faq
     * @param array $condition
     * @return array
     */
    public function getList($condition)
    {
        $perPage = $condition['perPage'];
        $guideAppId = $condition['guideAppId'];
        $faqCategoryId = $condition['faqCategoryId'];

        $query = self::join(
            'guide_faq_category AS category',
            'guide_faq.guide_faq_category_id',
            '=',
            'category.id'
        )->join(
            'guide_application AS app',
            'guide_faq.guide_application_id',
            '=',
            'app.id'
        )->select(
            'guide_faq.id AS id',
            'guide_faq.question AS question',
            'guide_faq.answer AS answer',
            'guide_faq.is_priority AS is_priority',
            'guide_faq.priority AS priority',
            'guide_faq.priority_in_category AS priority_in_category',
            'guide_faq.view_status AS view_status',
            'category.name AS name',
            'app.domain AS domain'
        );

        $query->where('guide_faq.guide_application_id', $guideAppId);
        if (empty($faqCategoryId) === false) {
            $query
                ->where('guide_faq.guide_faq_category_id', $faqCategoryId);
        }
        $query->orderByRaw('guide_faq.priority is null asc')
            ->orderBy('guide_faq.priority', 'asc')
            ->orderBy('guide_faq.stamp', 'asc');
        return $query->paginate($perPage);
    }

    /**
     * Get content by guide_faq priority list
     * @param  array $condition
     * @return array
     */
    public function getListPriority($condition = [])
    {
        // $condition = []
        $query = self::join(
            'guide_faq_category AS category',
            'guide_faq.guide_faq_category_id',
            '=',
            'category.id'
        )->select(
            'guide_faq.id AS id',
            'guide_faq.question AS question',
            'guide_faq.answer AS answer',
            'guide_faq.is_priority AS is_priority',
            'guide_faq.priority AS priority',
            'guide_faq.priority_in_category AS priority_in_category',
            'guide_faq.view_status AS view_status',
            'category.name AS name'
        );

        $query = $query->where('guide_faq.is_priority', 1);

        if (!empty($condition['guide_application_id'])) {
            $query = $query->where('guide_faq.guide_application_id', $condition['guide_application_id']);
        }
        if (!empty($condition['guide_faq_category_group_id'])) {
            $query = $query->where('category.guide_faq_category_group_id', $condition['guide_faq_category_group_id']);
        }

        $query->orderBy('guide_faq.priority', 'asc')->orderBy('guide_faq.priority', '=0');
        return $query->get();
    }

    /**
     * Get content by guide_faq priority list
     * @param integer $guideAppId
     * @param integer $categoryId
     * @return array
     */
    public function getListPriorityCategory($guideAppId, $categoryId)
    {
        $query = self::where('guide_application_id', $guideAppId)
            ->where('guide_faq_category_id', $categoryId);
        $query->orderBy('priority_in_category', 'asc')->orderBy('priority_in_category', '=0');
        return $query->get();
    }

    /**
     * Get content by guide_faq
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        return self::where('id', $id)->where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Get count by guide_faq
     * @param integer $guideAppId
     * @param integer $guideFaqCategoryId
     * @return array
     */
    public function getCountByGuideAppIdAndCategoryId($guideAppId, $guideFaqCategoryId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('guide_faq_category_id', $guideFaqCategoryId)->count();
    }

    /**
     * Update guide_faq
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_faq
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * FAQプライオリティカウントアップ
     * @param int $guideAppId
     * @return void
     */
    public function editCountUpPriority($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('is_priority', 1)->increment('priority');
    }
}
