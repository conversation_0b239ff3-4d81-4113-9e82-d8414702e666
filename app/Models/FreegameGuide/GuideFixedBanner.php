<?php

namespace App\Models\FreegameGuide;

class GuideFixedBanner extends FreegameGuide
{
    protected $table = 'guide_fixed_banner';

    public $timestamps = false;

    /**
     * Get content by guide_fixed_banner
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->get();
    }

    /**
     * Get content by guide_fixed_banner with guide_banner
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppIdJoinGuideBanner($guideAppId)
    {
        $query = self::from('guide_fixed_banner AS fixed')
            ->join('guide_banner AS banner', 'fixed.guide_banner_id', '=', 'banner.id')
            ->select(
                'fixed.id AS id',
                'fixed.guide_banner_id AS guide_banner_id',
                'fixed.placement AS placement',
                'fixed.view_status AS view_status',
                'banner.name AS name',
                'banner.image AS image',
                'banner.link AS link',
                'banner.start_datetime AS start_datetime',
                'banner.end_datetime AS end_datetime'
            );

        return $query->where('fixed.guide_application_id', $guideAppId)->get();
    }

    /**
     * Get content by guide_fixed_banner with guide_banner
     * @param integer $guideAppId
     * @param integer $placement
     * @return array
     */
    public function getOneByGuideAppIdAndPlacementJoinGuideBanner($guideAppId, $placement)
    {
        $query = self::from('guide_fixed_banner AS fixed')
            ->join('guide_banner AS banner', 'fixed.guide_banner_id', '=', 'banner.id')
            ->select(
                'fixed.id AS id',
                'fixed.guide_banner_id AS guide_banner_id',
                'fixed.placement AS placement',
                'fixed.view_status AS view_status',
                'banner.name AS name',
                'banner.image AS image',
                'banner.link AS link',
                'banner.start_datetime AS start_datetime',
                'banner.end_datetime AS end_datetime'
            );

        return $query->where('fixed.guide_application_id', $guideAppId)
            ->where('fixed.placement', $placement)->first();
    }

    /**
     * Update guide_fixed_banner
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_fixed_banner
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * Delete guide_fixed_banner
     * @param integer $bannerId
     * @return boolean
     */
    public function delByGuideBannerId($bannerId)
    {
        return self::where('guide_banner_id', $bannerId)->delete();
    }
}
