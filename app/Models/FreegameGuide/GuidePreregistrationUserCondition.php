<?php

namespace App\Models\FreegameGuide;

class GuidePreregistrationUserCondition extends FreegameGuide
{
    protected $table = 'guide_preregistration_user_condition';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * Get content by guide_preregistration_user_condition
     * @param integer $guideAppId
     * @return array
     */
    public function getOneByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_preregistration_user_condition
     * @param array $updateData
     * @param integer $guideAppId
     * @return boolean
     */
    public function edit($updateData, $guideAppId)
    {
        return self::updateOrCreate([
            'guide_application_id' => $guideAppId
        ], $updateData);
    }
}
