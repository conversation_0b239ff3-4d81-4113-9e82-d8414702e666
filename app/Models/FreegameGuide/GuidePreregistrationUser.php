<?php

namespace App\Models\FreegameGuide;

class GuidePreregistrationUser extends FreegameGuide
{
    protected $table = 'guide_preregistration_user';

    public $timestamps = false;

    /**
     * Get content by guide_preregistration_user
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->get();
    }

    /**
     * Get count by guide_preregistration_user
     * @param integer $guideAppId
     * @return array
     */
    public function getCountByUser($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->count('user_id');
    }
}
