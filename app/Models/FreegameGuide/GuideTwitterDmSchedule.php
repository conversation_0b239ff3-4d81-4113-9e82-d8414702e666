<?php

namespace App\Models\FreegameGuide;

class GuideTwitterDmSchedule extends FreegameGuide
{
    protected $table = 'guide_twitter_dm_schedule';

    public $timestamps = false;

    /**
     * Get content by guide_twitter_dm_schedule
     * @param array $condition
     * @return array
     */
    public function getList($condition)
    {
        $perPage = $condition['perPage'];
        $guideAppId = $condition['guideAppId'];

        $query = self::where('guide_application_id', $guideAppId);
        return $query->orderBy('send_at', 'desc')->paginate($perPage);
    }

    /**
     * Get guide_twitter_dm_schedule by id
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        return self::where('id', $id)->where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_twitter_dm_schedule
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_twitter_dm_schedule
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
