<?php

namespace App\Models\FreegameGuide;

class GuideOuterIp extends FreegameGuide
{
    protected $table = 'guide_outer_ip';

    public $timestamps = false;

    /**
     * Get content by guide_outer_ip
     * @param integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        $query =  self::select(
            'id',
            'guide_application_id',
            'name'
        )->selectRaw('INET_NTOA(ip_address) AS ip_address');

        return $query->where('guide_application_id', $guideAppId)->get();
    }

    /**
     * Get content by guide_outer_ip
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneByid($id, $guideAppId)
    {
        $query =  self::select(
            'id',
            'guide_application_id',
            'name'
        )->selectRaw('INET_NTOA(ip_address) AS ip_address');

        return $query->where('id', $id)->where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Insert guide_outer_ip
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function createOuterIp($insertData)
    {
        $insertData['ip_address'] = ip2long($insertData['ip_address']);
        return self::insert($insertData);
    }

    /**
     * Update guide_outer_ip
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        $updateData['ip_address'] = ip2long($updateData['ip_address']);
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_outer_ip
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }
}
