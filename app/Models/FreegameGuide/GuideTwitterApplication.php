<?php

namespace App\Models\FreegameGuide;

class GuideTwitterApplication extends FreegameGuide
{
    protected $table = 'guide_twitter_application';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * Get content by guide_twitter_application
     * @param integer $guideAppId
     * @return array
     */
    public function getOneByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->first();
    }

    /**
     * Update guide_twitter_application
     * @param array $updateData
     * @param integer $guideAppId
     * @return boolean
     */
    public function edit($updateData, $guideAppId)
    {
        return self::updateOrCreate([
            'guide_application_id' => $guideAppId
        ], $updateData);
    }
}
