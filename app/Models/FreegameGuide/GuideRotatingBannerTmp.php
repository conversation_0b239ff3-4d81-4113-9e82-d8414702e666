<?php

namespace App\Models\FreegameGuide;

class GuideRotatingBannerTmp extends FreegameGuide
{
    protected $table = 'guide_rotating_banner_tmp';

    public $timestamps = false;

    /**
     * Delete guide_rotating_banner_tmp
     * @param integer $guideAppId
     * @param string $previewHash
     * @return boolean
     */
    public function delByPreviewHash($guideAppId, $previewHash)
    {
        return self::where('guide_application_id', $guideAppId)
                ->where('preview_hash', $previewHash)->delete();
    }
}
