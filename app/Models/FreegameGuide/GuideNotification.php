<?php

namespace App\Models\FreegameGuide;

class GuideNotification extends FreegameGuide
{
    protected $table = 'guide_notification';

    public $timestamps = false;

    public function tag()
    {
        return $this->hasMany('App\Models\FreegameGuide\GuideNotificationTagRef', 'notification_id');
    }

    /**
     * Get content by guide_notification
     * @param array $condition
     * @return array
     */
    public function getList($condition)
    {
        $perPage = $condition['perPage'];
        $guideAppId = $condition['guideAppId'];
        $searchWord = $condition['searchWord'];
        $searchPriority = intval($condition['searchPriority']);

        $query = self::join(
            'guide_notification_category AS category',
            'guide_notification.guide_notification_category_id',
            '=',
            'category.id'
        )->join(
            'guide_application AS app',
            'guide_notification.guide_application_id',
            '=',
            'app.id'
        )->select(
            'guide_notification.id AS id',
            'guide_notification.title AS title',
            'guide_notification.body AS body',
            'guide_notification.device AS device',
            'guide_notification.is_priority AS is_priority',
            'guide_notification.priority AS priority',
            'guide_notification.view_status AS view_status',
            'guide_notification.published_datetime AS published_datetime',
            'category.name AS name',
            'app.domain AS domain'
        )
        ->with(['tag' => function($query) {
            $query->join('guide_notification_tag AS tag', 'tag_id', '=', 'tag.id')
                ->orderBy('tag.order_no');
        }]);

        $query->where('guide_notification.guide_application_id', $guideAppId);
        if (empty($searchWord) === false) {
            $query->where(function($subQuery) use ($searchWord) {
                $subQuery->where('title', 'like', '%' . $searchWord . '%')
                    ->orWhere('body', 'like', '%' . $searchWord . '%');
            });
        }
        if ($searchPriority === 1) {
            $query->where('is_priority', $searchPriority);
        }
        $query->orderBy('guide_notification.published_datetime', 'desc');
        return $query->paginate($perPage);
    }

    /**
     * Get content by guide_notification priority list
     * @param integer $guideAppId
     * @return array
     */
    public function getListPriority($guideAppId)
    {
        $query = self::join(
            'guide_notification_category AS category',
            'guide_notification.guide_notification_category_id',
            '=',
            'category.id'
        )->select(
            'guide_notification.id AS id',
            'guide_notification.title AS title',
            'guide_notification.body AS body',
            'guide_notification.device AS device',
            'guide_notification.is_priority AS is_priority',
            'guide_notification.priority AS priority',
            'guide_notification.view_status AS view_status',
            'guide_notification.published_datetime AS published_datetime',
            'category.name AS name'
        );

        $query->where('guide_notification.is_priority', 1)
            ->where('guide_notification.guide_application_id', $guideAppId);

        $query->orderBy('guide_notification.priority', 'asc')->orderBy('guide_notification.priority', '=0');
        return $query->get();
    }

    /**
     * Get content by guide_notification
     * @param integer $id
     * @param integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        return self::where('id', $id)
            ->where('guide_application_id', $guideAppId)
            ->with('tag')
            ->first();
    }

    /**
     * Get count by guide_notification
     * @param integer $guideAppId
     * @param integer $guideNoticCategoryId
     * @return array
     */
    public function getCountByGuideAppIdAndCategoryId($guideAppId, $guideNoticCategoryId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('guide_notification_category_id', $guideNoticCategoryId)->count();
    }

    /**
     * Update guide_notification
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_notification
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * お知らせプライオリティカウントアップ
     * @param int $guideAppId
     * @return void
     */
    public function editCountUpPriority($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('is_priority', 1)->increment('priority');
    }
}
