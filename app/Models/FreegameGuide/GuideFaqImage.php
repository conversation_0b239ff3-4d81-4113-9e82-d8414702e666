<?php

namespace App\Models\FreegameGuide;

class GuideFaqImage extends FreegameGuide
{
    protected $table = 'guide_faq_image';

    public $timestamps = false;

    /**
     * Get content by guide_faq_image
     * @param integer $faqId
     * @return array
     */
    public function getListByFaqId($faqId)
    {
        return self::where('guide_faq_id', $faqId)->get();
    }

    /**
     * Get content by guide_faq_image  after hour
     *
     * @return array
     */
    public function getListByAfterHour()
    {
        return self::where('guide_faq_id', 0)
            ->where('stamp', '<=', timestamp_to_sqldate(now_stamp() - 3600))
            ->get();
    }

    /**
     * Get content by guide_faq_image
     * @param integer $guideAppId
     * @param integer $guideFaqId
     * @return array
     */
    public function getListByWithInHour($guideAppId, $guideFaqId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('guide_faq_id', $guideFaqId)
            ->where('stamp', '>', timestamp_to_sqldate(now_stamp() - 3600))
            ->get();
    }

    /**
     * Update guide_faq_image
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * Delete guide_faq_image
     * @param integer $id
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * Delete guide_faq_image By guide_application_id AND guide_faq_id
     * @param integer $guideAppId
     * @param integer $guideFaqId
     * @return boolean
     */
    public function delByGuideAppIdAndGuideFaqId($guideAppId, $guideFaqId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('guide_faq_id', $guideFaqId)->delete();
    }

    /**
     * Delete guide_faq_image By idlist
     * @param array $idList
     * @return array
     */
    public function delByIdList($idList)
    {
        return self::whereIn('id', $idList)->delete();
    }
}
