<?php

namespace App\Models\FreegameGuide;

class GuidePreregistrationTwitterUser extends FreegameGuide
{
    protected $table = 'guide_preregistration_twitter_user';

    public $timestamps = false;

    /**
     * Get count by guide_preregistration_twitter_user
     * @param integer $guideAppId
     * @return array
     */
    public function getCountByUser($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)->count('twitter_user_id');
    }
}
