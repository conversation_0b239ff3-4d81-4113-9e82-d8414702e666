<?php

namespace App\Models\FreegameGuide;

class GuideFixedBannerTmp extends FreegameGuide
{
    protected $table = 'guide_fixed_banner_tmp';

    public $timestamps = false;

    /**
     * Delete guide_fixed_banner_tmp
     * @param integer $guideAppId
     * @param string $previewHash
     * @return boolean
     */
    public function delByPreviewHash($guideAppId, $previewHash)
    {
        return self::where('guide_application_id', $guideAppId)
                ->where('preview_hash', $previewHash)->delete();
    }
}
