<?php

namespace App\Models\FreegameGuide;

class GuideContentsTemplate extends FreegameGuide
{
    protected $table = 'guide_contents_template';

    public $timestamps = true;

    /**
     * 管理項目一覧取得
     * @param integer $masterId 取得対象マスターID
     * @return array
     */
    public function getByMasterId($masterId)
    {
        return self::where('master_id', $masterId)->get();
    }

    /**
     * 管理項目更新
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * 管理項目削除
     * @param array $ids 削除対象ID
     * @return array
     */
    public function deleteByIds($ids)
    {
        return self::whereIn('id', $ids)
            ->delete();
    }
}
