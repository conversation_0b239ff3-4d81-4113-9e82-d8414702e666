<?php

namespace App\Models\FreegameGuide;

class GuideContentsMaster extends FreegameGuide
{
    protected $table = 'guide_contents_master';

    public $timestamps = true;

    public function app()
    {
        return $this->belongsTo('App\Models\FreegameGuide\GuideApplication', 'guide_application_id');
    }

    public function templates()
    {
        return $this->hasMany('App\Models\FreegameGuide\GuideContentsTemplate', 'master_id');
    }

    /**
     * コンテンツマスター一覧取得
     * @param integer $guideAppId アプリID
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        return self::where('guide_application_id', $guideAppId)
            ->orderBy('group_name')
            ->orderBy('master_key')
            ->get();
    }

    /**
     * コンテンツマスター一覧検索
     * @param array $params 検索条件
     * @return array
     */
    public function search($params)
    {
        $data = self::select('id', 'name', 'master_key', 'group_name', 'description');

        if ($this->existsParam($params, 'guide_application_id')) {
            $data->where('guide_application_id', $params['guide_application_id']);
        }

        if ($this->existsParam($params, 'name')) {
            $data->where('name', 'LIKE', '%' . $params['name'] . '%');
        }

        if ($this->existsParam($params, 'master_key')) {
            $data->where('master_key', 'LIKE', '%' . $params['master_key'] . '%');
        }

        if ($this->existsParam($params, 'group_name')) {
            $data->where('group_name', 'LIKE', '%' . $params['group_name'] . '%');
        }

        if ($this->existsParam($params, 'description')) {
            $data->where('description', 'LIKE', '%' . $params['description'] . '%');
        }

        return $data->orderBy('group_name')
            ->orderBy('master_key')
            ->paginate($params['perPage']);
    }

    /**
     * 検索条件の検出
     * @param array $params
     * @param string $key
     * @return boolean
     */
    protected function existsParam($params, $key)
    {
        return isset($params[$key]) && $params[$key] != '';
    }

    /**
     * グループ一覧取得
     * @param integer $guideAppId アプリID
     * @return array
     */
    public function getGroupList($guideAppId)
    {
        return self::select('group_name')
            ->where('guide_application_id', $guideAppId)
            ->distinct()
            ->orderBy('group_name')
            ->get();
    }

    /**
     * コンテンツマスター取得
     * @param integer $masterId 取得対象マスターID
     * @return array
     */
    public function getMasterData($masterId)
    {
        return self::with('app')
            ->with(['templates' => function($query){
                $query->orderBy('id');
            }])
            ->where('id', $masterId)
            ->first();
    }

    /**
     * コンテンツマスター更新
     * @param array $updateData
     * @param integer $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * コンテンツマスター削除
     * @param integer $id 削除対象ID
     * @return boolean
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * マスター名存在確認
     * @param integer $guideAppId アプリID
     * @param string $masterName マスター名
     * @param string $group グループ
     * @return boolean
     */
    public function existsName($guideAppId, $masterName, $group, $ignoreId = null)
    {
        $query =  self::where('guide_application_id', $guideAppId)
            ->where('name', $masterName)
            ->where('group_name', $group);
        if ($ignoreId) {
            $query->where('id', '<>', $ignoreId);
        }
        return $query->exists();
    }

    /**
     * マスターキー存在確認
     * @param integer $guideAppId アプリID
     * @param string $masterKey マスターキー
     * @return boolean
     */
    public function existsMasterKey($guideAppId, $masterKey)
    {
        return self::where('guide_application_id', $guideAppId)
            ->where('master_key', $masterKey)
            ->exists();
    }
}
