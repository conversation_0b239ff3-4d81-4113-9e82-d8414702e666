<?php
namespace App\Models\FreegameCommunity;

/**
 * コミュニティ
 */
class Community extends FreegameCommunity
{
    protected $table = 'community';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;
    
    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('id', $id)->first();
    }

    /**
     * get list
     *
     * @param  array   $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['id'])) {
            $query = $query->where('id', $params['id']);
        }
        if (!empty($params['type'])) {
            $query = $query->where('type', $params['type']);
        }
        if (!empty($params['app_id'])) {
            if (is_array($params['app_id'])) {
                $query = $query->whereIn('app_id', $params['app_id']);
            } else {
                $query = $query->where('app_id', $params['app_id']);
            }
        }
        if (!empty($params['status'])) {
            $query = $query->where('status', $params['status']);
        }
        if (!empty($params['manager_user_id'])) {
            $query = $query->where('manager_user_id', $params['manager_user_id']);
        }

        return $query->orderBy('update_date', 'desc')->get();
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insertGetId($data);
    }

    /**
     * update
     *
     * @param  string $id
     * @param  array  $data
     *
     * @return object
     */
    public function edit($id, $data)
    {
        return self::where('id', $id)->update($data);
    }

    public function getOfficial($condition = [])
    {
        $query = self::query();
        if (! empty($condition['app_id'])) {
            $query = $query->where('app_id', $condition['app_id']);
        }
        return $query->where('type', 'official')->first();
    }
}
