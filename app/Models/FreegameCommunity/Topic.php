<?php
namespace App\Models\FreegameCommunity;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use DB;

/**
 * トピック
 */
class Topic extends FreegameCommunity
{
    use HybridRelations;

    protected $table = 'topic';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get data by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select('topic.*', 'community.title as community_title')
               ->join('community', 'topic.community_id', '=', 'community.id')
               ->where('topic.id', $id)
               ->first();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object $result
     */
    public function getList($params)
    {
        $query = self::select(
            'topic.*',
            'community.title as community_title',
            'community.manager_user_id',
            'community.vice_manager_user_id'
        )->join('community', 'topic.community_id', '=', 'community.id');

        if (!empty($params['app_id'])) {
            if (is_array($params['app_id'])) {
                $query = $query->whereIn('community.app_id', $params['app_id']);
            } else {
                $query = $query->where('community.app_id', $params['app_id']);
            }
        }
        if (!empty($params['id'])) {
            $query = $query->where('topic.id', $params['id']);
        }
        if (!empty($params['community_id'])) {
            $query = $query->where('topic.community_id', $params['community_id']);
        }
        if (!empty($params['title'])) {
            $query = $query->where('topic.title', 'like', '%'.$params['title'].'%');
        }
        if (!empty($params['status'])) {
            $query = $query->where('topic.status', $params['status']);
        }
        if (!empty($params['user_id'])) {
            $whereRaw = '(topic.create_user_id = ? OR topic.update_user_id = ? OR ';
            $whereRaw .= 'community.manager_user_id = ? OR community.vice_manager_user_id = ?)';
            $query = $query->whereRaw(
                $whereRaw,
                [$params['user_id'], $params['user_id'], $params['user_id'], $params['user_id']]
            );
        }

        $query = $query->orderBy('topic.id', 'desc');

        if (!empty($params['offset'])) {
            $result = $query->paginate($params['offset']);
        } else {
            $result = $query->get();
        }

        return $result;
    }

    /**
     * get list no topic autocreation
     *
     * @param  array  $params
     *
     * @return object $result
     */
    public function getListNoTopicAutocreation($params)
    {
        $query = self::select('topic.*', 'community.title as community_title')
                 ->join('community', 'topic.community_id', '=', 'community.id')
                 ->leftjoin('topic as nextTopic', function ($q) use ($params) {
                     $q->on('topic.next_id', '=', 'nextTopic.id')
                     ->where('nextTopic.status', '=', $params['status']);
                 })
                 ->leftjoin('topic_autocreation', function ($q) use ($params) {
                     $q->on('community.id', '=', 'topic_autocreation.community_id')
                     ->on('topic.id', '=', 'topic_autocreation.last_topic_id')
                     ->where('topic_autocreation.status', '=', $params['status']);
                 });

        $query = $query->where('community.type', $params['type']);
        // OLGPFCOMMUMISSION-176：自動生成トピックの公開以外のステータスを受け取れるように修正
        // $query = $query->where('community.status', $params['status']);
        $query = $query->where('topic.status', $params['status']);
        $query = $query->whereNull('nextTopic.id');
        $query = $query->whereNull('topic_autocreation.id');

        if (!empty($params['community_id'])) {
            if (is_array($params['community_id'])) {
                $query = $query->whereIn('community.id', $params['community_id']);
            } else {
                $query = $query->where('community.id', $params['community_id']);
            }
        }

        if (!empty($params['app_id'])) {
            if (is_array($params['app_id'])) {
                $query = $query->whereIn('community.app_id', $params['app_id']);
            } else {
                $query = $query->where('community.app_id', $params['app_id']);
            }
        }
        if (!empty($params['order'])) {
            foreach ($params['order'] as $order) {
                $query = $query->orderBy($order[0], $order[1]);
            }
        }

        return $query->get();
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * update
     *
     * @param  string $id
     * @param  array  $data
     *
     * @return object
     */
    public function edit($id, $data)
    {
        return self::where('id', $id)->update($data);
    }

    /**
     * delete
     *
     * @param  integer $id
     *
     * @return object
     */
    public function del($id)
    {
        return self::where('id', $id)->delete();
    }

    /**
     * Get Topic By Search Title
     * @param array $params
     * @return App\Models\FreegameCommunity\Topic
     */
    public function getTopicBySearchTitle($params)
    {
        $query = self::select('id', 'title');
        if (array_has($params, 's_topic_id')) {
            $query->where('id', $params['s_topic_id']);
        }
        if (array_has($params, 's_topic_name')) {
            $query->where('title', 'LIKE', '%'.$params['s_topic_name'].'%');
        }
        return $query;
    }
}
