<?php
namespace App\Models\FreegameCommunity;

/**
 * 固定トピック
 */
class FixTopicRef extends FreegameCommunity
{
    protected $table = 'fix_topic_ref';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $condition
     *
     * @return object $result
     */
    public function getList($condition)
    {
        $query = self::select('fix_topic_ref.*', 't.title')
        ->leftjoin('topic AS t', function ($join) {
            $join->on('fix_topic_ref.community_id', '=', 't.community_id')
            ->on('fix_topic_ref.topic_id', '=', 't.id');
        });

        if (isset($condition['community_id'])) {
            $query = $query->where('fix_topic_ref.community_id', $condition['community_id']);
        }

        return $query->orderBy('fix_topic_ref.disp_no')->get();
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * delete by community_id
     *
     * @param  integer $communityId
     *
     * @return object
     */
    public function delByCommunityId($communityId)
    {
        return self::where('community_id', $communityId)->delete();
    }
}
