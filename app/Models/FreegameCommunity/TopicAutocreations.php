<?php
namespace App\Models\FreegameCommunity;

/**
 * 自動生成トピック
 */
class TopicAutocreations extends FreegameCommunity
{
    protected $table = 'topic_autocreation';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get one
     *
     * @param  array  $params
     *
     * @return object $result
     */
    public function getOneByConditions($params)
    {
        $query = self::select();

        if (!empty($params['community_id'])) {
            $query = $query->where('community_id', $params['community_id']);
        }
        if (!empty($params['last_topic_id'])) {
            $query = $query->where('last_topic_id', $params['last_topic_id']);
        }
        if (!empty($params['status'])) {
            $query = $query->where('status', $params['status']);
        }
        if (!empty($params['id'])) {
            $query = $query->where('id', $params['id']);
        }

        return $query->first();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object $result
     */
    public function getList($params)
    {
        $query = self::select(
            'topic_autocreation.*',
            'topic.id as topic_id',
            'topic.title as topic_title',
            'community.id as community_id',
            'community.app_id',
            'community.title as community_title'
        )
            ->join('topic', 'topic_autocreation.last_topic_id', '=', 'topic.id')
            ->join('community', 'topic_autocreation.community_id', '=', 'community.id')
            ->where('community.type', '=', $params['type'])
            ->where('topic.status', '=', $params['status'])
            ->where('topic_autocreation.status', '=', $params['status'])
            ->orderBy('community.title', 'asc')
            ->orderBy('topic.title', 'asc');

        if (!empty($params['app_id'])) {
            if (is_array($params['app_id'])) {
                $query = $query->whereIn('community.app_id', $params['app_id']);
            } else {
                $query = $query->where('community.app_id', $params['app_id']);
            }
        }
        if (!empty($params['community_id'])) {
            $query = $query->where('community.id', $params['community_id']);
        }
        if (!empty($params['topic_id'])) {
            $query = $query->where('topic_autocreation.id', $params['topic_id']);
        }
        if (!empty($params['topic_title'])) {
            $query = $query->where('topic.title', 'like', '%'.$params['topic_title'].'%');
        }

        if (!empty($params['offset'])) {
            $result = $query->paginate($params['offset']);
        } else {
            $result = $query->get();
        }

        return $result;
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * delete
     *
     * @param  integer $id
     *
     * @return object
     */
    public function del($id)
    {
        return self::where('id', $id)->update(['status' => 'deactive']);
    }
}
