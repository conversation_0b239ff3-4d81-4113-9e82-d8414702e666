<?php
namespace App\Models\FreegameCommunity;

/**
 * ブラックユーザ
 */
class BlackUser extends FreegameCommunity
{
    protected $table = 'black_user';

//    public $timestamps = false;
    
    /**
     * get data by user_id
     *
     * @param  integer $user_id
     *
     * @return object
     */
    public function getOne($user_id)
    {
        return self::select()->where('user_id', $user_id)->first();
    }

    /**
     * get list
     *
     * @return object
     */
    public function getList()
    {
        $query = self::select();

        return $query->orderBy('regist_date', 'desc')->get();
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function addByUserId($user_id)
    {
        $insert_data = [
            'user_id'      => $user_id,
            'regist_date'  => date('Y-m-d H:i:s'),
            'stamp'        => timestamp_to_sqldate(now_stamp())
        ];

        return self::insertGetId($insert_data);
    }

    /**
     * destory
     *
     * @param  string $user_id
     *
     * @return object
     */
    public function deleteByUserId($user_id)
    {
        return self::where('user_id', $user_id)->delete();
    }

}
