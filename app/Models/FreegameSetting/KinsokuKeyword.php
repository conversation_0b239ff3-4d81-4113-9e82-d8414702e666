<?php
namespace App\Models\FreegameSetting;

/**
 * 禁則キーワードテーブル
 */
class KinsokuKeyword extends FreegameSetting
{
    protected $table = 'kinsoku_keyword';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object
     */
    public function getList($params)
    {
        $query = self::select([
            'keyword'
        ]);

        if (!empty($params['level'])) {
            $query = $query->where('level', $params['level']);
        }

        return $query->get();
    }
}
