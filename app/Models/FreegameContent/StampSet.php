<?php
namespace App\Models\FreegameContent;

class StampSet extends FreegameContent
{
    protected $table   = 'stamp_set';

    protected $guarded = ['id'];

    public $timestamps = false;

//*********************************************************************************************************************
    /**
     * ID指定取得
     * @param  integer $id
     * @return array
     */
    public function getById($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->first();
    }

    /**
     * ID・デベロッパ指定取得
     * @param  integer $id
     * @param  integer $devId
     * @return array
     */
    public function getByIdAndDeveloper($id, $devId)
    {
        if (empty($id) || empty($devId)) {
            return false;
        }
        return self::where('id', $id)->where('developer_id', $devId)->first();
    }

    /**
     * 一覧取得
     * @param  array  $where
     * @param  string $order
     * @return array
     */
    public function getList($where = [], $order = 'id ASC')
    {
        $perPage = isset($where['perPage']) ? $where['perPage'] : null;
        $query = $this->getWhere($where);
        $query = $this->getSelect($query);
        if ($perPage) {
            return $query->orderByRaw('id DESC')->paginate($perPage);
        } else {
            return $query->orderByRaw($order)->get();
        }
    }


    /**
     * product_idからcontent_owner_idを取得する
     * @param int $pid  product_id
     * @return string   content_owner_id
     */
    public function getContentOwnerIdByProductId($pid)
    {
        if (empty($pid)) {
            return false;
        }
        $query = self::select('content_owner_id')->where('product_id', $pid);
        return $query->first()->content_owner_id;
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::create($param);
    }

    /**
     * 更新
     * @param  integer $id
     * @param  array   $param
     * @return boolean
     */
    public function edit($id, $param)
    {
        if (empty($id) || empty($param)) {
            return false;
        }
        return self::where('id', $id)->update($param);
    }

    /**
     * 削除
     * @param  integer $id
     * @return boolean
     */
    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

//*********************************************************************************************************************
    /**
     * 検索条件取得
     * @param  array $param
     * @return object
     */
    private function getWhere($param = [])
    {
        $query     = self::from($this->table);

        $stampId   = isset($param['id'])           ? $param['id']           : null;
        $contentId = isset($param['content_id'])   ? $param['content_id']   : null;
        $productId = isset($param['product_id'])   ? $param['product_id']   : null;
        $devId     = isset($param['developer_id']) ? $param['developer_id'] : null;
        $ownerId   = isset($param['owner_id'])     ? $param['owner_id']     : null;
        $makerId   = isset($param['maker_id'])     ? $param['maker_id']     : null;

        if (! empty($stampId)) {
            $query->where('id', $stampId);
        }

        if (! empty($contentId)) {
            $query->where('content_id', $contentId);
        }

        if (! empty($productId)) {
            $query->where('product_id', $productId);
        }

        if (! empty($devId)) {
            $query->where('developer_id', $devId);
        }

        if (! empty($ownerId)) {
            $query->where('owner_id', $ownerId);
        }

        if (! empty($makerId)) {
            $query->where('maker_id', $makerId);
        }
        return $query;
    }


    /**
     * owner_id等を除いたSELECTをクエリに加える
     * @param Builder $query
     * @return Builder $query
     */
    private function getSelect($query) {
        return $query->select(
            'id',
            'content_id',
            'developer_id',
            'is_default',
            'name',
            'description',
            'point',
            'rate',
            'begin',
            'end',
            'expire',
            'is_voice',
            'status',
            'main_image',
            'icon_image',
            'stamp',
            'is_deleted',
            'content_owner_id'
            );
    }



}
