<?php

namespace App\Models\FreegameSandbox;

class Application extends FreegameSandbox
{
    protected $table = 'application';

    public $timestamps = false;

    /**
     * idから1件のレコードを取得
     * @param  integer $id
     * @return array
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 一覧取得
     *
     * @param $developerIdList
     * @param $offset
     *
     * @return object
     */
    public function getList($developerIdList, $offset)
    {
        return
            self::select(['a.*', 'ad.*'])
            ->from($this->table . ' AS a')
            ->join('application_device AS ad', 'a.id', '=', 'ad.app_id')
            ->whereIn('a.developer_id', $developerIdList)
            ->where('ad.status', 'active')
            ->groupBy('a.id')
            ->orderBy('a.stamp', 'desc')
            ->paginate($offset);
    }

    /**
     * consumer_keyから1件のレコードを取得
     * @param  string $consumer_key
     * @return array
     */
    public function getOneByConsumerKey($consumer_key)
    {
        return self::where('consumer_key', $consumer_key)->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                'id' => $params['id'],
                'developer_id' => $params['developer_id'],
                'url' => $params['url'],
                'consumer_key' => $params['consumer_key'],
                'consumer_secret' => $params['consumer_secret'],
                'title' => $params['title'],
                'general' => $params['general'],
                'adult' => $params['adult'],
            )
        );
        return $result;
    }

    /**
     * Update application
     * @param array $updateData
     * @param array $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }

    /**
     * developer_idの配列からアプリのリストを取得
     *
     * @param $developerIdList
     *
     * @return object
     */
    public function getListByDeveloperId($developerIdList)
    {
        return
            self::select(['a.*', 'ad.*'])
            ->from($this->table . ' AS a')
            ->join('application_device AS ad', 'a.id', '=', 'ad.app_id')
            ->whereIn('a.developer_id', $developerIdList)
            ->where('ad.status', 'active')
            ->groupBy('a.id')
            ->orderBy('a.stamp', 'desc')
            ->get();
    }
}
