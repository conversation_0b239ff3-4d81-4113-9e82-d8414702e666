<?php

namespace App\Models\FreegameSandbox;

class ApplicationInstall extends FreegameSandbox
{
    protected $table = 'application_install';

    public $timestamps = false;

    /**
     * id(とstatus)を条件にして、対象のリストを取得
     *
     * @param  int $user_id
     * @param  string $status
     *
     * @return object
     */
    public function getListByUserId($uid, $status = null)
    {
        $query = self::from($this->table);
        $query = $query->where('user_id', $uid);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->get();
    }

    /**
     * Update application_install
     * @param array $updateData
     * @param array $id
     * @param array $app_id
     * @param array $status
     * @return boolean
     */
    public function edit($updateData, $uid, $app_id, $status = null)
    {
        $query = self::where('user_id', $uid);
        $query = $query->where('app_id', $app_id);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->update($updateData);
    }

    /**
     * 対象のレコード1件を取得
     *
     * @param  int $conditions
     * @param  int $app_id
     *
     * @return object
     */
    public function getOneByConditions($conditions)
    {
        $query = self::from($this->table);
        if (! empty($conditions['user_id'])) {
            $query = $query->where('user_id', $conditions['user_id']);
        }
        if (! empty($conditions['app_id'])) {
            $query = $query->where('app_id', $conditions['app_id']);
        }
        if (! empty($conditions['status'])) {
            $query = $query->where('status', $conditions['status']);
        }

        return $query->first();
    }

}
