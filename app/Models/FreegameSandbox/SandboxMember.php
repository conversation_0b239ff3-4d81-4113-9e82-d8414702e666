<?php

namespace App\Models\FreegameSandbox;

use DB;

class SandboxMember extends FreegameSandbox
{
    protected $table = 'sandbox_member';

    protected $hidden = [
        'password'
    ];

    public $timestamps = false;

    /**
     * パスワードをハッシュ化して格納する
     * @param string $value パスワード値
     */
    public function setPasswordAttribute($value)
    {
        // md5ハッシュ化
        $this->attributes['password'] = md5($value);
    }

    /**
     * idをキーにして1件レコード取得
     *
     * @param  int $id
     *
     * @return object
     */

    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 一覧取得
     *
     * @param  object $params
     *
     * @return object
     */
    public function getList($param)
    {
        $query = self::select('u.id', 'u.nickname', 'u.member_id', 'u.regist_date')
            ->from($this->table . ' AS sm')
            ->join('user AS u', 'sm.id', '=', 'u.member_id');
        if (! empty($param['developer_id'])) {
            $query = $query->where('sm.developer_id', $param['developer_id']);
        }
        if (! empty($param['status'])) {
            $query = $query->where('sm.status', $param['status']);
            $query = $query->where('u.status', $param['status']);
        }
        $query = $query->orderBy('u.regist_date', 'desc');
        return $query->paginate($param['offset']);
    }

    /**
     * 一覧取得（getListをソート機能用に修正）
     *
     * @param  object $params
     *
     * @return object
     */
    public function getListSort($param, $order='u.regist_date desc')
    {
        $query = self::select('u.id', 'u.nickname', 'u.member_id', 'u.regist_date', DB::raw('IFNULL(sp.point,0) AS point'))
            ->from($this->table . ' AS sm')
            ->join('user AS u', 'sm.id', '=', 'u.member_id')
            ->leftJoin('sandbox_point AS sp', 'sp.member_id', '=', 'u.member_id');

        if (! empty($param['developer_id'])) {
            $query = $query->where('sm.developer_id', $param['developer_id']);
        }
        if (! empty($param['status'])) {
            $query = $query->where('sm.status', $param['status']);
            $query = $query->where('u.status', $param['status']);
        }
        if (! empty($param['id'])) {
            $query = $query->where('u.id', $param['id']);
        }
        if (! empty($param['nickname'])) {
            $query = $query->where('u.nickname', 'LIKE', "%{$param['nickname']}%");
        }

        $query = $query->orderByRaw($order);
        return $query->paginate($param['offset']);
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        if (isset($params['password'])) {
            // パスワードハッシュ化のため[setPasswordAttribute()]関数を通す
            $this->password = $params['password'];
            $params['password'] = $this->password;
        }

        $result = self::insert(
            array(
                'id'   => $params['member_id'],
                'developer_id' => $params['developer_id'],
                'password' => $params['password']
            )
        );

        return $result;
    }

    /**
     * Update sandbox_member
     * @param array $updateData
     * @param int $id
     * @param string $status
     * @return boolean
     */
    public function edit($updateData, $id, $status = null)
    {
        $query = self::where('id', $id);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->update($updateData);
    }

    /**
     * idとdeveloper_idをキーにして1件レコード取得
     *
     * @param  int $id
     * @return object
     */

    public function getOneByIdAndDeveloperId($id, $developer_id)
    {
        return self::where('id', $id)->where('developer_id', $developer_id)->first();
    }

    /**
     * ユーザーIDとメンバーIDの一覧取得
     *
     * @param  object $params
     *
     * @return object
     */
    public function getIdListByDeveloperIdAndStatus($developerId, $status)
    {
        $query = self::select('u.id', 'u.member_id')
            ->from($this->table . ' AS sm')
            ->join('user AS u', 'sm.id', '=', 'u.member_id');
        if (! empty($developerId)) {
            $query = $query->where('sm.developer_id', $developerId);
        }
        if (! empty($param['status'])) {
            $query = $query->where('sm.status', $status);
            $query = $query->where('u.status', $status);
        }
        $query = $query->orderBy('u.regist_date', 'desc');
        return $query->get();
    }
}
