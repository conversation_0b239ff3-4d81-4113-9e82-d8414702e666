<?php

namespace App\Models\FreegameSandbox;

class ClUser extends FreegameSandbox
{
    protected $table = 'cl_user';

    public $timestamps = false;

    /**
     * Get developer account
     * @param  integer $id
     * @return array
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean $result
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                'id' => $params['id'],
                'member_id' => $params['member_id'],
                'status' => $params['status'],
                'type' => $params['type'],
                'regist_date' => date('Y-m-d H:i:s'),
            )
        );

        return $result;
    }

    /**
     * Get cl_user
     * @param  integer $mamber_id
     * @param  string $status
     * @return array
     */
    public function getOneByMemberId($member_id, $status = null)
    {
        $query = self::where('member_id', $member_id);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->first();
    }

    /**
     * Update cl_user
     * @param array $updateData
     * @param int $id
     * @param string $status
     * @return boolean
     */
    public function edit($updateData, $id, $status)
    {
        $query = self::where('id', $id);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->update($updateData);
    }
}
