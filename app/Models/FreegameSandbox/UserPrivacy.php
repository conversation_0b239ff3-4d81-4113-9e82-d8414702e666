<?php

namespace App\Models\FreegameSandbox;

class UserPrivacy extends FreegameSandbox
{
    protected $table = 'user_privacy';

    public $timestamps = false;

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                'user_id' => $params['user_id'],
                'age' => $params['age'],
                'birthday' => $params['birthday']
            )
        );

        return $result;
    }
}
