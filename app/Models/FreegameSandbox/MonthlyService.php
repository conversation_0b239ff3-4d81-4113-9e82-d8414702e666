<?php
namespace App\Models\FreegameSandbox;

use DB;

/**
 * 月額サービステーブル
 */
class MonthlyService extends FreegameSandbox
{
    protected $table   = 'monthly_service';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * get list with application for sandbox
     *
     * @param  array $conditions
     *
     * @return array $list
     *
     */
    public function getListWithApplication($conditions = [])
    {
        $query = self::select(
            'monthly_service.id',
            'monthly_service.service_name',
            'monthly_service.app_id',
            'application.title'
        )->join('application', 'monthly_service.app_id', '=', 'application.id')
        ->join('application_device', 'monthly_service.app_id', '=', 'application_device.app_id');

        if (!empty($conditions['status'])) {
            $query = $query->where('application_device.status', $conditions['status']);
        }

        if (!empty($conditions['developer_id'])) {
            if (is_array($conditions['developer_id'])) {
                $query = $query->whereIn('application.developer_id', $conditions['developer_id']);
            } else {
                $query = $query->where('application.developer_id', $conditions['developer_id']);
            }
        }

        $query->orderBy('application.stamp', 'desc');

        return $query->get();
    }

    /**
     * app_idから該当するリストを取得
     *
     * @param  array $conditions
     *
     * @return object
     *
     */
    public function getListByAppId($conditions = [])
    {
        $query = self::select(
                                'ms.id as service_id'
                                , 'ms.developer_id AS developer_id'
                                , 'ms.service_name AS service_name'
                                , 'a.title AS title'
                )
                ->from($this->table . ' AS ms')
                ->join('application AS a', 'ms.app_id', '=', 'a.id');
        if (! empty($conditions['appId'])) {
            if (is_array($conditions['appId'])) {
                $query = $query->whereIn('a.id', $conditions['appId']);
            } else {
                $query = $query->where('a.id', $conditions['appId']);
            }
        }
        $query = $query->orderBy('ms.id');
        return $query->paginate($conditions['offset']);
    }

    /**
     * idから1件のレコードを取得
     * @param  integer $id
     * @return array
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 登録
     *
     * @param object $params
     * @return boolean
     */
    public function add($params)
    {
        return self::insert($params);
    }

    /**
     * 編集
     *
     * @param object $params
     * @param int $id
     * @return boolean
     */
    public function edit($params, $id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->update($params);
    }

    /**
     * 削除
     * @param  int $id
     * @return boolean
     */
    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

}
