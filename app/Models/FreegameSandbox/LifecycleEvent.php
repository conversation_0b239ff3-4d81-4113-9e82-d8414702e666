<?php

namespace App\Models\FreegameSandbox;

class LifecycleEvent extends FreegameSandbox
{
    protected $table = 'lifecycle_event';

    public $timestamps = false;

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                'type' => $params['type'],
                'app_id' => $params['app_id'],
                'user_id' => $params['user_id']
            )
        );

        return $result;
    }
}
