<?php

namespace App\Models\FreegameSandbox;

class ApplicationDevice extends FreegameSandbox
{
    protected $table = 'application_device';

    public $timestamps = false;

    /**
     * 指定したapp_idのレコードを取得
     *
     * @param int $app_id
     * @param string status
     *
     * @return object
     */
    public function getListByAppId($app_id, $status = '')
    {
        $query = self::where('app_id', $app_id);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->get();
    }

    /**
     * 指定したapp_idとデバイスのレコード1件を取得
     *
     * @param int $app_id
     * @param string $device
     * @param string status
     * @return object
     */
    public function getOnetByAppIdAndDevice($app_id, $device, $status = '')
    {
        $query = self::where('app_id', $app_id);
        $query = $query->where('device', $device);
        if (! empty($status)) {
            $query = $query->where('status', $status);
        }
        return $query->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $addData = array_intersect_key($params, array_flip([
            'app_id',
            'device',
            'begin',
            'end',
            'status',
            'domain_type',
            'browser_sdk',
        ]));

        return self::insert($addData);
    }

    /**
     * Update application_device
     * @param array $updateData
     * @param array $app_id
     * @param array $device
     * @return boolean
     */
    public function edit($updateData, $app_id, $device)
    {
        return self::where('app_id', $app_id)
            ->where('device', $device)
            ->update($updateData);
    }
}
