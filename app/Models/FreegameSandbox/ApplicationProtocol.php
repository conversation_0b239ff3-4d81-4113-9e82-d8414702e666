<?php
namespace App\Models\FreegameSandbox;

/**
 * ソーシャルゲーム用通信プロトコル管理テーブル
 */
class ApplicationProtocol extends FreegameSandbox
{
    protected $table = 'application_protocol';

    protected $primaryKey = 'app_id';

    protected $fillable = [
        'app_id',
        'is_ssl',
    ];

    public $timestamps = false;

    public function getOneByAppId($appId)
    {
        if (empty($appId)) {
            return false;
        }

        return self::where('app_id', $appId)->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $timestamp = timestamp_to_date(now_stamp());
        $result = self::insert(
            array(
                'app_id' => $params['app_id'],
                'is_ssl' => $params['is_ssl'],
                'created_at' => $timestamp,
                'updated_at' => $timestamp,
            )
        );
        return $result;
    }

    /**
     * 更新
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function edit($params)
    {
        if (empty($params['app_id']) || is_null($params['is_ssl'])) {
            return false;
        }

        return self::updateOrCreate(['app_id' => $params['app_id']], $params);
    }
}
