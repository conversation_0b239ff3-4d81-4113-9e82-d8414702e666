<?php

namespace App\Models\FreegameSandbox;

class ApplicationApk extends FreegameSandbox
{
    protected $table = 'application_apk';

    public $timestamps = false;
    /**
     * idから1件のレコードを取得
     * @param  integer $app_id
     * @return object
     */
    public function getOneByAppId($app_id)
    {
        return self::where('app_id', $app_id)->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                'app_id' => $params['app_id'],
                'apk_name' => $params['apk_name'],
                'package_name' => $params['package_name'],
            )
        );
        return $result;
    }

    /**
     * Update application_apk
     * @param array $updateData
     * @param array $app_id
     * @return boolean
     */
    public function edit($updateData, $app_id)
    {
        return self::where('app_id', $app_id)
            ->update($updateData);
    }
}
