<?php
namespace App\Models\FreegameSandbox;

/**
 * ソーシャルゲーム用spメニューボタン初期位置管理テーブル
 */
class ApplicationSpMenu extends FreegameSandbox
{

    protected $table = 'application_sp_menu';

    protected $primaryKey = 'app_id';

    protected $fillable = [
        'app_id',
        'portrait_position',
        'landscape_position',
    ];

    public $timestamps = false;

    public function getByAppId($appId)
    {
        if (empty($appId)) {
            return null;
        }

        return self::where('app_id', $appId)->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $date = timestamp_to_date(now_stamp());
        $result = self::insert(
            array(
                'app_id' => $params['app_id'],
                'portrait_position' => (int)$params['portrait_position'],
                'landscape_position' => (int)$params['landscape_position'],
                'created_at' => $date,
                'updated_at' => $date,
            )
        );
        return $result;
    }

    /**
     * 更新
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function edit($params)
    {
        if (empty($params['app_id']) || is_null($params['portrait_position']) || is_null($params['landscape_position'])) {
            return false;
        }

        return self::updateOrCreate(['app_id' => $params['app_id']], $params);
    }
}
