<?php

namespace App\Models\FreegameSandbox;

class SandBoxPoint extends FreegameSandbox
{
    protected $table = 'sandbox_point';

    public $timestamps = false;

    /**
     * 配列のmember_idと一致するレコードのポイントを取得
     *
     * @param  array $menberIdList
     *
     * @return object
     */
    public function getListByMemberIds($menberIdList)
    {
        return self::whereIn('member_id', $menberIdList)->lists('point', 'member_id');
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                'member_id' => $params['member_id'],
                'point' => $params['point'],
            )
        );

        return $result;
    }

    /**
     * Get developer account
     * @param  integer $id
     * @return array
     */
    public function getOneByMemberId($member_id)
    {
        return self::where('member_id', $member_id)->first();
    }

    /**
     * Update sandbox_point
     * @param array $updateData
     * @param array $member_id
     * @return boolean
     */
    public function edit($updateData, $member_id)
    {
        return self::where('member_id', $member_id)
            ->update($updateData);
    }
}
