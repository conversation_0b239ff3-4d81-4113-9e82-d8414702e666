<?php
namespace App\Models\FreegameSandbox;

use DB;

/**
 * 月額サービステーブル
 */
class MonthlyServiceUser extends FreegameSandbox
{
    protected $table   = 'monthly_service_user';

    protected $guarded = [
        'id'
    ];

    public $timestamps = false;

    /**
     * 月額サービスidから該当するリストを取得
     *
     * @param  int $monthlyServiceId
     *
     * @return object
     *
     */
    public function getListByMonthlyServiceId($monthlyServiceId)
    {
        $subQuery = self::selectRaw("MAX(id) AS id")
            ->from($this->table)
            ->groupBy('monthly_service_user_id', 'monthly_service_id');
        $query = self::select(
            'msu.monthly_service_user_id AS monthly_service_user_id',
            'msu.status AS status')
            ->from($this->table . ' AS msu')
            ->join(DB::raw("({$subQuery->toSql()}) AS tmp"), 'msu.id', '=', 'tmp.id')
            ->where('msu.monthly_service_id', $monthlyServiceId)
            ->orderBy('msu.id', 'DESC');
        return $query->get();
    }

    /**
     * 現在、契約有効期間内のレコードがあるか確認
     *
     * @param  array $conditions 
     * @return object
     *
     */
    public function getEntryNow($conditions)
    {
        $query = self::from($this->table);
        if (! empty($conditions['monthly_service_id'])) {
            $query = $query->where('monthly_service_id', $conditions['monthly_service_id']);
        }
        if (! empty($conditions['monthly_service_user_id'])) {
            $query = $query->where('monthly_service_user_id', $conditions['monthly_service_user_id']);
        }
        $query->where('expire_datetime', '>', date('Y-m-d H:i:s'));
        return $query->get();
    }

    /**
     * 登録
     *
     * @param object $params
     * @return boolean
     */
    public function add($params)
    {
        return self::insert($params);
    }

    /**
     * 編集
     *
     * @param object $params
     * @param int $id
     * @return boolean
     */
    public function edit($id, $params)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)
            ->update($params);
    }
}
