<?php

namespace App\Models\FreegameSandbox;

class DmmPremiumUser extends FreegameSandbox
{
    protected $table = 'dmm_premium_user';

    protected $primaryKey = 'member_id';

    public $timestamps = false;

    /**
     * 登録
     * @param string $memberId
     * @param int    $status
     * @param string $begin
     * @param string $end
     * @return boolean
     */
    public function add($memberId, $status, $begin = '', $end = '')
    {
        $result = self::insert([
            'member_id' => $memberId,
            'status' => $status,
            'begin_at' => $begin,
            'end_at' => $end,
        ]);

        return $result;
    }
}
