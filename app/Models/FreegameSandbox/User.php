<?php

namespace App\Models\FreegameSandbox;

class User extends FreegameSandbox
{
    protected $table = 'user';

    public $timestamps = false;

    /**
     * Get developer account
     * @param  integer $id
     * @return array
     */
    public function getOne($id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 登録
     *
     * @param  object $params
     *
     * @return boolean
     */
    public function add($params)
    {
        $result = self::insert(
            array(
                    'id' => $params['id'],
                    'member_id' => $params['member_id'],
                    'nickname' => $params['nickname'],
                    'type' => $params['type'],
                    'grade' => $params['grade'],
                    'user_hash' => $params['user_hash'],
                    'status' => $params['status'],
                    'gender' => $params['gender'],
                    'birth_date' => $params['birth_date'],
                    'blood_type' => $params['blood_type'],
                    'regist_date' => $params['regist_date'],
            )
        );
        return $result;
    }

    /**
     * idをキーにしてsandbox_memberテーブルと連結した1行を取得
     *
     * @param  object $params
     *
     * @return object $result
     */
    public function getOneByUidWithMember($id)
    {
        return
            self::from($this->table . ' AS u')
            ->join('sandbox_member AS sm', 'u.member_id', '=', 'sm.id')
            ->where('u.id', $id)
            ->first();
    }

    /**
     * Update user
     * @param array $updateData
     * @param array $id
     * @return boolean
     */
    public function edit($updateData, $id)
    {
        return self::where('id', $id)
            ->update($updateData);
    }
}
