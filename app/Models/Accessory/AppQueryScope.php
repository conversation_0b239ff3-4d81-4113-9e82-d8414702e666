<?php
namespace App\Models\Accessory;

trait AppQueryScope
{

    /**
     * 特定のアプリを除外する条件。
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExceptApp($query, $except = [])
    {
        if (empty($except)) {
            $except = config('forms.common.exceptApp', []);
        }
        foreach ($except as $data) {
            $params = [];
            $params[] = $data['app_id'];
            $params[] = $data['device'];
            $query->whereRaw('not (app_id = ? and device = ?)')->addBinding($params);
        }
        return $query;
    }
}
