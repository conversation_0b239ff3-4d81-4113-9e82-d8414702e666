<?php

namespace App\Models\FreegameCommunityMongo;

use Carbon\Carbon;
use \MongoDB\BSON\UTCDateTime;

/**
 * コメント
 */
class CommentMongo extends FreegameCommunityMongo
{
    protected $table = 'comment';

    public $timestamps = false;

    /**
     * get one by comment_key
     *
     * @param  string $commentKey
     *
     * @return object
     */
    public function getOne($commentKey)
    {
        return self::where('comment_key', $commentKey)->first();
    }

    /**
     * get list
     *
     * @param  array  $params
     *
     * @return object $result
     */
    public function getList($params)
    {
        $query = self::select();

        if (!empty($params['comment_key'])) {
            $query = $query->where('comment_key', $params['comment_key']);
        }
        if (!empty($params['topic_id'])) {
            $query = $query->where('topic_id', $params['topic_id']);
        }
        if (isset($params['status'])) {
            if (is_array($params['status'])) {
                $query = $query->whereIn('status', $params['status']);
            } else {
                $query = $query->where('status', $params['status']);
            }
        } else {
            $query = $query->where('status', 'active');
        }

        $query = $query->orderBy('comment_id', 'desc');

        if (!empty($params['offset'])) {
            $result = $query->paginate($params['offset']);
        } else {
            $result = $query->get();
        }

        return $result;
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * delete
     *
     * @param  string $commentKey
     * @param  array  $data
     *
     * @return object
     */
    public function del($commentKey, $data)
    {
        return self::where('comment_key', $commentKey)->update($data);
    }

    /**
     * Get Comment Search List
     * @param array $params
     * @return App\Models\FreegameCommunityMongo\CommentMongo
     */
    public function getCommentSearchList($params)
    {
        $query = self::select();
        if (array_has($params, 's_community_id')) {
            $query->where('community_id', $params['s_community_id']);
        }
        if (array_has($params, 'topic_id') && is_array($params['topic_id'])) {
            $query->whereIn('topic_id', $params['topic_id']);
        } elseif (array_has($params, 's_topic_id')) {
            $query->where('topic_id', $params['s_topic_id']);
        }
        if (array_has($params, 's_start_date') && array_has($params, 's_end_date')) {
            $startDate = new UTCDateTime(Carbon::parse($params['s_start_date'])->timestamp * 1000);
            $endDate = new UTCDateTime(Carbon::parse($params['s_end_date'])->timestamp * 1000);
            $query->where('create_info.date', '>=', $startDate);
            $query->where('create_info.date', '<=', $endDate);
        }
        if (array_has($params, 's_dmm_game_id')) {
            $query->where('create_info.user_id', $params['s_dmm_game_id']);
        }
        if (array_has($params, 's_keywordk') && is_array($params['s_keywordk'])) {
            $query->where(function ($query) use ($params) {
                foreach ($params['s_keywordk'] as $key => $value) {
                    $query->where('text', 'LIKE', '%'.$value.'%');
                }
            });
        }
        if (array_has($params, 'order')) {
            $query->orderBy('comment_id', $params['order']);
        }
        return $query;
    }
}
