<?php
namespace App\Models\FreegameCommunityMongo;

use MongoDB\Operation\FindAndModify;

/**
 * コメント件数
 */
class CommentCountMongo extends FreegameCommunityMongo
{
    protected $table = 'comment_count';

    public $timestamps = false;

    /**
     * get date by id
     *
     * @param  integer $id
     *
     * @return object
     */
    public function getOne($id)
    {
        return self::select()->where('_id', $id)->first();
    }

    /**
     * insert
     *
     * @param  array  $data
     *
     * @return object
     */
    public function add($data)
    {
        return self::insert($data);
    }

    /**
     * update
     *
     * @param  string $id
     * @param  array  $data
     *
     * @return object
     */
    public function edit($id, $data)
    {
        return self::where('_id', $id)->update($data);
    }
}
