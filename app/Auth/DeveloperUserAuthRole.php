<?php
namespace App\Auth;

trait DeveloperUserAuthRole
{

    public function getAuthRole()
    {
        return session('Auth.role', []);
    }

    public function setAuthRole($value)
    {
        session()->set('Auth.role', $value);
    }

    public function removeAuthRole()
    {
        session()->remove('Auth.role');
    }

    public function hasAuthRole($value)
    {
        $value = ltrim($value, '/');
        foreach ($this->getAuthRole() as $role) {
            // パスワード変更の場合は常時trueとする
            if (str_is('password/edit', $value) || str_is('password/update', $value)) {
                return true;
            }
            
            $pattern = ltrim($role['url'], '/');
            if (str_is($pattern, $value)) {
                return true;
            }
            if (str_is($pattern . '?*', $value)) {
                return true;
            }
            if (str_is($pattern . '#*', $value)) {
                return true;
            }
        }
        return false;
    }
}
