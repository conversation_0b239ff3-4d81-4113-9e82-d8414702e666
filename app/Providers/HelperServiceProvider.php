<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->injectHelpers();
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Inject helpers
     *
     * @param none
     * @return none
     */
    private function injectHelpers()
    {
        foreach (glob(app_path() . '/Libs/Helpers/*.php') as $filename) {
            require_once($filename);
        }
    }
}
