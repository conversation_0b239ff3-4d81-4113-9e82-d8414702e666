<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Libs\Validation\Validator\ExceptTagHtml;
use Validator;

class CustomValidationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->injectCustomValidationMethods();
        Validator::extend('exceptTagHtml', ExceptTagHtml::class.'@validate');
        Validator::replacer('exceptTagHtml', ExceptTagHtml::class.'@errorMessage');
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ExceptTagHtml::class, function () {
            return new ExceptTagHtml;
        });
    }

    /**
     * Inject custom validation classes.
     *
     * @param none
     * @return none
     */
    private function injectCustomValidationMethods()
    {
        $classPath = config('customvalidation.path');

        foreach (config('customvalidation.classes') as $class) {
            $classFullPath = $classPath . $class;

            $methodList = array_diff(
                get_class_methods($classFullPath),
                get_class_methods('Illuminate\Validation\Validator')
            );

            foreach ($methodList as $method) {
                if (substr($method, 0, 8) == 'validate') {
                    $ruleName = ltrim(strtolower(preg_replace('/[A-Z]/', '_$0', substr($method, 8))), '_');

                    Validator::extend(
                        $ruleName,
                        function ($attribute, $value, $parameters, $validator) use ($classFullPath, $method) {
                            return $classFullPath::$method($attribute, $value, $parameters);
                        }
                    );
                } elseif (substr($method, 0, 7) == 'replace') {
                    $ruleName = ltrim(strtolower(preg_replace('/[A-Z]/', '_$0', substr($method, 7))), '_');

                    Validator::replacer(
                        $ruleName,
                        function ($message, $attribute, $rule, $parameters) use ($classFullPath, $method) {
                            return $classFullPath::$method($message, $attribute, $rule, $parameters);
                        }
                    );
                }
            }
        }
    }
}
