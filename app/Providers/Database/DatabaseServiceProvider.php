<?php

namespace App\Providers\Database;

use Illuminate\Database\DatabaseManager;
use Illuminate\Database\DatabaseServiceProvider as BaseServiceProvider;
use Illuminate\Database\Eloquent\Model;
use App\Providers\Database\Connectors\DeferredConnectionFactory;

/**
 * DatabaseServiceProvider
 */
class DatabaseServiceProvider extends BaseServiceProvider
{
    /** {@inheritdoc} */
    public function register()
    {
        Model::clearBootedModels();

        $this->registerEloquentFactory();

        $this->registerQueueableEntityResolver();

        $this->registerDatabaseManager();

        $this->registerConnectionFactory();

        $this->registerConnection();
    }

    /**
     * registerDatabaseManager
     *
     * @return void
     */
    protected function registerDatabaseManager()
    {
        $this->app->singleton('db', function ($app) {
            return new DatabaseManager($app, $app['db.factory']);
        });
    }

    /**
     * registerConnectionFactory
     *
     * @return void
     */
    protected function registerConnectionFactory()
    {
        $this->app->singleton('db.factory', function ($app) {
            return new DeferredConnectionFactory($app);
        });
    }

    /**
     * registerConnection
     *
     * @return void
     */
    protected function registerConnection()
    {
        $this->app->bind('db.connection', function ($app) {
            return $app['db']->connection();
        });
    }
}
