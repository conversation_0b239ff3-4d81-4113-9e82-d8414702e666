<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Auth\DeveloperEloquentUserProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app['auth']->extend('developer_eloquent', function () {
            $model = $this->app['config']['auth.model'];
            return new DeveloperEloquentUserProvider($this->app['hash'], $model);
        });
    }
}
