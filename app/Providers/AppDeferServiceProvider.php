<?php

namespace App\Providers;

use App\Libs\Sso\KeycloakAdminClient;
use App\Libs\Sso\KeycloakClient;
use Illuminate\Support\ServiceProvider;

/**
 * サービスプロバイダー(実行時ロード)
 */
class AppDeferServiceProvider extends ServiceProvider
{
    protected $defer = true;
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('App\Libs\Sso\KeycloakClient', function ($app) {
            return KeycloakClient::create();
        });
        $this->app->bind('App\Libs\Sso\KeycloakAdminClient', function ($app) {
            return KeycloakAdminClient::create();
        });
    }

    public function provides()
    {
        return [
            'App\Libs\Sso\KeycloakClient',
            'App\Libs\Sso\KeycloakAdminClient',
        ];
    }
}
