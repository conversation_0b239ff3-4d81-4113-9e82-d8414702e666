<?php

namespace App\Services;

use App\Services\SubscriptionApiService;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Contracts\Logging\Log;

/**
 * 定期購入(サンドボックス)APIサービス
 */
class SubscriptionSandBoxApiService extends SubscriptionApiService
{
    /**
     * 定期購入(サンドボックス)APIサービスコンストラクタ
     * 
     * @param HttpClient
     * @param Log
     *
     */
    public function __construct(
        HttpClient $httpClient,
        Log $log
    ) {
        parent::__construct(true, $httpClient, $log);
    }

    /**
     * ユーザーの購入した定期購入アイテム取得
     * 
     * @param array $pathParameter パスパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getUserItemList($pathParameter)
    {
        $endPoint = config('subscription-api.path_with_parameters.user_item');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameter
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 購入済み有効な定期購入アイテムの解約予約
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $bodyParameter ボディパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function cancelReservation($pathParameter, $bodyParameter)
    {
        $endPoint = config('subscription-api.path_with_parameters.user_item_cancel');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameter
        );
        $options = [
            'json' => $bodyParameter,
        ];

        return $this->executeApi('PATCH', $endPoint, $options);
    }

    /**
     * 購入済み定期購入アイテムの解約予約を解除
     * 
     * @param array $pathParameter パスパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function resetReservation($pathParameter)
    {
        $endPoint = config('subscription-api.path_with_parameters.user_item_activation');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameter
        );

        return $this->executeApi('PATCH', $endPoint);
    }
}
