<?php
namespace App\Services\Accessory;

use Response;

trait CsvFileDownload
{

    /**
     * Download CSV
     * @param string $fileName
     * @param array $contents
     * @param array $column
     * @param boolean $addTitle
     * @param boolean $escapeDq
     * @return
     */
    public function downloadCsv($fileName, $contents, $column, $addTitle = false, $escapeDq = false)
    {
        $csv = $this->makeCsv($contents, $column, $addTitle, $escapeDq);
        $headers = array(
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        );
        return \Response::make($csv, 200, $headers);
    }

    /**
     * Makes CSV data and get
     * @param array $contents ([ ['keyColumnName' => ['parameter'],[...]] ,  ['keyColumnName' =>...],[...], ...])
     * @param array $column (['keyColumnName' => 'viewTitle'],[...]...)
     * @param boolean $addTitle
     * @param boolean $escapeDq
     * @return stream
     */
    protected function makeCsv($contents, $column, $addTitle = false, $escapeDq = false)
    {
        $csv = '';
        //仮Open
        $stream = fopen('php://temp', 'w');
        try {
            // カラム名の値を入れるフラグがある場合
            if ($addTitle) {
                $csvParam = [];
                // $column の順番で値を投入
                foreach ($column as $title) {
                    $csvParam[] = $title;
                }
                $this->mbFputCsv($stream, $csvParam, $escapeDq);
            }
            foreach ($contents as $val) {
                $csvParam = [];
                // $column の順番で値を投入、存在しないものは空値で埋める
                foreach ($column as $keyIndex => $title) {
                    $csvParam[] = isset($val[$keyIndex]) ? $val[$keyIndex] : '';
                }
                $this->mbFputCsv($stream, $csvParam, $escapeDq);
            }
            rewind($stream);

            $csv = stream_get_contents($stream);
        } catch (Exception $e) {
            fclose($stream);
            throw $e;
        }
        fclose($stream);
        // Excelでも開けるように文字コード変換
        $csv = mb_convert_encoding($csv, 'sjis-win', 'UTF-8');

        return $csv;
    }

    /**
     * write strings for CSV
     * @param file stream $fp
     * @param array $param
     * @param boolean $escapeDq
     * @return void
     */
    protected function mbFputCsv(&$fp, $param, $escapeDq = false)
    {
        $strData = null;
        $index = 1;
        $columnMax = count($param);
        foreach ($param as $val) {
            //ダブルクォーテーションのエスケープ
            if ($escapeDq) {
                $val = preg_replace('/\"/u', '""', $val);
            } else {
                $val = preg_replace('/\"/u', '\"', $val);
            }
            // 全ての文字を " で囲む
            $strData .= '"' . $val . '"';
            if ($index < $columnMax) {
                $strData .=  ',';
            }
            $index++;
        }
        // 1行分のデータ終了を示す改行コード
        $strData .= "\n";
        fwrite($fp, $strData);
    }
}
