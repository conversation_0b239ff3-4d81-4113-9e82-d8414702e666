<?php
namespace App\Services\Accessory;

/**
 * 言語情報
 */
trait LanguageSupport
{
    // デフォルト言語名
    private $defaultLanguageName = '日本語';

    /**
     * アプリケーションごとの言語を取得
     * ※使用する際は以下をインジェクションしてメンバ変数に保持しておくこと
     * 　App\Models\Freegame\ApplicationLanguageGroup;
     * 　App\Models\FreegameSetting\Language;
     *
     * @param $condition 検索条件
     * @return array 'app_id' => '言語名'の配列
     */
    public function getApplicationLanguageList($condition)
    {
        $languageList = [];
        
        // アプリ-言語情報を取得
        $appLangInfos = $this->applicationLanguageGroup->getList($condition);
        // 言語マスタを取得
        $languageMap = collect($this->language->all()->toArray())->keyBy('id');
        
        // デフォルト言語名をDBから取得
        $defaultLanguage = $languageMap->where('language_code', 'ja')->first();
        if(!is_null($defaultLanguage)) {
            $this->defaultLanguageName = $defaultLanguage['language_name'];
        }
        
        // アプリIDをキーにした配列に変換
        foreach($appLangInfos as $app) {
            $language = $languageMap->get($app->language_id);
            if(is_null($language)) {
                $languageList[$app->app_id] = $this->defaultLanguageName;
            } else {
                $languageList[$app->app_id] = $language['language_name'];
            }
        }
        
        return $languageList;
    }
    
    /**
     * 対象アプリの言語を取得
     *
     * @param languageList 言語リスト
     * @param appId アプリID
     * @return string 言語名
     */
    public function getLanguage($languageList, $appId)
    {
        $languageName = $this->defaultLanguageName;

        // 言語リストが存在し、対象のアプリの情報が存在する場合のみ言語名を返却
        if(!empty($languageList) && isset($languageList[$appId])) {
            $languageName = $languageList[$appId];
        }
        
        return $languageName;
    }
}
