<?php
namespace App\Services\Accessory;

use Flysystem;
use Log;

trait ImageFileUpload
{

    /**
     * upload image files
     * To img-freegames.dmm.com
     *
     * @param string $localFilePath
     * @param string  $fileName
     * @param string $uploadPath
     * @return boolean
     */
    public function uploadFile($localFilePath, $fileName, $uploadPath)
    {
        $baseDirectory =  env('IMG_UPLOAD_BASEPATH', '/home/<USER>/netgame_image') . '/';
        $filePath = $baseDirectory . $uploadPath . $fileName;

        try {
            \Flysystem::connection('img_upload_freegames_sftp')->getAdapter()->setDirectoryPerm(0755);
            \Flysystem::connection('img_upload_freegames_sftp')->getAdapter()->setPermPublic(0644);

            // フォルダ作成
            if (!\Flysystem::connection('img_upload_freegames_sftp')->has($baseDirectory . $uploadPath)) {
                \Flysystem::connection('img_upload_freegames_sftp')->createDir($baseDirectory . $uploadPath);
            }

            // 画像アップロード
            $fp = fopen($localFilePath, "r");
            \Flysystem::connection('img_upload_freegames_sftp')->putStream($filePath, $fp, ['visibility' => 'public']);
            fclose($fp);

            // キャッシュの削除
            if (\Flysystem::connection('img_upload_freegames_sftp')->has($filePath)) {
                $this->deleteImageCache($uploadPath . $fileName);
            }
        } catch (\Exception $e) {
            if (\Flysystem::connection('img_upload_freegames_sftp')->has($filePath)) {
                \Flysystem::connection('img_upload_freegames_sftp')->delete($filePath);
            }
            // OLGPFENHANCE-15689 アップロード関連処理エラーでerrorログ出力
            Log::error(sprintf('uploadFile(img-freegames) Error: FilePath: %s $e: %s', $filePath, $e));

            throw $e;
        }

        return \Flysystem::connection('img_upload_freegames_sftp')->has($filePath);
    }

    /**
     * upload image files to master server
     * To pics.dmm.com
     *
     * @param string $localFilePath
     * @param string $fileName
     * @param string $uploadPath
     * @param string $fp
     * @return boolean
     */
    public function uploadFileToPics($localFilePath, $fileName, $uploadPath, $fp = null)
    {
        $baseDirectory =  env('FTP_PICS_BASEPATH', '/home/<USER>/freegame') . '/';
        $filePath = $baseDirectory . $uploadPath . $fileName;
        $putResult = false;

        try {
            // ディレクトリ作成時のデフォルト権限744だと画像参照できなくなるため755に変更しておく
            \Flysystem::connection('ftp_pics_master_sftp')->getAdapter()->setDirectoryPerm(0755);

            // フォルダ作成
            if (!\Flysystem::connection('ftp_pics_master_sftp')->has($baseDirectory . $uploadPath)) {
                \Flysystem::connection('ftp_pics_master_sftp')->createDir($baseDirectory . $uploadPath);
            }

            // 画像アップロード
            if (! $fp) {
                $fp = fopen($localFilePath, "r");
                $putResult = \Flysystem::connection('ftp_pics_master_sftp')->putStream($filePath, $fp);
                fclose($fp);
            } else {
                $putResult = \Flysystem::connection('ftp_pics_master_sftp')->putStream($filePath, $fp);
            }

            // キャッシュの削除
            if (\Flysystem::connection('ftp_pics_master_sftp')->has($filePath)) {
                $this->deleteImageCacheToPics($uploadPath . $fileName);
            }
        } catch (\Exception $e) {
            if (\Flysystem::connection('ftp_pics_master_sftp')->has($filePath)) {
                \Flysystem::connection('ftp_pics_master_sftp')->delete($filePath);
            }
            // OLGPFENHANCE-15689 アップロード関連処理エラーでerrorログ出力
            Log::error(sprintf('uploadFileToPics Error: FilePath: %s $e: %s', $filePath, $e));

            throw $e;
        }
        
        if($putResult){
            return \Flysystem::connection('ftp_pics_master_sftp')->has($filePath);
        }else{
            // 更新失敗
            $errMsg = "Couldn't Upload image to pics. file=" . $filePath;
            $SFTPErrors = \Flysystem::connection('ftp_pics_master_sftp')->getAdapter()->getConnection()->getSFTPErrors();
            $this->picsWarningLog($errMsg, $SFTPErrors);
            return false;
        }
    }

    /**
     * delete cache image files
     *
     * @param string $uploadFile
     * @param integer $retryCnt
     * @return boolean
     */
    public function deleteImageCache($uploadFile, $retryCnt = 0)
    {
        $check = env('IMG_UPLOAD_CACHE_CLEAR', false);

        if (! $check) {
            return true;
        }

        $curl_url = env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/purge/' . ltrim($uploadFile, '/');

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_URL, $curl_url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'PURGE');
        $result = curl_exec($curl);
        $info = curl_getinfo($curl);
        curl_close($curl);

        if (strpos($info['http_code'], '20') !== 0) {
            if ($retryCnt < env('IMG_UPLOAD_CACHE_CLEAR_RETRY_COUNT', 3)) {
                sleep(env('IMG_UPLOAD_CACHE_CLEAR_RETRY_SLEEP', 1)); // 1秒後に再度実行
                return $this->deleteImageCache($uploadFile, $retryCnt + 1);
            } else {
                // 3回失敗
                if ((int) $info['http_code'] !== 404) {
                    $errMsg = "Couldn't Delete cache image. info=" . var_export($info, true);
                    Log::error($errMsg);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * delete cache image files to pics
     *
     * @param string $uploadFile
     * @param integer $retryCnt
     * @return boolean
     */
    public function deleteImageCacheToPics($uploadFile, $retryCnt = 0)
    {
        $check = env('FTP_PICS_CACHE_CLEAR', false);

        if (! $check) {
            return true;
        }

        $curl_url = env('HTTP_OLGPF_CACHE_TOOL_API_URL', 'http://localhost') . '/api/purge';
        $image_url = env('HTTP_PICS_ADULT_URL', 'http://localhost') . '/freegame/' . ltrim($uploadFile, '/');

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_URL, $curl_url);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, [
            'PATH' => $image_url
        ]);
        $result = curl_exec($curl);
        $info = curl_getinfo($curl);
        curl_close($curl);

        if (strpos($info['http_code'], '20') !== 0) {
            if ($retryCnt < env('FTP_PICS_CACHE_CLEAR_RETRY_COUNT', 3)) {
                sleep(env('FTP_PICS_CACHE_CLEAR_RETRY_SLEEP', 1)); // 1秒後に再度実行
                return $this->deleteImageCacheToPics($uploadFile, $retryCnt + 1);
            } else {
                // 3回失敗
                if ((int) $info['http_code'] !== 404) {
                    $errMsg = "Couldn't Delete cache image to pics. info=" . var_export($info, true);
                    Log::error($errMsg);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * delete image files
     * To img-freegames.dmm.com
     *
     * @param string $uploadFileName
     * @return boolean
     */
    public function deleteFile($uploadFileName)
    {
        $baseDirectory =  env('IMG_UPLOAD_BASEPATH', '/home/<USER>/netgame_image') . '/';
        if (\Flysystem::connection('img_upload_freegames_sftp')->has($baseDirectory . $uploadFileName)) {
            \Flysystem::connection('img_upload_freegames_sftp')->delete($baseDirectory . $uploadFileName);
        }
        return !(\Flysystem::connection('img_upload_freegames_sftp')->has($baseDirectory . $uploadFileName));
    }

    /**
     * delete image files to master server
     * To pics.dmm.com
     *
     * @param string $uploadFileName
     * @return boolean
     */
    public function deleteFileToPics($uploadFileName)
    {
        $baseDirectory =  env('FTP_PICS_BASEPATH', '/home/<USER>/freegame') . '/';
        if (\Flysystem::connection('ftp_pics_master_sftp')->has($baseDirectory . $uploadFileName)) {
            \Flysystem::connection('ftp_pics_master_sftp')->delete($baseDirectory . $uploadFileName);
        }
        // ファイルが残っていたら削除失敗、ログ出力する。
        if(\Flysystem::connection('ftp_pics_master_sftp')->has($baseDirectory . $uploadFileName)){
            $errMsg = "Couldn't Delete image to pics. file=" . $baseDirectory . $uploadFileName;
            $SFTPErrors = \Flysystem::connection('ftp_pics_master_sftp')->getAdapter()->getConnection()->getSFTPErrors();
            $this->picsWarningLog($errMsg, $SFTPErrors);
            return false;
        }
        return true;
    }

    /**
     * Make random name for image file
     */
    public function makeRandImageName($length = 8)
    {
        $str = array_merge(range('a', 'z'), range('0', '9'), range('A', 'Z"'));
        $randStr = '';
        for ($i = 0; $i < $length; $i++) {
            $tmpRndStr = rand(0, count($str)-1);
            if (isset($str[$tmpRndStr])) {
                $randStr .= $str[$tmpRndStr];
            } else {
                $randStr .= 'a';
            }
        }
        return $randStr;
    }

    /**
     * get image files
     * To img-freegames.dmm.com
     *
     * @param string $uploadFileName
     * @return string
     */
    public function getFileToImg($uploadFileName)
    {
        $baseDirectory = env('IMG_UPLOAD_BASEPATH', '/home/<USER>/netgame_image') . '/';
        $filePath      = $baseDirectory . $uploadFileName;
        $fileData      = null;

        if (\Flysystem::connection('img_upload_freegames_sftp')->has($filePath)) {
            $fileData  = \Flysystem::connection('img_upload_freegames_sftp')->readStream($filePath);
        }
        return $fileData;
    }
    
    /**
     * picsへの更新失敗した際にログを成形して出力する。
     * @param string $errmgs
     * @param array $errors
     */
    private function picsWarningLog($errMsg,$SFTPErrors){
        // OLGPFENHANCE-15689 warningだとログが全く出ないので、アップロードエラー調査のためerrorへ変更
        Log::error($errMsg);
        if(!empty($SFTPErrors)){
            Log::error(array_pop($SFTPErrors));
        }
    }
}
