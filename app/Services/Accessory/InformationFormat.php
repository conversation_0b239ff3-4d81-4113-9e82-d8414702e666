<?php

namespace App\Services\Accessory;

trait InformationFormat
{
    /**
     * Format row with icon new
     * @param object $row
     * @param array $config
     * @return self
     */
    public function formatIconNew($row, $config)
    {
        $row->iconNew = '';

        if ($row->notice_date->addDay($config['newDay'])->isFuture()) {
            $row->iconNew = $config['categoryIcon']['new'];
        }

        return $this;
    }

    /**
     * Format row with icon required
     * @param object $row
     * @param array $config
     * @return self
     */
    public function formatIconRequired($row, $config)
    {
        $row->iconRequired = '';

        if ($row->category == 'required_info' || $row->category == 'required_important') {
            $row->iconRequired = $config['categoryIcon'][$row->category];
        }

        return $this;
    }

    /**
     * Format row with icon important
     * @param object $row
     * @param array $config
     * @return self
     */
    public function formatIconImportant($row, $config)
    {
        $row->iconImportant = '';

        if ($row->category == 'info_important' || $row->category == 'required_important') {
            $row->iconImportant = $config['categoryIcon']['info_important'];
        }

        return $this;
    }

    /**
     * Format row with icon failure occurrence
     * @param object $row
     * @param array $config
     * @return self
     */
    public function formatIconFailureOccurrence($row, $config)
    {
        $row->iconFailureOccurrence = '';

        if ($row->category == 'failure_occurrence') {
            $row->iconFailureOccurrence = $config['categoryIcon']['failure_occurrence'];
        }

        return $this;
    }

    /**
     * Format row with icon failure recovery
     * @param object $row
     * @param array $config
     * @return self
     */
    public function formatIconFailureRecovery($row, $config)
    {
        $row->iconFailureRecovery = '';

        if ($row->category == 'failure_recovery') {
            $row->iconFailureRecovery = $config['categoryIcon']['failure_recovery'];
        }

        return $this;
    }

    /**
     * Format list
     * @param array $list
     * @param array $formatters
     * @return array
     */
    public function formatList($list, $formatters = [])
    {
        if (empty($formatters)) {
            return $list;
        }

        $config = config('forms.Informations');

        foreach ($list as $row) {
            foreach ($formatters as $formatter) {
                $formatter($row, $config);
            }
        }

        return $list;
    }

    /**
     * Format maintenance list
     * @param array $list
     * @return array
     */
    public function formatMaintenanceList($list)
    {
        return $this->formatList(
            $list,
            [
                [$this, 'formatIconNew'],
            ]
        );
    }

    /**
     * Format info list
     * @param array $list
     * @return array
     */
    public function formatInfoList($list)
    {
        return $this->formatList(
            $list,
            [
                [$this, 'formatIconNew'],
                [$this, 'formatIconRequired'],
                [$this, 'formatIconImportant'],
            ]
        );
    }

    /**
     * Format failure list
     * @param array $list
     * @return array
     */
    public function formatFailureList($list)
    {
        return $this->formatList(
            $list,
            [
                [$this, 'formatIconNew'],
                [$this, 'formatIconFailureOccurrence'],
                [$this, 'formatIconFailureRecovery'],
            ]
        );
    }
}
