<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\FreegameDeveloper\InquiryMessage;
use App\Models\FreegameDeveloper\InquiryReplayStaff;
use App\Models\Freegame\ApplicationDevice;

class ReportInquiryReplyService extends CustomService
{
    protected $application;
    protected $developerApplication;
    protected $appTitleType;
    protected $inquiryMessage;
    protected $inquiryReplayStaff;
    protected $applicationDevice;

    public function __construct(
        Application $application,
        DeveloperApplication $developerApplication,
        InquiryMessage $inquiryMessage,
        InquiryReplayStaff $inquiryReplayStaff,
        ApplicationDevice $applicationDevice
    ) {
        $this->application          = $application;
        $this->developerApplication = $developerApplication;
        $this->inquiryMessage       = $inquiryMessage;
        $this->inquiryReplayStaff   = $inquiryReplayStaff;
        $this->applicationDevice    = $applicationDevice;
    }

    public function getFormData()
    {
        $reportInquiryReply = config('forms.ReportInquiryReply');
        $appTitleType = $this->getAppTitleType();
        $humanInCharge = $this->getHumanInCharge(array_keys($appTitleType));
        return [
            'menuName'      => $reportInquiryReply['menuName'],
            'screenName'    => $reportInquiryReply['screenName'],
            'periodType'    => $reportInquiryReply['exportCsv']['defaultValues']['periodType'],
            'deviceType'    => $reportInquiryReply['exportCsv']['defaultValues']['deviceType'],
            'timeCheck'     => $reportInquiryReply['exportCsv']['defaultValues']['timeCheck'],
            'appTitle'      => $reportInquiryReply['exportCsv']['defaultValues']['appTitle'],
            'appTitleType'  => $appTitleType,
            'humanInCharge' => $humanInCharge
        ];
    }

    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $this->appTitleType;
    }

    /**
     * @param  array $appId list app id
     * @return array
     */
    public function getHumanInCharge($appId)
    {
        if (!$appId) {
            return [];
        }
        if ($human = $this->inquiryMessage->getInquiryStaff($appId)) {
            return $human->toArray();
        }
        return [];
    }

    /**
     * @param  string  $staffName
     * @param  integer $appId
     * @return boolean
     */
    public function isValidHuman($staffName, $appId)
    {
        if ($staffName == "all") {
            return (bool)$this->inquiryReplayStaff->countByAppId($appId);
        }
        return (bool)$this->inquiryReplayStaff->findByStaffNameAndAppId($staffName, $appId);
    }

    /**
     * get csv header, filename config and case submited
     * @param  array $request $request->toArray()
     * @return array
     */
    public function getCsvFormatConfig($request)
    {
        $csvConfig        = config('forms.ReportInquiryReply.exportCsv');
        $request['begin'] = date('Y-m-d', strtotime($request['begin']));
        $request['end']   = date('Y-m-d', strtotime($request['end']));

        if (isset($request['time_check']) && $request['time_check']) {
            if ((isset($request['human_in_charge'])
            && !$request['human_in_charge'] || $request['human_in_charge'] == 'all')
            || !isset($request['human_in_charge'])) {
                return array(
                    'filename' => sprintf($csvConfig['filename']['byDateTime'], $request['begin'], $request['end']),
                    'header'   => $csvConfig['header']['byDateTime'],
                    'filter'   => 'byDateTime',
                );
            }
            return array(
                'filename' => sprintf(
                    $csvConfig['filename']['byHumanInChargeAndDateTime'],
                    $request['human_in_charge'],
                    $request['begin'],
                    $request['end']
                ),
                'header'   => $csvConfig['header']['byHumanInChargeAndDateTime'],
                'filter'   => 'byHumanInChargeAndDateTime',
            );
        } elseif (isset($request['human_in_charge'])
            && $request['human_in_charge']
            && $request['human_in_charge'] != 'all') {
            return array(
                'filename' => sprintf(
                    $csvConfig['filename']['byHumanInChargeAndDate'],
                    $request['human_in_charge'],
                    $request['begin'],
                    $request['end']
                ),
                'header'   => $csvConfig['header']['byHumanInChargeAndDate'],
                'filter'   => 'byHumanInChargeAndDate',
            );
        }
        return array(
            'filename' => sprintf($csvConfig['filename']['byDate'], $request['begin'], $request['end']),
            'header'   => $csvConfig['header']['byDate'],
            'filter'   => 'byDate',
        );
    }

    /**
     * get data from db then format and export to csv
     * @param  array  $request  $request->toArray()
     * @param  string $filter   the filter depend on user input selection
     * @return mixed
     */
    public function getCsvList($request, $filter)
    {
        $bindings          = [];
        $request['begin']  = date('Y-m-d', strtotime($request['begin']));
        $request['end']    = date('Y-m-d', strtotime($request['end']));
        $request['filter'] = $filter;

        $sql      = $this->inquiryMessage->getInquiriesToExport($request, true, $bindings);
        $result   = $this->inquiryMessage->getExportCsvData($sql, $bindings);
        $appTitle = $this->getAppTitleType();

        if ($filter === 'byDateTime' || $filter === 'byHumanInChargeAndDateTime') {
            $exportByHours = true;
        } else {
            $exportByHours = false;
        }

        if ($filter === 'byDate' || $filter === 'byDateTime') {
            $exportSendReceive = true;
        } else {
            $exportSendReceive = false;
        }

        $rawData = $this->fetchReportResult($result);
        $this->setParentDevice($rawData);
        $prepareData = $this->prepareData($rawData);

        $consecutiveDateRange = $this->generateConsecutiveDateRange($request['begin'], $request['end']);
        if ($exportByHours) {
            $consecutiveDateRange = $this->generateHourOfDate($consecutiveDateRange);
        }

        $this->exportCsvData($consecutiveDateRange, $prepareData, $exportByHours, $exportSendReceive, $request);
    }

    /**
     * @param  array $dataIn
     * @return array
     */
    protected function fetchReportResult($dataIn)
    {
        $dataOut = [];
        while ($row = $dataIn->fetch()) {
            if ($row) {
                $dataOut[] = $this->formatRawData($row);
            }
        }
        return $dataOut;
    }

    /**
     * @param array &$data
     */
    protected function setParentDevice(&$data)
    {
        foreach ($data as &$item) {
            if (!$item['device']) {
                $parentDevice = $this->findDeviceByInquiryId($data, $item['inquiryId']);
                if ($parentDevice) {
                    $item['device'] = $parentDevice;
                }
            }
        }
    }

    /**
     * @param  array  $data
     * @param  string $inquiryId
     * @return mixed  returns the device found in the data array or db, if no matching, return null
     */
    protected function findDeviceByInquiryId($data, $inquiryId)
    {
        foreach ($data as $key => $value) {
            if ($value['inquiryId'] == $inquiryId && $value['isParent'] == 1) {
                return $value['device'];
            }
        }
        if ($inquiryMessage = $this->inquiryMessage->getByInquiryId($inquiryId)) {
            return $inquiryMessage->toArray()['device'];
        }
        return null;
    }

    /**
     * format data from sql result
     * @param  array   $data         description
     * @return array   $result       return a formated array
     */
    protected function formatRawData($data)
    {
        $result = [
            'date'      => $data->date,
            'appTitle'  => $data->app_title,
            'staffName' => $data->staff_name,
            'isParent'  => $data->is_parent,
            'id'        => $data->id,
            'inOut'     => $data->in_out
        ];

        if (isset($data->time)) {
            $result['time'] = $data->time;
        }

        $result['device'] = $data->device;
        $result['inquiryId'] = $data->inquiry_id;

        return $result;
    }

    /**
     * export csv data line by line
     * @param  array   $dateTimeRange
     * @param  array   $prepareData            the result of prepareData function
     * @param  boolean $exportByHours          is export by hours?
     * @param  boolean $exportSendReceive      should export send_receive?
     * @param  array   $request
     * @return string                          echo data line by line
     */
    protected function exportCsvData($dateTimeRange, $prepareData, $exportByHours, $exportSendReceive, $request)
    {
        $csvConfig = config('forms.ReportInquiryReply.exportCsv.defaultValues');
        $appTitle  = (isset($request['app_title']) && $request['app_title'])
                        ? $request['app_title'] : $csvConfig['appTitle'];
        $staffName = $this->getStaffName($request, $csvConfig);
        
        foreach ($dateTimeRange as $dateTimeFromRange) {
            $separateDateTime = $this->getSeparateDateTime($dateTimeFromRange, $exportByHours);
            $isPrint = false;

            foreach ($prepareData as $dateTimeFromPrepareData => $data) {
                if ($dateTimeFromPrepareData == $dateTimeFromRange) {
                    $isPrint = true;
                    $this->printData(
                        $printCase = 'summary',
                        $separateDateTime,
                        $appTitle,
                        $staffName,
                        $exportByHours,
                        $exportSendReceive,
                        $data
                    );

                    if ($request['human_in_charge'] == 'all') {
                        foreach ($data as $stfName => $item) {
                            if (is_array($item)) {
                                if ($stfName != 'unknownName' && $stfName != 'blankName') {
                                    $this->printData(
                                        $printCase = 'detail',
                                        $separateDateTime,
                                        $appTitle,
                                        $stfName,
                                        $exportByHours,
                                        $exportSendReceive,
                                        $item
                                    );
                                }
                            }
                        }
                        if (isset($data['blankName']) && $data['blankName']['totalSend']) {
                            $stfName = $csvConfig['unknownStaffName'];
                            $this->printData(
                                $printCase = 'detail',
                                $separateDateTime,
                                $appTitle,
                                $stfName,
                                $exportByHours,
                                $exportSendReceive,
                                $data['blankName']
                            );
                        }
                        if (isset($data['unknownName']) && !$data['unknownName']['totalReceive']) {
                            $this->printData(
                                $printCase = 'detail',
                                $separateDateTime,
                                $appTitle,
                                $csvConfig['unknownStaffName'],
                                $exportByHours,
                                $exportSendReceive,
                                $data['unknownName']
                            );
                        }
                    }
                }
            }
            if (!$isPrint) {
                $this->printData(
                    $printCase = 'nodata',
                    $separateDateTime,
                    $appTitle,
                    $staffName,
                    $exportByHours,
                    $exportSendReceive
                );
            }
        }
    }

    protected function getStaffName($request, $csvConfig)
    {
        if (isset($request['human_in_charge'])) {
            if ($request['human_in_charge'] == 'all' || $request['human_in_charge'] == '') {
                $staffName = $csvConfig['staffName'];
            } else {
                $staffName = $request['human_in_charge'];
            }
        } else {
            $staffName = $csvConfig['staffName'];
        }
        return $staffName;
    }

    /**
     * to separate all data from string
     * @param  string  $data         Y/m/d H:i:s, example: '2017/08/20 00:00:00'
     * @param  boolean $exportByHours
     * @return array                 array("date" => "2017/08/20", ["time" => "00:00:00"])
     */
    protected function getSeparateDateTime($data, $exportByHours)
    {
        $arrData = explode(' ', $data);
        $result = array(
            'date'      => date("Y/m/d", strtotime($arrData[0])),
        );

        if ($exportByHours) {
            $result['time'] = date("H:i:s", strtotime($arrData[1]));
        }
        return $result;
    }

    /**
     * generate consecutive date range
     * @param  string  $begin begin time, ex: 2017-12-01 00:00:00
     * @param  string  $end   end time, ex: 2017-12-31 23:59:59
     * @return array   $result      consecutive date range
     */
    protected function generateConsecutiveDateRange($begin, $end)
    {
        $begin  = strtotime($begin);
        $end    = strtotime($end);
        $result = [];

        while ($begin <= $end) {
            $begin = date("Y/m/d", $begin);
            $result[] = $begin;
            $begin = strtotime($begin . "+1 day");
        }
        return $result;
    }

    /**
     * generate hour from 1 to 23 in a date
     * @param  array $dateRangeDeviceList
     * @return array $result
     */
    protected function generateHourOfDate($dateRangeDeviceList)
    {
        $result = [];
        $maxHour = config('forms.ReportInquiryReply.exportCsv.defaultValues.maxHourInDate');
        foreach ($dateRangeDeviceList as $item) {
            for ($i = 0; $i < $maxHour; $i ++) {
                $result[] = $item . ($i < 10 ? " 0$i:00:00" : " $i:00:00");
            }
        }
        return $result;
    }

    protected function prepareData($rawData)
    {
        $groupByDate = [];
        foreach ($rawData as $data) {
            $key = $data['date'];
            if (is_null($data['staffName'])) {
                $data['staffName'] = 'unknownName';
            } elseif ($data['staffName'] == '') {
                $data['staffName'] = 'blankName';
            }
            $groupByDate[$key][] = $data;
        }

        foreach ($groupByDate as $date => $data) {
            $receivePc = $sendPc = $receiveSp = $sendSp = $receiveMobile = $sendMobile = 0;
            foreach ($data as $item) {
                if ($item['inOut'] == 'in') {
                    ${'receive' . ucfirst($item['device'])}++;
                } elseif ($item['inOut'] == 'out') {
                    ${'send' . ucfirst($item['device'])}++;
                }
            }
            $groupByDate[$date]['receivePC']        = $receivePc;
            $groupByDate[$date]['receiveSP']        = $receiveSp;
            $groupByDate[$date]['receiveMobile']    = $receiveMobile;
            $groupByDate[$date]['sendPC']           = $sendPc;
            $groupByDate[$date]['sendSP']           = $sendSp;
            $groupByDate[$date]['sendMobile']       = $sendMobile;
            $totalReceive                           = $receivePc + $receiveSp + $receiveMobile;
            $totalSend                              = $sendPc + $sendSp + $sendMobile;
            $groupByDate[$date]['totalReceive']     = $totalReceive;
            $groupByDate[$date]['totalSend']        = $totalSend;
            $groupByDate[$date]['totalReceiveSend'] = $totalReceive + $totalSend;
        }

        $groupByName = [];
        foreach ($groupByDate as $date => $data) {
            foreach ($data as $key => $item) {
                if (is_array($item)) {
                    $groupByName[$date][$item['staffName']][] = $item;
                } else {
                    $groupByName[$date][$key] = $item;
                }
            }
        }

        foreach ($groupByName as $date => $data) {
            foreach ($data as $staffName => $items) {
                if (is_array($items)) {
                    $receivePc = $sendPc = $receiveSp = $sendSp = $receiveMobile = $sendMobile = 0;
                    foreach ($items as $item) {
                        if ($item['inOut'] == 'in') {
                            ${'receive' . ucfirst($item['device'])}++;
                        } elseif ($item['inOut'] == 'out') {
                            ${'send' . ucfirst($item['device'])}++;
                        }
                        $groupByName[$date][$staffName]['receivePC']        = $receivePc;
                        $groupByName[$date][$staffName]['receiveSP']        = $receiveSp;
                        $groupByName[$date][$staffName]['receiveMobile']    = $receiveMobile;
                        $groupByName[$date][$staffName]['sendPC']           = $sendPc;
                        $groupByName[$date][$staffName]['sendSP']           = $sendSp;
                        $groupByName[$date][$staffName]['sendMobile']       = $sendMobile;
                        $totalReceive                                       = $receivePc + $receiveSp + $receiveMobile;
                        $totalSend                                          = $sendPc + $sendSp + $sendMobile;
                        $groupByName[$date][$staffName]['totalReceive']     = $totalReceive;
                        $groupByName[$date][$staffName]['totalSend']        = $totalSend;
                        $groupByName[$date][$staffName]['totalReceiveSend'] = $totalReceive + $totalSend;
                    }
                }
            }
        }
        return $groupByName;
    }

    protected function printData($printCase, $dateTime, $appTitle, $staffName, $exportByHours, $exportSendReceive, $data = [])
    {
        $result = [
            'date'      => $dateTime['date'],
            'appTitle'  => $appTitle,
            'staffName' => $staffName,
        ];
        if ($exportByHours) {
            $result['time'] = $dateTime['time'];
        }

        switch ($printCase) {
            case 'summary':
                $result['receivePC']     = $data['receivePC'];
                $result['receiveSP']     = $data['receiveSP'];
                $result['receiveMobile'] = $data['receiveMobile'];
                $result['totalReceive']  = $data['totalReceive'];
                $result['totalSend']     = $data['totalSend'];
                if ($exportSendReceive) {
                    $result['totalReceiveSend'] = $data['totalReceiveSend'];
                }
                break;
            case 'detail':
                $result['receivePC']     = $data['receivePC'] ? : '';
                $result['receiveSP']     = $data['receiveSP'] ? : '';
                $result['receiveMobile'] = $data['receiveMobile'] ? : '';
                $result['totalReceive']  = $data['totalReceive'] ? : '';
                $result['totalSend']     = $data['totalSend'] ? : '';
                break;
            case 'nodata'://fall through default
            default:
                $result['receivePC']     = 0;
                $result['receiveSP']     = 0;
                $result['receiveMobile'] = 0;
                $result['totalReceive']  = 0;
                $result['totalSend']     = 0;
                if ($exportSendReceive) {
                    $result['totalReceiveSend'] = 0;
                }
                break;
        }
        echo mb_convert_encoding(implode(',', $result), 'sjis-win', 'UTF-8')."\n";
        return;
    }
}
