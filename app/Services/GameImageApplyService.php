<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\FreegameDeveloper;
use App\Models\FreegameDeveloper\ApplicationImage;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\ApplicationTagRef;

class GameImageApplyService extends CustomService
{
    protected $application;
    protected $applicationImage;
    protected $developerApplication;
    protected $infoService;
    protected $controllerName;
    protected $applicationTagRef;

    public function __construct(
        Application           $application,
        ApplicationImage      $applicationImage,
        DeveloperApplication  $developerApplication,
        GameImageInfoService  $infoService,
        ApplicationTagRef $applicationTagRef
    ) {
        $this->application          = $application;
        $this->applicationImage     = $applicationImage;
        $this->developerApplication = $developerApplication;
        $this->infoService          = $infoService;
        $this->applicationTagRef    = $applicationTagRef;
    }

//*********************************************************************************************************************
    /**
     * 詳細取得
     * @param  integer $imgId
     * @return array
     */
    public function getDetail($imgId)
    {
        $imgData = $this->applicationImage->getByIdAndDeveloper($imgId, auth_user_id());

        if ($imgData) {
            $appData = $this->getApplication($imgData->app_id);
            if ($appData) {
                $this->setFileDetail($imgData);
                $this->setApplicationDetail($imgData);
            } else {
                $imgData->exists = false;
            }
        }
        return $imgData;
    }

    /**
     * ファイル情報設定
     * @param array $imgData
     */
    private function setFileDetail($imgData)
    {
        if ($imgData->image_type == config('forms.GameImage.gameImage')
        && (strcasecmp($imgData->device, config('forms.GameImage.pc')) === 0)) {
            $imgData->file_url = $imgData->expansion_image_url;
        } else {
            $imgData->file_url = $imgData->image_url;
        }

        $path = pathinfo($imgData->file_url);
        $imgData->file_type    = isset($path['extension']) ? $path['extension'] : '';
    }

    /**
     * アプリケーション情報設定
     * @param array $imgData
     */
    private function setApplicationDetail($imgData)
    {
        $appData = $this->application->getOne($imgData->app_id);
        if (empty($appData)) {
            return false;
        }

        $title   = $appData->title;
        $type    = [];

        if ($appData->general == 1) {
            $type[] = '一般';
        }

        if ($appData->adult   == 1) {
            $type[] = 'アダルト';
        }
        $imgData->app_title = $title;
        $imgData->app_type  = implode('／', $type);
    }

//*********************************************************************************************************************
    /**
     * ユーザタイトル取得
     * @param  integer $appId
     * @return array;
     */
    public function getApplication($appId)
    {
        if (auth_is_user_sap()) {
            $appData = $this->developerApplication->getApplication($appId, auth_user_id());
            $appData = $this->application->getOne($appData['app_id']);
        } else {
            $appData = $this->application->getOne($appId);
        }
        return $appData;
    }

    /**
     * セレクトボックス用ユーザタイトル一覧取得
     * @return array;
     */
    public function getSelectApplicationList()
    {
        $devList    = $this->developerApplication->getApplicationAppIdList(['developer_id' => auth_user_id()]);
        $appIds     = [];

        foreach ($devList as $dev) {
            $appIds[$dev['app_id']] = $dev['app_id'];
        }
        $selectList = $this->application->getListTitleSortByBinaryTitle($appIds)->all();
        return $selectList;
    }

    //=================================================================================================================
    /**
     * 登録情報表示用配列取得
     * @param  integer $appId
     * @return array
     */
    public function getViewInfoList($appId)
    {
        $keys = $this->infoService->getDataKeyList();
        $list = [];

        foreach ($keys as $key) {
            $info = $this->infoService->getData($appId, $key);
            $data = $info->toArrayViewData();

            if ($data) {
                $key          = key($data);
                $list[$key][] = $data[$key];
            }
        }
        return $list;
    }

    /**
     * 登録情報セレクトボックス用配列取得
     * @param  integer $appId
     * @param boolean $movie 動画
     * @return array
     */
    public function getSelectInfoList($appId, $movie=false)
    {
        $keys = $this->infoService->getDataKeyList();
        $list = [];

        foreach ($keys as $key) {
            if (in_array($key, ['thumbnailPx80', 'thumbnailPx60'])) {
                continue;
            }

            // TODO : サムネイル（総合トップ等）を正式稼働する際に取り除く
            if ($key == 'overallRatedThumbnail') {
                continue;
            }

            $info = $this->infoService->getData($appId, $key);
            $data = $info->toArraySelectData($movie);

            if ($data) {
                $key        = key($data);
                $list[$key] = $data[$key];
            }
        }
        return $list;
    }

//*********************************************************************************************************************
    /**
     * 登録中画像一覧取得
     * @param  integer $appId
     * @return array
     */
    public function getExamRegisterList($appId)
    {
        $param = [
                'app_id'       => $appId,
                'developer_id' => auth_user_id(),
        ];
        $imgDataList = $this->applicationImage->getExamRegisterList($param, 'id desc');

        foreach ($imgDataList as $imgData) {
            $this->setFileDetail($imgData);
            $this->setApplicationDetail($imgData);
        }
        return $imgDataList;
    }

    /**
     * 画像検索一覧取得
     * @param  array $param
     * @return array
     */
    public function getSearchList($param)
    {
        // 検索条件設定
        if (request()->has('search')) {
            $param   = session("{$this->controllerName}.search", []);
            request()->merge($param);
        }
        if (! isset($param['perPage'])) {
            $param  += ['perPage'      => config('forms.GameImageApply.perPage')];
        }
        if (! isset($param['page'])) {
            $param  += ['page' => 1];
        }
        $param = array_only($param, [
            'perPage',
            'request_date_from',
            'request_date_to',
            'app_id',
            'image_type',
            'device',
            'examination_result',
            'examination_situation',
            'page'
        ]);
        
        $appends = $param;
        request()->session()->set("{$this->controllerName}.search", $param);

        // その他条件
        $param      += ['developer_id' => auth_user_id()];

        // 取得・整形
        $imgDataList = $this->applicationImage->getList($param, 'request_date desc, id desc');
        $imgDataList->appends($appends);

        foreach ($imgDataList as $imgData) {
            $this->setFileDetail($imgData);
            $this->setApplicationDetail($imgData);
        }
        return $imgDataList;
    }

    /**
     * 画像の申請数を取得する
     * @param  array $param
     * @return array
     */
    public function getApplyCount($param)
    {
        $param = array_only($param, [
            'app_id',
            'image_type',
            'image_size',
            'device',
            'examination_result',
            'examination_situation',
        ]);
        $param += ['developer_id' => auth_user_id()];

        $imgDataList = $this->applicationImage->getList($param);
        return $imgDataList->count();
    }

//*********************************************************************************************************************
    /**
     * 画像登録
     * @param  array $param
     * @return boolean
     */
    public function registerStore($param)
    {
        FreegameDeveloper::beginTransaction();
        try {
            $config = config('forms.GameImage');
            $appId  = $param['app_id'];
            $file   = $param['upfile'];
            $type   = explode('-', $param['type']);

            $param  = [
                    'developer_id'             => auth_user_id(),
                    'app_id'                   => $appId,
                    'image_url'                => '',
                    'expansion_image_url'      => '',
                    'image_type'               => $type[0],
                    'image_size'               => $type[1] ?: $config['none'],
                    'device'                   => $type[2] ?: $config['deviceNone'],
                    'examination_situation'    => $config['register'],
                    'examination_result'       => $config['none'],
                    'request_date'             => timestamp_to_sqldate(now_stamp()),
                    'examination_date'         => '0000-00-00 00:00:00',
                    'exmaination_developer_id' => 0,
                    'sort_num'                 => 0,
            ];
            $imgData = $this->applicationImage->add($param);
            $info    = $this->infoService->getDataByType($appId, $type[0], $type[1], $type[2]);

            // 画像登録
            $upUrl   = $this->uploadImage($imgData, $file->getRealPath(), $file->getMimeType());

            // URL更新
            if ($imgData->image_type == $config['gameImage']
            && (strcasecmp($imgData->device, $config['pc']) === 0)) {
                $param = ['expansion_image_url' => $upUrl];
            } else {
                $param = ['image_url'           => $upUrl];
            }
            $this->applicationImage->edit($imgData->id, $param);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 動画登録
     * @param  array $param
     * @return boolean
     */
    public function registerMovieStore($param)
    {

        FreegameDeveloper::beginTransaction();
        try {
            $config  = config('forms.GameImage');
            $appId   = $param['app_id'];
            $movieId = $param['movie_id'];
            $type    = explode('-', $param['type']);

            $param  = [
                    'developer_id'             => auth_user_id(),
                    'app_id'                   => $appId,
                    'image_url'                => $movieId,
                    'expansion_image_url'      => '',
                    'image_type'               => $type[0],
                    'image_size'               => $type[1] ?: $config['none'],
                    'device'                   => $type[2] ?: $config['deviceNone'],
                    'examination_situation'    => $config['register'],
                    'examination_result'       => $config['none'],
                    'request_date'             => timestamp_to_sqldate(now_stamp()),
                    'examination_date'         => '0000-00-00 00:00:00',
                    'exmaination_developer_id' => 0,
                    'sort_num'                 => 0,
            ];
            $this->applicationImage->add($param);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 画像審査設定
     * @param  array $imgDataList
     * @return boolean
     */
    public function store($imgDataList)
    {
        FreegameDeveloper::beginTransaction();
        try {
            $param = [
                    'examination_situation' => config('forms.GameImage.review'),
                    'examination_result'    => config('forms.GameImage.none'),
                    'request_date'          => timestamp_to_sqldate(now_stamp()),
            ];

            foreach ($imgDataList as $imgData) {
                $this->applicationImage->edit($imgData->id, $param);
            }

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 削除
     * @param  array $imgData
     * @return boolean
     */
    public function destroy($imgData)
    {
        FreegameDeveloper::beginTransaction();
        try {
            $imgId = $imgData->id;
            $this->applicationImage->del($imgId);

            // 画像削除
            $this->deleteImage($imgData);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

//*********************************************************************************************************************
    /**
     * 画像登録
     * @param  array  $imgData
     * @param  string $fileUrl
     * @param  string $fileType
     * @return string
     */
    private function uploadImage($imgData, $fileUrl, $fileType)
    {
        $imgId = $imgData->id;
        $upUrl = '';

        $info  = $this->infoService->getDataByType(
            $imgData->app_id,
            $imgData->image_type,
            $imgData->image_size,
            $imgData->device
        );

        if ($fileData = $info->toArrayUploadData($imgId, $fileType)) {
            $upUrl    = $fileData['url'];
            $this->uploadFile($fileUrl, $fileData['name'], $fileData['dir']);
        }
        return $upUrl;
    }

    /**
     * 画像削除
     * @param  array   $imgData
     * @return string
     */
    private function deleteImage($imgData)
    {
        $imgId    = $imgData->id;
        $fileUrl  = $imgData->file_url;
        $fileType = $imgData->file_type;
        $upUrl    = '';

        $info     = $this->infoService->getDataByType(
            $imgData->app_id,
            $imgData->image_type,
            $imgData->image_size,
            $imgData->device
        );

        if ($fileData = $info->toArrayUploadData($imgId, $fileType)) {
            $upUrl    = $fileData['url'];
            $this->deleteFile($fileData['dir'].$fileData['name']);
        }
        return $upUrl;
    }

//*********************************************************************************************************************
    /**
     * 登録中か確認
     * @param  array $imgData
     * @return boolean
     */
    public function isEnableRegisterDelete($imgData)
    {
        if ($imgData->examination_situation == config('forms.GameImage.register')) {
            return true;
        }
        return false;
    }

    /**
     * 審査中か確認
     * @param  array $imgData
     * @return boolean
     */
    public function isEnableReviewDelete($imgData)
    {
        if ($imgData->examination_situation == config('forms.GameImage.review')) {
            return true;
        }
        return false;
    }

//*********************************************************************************************************************
    /**
     * セレクトボックス用配列取得：画像種別
     * @return array
     */
    public function getSelectImageType()
    {
        $config = config('forms.GameImage');
        return [
                $config['thumbnail']                => $config['namesImageType'][$config['thumbnail']],
                $config['gameImage']                => $config['namesImageType'][$config['gameImage']],
                $config['overallRatedThumbnail']    => $config['namesImageType'][$config['overallRatedThumbnail']],
                $config['banner']                   => $config['namesImageType'][$config['banner']],
                $config['gameImageGirls']           => $config['namesImageType'][$config['gameImageGirls']],
                $config['movie']                    => $config['namesImageType'][$config['movie']],
                $config['rotateBanner']             => $config['namesImageType'][$config['rotateBanner']],
        ];
    }

    /**
     * セレクトボックス用配列取得：デバイス
     * @return array
     */
    public function getSelectDevice()
    {
        $config = config('forms.GameImage');

        return [
                $config['thumbnail']      => [
                    $config['px60'] => $config['namesImageSize'][$config['px60']],
                    $config['px80'] => $config['namesImageSize'][$config['px80']],
                    $config['px200'] => $config['namesImageSize'][$config['px200']]
                ],
                $config['overallRatedThumbnail']         => [
                    $config['px400'] => $config['namesImageSize'][$config['px400']]
                ],
                $config['gameImage']      => [
                    $config['pc'] => $config['namesDevice'][$config['pc']],
                    $config['sp'] => $config['namesDevice'][$config['sp']]
                ],
                $config['banner']         => [
                    $config['pc'] => $config['namesDevice'][$config['pc']]
                ],
                $config['gameImageGirls']         => [
                    $config['sp'] => $config['namesDevice'][$config['sp']]
                ],
                $config['movie']         => [
                    $config['android'] => $config['namesDevice'][$config['android']],
                    $config['ios'] => $config['namesDevice'][$config['ios']],
                ],
                $config['rotateBanner'] => [
                    $config['pc'] => $config['namesDevice'][$config['pc']],
                    $config['sp'] => $config['namesDevice'][$config['sp']],
                ]
        ];
    }

    /**
     * セレクトボックス用配列取得：審査状況
     * @return array
     */
    public function getSelectExamSituation()
    {
        $config = config('forms.GameImage');

        return [
                $config['review']         => $config['namesExamSituation'][$config['review']],
                $config['completed']      => $config['namesExamSituation'][$config['completed']],
                $config['remand']         => $config['namesExamSituation'][$config['remand']],
                $config['delete']         => $config['namesExamSituation'][$config['delete']],
        ];
    }

    /**
     * セレクトボックス用配列取得：審査結果
     * @return array
     */
    public function getSelectExamResult()
    {
        $config = config('forms.GameImage');

        return [
                $config['ok']             => $config['namesExamResult'][$config['ok']],
                $config['ng']             => $config['namesExamResult'][$config['ng']],
        ];
    }

//*********************************************************************************************************************
    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $applyConfig = config('forms.GameImageApply');
        $config      = config('forms.GameImage');

        return [
                'formData' => [
                        'screenName'  => $applyConfig['screenName'],
                        'breadcrumbs' => $applyConfig['breadcrumbsParent'],
                ],
                'selectImageType'     => $this->getSelectImageType(),
                'selectDevice'        => $this->getSelectDevice(),
                'selectExamSituation' => $this->getSelectExamSituation(),
                'selectExamResult'    => $this->getSelectExamResult(),
                'namesImageType'      => $config['namesImageType'],
                'namesImageSize'      => $config['namesImageSize'],
                'namesDevice'         => $config['namesDevice'],
                'namesExamSituation'  => $config['namesExamSituation'],
                'namesExamResult'     => $config['namesExamResult'],
        ];
    }

    /**
     * コントローラ名設定
     * @param string $controllerName
     */
    public function setControllerName($controllerName)
    {
        $this->controllerName = $controllerName;
    }

    /**
     * Get Girls Application
     * @return array
     */
    public function getGirlsAppList()
    {
        $config = config('forms.GameImageApply');
        return $this->applicationTagRef->getAppIdListByTagId($config['girlsAppTagId'])->all();
    }
}
