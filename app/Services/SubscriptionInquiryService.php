<?php

namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;
use Carbon\Carbon;
use Exception;

/**
 * 定期購入：問い合わせ
 */
class SubscriptionInquiryService extends CustomService
{
    /** @var Application */
    protected $application;
    /** @var DeveloperApplication */
    protected $developerApplication;
    /** @var SubscriptionApiService */
    protected $subscriptionApiService;
    /** @var AccountApiService */
    protected $accountApiService;

    /**
     * コンストラクタ
     * 
     * @param Application $application
     * @param DeveloperApplication $developerApplication
     * @param SubscriptionApiService $subscriptionApiService
     * @param AccountApiService $accountApiService
     */
    public function __construct(
        Application $application,
        DeveloperApplication $developerApplication,
        SubscriptionApiService $subscriptionApiService,
        AccountApiService $accountApiService
    ) {
        $this->application = $application;
        $this->developerApplication = $developerApplication;
        $this->subscriptionApiService = $subscriptionApiService;
        $this->accountApiService = $accountApiService;
    }

    /**
     * フォームに設定するデータを取得
     * 
     * @return array
     */
    public function getFormData()
    {
        return [
            'menuName' => config('forms.SubscriptionInquiry.menuName'),
            'screenName' => config('forms.SubscriptionInquiry.screenName'),
            'appTitleType' => $this->getApplicationTitleList()
        ];
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        $deviceList = config('forms.SubscriptionInquiry.device');
        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap(array_keys($deviceList));
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice(array_keys($deviceList));
        }

        $opts = ['' => ['' => 'タイトルを選択してください']];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $data->title . $deviceList[$data->device];
        }
        return $opts;
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array $list
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        $list = [];
        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return $list;
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }

    /**
     * 定期購入問い合わせ情報を取得する
     * 
     * @param  array $condition　 問い合わせ情報取得に必要な配列
     * @return array $inquiryData 定期購入問い合わせ情報
     */
    public function getSubscriptionInquiry($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-api.api_kind'),
            'device' => $condition['device']
        ];
        // APIのクエリパラメーターに使用する値をまとめる
        $query = [];
        if (!empty($condition['order_id'])) {
            $query['orderId'] = $condition['order_id'];
        }
        if (!empty($condition['inquiry_id'])) {
            $query['inquiryId'] = $condition['inquiry_id'];
        }

        $response = $this->subscriptionApiService->getInquiry($path, $query);
        // 検索結果が取得できた(200)、検索結果が0件(404)を除く
        if (!($response['resultCode'] === 200 || $response['resultCode'] === 404)) {
            throw new Exception($response['resultMessage']);
        }
        // ビューの表示制御のため検索を行ったかも保持する
        $result = [
            'detail' => [],
            'isSearched' => true
        ];
        if (isset($response['response']['body'])) {
            $result['detail'] = $this->formatInquiryDetail($response['response']['body']);
        }

        return $result;
    }

    /**
     * 表示用の問い合わせ詳細パラメーターを生成する
     * 
     * @param  array $body APIレスポンスのボディ
     * @return array 表示用パラメーター
     */
    private function formatInquiryDetail($body)
    {
        $account = $this->accountApiService->getAccountFromOpenId($body['openId']);
        if (!$account['resultStatus']) {
            throw new Exception($account['resultMessage']);
        }

        $purchasedStamp = Carbon::createFromTimestamp($body['purchasedAt']);
        $purchasedAt = $purchasedStamp->format('Y/m/d H:i:s');
        $acknowledgedAt = '-';
        // 承認済み定期購入アイテムのみ日付の値が入っており、未承認の場合はnullが返されるため
        if (!is_null($body['acknowledgedAt'])) {
            $acknowledgedStamp = Carbon::createFromTimestamp($body['acknowledgedAt']);
            $acknowledgedAt = $acknowledgedStamp->format('Y/m/d H:i:s');
        }

        return [
            'gamesId' => isset($account['response']['games_id']) ? $account['response']['games_id'] : '-',
            'orderId' => $body['orderId'],
            'inquiryId' => $body['inquiryId'],
            'subscriptionSku' => $body['subscriptionSku'],
            'subscriptionTitle' => $body['subscriptionTitle'],
            'basePlanSku' => !empty($body['basePlanSku']) ? $body['basePlanSku'] : '-',
            'offerSku' => !empty($body['offerSku']) ? $body['offerSku'] : '-',
            'phaseType' => !empty($body['phaseType']) ? config('forms.SubscriptionInquiry.phaseType.' . $body['phaseType']) : '-',
            'status' => config('forms.SubscriptionInquiry.status.' . $body['status']),
            'purchasedAt' => $purchasedAt,
            'acknowledgedAt' => $acknowledgedAt,
            'acknowlegedUserName' => !empty($body['acknowlegedUserName']) ? $body['acknowlegedUserName'] : '-',
            'developerPayload' => !empty($body['developerPayload']) ? $body['developerPayload'] : '-'
        ];
    }
}
