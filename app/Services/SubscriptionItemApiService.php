<?php

namespace App\Services;

use App\Services\OlympusApiService;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Contracts\Logging\Log;

/**
 * 定期購入商品APIサービス
 */
class SubscriptionItemApiService extends OlympusApiService
{
    /**
     * 定期購入商品APIサービスコンストラクタ
     * 
     * @param HttpClient
     * @param Log
     *
     */
    public function __construct(
        HttpClient $httpClient,
        Log $log
    ) {
        parent::__construct($httpClient, $log);
        $this->baseUrl = config('subscription-item-api.base_uri');
    }

    /**
     * アプリケーション情報詳細取得
     *
     * @param array $pathParameters
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getSubscriptions($pathParameters)
    {
        $endPoint = config('subscription-item-api.path_with_parameters.subscriptions');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 定期購入アイテムを取得
     * 
     * @param array $pathParameters
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getSubscription($pathParameters)
    {
        $endPoint = config('subscription-item-api.path_with_parameters.subscription');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 定期購入アイテムを登録
     * 
     * @param array $pathParameters
     * @param array $bodyParameters
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function storeSubscription($pathParameters, $bodyParameters)
    {
        $endPoint = config('subscription-item-api.path_with_parameters.subscription');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        );
        $options = [
            'json' => $bodyParameters,
        ];

        return $this->executeApi('POST', $endPoint, $options);
    }

    /**
     * 定期購入アイテムを更新
     * 
     * @param array $pathParameters
     * @param array $bodyParameters
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function updateSubscription($pathParameters, $bodyParameters)
    {
        $endPoint = config('subscription-item-api.path_with_parameters.subscription');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        );
        $options = [
            'json' => $bodyParameters,
        ];

        return $this->executeApi('PATCH', $endPoint, $options);
    }

    /**
     * 基本プラン取得
     * 
     * @param array $pathParameter パスパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getBasePlan($pathParameter)
    {
        $endPoint = config('subscription-item-api.path_with_parameters.baseplan');
        $endPoint = $this->buildUrl(
            $endPoint,
            [
                'appId' => $pathParameter['appId'],
                'device' => $pathParameter['device'],
                'kind' => config('subscription-item-api.api_kind'),
                'subsSku' => $pathParameter['subsSku'],
                'planSku' => $pathParameter['planSku']
            ]
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 基本プラン登録
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $bodyParameter ボディパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function storeBasePlan($pathParameter, $bodyParameter)
    {
        $endPoint = $this->buildUrl(
            config('subscription-item-api.path_with_parameters.baseplan'),
            [
                'appId' => $pathParameter['appId'],
                'device' => $pathParameter['device'],
                'kind' => config('subscription-item-api.api_kind'),
                'subsSku' => $pathParameter['subsSku'],
                'planSku' => $pathParameter['planSku']
            ]
        );
        $options = [
            'json' => [
                'priceAmountMicros' => $bodyParameter['priceAmountMicros'],
                'duration' => $bodyParameter['duration'],
                'status' => $bodyParameter['status']
            ]
        ];

        return $this->executeApi('POST', $endPoint, $options);
    }

    /**
     * 基本プラン更新
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $bodyParameter ボディパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function updateBasePlan($pathParameter, $bodyParameter)
    {
        $endPoint = $this->buildUrl(
            config('subscription-item-api.path_with_parameters.baseplan'),
            [
                'appId' => $pathParameter['appId'],
                'device' => $pathParameter['device'],
                'kind' => config('subscription-item-api.api_kind'),
                'subsSku' => $pathParameter['subsSku'],
                'planSku' => $pathParameter['planSku']
            ]
        );
        $options = [
            'json' => [
                'duration'          => $bodyParameter['duration'],
                'priceAmountMicros' => $bodyParameter['priceAmountMicros'],
                'status'            => $bodyParameter['status']
            ]
        ];

        return $this->executeApi('PATCH', $endPoint, $options);
    }

    /**
     * 特典登録
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $bodyParameter ボディパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function storeOffer($pathParameter, $bodyParameter)
    {
        $endPoint = $this->buildUrl(
            config('subscription-item-api.path_with_parameters.offer'),
            [
                'appId' => $pathParameter['appId'],
                'device' => $pathParameter['device'],
                'kind' => config('subscription-item-api.api_kind'),
                'subsSku' => $pathParameter['subsSku'],
                'planSku' => $pathParameter['planSku'],
                'offerSku' => $pathParameter['offerSku']
            ]
        );
        $options = [
            'json' => $bodyParameter,
        ];

        return $this->executeApi('POST', $endPoint, $options);
    }

    /**
     * 基本プラン取得
     * 
     * @param array $pathParameter パスパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getOffer($pathParameter)
    {
        $endPoint = config('subscription-item-api.path_with_parameters.offer');
        $endPoint = $this->buildUrl(
            $endPoint,
            [
                'appId' => $pathParameter['appId'],
                'device' => $pathParameter['device'],
                'kind' => config('subscription-item-api.api_kind'),
                'subsSku' => $pathParameter['subsSku'],
                'planSku' => $pathParameter['planSku'],
                'offerSku' => $pathParameter['offerSku']
            ]
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 特典更新
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $bodyParameter ボディパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function updateOffer($pathParameter, $bodyParameter)
    {
        $endPoint = $this->buildUrl(
            config('subscription-item-api.path_with_parameters.offer'),
            [
                'appId' => $pathParameter['appId'],
                'device' => $pathParameter['device'],
                'kind' => config('subscription-item-api.api_kind'),
                'subsSku' => $pathParameter['subsSku'],
                'planSku' => $pathParameter['planSku'],
                'offerSku' => $pathParameter['offerSku']
            ]
        );
        $options = [
            'json' => $bodyParameter,
        ];

        return $this->executeApi('PATCH', $endPoint, $options);
    }
}
