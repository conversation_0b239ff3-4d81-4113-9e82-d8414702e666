<?php

namespace App\Services;

use App\Services\OlympusApiService;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Contracts\Logging\Log;

/**
 * 定期購入APIサービス
 */
class SubscriptionApiService extends OlympusApiService
{
    /**
     * 定期購入APIサービスコンストラクタ
     * 
     * @param bool 定期購入サンドボックスAPI有効の状態
     * @param HttpClient
     * @param Log
     *
     */
    public function __construct(
        $isSandbox = false,
        HttpClient $httpClient,
        Log $log
     ) {
        parent::__construct($httpClient, $log);
        $this->baseUrl = $isSandbox ? config('subscription-api.subscription_sandbox_base_uri') : config('subscription-api.subscription_base_uri');
    }

    /**
     * アプリケーション情報詳細取得
     *
     * @param int $appId
     * @param string $device
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getApplicationDetails($appId, $device)
    {
        $endPoint = config('subscription-api.path_with_parameters.application_details');
        $endPoint = $this->buildUrl(
            $endPoint,
            [
                'appId' => $appId,
                'device' => $device,
                'kind' => config('subscription-api.api_kind'),
            ]
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 未承認アイテム一覧の取得
     * 
     * @param array $pathParameters configで定義されているURLの変換を行うための連想配列
     * @param array $queryParameters URLの語尾に設定するクエリパラメーター
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getUnapprovedList($pathParameters, $queryParameters)
    {
        $endPoint = config('subscription-api.path_with_parameters.unapproved_list');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        ) . '?' . http_build_query($queryParameters);

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 未承認アイテムの情報取得
     * 
     * @param array $pathParameters configで定義されているURLの変換を行うための連想配列
     * @param array $queryParameters URLの語尾に設定するクエリパラメーター
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getUnapprovedItem($pathParameters, $queryParameters)
    {  
        $endPoint = config('subscription-api.path_with_parameters.unapproved_item');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        ) . '?' . http_build_query($queryParameters);

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 未承認アイテムの承認
     * 
     * @param array $pathParameters configで定義されているURLの変換を行うための連想配列
     * @param array $bodyParameters リクエストBody部に設定する配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function setUnapprovedItemApprove($pathParameters, $bodyParameters)
    {
        $endPoint = config('subscription-api.path_with_parameters.unapproved_item_approve');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        );
        $options = [
            'json' => $bodyParameters,
        ];

        return $this->executeApi('PATCH', $endPoint, $options);
    }


    /**
     * レシート問い合わせ
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $queryParameter クエリパラメーター配列
     * 
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getInquiry($pathParameter, $queryParameter)
    {
        $query = http_build_query($queryParameter);
        $endPoint = $this->buildUrl(
            config('subscription-api.path_with_parameters.receipt_inquiry'),
            $pathParameter
        );
        $endPoint .= '?' . $query;

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 利用状況レポート
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $queryParameter クエリパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getUtilizationReport($pathParameter, $queryParameter)
    {
        $query = http_build_query($queryParameter);
        $endPoint = $this->buildUrl(
            config('subscription-api.path_with_parameters.utilization_report'),
            $pathParameter
        );
        $endPoint .= '?' . $query;

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 課金ログ取得
     * 
     * @param array $pathParameters パスパラメーター配列
     * @param array $bodyParameters リクエストBody用配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getPaymentLog($pathParameters, $bodyParameters)
    {
        
        $endPoint = config('subscription-api.path_with_parameters.payment_log');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameters
        );

        $options = [
            'json' => $bodyParameters,
        ];

        return $this->executeApi('GET', $endPoint, $options);
    }

    /**
     * ユーザーの購入した定期購入アイテム取得
     * 
     * @param array $pathParameter パスパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function getUserItems($pathParameter)
    {
        $endPoint = config('subscription-api.path_with_parameters.user_item');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameter
        );

        return $this->executeApi('GET', $endPoint);
    }

    /**
     * 購入済み有効な定期購入アイテムの解約予約
     * 
     * @param array $pathParameter パスパラメーター配列
     * @param array $bodyParameter ボディパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function cancelReservation($pathParameter, $bodyParameter)
    {
        $endPoint = config('subscription-api.path_with_parameters.user_item_cancel');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameter
        );
        $options = [
            'json' => $bodyParameter,
        ];

        return $this->executeApi('PATCH', $endPoint, $options);
    }

    /**
     * 購入済み定期購入アイテムの解約予約を解除
     * 
     * @param array $pathParameter パスパラメーター配列
     * @return array ['resultMessage' => string, 'resultData' => array, 'resultStatus' => int]
     */
    public function resetReservation($pathParameter)
    {
        $endPoint = config('subscription-api.path_with_parameters.user_item_activation');
        $endPoint = $this->buildUrl(
            $endPoint,
            $pathParameter
        );

        return $this->executeApi('PATCH', $endPoint);
    }
}
