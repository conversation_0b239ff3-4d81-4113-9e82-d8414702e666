<?php
namespace App\Services;

use App\Models\FreegameDeveloper\Developer;
use App\Models\FreegameDeveloper\DeveloperAuthRef;
use Cache;
use Carbon\Carbon;

/**
 * ログイン認証
 */
class UsersService extends CustomService
{

    // アカウントがロック対象になる未ログイン月数
    const LOCK_INACTIVE_USER_MONTH_LIMIT = 3;

    protected $developer;

    protected $developerAuthRef;

    public function __construct(Developer $developer, DeveloperAuthRef $developerAuthRef)
    {
        $this->developer = $developer;
        $this->developerAuthRef = $developerAuthRef;
    }

    public function setFormData()
    {
        $input = request()->only([
            'return_url',
            'login_id',
            'password',
            'to'
        ]);
        foreach ($input as $key => $val) {
            while (is_array($val)) {
                $val = last($val);
            }
            $input[$key] = $val;
        }
        request()->merge($input);
    }

    public function getFormData()
    {
        return [
            'screenName'   => config('forms.Users.screenName'),
            'appEnvType'   => config('forms.Users.appEnvType')
        ];
    }

    public function getMessage($code = null)
    {
        $list = [
            'userNotFound' => trans('validationmessage.MSG230'),
            'userLoginTempLocked' => trans('validationmessage.MSG231'),
            'userLoginLocked' => trans('validationmessage.MSG319')
        ];
        if (isset($code)) {
            return $list[$code];
        }
        return $list;
    }

    public function getLoginDataKey($login_id)
    {
        return sprintf(config('forms.Users.loginDataKey'), $login_id);
    }

    public function getLoginData($login_id)
    {
        $key = $this->getLoginDataKey($login_id);
        $data = Cache::get($key);
        if (empty($data)) {
            $data['login_error_date'] = date('Y-m-d H:i:s');
            $data['login_error_count'] = 0;
            $data['login_locked'] = 0;
        } else {
            $data = unserialize($data);
        }
        return $data;
    }

    public function setLoginData($login_id, $data = [])
    {
        $key = $this->getLoginDataKey($login_id);
        $expire = Carbon::now()->addMinute(env('AUTH_LOCKOUT_TIME', 30));
        if (empty($data)) {
            Cache::forget($key);
        } else {
            Cache::put($key, serialize($data), $expire);
        }
    }

    public function login($data)
    {
        $login_id = $data['login_id'];
        $user = $this->developer->getOneByLoginId($login_id);
        if (empty($user->exists) || ! in_array($user->type, config('forms.Users.loginUserType'))) {
            $errros['login_id'] = $this->getMessage('userNotFound');
            return $errros;
        }

        $login_data = $this->getLoginData($login_id);
        if (! empty($login_data['login_locked'])) {
            $errros['login_id'] = $this->getMessage('userLoginTempLocked');
            return $errros;
        }
        $user = $this->developer->getOneForAuth($login_id, $data['password']);
        if (empty($user->exists)) {
            $login_data['login_error_count'] ++;
            $login_data['login_error_date'] = date('Y-m-d H:i:s');
            $max_login_attempts = (int) env('AUTH_MAX_LOGIN_ATTEMPTS', 10);
            if ($login_data['login_error_count'] >= $max_login_attempts) {
                $login_data['login_locked'] = 1;
                $errros['login_id'] = $this->getMessage('userLoginTempLocked');
            } else {
                $login_data['login_locked'] = 0;
                $errros['login_id'] = $this->getMessage('userNotFound');
            }
            $this->setLoginData($login_id, $login_data);
            return $errros;
        }

        // adminではなければ、xヶ月間ログインされてないならアカウントがロックされる。
        if ($user->locked || (env('APP_ENV') === 'production' 
            && $user->type !== 'admin' 
            && $user->last_login_date < Carbon::now()->subMonths(self::LOCK_INACTIVE_USER_MONTH_LIMIT))){

            // DBの情報が更新されてないなら更新する。
            if (!$user->locked) {
                $this->developer->edit(['locked' => true], $user->id);
            }

            // ユーザーがロックされてるならログインせずにエラーメッセージを表示する
            $errros['login_id'] = $this->getMessage('userLoginLocked');

            return $errros;
        }

        // 最新ログイン日を更新する
        $this->developer->edit(['last_login_date' => Carbon::now()], $user->id);

        $this->setLoginData($login_id);
        $condition['developer_id'] = $user->id;
        $role = $this->developerAuthRef->getListForAuth($condition);
        $user->setAuthRole($role->toArray());
        auth()->login($user);
        return true;
    }

    public function logout()
    {
        auth()->user()->removeAuthRole();
        auth()->logout();
        return true;
    }
}
