<?php

namespace App\Services;

use GuzzleHttp\Client as HttpClient;
use Illuminate\Contracts\Logging\Log;
use \Exception;

/**
 * ストアAPIサービス
 */
class StoreApiService
{
    /** @var HttpClient */
    protected $httpClient;

    /**
     * コンストラクタ
     * 
     * @param HttpClient
     */
    public function __construct(
        HttpClient $httpClient
     ) {
        $this->httpClient = $httpClient;
    }

    /**
     * APIのエンドポイントを生成する
     * 
     * @param  string $endPoint   エンドポイント
     * @param  array  $paramaters 置換するパラメーター(連想配列)
     * @return string $endPoint   エンドポイント
     */
    protected function buildUrl($endPoint, $paramaters=[])
    {
        // パスパラメーター{$paramKey}の値を置換する
        foreach($paramaters as $paramKey => $paramValue) {
            $endPoint = str_replace("{{$paramKey}}", $paramValue, $endPoint);
        }

        return $endPoint;
    }

    /**
     * API実行
     * 
     * @param string $method     メソッド
     * @param string $endpoint   エンドポイント
     * @param array  $apiOptions オプション
     * @return array ['resultStatus' => int, 'response' => array]
     */
    protected function executeApi($method, $endpoint, $bodyParams = [])
    {
        $result = [];

        if (empty($method) || empty($endpoint)) {
            throw new Exception('Invalid parameter');
        }

        $url = config('store-api.base_url');
        $url .= $endpoint;

        // オプション情報
        $options = config('store-api.default_options');
        if (!empty($bodyParams)) {
            $options['json'] = $bodyParams;
        }

        // API実行処理
        $response = $this->httpClient->request($method, $url, $options);

        // 結果設定
        $result['statusCode'] = $response->getStatusCode();

        $body = $response->getBody();
        if (!empty($body)) {
            $contents = $body->getContents();
            if (!empty($contents)) {
                $result['response'] = json_decode($contents, true);
            }
        }

        // エラーチェック
        if (!empty($result['response']['error'])) {
            // API がエラー詳細を返している場合、ステータスコードに関わらず例外は発生させない。
            $error = $result['response']['error'];
            \Log::error(
                'Store API call failed :'.
                ' method = '.$method.
                ' url = '.$url.
                ' options = '.json_encode($options).
                ' status_code = '.$result['statusCode'].
                ' code = '.$error['code'].
                ' message = '.$error['message']
            );
        } else {
            $isError = false;

            if ((500 <= $result['statusCode']) && ($result['statusCode'] < 600)) {
                // 500系はエラー扱いにする
                $isError = true;
            }

            if ((400 <= $result['statusCode']) && ($result['statusCode'] < 500)) {
                // 400系は404(Not Found)、410(Gone)以外をエラー扱いにする
                if (!in_array($result['statusCode'], [404, 410])) {
                    $isError = true;
                }
            }

            if ($isError) {
                throw new Exception(
                    'Store API call failed :'.
                    ' method = '.$method.
                    ' url = '.$url.
                    ' options = '.json_encode($options).
                    ' status_code = '.$result['statusCode']
                );
            }
        }

        return $result;
    }

    /**
     * アプリ情報取得API
     *
     * @param int $appId
     * @return array
     */
    public function getIosVersionLatest($appId)
    {
        $url = $this->buildUrl(
            '/ios/app/{app_id}/version/latest',
            ['app_id' => $appId]
        );

        $result = $this->executeApi('GET', $url);

        return $result['response'];
    }

    /**
     * アプリ最新バージョン確定API
     *
     * @param int $appId
     * @param string $adpId
     * @param string $adpVerId
     * @return array
     */
    public function postIosVersionLatest($appId, $adpId, $adpVerId)
    {
        $url = $this->buildUrl(
            '/ios/app/{app_id}/version/latest',
            ['app_id' => $appId]
        );

        $result = $this->executeApi(
            'POST',
            $url,
            [
                'adp_id' => $adpId,
                'adp_ver_id' => $adpVerId,
            ]
        );

        return $result['response'];
    }

    /**
     * MarketplaceToken発行API
     *
     * @param string $appleDeveloperId
     * @return array
     */
    public function postMarketplaceToken($appleDeveloperId)
    {
        $url = $this->buildUrl(
            '/ios/app/marketplace-token/issue'
        );

        $result = $this->executeApi(
            'POST',
            $url,
            [
                'apple_developer_id' => $appleDeveloperId,
            ]
        );

        return $result['response'];
    }
}
