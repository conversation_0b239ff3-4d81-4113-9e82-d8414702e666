<?php

namespace App\Services;

use Exception;
use App\Libs\Apply\Preregistration;
use App\Models\Freegame\ApplicationDevice;
use App\Models\Freegame\ChApplication;
use App\Models\FreegameDeveloper\ApplicationDetail;
use App\Models\FreegameDeveloper\ApplicationImage;
use App\Models\FreegameDeveloper\ChApplicationImage;
use App\Models\FreegameDeveloper\ClApplicationImage;

/**
 * 事前登録申請サービス
 */
class ApplyPreregistrationService
{
    /** @var ApplyPreregistrationApiService */
    private $applyPreregistrationAPIService;

    /** @var ApplyExaminationFileApiService */
    private $applyExaminationFileApiService;
    
    /** @var CommunitiesService */
    protected $communitiesService;

    /** @var TopicService */
    protected $topicService;

    /** @var ApplicationDevice */
    protected $applicationDevice;

    /** @var ChApplication */
    protected $chApplication;

    /** @var ApplicationDetail */
    protected $applicationDetail;

    /** @var ApplicationImage */
    protected $applicationImage;

    /** @var ChApplicationImage */
    protected $chApplicationImage;

    /** @var ClApplicationImage */
    protected $clApplicationImage;

    /**
     * 事前登録サービスコンストラクタ
     *
     * @param ApplyPreregistrationApiService $applyPreregistrationAPIService
     * @param ApplyExaminationFileApiService $applyExaminationFileApiService
     * @param ApplicationDevice $applicationDevice
     * @param ChApplication $chApplication
     * @param ApplicationDetail $applicationDetail
     * @param CommunitiesService $communitiesService
     * @param TopicService $topicService
     * @param ApplicationImage $applicationImage
     * @param ChApplicationImage $chApplicationImage
     * @param ClApplicationImage $clApplicationImage
     */
    public function __construct(
        ApplyPreregistrationApiService $applyPreregistrationAPIService,
        ApplyExaminationFileApiService $applyExaminationFileApiService,
        ApplicationDevice $applicationDevice,
        ChApplication $chApplication,
        ApplicationDetail $applicationDetail,
        CommunitiesService $communitiesService,
        TopicService $topicService,
        ApplicationImage $applicationImage,
        ChApplicationImage $chApplicationImage,
        ClApplicationImage $clApplicationImage
    ) {
        $this->applyPreregistrationAPIService = $applyPreregistrationAPIService;
        $this->applyExaminationFileApiService = $applyExaminationFileApiService;
        $this->applicationDevice = $applicationDevice;
        $this->chApplication = $chApplication;
        $this->applicationDetail = $applicationDetail;
        $this->communitiesService = $communitiesService;
        $this->topicService = $topicService;
        $this->applicationImage = $applicationImage;
        $this->chApplicationImage = $chApplicationImage;
        $this->clApplicationImage = $clApplicationImage;
    }
    
    /**
     * 事前登録情報を取得する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return Preregistration
     */
    public function getPreregistration($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $result = $this->applyPreregistrationAPIService
            ->getPreregistration($appId , $kindAndDevice[0], $kindAndDevice[1]);

        return new Preregistration($result['response']['body']);
    }
    
    /**
     * 審査用の画像を審査に提出する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return void
     */
    public function applyExaminationImages($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'examinationImages' => [
                'similarityCheckMaterials' => (object)[],
                'logicalCheckMaterials' => (object)[],
            ],
        ];
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'examinationImages', $content);
    }
        
    /**
     * ゲーム紹介ページ：デザイン部分素材カテゴリを審査に提出する
     *
     * @return void
     */
    public function applyIntroductionImages(
        $appId, $device, 
        $hasCatchphraseImage, $catchphraseImageText,
        $isDesignated, $isCharacterPriorityDescriptionText,
        $isUseCopyright, $copyrightText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'introductionImages' => [
                'characterImages' => (object)[],
                'titleLogoImages' => (object)[],
                'backgroundImages' => (object)[],
                'screenshotImageDiagram' => (object)[],
                'catchphraseImageDiagram' => [
                    'hasCatchphraseImage' => ($hasCatchphraseImage == 'true'),
                    'catchphraseImageText' => $catchphraseImageText,
                ],
                'isCharacterPriorityNotes' => [
                    'isDesignated' => ($isDesignated == 'true'),
                    'isCharacterPriorityDescriptionText' => $isCharacterPriorityDescriptionText,
                ],
                'copyright' => [
                    'isUseCopyright' => ($isUseCopyright == 'true'),
                    'copyrightText' => $copyrightText,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'introductionImages', $content);
    }

    /**
     * プラットフォーム上に掲載される画像を審査に提出
     *
     * @return void
     */
    public function applyPlatformImages($appId, $device, $releaseScheduleSeasonText)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'platformImages' => [
                'thumbnailImage' => (object)[],
                'preReleaseSmallBanner' => [
                    'hasPreReleaseSmallBanner' => (object)[],
                ],
                'preReleaseBigBanner' => [
                    'hasPreReleaseBigBanner' => (object)[],
                ],
                'releaseScheduleSeason' => [
                    'releaseScheduleSeasonText' => $releaseScheduleSeasonText,
                ],
                'gameIntroductionImage' => [
                    'hasGameIntroductionImage' => (object)[],
                ],
                'android192x192' => [
                    'hasAndroid192x192' => (object)[],
                ],
                'android512x512' => [
                    'hasAndroid512x512' => (object)[],
                ],
                'appleTouchIcon' => [
                    'hasAppleTouchIcon' => (object)[],
                ],
                'gameIntroductionMovie' => [
                    'gameIntroductionMovie' => (object)[],
                ],
                'gameIntroductionThumbnail' => [
                    'gameIntroductionThumbnail' => (object)[],
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'platformImages', $content);
    }

    /**
     * 事前登録サイト情報を更新する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @param  mixed $isFollowingTermsCreate
     * @param  mixed $isDeployOfficialSite
     * @param  mixed $isPreRegisterCheck
     * @param  mixed $meansVerificationText
     * @param  mixed $officialSiteUrlText
     * @return void
     */
    public function applyPreRegistrationSite(
        $appId, $device, 
        $isFollowingTermsCreate,
        $isDeployOfficialSite, $isPreRegisterCheck,
        $meansVerificationText,
        $officialSiteUrlText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'preregistrationSite' => [
                'designDelivery' => (object)[],
                'createdOfficialSite' => [
                    'isFollowingTermsCreate' => $isFollowingTermsCreate,
                ],
                'serverDeploy' => [
                    'isDeployOfficialSite' => $isDeployOfficialSite,
                    'isPreRegisterCheck' => $isPreRegisterCheck,
                ],
                'meansVerification' => [
                    'meansVerificationText' => $meansVerificationText,
                ],
                'officialSiteDetail' => [
                    'officialSiteUrlText' => $officialSiteUrlText,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'preregistrationSite', $content);
    }

    /**
     * サンドボックス環境 事前登録検証を審査に提出
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @param  mixed $isProductionTestingPreparationDone
     * @return void
     */
    public function applySandboxVerification(
        $appId, $device,
        $isUserTypeSpecialProcessingDone,
        $sandboxTestGameAppIdText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'sandboxVerification' => [
                'sandboxPreRegistrationVerifications' => [
                    'isUserTypeSpecialProcessingDone' => $isUserTypeSpecialProcessingDone,
                ],
                'sandboxTestGameAppId' => [
                    'sandboxTestGameAppIdText' => $sandboxTestGameAppIdText,
                ],
            ],
        ]);

        if (empty($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'sandboxVerification', $content);
    }

    /**
     * 動作検証を審査に提出
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @param  mixed $isProductionTestingPreparationDone
     * @return void
     */
    public function applyVerification(
        $appId, $device,
        $isProductionTestingPreparationDone
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'verification' => [
                'preRegistrationVerifications' => [
                    'isProductionTestingPreparationDone' => $isProductionTestingPreparationDone,
                ],
            ],
        ]);

        if (empty($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'verification', $content);
    }
    
    /**
     * 審査用の画像を審査に提出する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return void
     */
    public function applyCommunity($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'community' => [
                'communityCreate' => (object)[],
                'topicCreate' => (object)[],
            ],
        ];
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'community', $content);
    }

    /**
     * ゲーム内画像申請が行われているか確認する
     *
     * @param  mixed $appId
     * @param  mixed $kind
     * @param  mixed $imageType
     * @return void
     */
    public function isExaminationFileUpload($appId, $device, $imageType)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $result = $this->applyExaminationFileApiService->getExaminationFileInfo($appId, $kindAndDevice[0], $imageType, 'preregistration');

        return !empty($result['body']['uploadFileList']);
    }
    
    /**
     * コミュニティが作成されているか
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isCommunityCreate($appId)
    {
        $list = $this->communitiesService->getCommunitiesList($appId);
        $count = count($list);
        return $count > 0;
    }
    
    /**
     * トピックが作成されているか
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isTopicCreate($appId)
    {
        $list = $this->topicService->getList(['app_id' => $appId]);
        $count = count($list);
        return $count > 0;
    }

    /**
     * 指定デバイスが対象かどうかを確認する
     *
     * @param mixed $device
     * @return boolean
     */
    public function isTargetDevice($device)
    {
        $preregistration = new Preregistration();
        return $preregistration->isTargetDevice($device);
    }

    /**
     * Olympus用の種別とデバイスを取得する
     *
     * @param  mixed $device
     * @return void
     */
    private function getKindAndDevice($device)
    {
        $result = null;
        switch ($device) {
            case 'pc':
                $result = ['application','pc'];
                break;
            
            case 'sp':
                $result = ['application','sp'];
                break;
            
            case 'android_app':
                $result = ['application','android_app'];
                break;
        
            case 'pc_channeling':
                $result = ['ch_application','pc'];
                break;
    
            case 'sp_channeling':
                $result = ['ch_application','sp'];
                break;
                
            case 'client':
                $result = ['cl_application','client'];
                break;
            default:
                throw new Exception($device.' is not target device.');
                break;
        }
        return $result;
    }

    /**
     * 問い合わせ先メールアドレスが設定されているか確認する
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isRegisteredContactMailAddress($appId)
    {
        $result = $this->applicationDetail->getApplicationDetail($appId);
        if (empty($result)) {
            return false;
        }

        return !empty($result->email);
    }

    /**
     * デバイス別ゲーム情報が設定されているか確認する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @param  mixed $target
     * @return bool
     */
    public function isRegisteredGameInfo($appId, $device, $target)
    {
        if (strpos($device, 'channeling') === false) {
            $result = $this->applicationDevice->getOne($appId, $device);
        } else {
            $result = $this->chApplication->getOne($appId);
        }
        if (empty($result)) {
            return false;
        }

        switch ($target) {
            case 'game_introduction':
                return !empty($result->description_middle);
            case 'channeling_game_introduction':
                return !empty($result->description_middle);
            case 'game_introduction_detail':
                return !empty($result->description);
            case 'supported_device':
                return !empty($result->restrictions);
            default:
                return false;
        }
    }

    /**
     * 事前登録申請のサムネイル画像が登録されているかを確認する
     *
     * @param mixed $appId
     * @param mixed $imageSize
     * @param mixed $device
     * @return bool
     */
    public function isThumbnailImageRegistered($appId, $imageSize, $device)
    {
        $gamesApplyDeviceTypeList = config('forms.Games.applyDeviceType');
        $chGamesApplyDeviceTypeList = config('forms.ChGames.applyDeviceType');
        $clGamesApplyDeviceTypeList = config('forms.ClGames.applyDeviceType');
        if (in_array($device, $gamesApplyDeviceTypeList)) {
            // ソーシャルゲームの場合
            $thumbnailList = $this->applicationImage->getPostThumbnailList($appId)->toArray();
        } else if (in_array($device, $chGamesApplyDeviceTypeList)) {
            // チャネリングゲームの場合
            $thumbnailList = $this->chApplicationImage->getPostThumbnailList($appId)->toArray();
        } else if (in_array($device, $clGamesApplyDeviceTypeList)) {
            // クライアントゲームの場合
            $thumbnailList = $this->clApplicationImage->getPostThumbnailList($appId)->toArray();
        } else {
            throw new Exception($device.' is not target device.');
        }

        $thumbnailSizes = array_column($thumbnailList, 'image_size');

        return in_array($imageSize, $thumbnailSizes);
    }

    /**
     * 事前登録申請のゲーム情報入力を審査に提出する
     *
     * @return void
     */
    public function applyGameInformation(
        $appId, $device, 
        $preReleaseGameAnnouncement, $recommendationAgeDivision, $taxIncludedPrice, $taxExcludedPrice, $clientGameIntroduction
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'gameInformation' => [
                'clientGameIntroduction' => ['clientGameIntroductionText' => $clientGameIntroduction],
                'contactMailAddress' => (object)[],
                'gameIntroduction' => (object)[],
                'channelingGameIntroduction' => (object)[],
                'gameIntroductionDetail' => (object)[],
                'supportedDevice' => (object)[],
                'preReleaseGameAnnouncement' => [
                    'isPublishing' => filter_var($preReleaseGameAnnouncement, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                ],
                'recommendationAgeDivision' => [
                    'divisionValue' => $recommendationAgeDivision
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'gameInformation', $content);
    }

    /**
     * 事前登録申請のWin対応環境を審査に提出する
     *
     * @return void
     */
    public function applyWindowsSupportedEnvironment(
        $appId, $device, 
        $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'windowsSupportedEnvironment' => [
                'osVersion' => ['osVersionText' => $osVersionText],
                'processor' => ['processorText' => $processorText],
                'memory' => ['memorySize' => (int)$memorySize, 'memorySizeUnit' => $memorySizeUnit],
                'graphics' => ['graphicsText' => $graphicsText],
                'diskFreeSpace' => ['capacitySize' => (int)$capacitySize, 'capacitySizeUnit' => $capacitySizeUnit],
                'note' => ['noteText' => $noteText],
            ],
        ];
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'windowsSupportedEnvironment', $content);
    }

    /**
     * 事前登録申請のMac対応環境を審査に提出する
     *
     * @return void
     */
    public function applyMacSupportedEnvironment(
        $appId, $device, 
        $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'macSupportedEnvironment' => [
                'osVersion' => ['osVersionText' => $osVersionText],
                'processor' => ['processorText' => $processorText],
                'memory' => ['memorySize' => (int)$memorySize, 'memorySizeUnit' => $memorySizeUnit],
                'graphics' => ['graphicsText' => $graphicsText],
                'diskFreeSpace' => ['capacitySize' => (int)$capacitySize, 'capacitySizeUnit' => $capacitySizeUnit],
                'note' => ['noteText' => $noteText],
            ],
        ];
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'macSupportedEnvironment', $content);
    }

    /**
     * サイズユニットが定義内のものか確認
     *
     * @param  string
     * @return bool
     */
    public function ofSizeUnit($value)
    {
        $sizeUnit = config('forms.Games.sizeUnit');
        return in_array($value, $sizeUnit);
    }

    /**
     * 審査項目から指定デバイスで対象のものを返す
     *
     * @param  mixed $device
     * @param  mixed $content
     * @return array
     */
    private function getTargetContent($device, $content){
        $preregistration = new Preregistration();
        return $preregistration->getTargetContent($device, $content);
    }

    /**
     * 審査項目が空かを返す。
     *
     * @param  mixed $content
     * @return boolean
     */
    private function isEmptyContent($content){
        foreach ($content as $items) {
            if (!empty($items)) {
                return false;
            }
        }
    }

    /**
     * 事前登録申請のCEROを審査に提出する
     *
     * @return void
     */
    public function applyCero(
        $appId, $device, 
        $classificationText, $contentIcons
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'cero' => [
                'classification' => ['classificationText' => $classificationText],
                'contentIcons' => ['icons' => $contentIcons],
            ]
        ];
        
        $this->applyPreregistrationAPIService
            ->patchPreregistration(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'cero', $content);
    }

    /**
     * CERO区分が定義内のものか確認
     *
     * @param  string
     * @return bool
     */
    public function ofClassification($value)
    {
        $classification = config('forms.Games.CEROClassification');
        return in_array($value, $classification);
    }

    /**
     * CEROコンテンツアイコンが定義内のものか確認
     *
     * @param  array
     * @return bool
     */
    public function ofContentIcon($value)
    {
        $contentIcons = config('forms.Games.CEROContentIcons');
        foreach ($value as $icon) {
            if (!array_key_exists($icon, $contentIcons)) {
                return false;
            }
        }

        return true;
    }
}