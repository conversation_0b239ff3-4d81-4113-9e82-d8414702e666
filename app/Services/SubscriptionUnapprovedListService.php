<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\Freegame\User;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Services\AccountApiService;
use App\Services\SubscriptionApiService;
use Exception;

class SubscriptionUnapprovedListService extends CustomService
{
    protected $accountApiService;
    protected $application;
    protected $developerApplication;
    protected $subscriptionApiService;
    protected $user;

    public function __construct(
        AccountApiService $accountApiService,
        Application $application,
        DeveloperApplication $developerApplication,
        SubscriptionApiService $subscriptionApiService,
        User $user
    ) {
        $this->accountApiService                  = $accountApiService;
        $this->application                        = $application;
        $this->developerApplication               = $developerApplication;
        $this->subscriptionApiService             = $subscriptionApiService;
        $this->user                               = $user;
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        // 定期購入用デバイスリスト取得
        $deviceList = config('subscription.device');

        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap($deviceList);
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice($deviceList);
        }

        $opts = ['' => ['' => 'タイトルを選択してください']];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $this->convertDisplayTitle($data->title, $data->device);
        }
        return $opts;
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return [];
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }

    /**
     * 画面描画用データの取得
     * 
     * @return array
     */
    public function getFormData()
    {
        return array_merge(config('forms.SubscriptionUnapprovedList'), ['appTitleType' => $this->getApplicationTitleList()]);
    }

    /**
     * 画面表示用にアプリタイトルをコンバートする
     * 
     * @param string $title
     * @param string $device
     * @return string
     */
    private function convertDisplayTitle($title, $device)
    {
        $displayString = config('forms.Subscription.device');
        return $title . $displayString[$device];
    }

    /**
     * GamesIDからopenIDを取得する
     * 
     * @param int $gamesId
     * @return string
     */
    public function getOpenId($gamesId)
    {
        $accountApiResponse = $this->accountApiService->getAccountFromGamesId($gamesId);
        if (!$accountApiResponse['resultStatus']) {
            // APIエラーであった場合
            throw new Exception($accountApiResponse['resultMessage']);
        }
        return $accountApiResponse['response']['open_id'];	
    }

    /**
     * OpenIDからGamesIDを取得する
     * 
     * @param int $openId
     * @return string
     */
    public function getGamesId($openId)
    {
        // OpenIDからユーザーアカウント情報を取得を試みる
        $accountApiResponse = $this->accountApiService->getAccountFromOpenId($openId);
        if (!$accountApiResponse['resultStatus']) {
            // その他エラー
            throw new Exception($accountApiResponse['resultMessage']);
        }

        // GAMES IDが取得できる場合はそのまま値を返す
        // (登録状態によって取れない事もあるらしい https://confl.arms.dmm.com/pages/viewpage.action?pageId=**********)
        if (!empty($accountApiResponse['response']['games_id'])) {
            return $accountApiResponse['response']['games_id'];
        }
        throw new Exception(config('forms.SubscriptionUnapprovedList.messages.games_id_not_found'));
    }

    /**
     * 検索条件をセッションで保持
     *
     * @param  array $search
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('SubscriptionUnapproved.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'app_id',
            'device',
            'games_id',
            'order_id',
        ]);
        request()->session()->set('SubscriptionUnapproved.search', $search);
        return $search;
    }

    /**
     * 未承認のレシート検索APIを実行してレシート情報を取得する
     * 
     * @param array $condition
     * @return array
     */
    public function getSearchList($condition)
    {
        // パスパラメーターの作成
        $pathParameters = [
            'appId' => $condition['app_id'],
            'device' => $condition['device'],
            'kind' => config('subscription-api.api_kind'),
        ];
        // クエリパラメーターの作成
        $queryParameters = [];
        if (!empty($condition['games_id'])) {
            $openId = $this->getOpenId($condition['games_id']);
            $queryParameters['openId'] = $openId;
        }
        if (!empty($condition['order_id'])) {
            $queryParameters['orderId'] = $condition['order_id'];
        }
        // 未承認アイテム一覧取得
        $unapprovedList = $this->subscriptionApiService->getUnapprovedList($pathParameters, $queryParameters);
        if ($unapprovedList['resultStatus']) {
            return $unapprovedList['response']['body']['receipts'];
        }
        // 存在しない場合は404エラーになるため、その場合は空は配列を返す
        if ($unapprovedList['resultCode'] === 404) {
            return [];
        }
        throw new Exception($unapprovedList['resultMessage']);
    }

    /**
     * 定期購入のレシートに関する情報取得APIを実行して詳細情報を取得する
     * 
     * @param array $condition
     * @return array
     */
    public function getItem($condition)
    {
        // パスパラメーターの作成
        $pathParameters = [
            'appId' => $condition['app_id'],
            'device' => $condition['device'],
            'kind' => config('subscription-api.api_kind'),
        ];
        // クエリパラメーターの作成
        $queryParameters = [
            'orderId' => $condition['order_id'],
        ];
        // 定期購入のレシートに関する情報を取得
        $unapprovedItem = $this->subscriptionApiService->getUnapprovedItem($pathParameters, $queryParameters);
        if (!$unapprovedItem['resultStatus']) {
            throw new Exception($unapprovedItem['resultMessage']);
        }
        // GamesIDの設定
        $unapprovedItem['response']['body']['gamesId'] = $this->getGamesId($unapprovedItem['response']['body']['openId']);
        return $unapprovedItem['response']['body'];
    }

    /**
     * 未承認のレシートを手動承認する
     * 
     * @param array $condition
     * @return array
     */
    public function setUnapproveItemApprove($condition)
    {
        // パスパラメーターの作成
        $pathParameters = [
            'appId' => $condition['app_id'],
            'device' => $condition['device'],
            'kind' => config('subscription-api.api_kind'),
            'orderId' => $condition['order_id'],
        ];
        $queryParameters = [
            'openId' => $condition['open_id'],
            'adminAcknowledgedAccountName' => sprintf('DEVELOPER-GAMES@%s', auth()->user()->login_id),
        ];
        // 手動承認
        $approveItem = $this->subscriptionApiService->setUnapprovedItemApprove($pathParameters, $queryParameters);
        if (!$approveItem['resultStatus']) {
            throw new Exception($approveItem['resultMessage']);
        }

        return $approveItem['response']['body'];
    }

    /**
     * 承認済判定を行う
     *
     * @param array $condition
     * @return bool true:承認済/false:未承認
     */
    public function isApproved($condition)
    {
        // 定期購入のレシートに関する未承認アイテムの情報を取得、取得できない場合は承認できる状態(未承認ではない)
        $unapprovedItem = $this->getSearchList($condition);
        return $unapprovedItem ? false : true;
    }
}