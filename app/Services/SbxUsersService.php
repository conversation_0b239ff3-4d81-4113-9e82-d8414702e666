<?php

namespace App\Services;

use App\Http\Requests\SbxUsers\SbxUsersCreateRequest;
use App\Http\Requests\SbxUsers\SbxUsersEditRequest;
use Carbon\Carbon;
use \Exception;
use Cache;

use App\Models\FreegameSandbox\FreegameSandbox;
use App\Models\FreegameSandbox\SandboxMember;
use App\Models\FreegameSandbox\SandboxPoint;
use App\Models\FreegameSandbox\ClUser;
use App\Models\FreegameSandbox\User;
use App\Models\FreegameSandbox\UserPrivacy;
use App\Models\FreegameSandbox\UserNotification;
use App\Models\FreegameSandbox\ApplicationInstall;
use App\Models\FreegameSandbox\LifecycleEvent;
use App\Models\FreegameSandbox\DmmPremiumUser;

use App\Models\FreegameDeveloper\Developer;
use App\Models\NetgameCstool\TestAccount;

class SbxUsersService extends CustomService
{
    protected $sandboxMember;
    protected $sandboxPoint;
    protected $clUser;
    protected $user;
    protected $userPlivacy;
    protected $userNotification;
    protected $applicationInstall;
    protected $lifecycleEvent;
    protected $dmmPremiumUser;

    protected $developer;
    protected $testAccount;

    // member_id生成の為の変数
    protected $words = '2345679abcdefghijkmnopqrstuvwxyz';
    protected $memberIdPrefix = 'D100';

    public function __construct(
        SandboxMember $sandboxMember,
        SandboxPoint $sandboxPoint,
        ClUser $clUser,
        User $user,
        UserPrivacy $userPrivacy,
        UserNotification $userNotification,
        ApplicationInstall $applicationInstall,
        LifecycleEvent $lifecycleEvent,
        DmmPremiumUser $dmmPremiumUser,
        Developer $developer,
        TestAccount $testAccount
    ) {
        $this->sandboxMember = $sandboxMember;
        $this->sandboxPoint = $sandboxPoint;
        $this->clUser = $clUser;
        $this->user = $user;
        $this->userPrivacy = $userPrivacy;
        $this->userNotification = $userNotification;
        $this->applicationInstall = $applicationInstall;
        $this->LifecycleEvent = $lifecycleEvent;
        $this->dmmPremiumUser = $dmmPremiumUser;
        $this->developer = $developer;
        $this->testAccount = $testAccount;
    }

    /**
     * member_idを生成
     *
     * @return string member_id
     */
    public function memberIdGenerator()
    {
        $wordsList = str_split($this->words);
        $hash = '';
        for ($i = 0; $i < 8; $i++) {
            $hash .= $wordsList[array_rand($wordsList)];
        }
        return $this->memberIdPrefix . $hash;
    }

    /**
     * member_idの取得(同じmember_idが存在しないことを確認)
     *
     * @param array $exclusionMeberIds 除外IDリスト。
     * @return string
     */
    public function getMemberId($exclusionMeberIds=[])
    {
        do {
            $member_id = $this->memberIdGenerator();
        } while (in_array($member_id, $exclusionMeberIds) || !empty($this->sandboxMember->getOne($member_id)));

        return $member_id;
    }

    /**
     * user_idを取得(同じmember_idが存在しないことを確認)<br>
     * 
     * @param array $exclusionUds 除外IDリスト。
     * @return int uid
     */
    public function getUid($exclusionUds=[])
    {
        do {
            $uid = mt_rand(1, 9999999);
        } while (in_array($uid, $exclusionUds) || !empty($this->user->getOne($uid)));

        return $uid;
    }

    /**
     * Get values for form select box,radio box
     *
     * @return array
     */
    public function getFormData()
    {
        return config('forms.SbxUsers');
    }

    /**
     * Get sbx user list
     *
     * @return array
     */
    public function getSbxUserList($form)
    {
        // 表示件数の取得
        if (empty($form['perPage'])) {
            $form['perPage'] = null;
        }
        if (! isset($form['userSort']) || empty(config("forms.SbxUsers.namesUserSortType.{$form['userSort']}"))) {
            $userSort   = "date_desc";
        } else {
            $userSort   = $form['userSort'];
        }
        if (empty($form['id'])) {
            $form['id'] = null;
        }
        if (empty($form['nickname'])) {
            $form['nickname'] = null;
        }

        $offset = config('forms.SbxUsers.offset');
        if (! empty($form['perPage'])) {
            $offset = $form['perPage'];
        }
        // sandbox_memberから対象レコード取得
        $param = array(
            'developer_id' => auth_user_id(),
            'offset' => $offset,
            'perPage' => $offset,
            'status' => 'active',
            'id' => $form['id'],
            'nickname' => $form['nickname'],
            'userSort' => $userSort,
        );
        $order = config("forms.SbxUsers.namesUserSortType.{$userSort}.order");
        $sbxUserList = $this->sandboxMember->getListSort($param, $order);

        // 表示件数の設定
        $appends      = array_only($param, [
            'perPage',
            'id',
            'nickname',
            'userSort',
        ]);
        $sbxUserList->appends($appends);
        $sbxUserList->userSort = $userSort;

        return $sbxUserList;
    }

    /**
     * cl_userに指定のidが存在「しない」か確認
     * @param int $cl_uid
     * @return boolean
     */
    public function isNotExists($cl_uid)
    {
        $record = $this->clUser->getOne($cl_uid);
        if (empty($record)) {
            return true;
        }
        return false;
    }

    /**
     * サンドボックステストアカウント登録
     *
     * @param SbxUsersCreateRequest $request
     * @return array idをキーに持つニックネームの配列を返す。
     * @throws Exception
     */
    public function storeSbxUser(SbxUsersCreateRequest $request)
    {
        $password = $request['password'];
        $grade = $request['grade'];
        $nickname = $request['nickname'];
        $grade = $request['grade'];
        $gender = $request['gender'];
        $birth = $request['birth'];
        $blood = $request['blood'];
        $point = $request['point'];
        $cl_uid = $request['cl_uid'];
        $create_count = (intval($request['create_count']) <= 1) ? 1 : intval($request['create_count']);

        $developer_id = auth_user_id();

        // 現在利用のアカウントからtypeを設定
        $type = '';
        if (auth_is_pf()) {
            // ログインユーザー区分が「PF」または「admin」
            $type = 'staff';
        } elseif (auth_is_user_sap()) {
            // ログインユーザー区分が「SAP」
            $type = 'developer';
        }

        // 生成済みIDリスト
        $exclusionMeberIds = [];
        $exclusionUids = [];

        // 生成したユーザーリスト
        $users = [];

        // 登録(トランザクション)開始
        FreegameSandbox::beginTransaction();
        try {
            for ($userNumber=1; $userNumber<=$create_count; $userNumber++) {
                // member_idの生成
                $member_id = $this->getMemberId($exclusionMeberIds);
                $exclusionMeberIds[] = $member_id;

                // userのidの生成
                $uid = $this->getUid($exclusionUids);
                $exclusionUids[] = $uid;

                // sandbox_member
                $this->sandboxMember->add(
                    [
                        'member_id' => $member_id,
                        'developer_id' => $developer_id,
                        'password' => $password,
                    ]
                );

                // user
                $saveNickname = ($create_count <= 1) ? $nickname : $nickname.'_'.str_pad($userNumber, 2, '0', STR_PAD_LEFT);

                $this->user->add(
                    [
                        'id' => $uid,
                        'member_id' => $member_id,
                        'nickname' => $saveNickname,
                        'type' => $type,
                        'grade' => $grade,
                        'user_hash' => md5(uniqid(rand(), true)),
                        'status' => 'active',
                        'gender' => $gender,
                        'birth_date' => date('Y-m-d', strtotime($birth)),
                        'blood_type' => $blood,
                        'regist_date' => date('Y-m-d H:i:s'),
                    ]
                );

                // user_privacy
                $this->userPrivacy->add(
                    [
                        'user_id' => $uid,
                        'age' => 1,
                        'birthday' => 1,
                    ]
                );

                // user_notification
                $this->userNotification->add(
                    [
                        'user_id' => $uid,
                    ]
                );

                //sandbox_point
                $this->sandboxPoint->add(
                    [
                        'member_id' => $member_id,
                        'point' => $point,
                    ]
                );

                // cl_user
                if ($cl_uid) {
                    $clUser = $this->clUser->getOne($cl_uid);
                    if (empty($clUser)) {
                        $this->clUser->add(
                            [
                                'id' => $cl_uid,
                                'member_id' => $member_id,
                                'status' => 'active',
                                'type' => '',
                            ]
                        );
                    }
                }

                $users[$uid] = $saveNickname;

                $this->dmmPremiumUser->add($member_id, 0);
            }

            // 登録(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }
        return $users;
    }

    /**
     * netgame_cstool.test_accountに<br>
     * cl_id=test_account_idのレコードが存在するか確認
     * @param int $cl_id
     * @return boolean
     */
    public function isTestAccountExists($cl_uid)
    {
        $record = $this->testAccount->getOneByIdAndDeleteFlag($cl_uid);
        if (! empty($record)) {
            return true;
        }
        return false;
    }

    /**
     * uidからuser、sandbox_member、cl_user、sandbox_pointを取得
     * @param int $uid
     * @return array
     */
    public function getUserByIdWithMember($uid)
    {
        // user、sandbox_member
        $user = $this->user->getOneByUidWithMember($uid);

        if ($user['developer_id'] != auth_user_id()) {
            abort(400);
        }

        $user['id'] = $uid;

        // cl_user
        $cl_user = $this->clUser->getOneByMemberId($user['member_id']);
        if (! empty($cl_user)) {
            $user['cl_uid'] = $cl_user['id'];
            // 現状DB登録中か否かの情報が必要
            $user['isEntryClUid'] = true;
        }

        // sandbox_point
        $point = $this->sandboxPoint->getOneByMemberId($user['member_id']);
        if (! empty($point)) {
            $user['point'] = $point['point'];
        }

        return $user;
    }

    /**
     * サンドボックステストアカウント更新
     *
     * @param SbxUsersEditRequest $request
     * @return bool
     * @throws Exception
     */
    public function updateSbxUser(SbxUsersEditRequest $request)
    {
        // member_id取得
        $user = $this->user->getOne($request['id']);
        $member_id = $user['member_id'];

        // 権限確認
        $isTrueUser = $this->sandboxMember->getOneByIdAndDeveloperId($member_id, auth_user_id());
        if (! $isTrueUser) {
            abort(400);
        }

        // typeの'null'文字列をnullに変換
        if ($request['type'] == 'null') {
            $request['type'] = null;
        }

        // 更新(トランザクション)開始
        FreegameSandbox::beginTransaction();
        try {
            // user
            $this->user->edit(
                [
                    'nickname' => $request['nickname'],
                    'type' => $request['type'],
                    'grade' => $request['grade'],
                ],
                $request['id']
            );

            // sandbox_point
            if (! empty($request['point'])) {
                // 現在のポイントと加算
                $point = $this->sandboxPoint->getOneByMemberId($member_id);
                $updatePoint = $point['point'] + $request['point'];
                $this->sandboxPoint->edit(
                    [
                        'point' => $updatePoint,
                    ],
                    $member_id
                );
            }

            // cl_user
            if (! $request['isEntryClUid'] && ! empty($request['cl_uid'])) {
                $clUser = $this->clUser->getOne($request['cl_uid']);
                if (empty($clUser)) {
                    $this->clUser->add(
                        [
                            'id' => $request['cl_uid'],
                            'member_id' => $member_id,
                            'status' => 'active',
                            'type' => '',
                        ]
                    );
                }
            }

            // 更新(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }

        // キャッシュ削除
        try {
            $this->sendXmlRpcData([
                'message' => 'Developer_User.CacheClear',
                'params' => [
                    'user_id' => $request['id'],
                ]
            ]);
        } catch (Exception $e) {
            return [
                'message' => $e->getMessage()
            ];
        }
        return true;
    }

    /**
     * サンドボックステストアカウント削除
     *
     * @return boolean
     */
    public function deleteSbxUser($id)
    {
        // member_id取得
        $user = $this->user->getOne($id);
        $member_id = $user['member_id'];

        // 権限確認
        $isTrueUser = $this->sandboxMember->getOneByIdAndDeveloperId($member_id, auth_user_id());
        if (! $isTrueUser) {
            abort(400);
        }

        // cl_userの登録状況確認
        $clUser = $this->clUser->getOneByMemberId($member_id, 'active');

        // application_install取得
        $appList = $this->applicationInstall->getListByUserId($id, 'active');

        // 更新(トランザクション)開始
        FreegameSandbox::beginTransaction();
        try {
            // user
            $this->user->edit(
                [
                    'withdrawal_date' => date('Y-m-d H:i:s'),
                ],
                $id
            );

            // sandbox_member
            $this->sandboxMember->edit(
                [
                    'status' => 'withdrawal',
                ],
                $member_id
            );

            // 削除対象のアプリが存在する場合
            if (count($appList) > 0) {
                foreach ($appList as $app) {
                    // application_install
                    $this->applicationInstall->edit(
                        [
                            'status' => 'suspend',
                            'end_date' => date('Y-m-d H:i:s'),
                        ],
                        $id,
                        $app['app_id'],
                        'active'
                    );

                    // lifecycle_event(削除の旨を新規登録)
                    $this->LifecycleEvent->add(
                        [
                            'type' => 'event.removeapp',
                            'app_id' => $app['app_id'],
                            'user_id' => $id,
                        ],
                        $member_id
                    );
                }
            }

            // cl_user
            if (! empty($clUser)) {
                $this->clUser->edit(
                    [
                        'withdrawal_date' => date('Y-m-d H:i:s'),
                        'status' => 'withdrawal',
                    ],
                    $clUser['id'],
                    'active'
                );
            }

            // 更新(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 検索条件セッション保持
     * @param array $search
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('SbxUsers.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'id',
            'nickname',
            'userSort',
            'perPage',
            'page'
        ]);
        request()->session()->set('SbxUsers.search', $search);
        return $search;
    }
}
