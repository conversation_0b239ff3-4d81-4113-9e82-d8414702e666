<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Services\AccountApiService;
use App\Services\SubscriptionApiService;
use Exception;

class SubscriptionPaymentLogService extends CustomService
{
    protected $accountApiService;
    protected $developerApplication;
    protected $application;
    protected $subscriptionApiService;

    public function __construct(
        Application $application,
        AccountApiService $accountApiService,
        DeveloperApplication $developerApplication,
        SubscriptionApiService $subscriptionApiService
    ) {
        $this->application                        = $application;
        $this->accountApiService                  = $accountApiService;
        $this->developerApplication               = $developerApplication;
        $this->subscriptionApiService             = $subscriptionApiService;
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        // 定期購入用デバイスリスト取得
        $deviceList = config('subscription.device');

        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap($deviceList);
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice($deviceList);
        }

        $opts = ['' => ['' => 'タイトルを選択してください']];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $this->convertDisplayTitle($data->title, $data->device);
        }
        return $opts;
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return [];
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }

    /**
     * 画面描画用データの取得
     * @return array
     */
    public function getFormData()
    {
        return array_merge(config('forms.SubscriptionPaymentLog'), ['appTitleType' => $this->getApplicationTitleList()]);
    }

    /**
     * 画面表示用にアプリタイトルをコンバートする
     * 
     * @param string $title
     * @param string $device
     * @return string
     */
    private function convertDisplayTitle($title, $device)
    {
        $displayString = config('forms.Subscription.device');
        return $title . $displayString[$device];
    }

    /**
     * GamesIdからopenIdを取得する
     * 
     * @param int $gamesId
     * @return string
     */
    public function getOpenId($gamesId)
    {
        $accountApiResponse = $this->accountApiService->getAccountFromGamesId($gamesId);
        if (!$accountApiResponse['resultStatus']) {
            throw new Exception($accountApiResponse['resultMessage']);
        }
        return $accountApiResponse['response']['open_id'];	
    }

    /**
     * 課金ログAPIを実行し、正常終了していればCSV出力データを返す
     * 
     * @param array $pathParameters
     * @param array $queryParameters
     * @param int   $userId
     * @return array
     */
    private function getCsvPaymentLog($pathParameters, $queryParameters, $userId)
    {
        $paymentLog = $this->subscriptionApiService->getPaymentLog($pathParameters, $queryParameters);
        // 指定の条件で課金ログが(0件)取得できない場合
        if ($paymentLog['resultCode'] === 404) {
            return [];
        }
        // エラーの場合
        if (!$paymentLog['resultStatus']) {
            throw new Exception($paymentLog['resultMessage']);
        }

        $list = [];
        foreach ($paymentLog['response']['body']['logs'] as $paymentLog) {
            $list[] = $this->makeCsvRow($paymentLog, $userId);
        }
        return $list;
    }

    /**
     * CSVに出力する行データを取得する
     * 
     * @param array  $paymentLog
     * @param int    $gamesId
     * @return array
     */
    private function makeCsvRow($paymentLog, $gamesId)
    {
        return [
            'app_id' => $paymentLog['appId'],
            'games_id' => $gamesId,
            'order_id' => $paymentLog['orderId'],
            'inquery_id' => $paymentLog['inquiryId'],
            'subscripsion_id' => $paymentLog['subscriptionSku'],
            'plan_id' => $paymentLog['planSku'],
            'benefit_id' => $paymentLog['offerSku'],
            'paid_method' => $paymentLog['paidMethod'],
            'paid_amount' => $paymentLog['fullPaidPrice'],
            'date' => date('Y-m-d H:i:s', $paymentLog['date']),
            'is_staff' => empty($paymentLog['isStaff']) ? 0 : 1,
            'status' => config('forms.SubscriptionPaymentLog.status.' . $paymentLog['status']),
        ];
    }

    /**
     * 出力する利用状況レポートCSVのヘッダーを取得する
     * 
     * @return array
     */
    private function getCsvPaymentLogHeader()
    {
        return config('forms.SubscriptionPaymentLog.exportCsv.header');
    }

    /**
     * 出漁する利用状況レポートCSVのファイル名を取得する
     * 
     * @return string
     */
    private function getCsvPaymentLogFileName()
    {
        return sprintf(config('forms.SubscriptionPaymentLog.exportCsv.filename'), date('Y-m-d_His'));
    }

    /**
     * CSVダウンロード
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return
     */
    public function csvDownload($request)
    {
        $pathParameters = [
            'appId' => $request->get('app_id', ''),
            'device' => $request->get('device', ''),
            'kind' => config('subscription-api.api_kind'),
        ];
        $queryParameters = [
            'openId' => $this->getOpenId($request->get('games_id')),
            'fromDate' => str_replace('/', '-', $request->get('request_date_from')),
            'tillDate' => str_replace('/', '-', $request->get('request_date_to')),
        ];
        $gamesId = $request->get('games_id');

        return $this->downloadCsv(
            $this->getCsvPaymentLogFileName(),
            $this->getCsvPaymentLog($pathParameters, $queryParameters, $gamesId),
            $this->getCsvPaymentLogHeader(),
            true
        );
    }
}