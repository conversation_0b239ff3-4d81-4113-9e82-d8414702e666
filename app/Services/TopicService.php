<?php
namespace App\Services;

use App\Models\Freegame\User;
use App\Models\FreegameCommunity\Community;
use App\Models\FreegameCommunity\Topic;
use App\Models\FreegameDeveloper\DeveloperApplication;

/**
 * トピック管理
 */
class TopicService extends CustomService
{
    protected $User;
    protected $Community;
    protected $Topic;
    protected $DeveloperApplication;

    public function __construct(
        User                 $User,
        Community            $Community,
        Topic                $Topic,
        DeveloperApplication $DeveloperApplication
    ) {
        $this->User                  = $User;
        $this->Community             = $Community;
        $this->Topic                 = $Topic;
        $this->DeveloperApplication  = $DeveloperApplication;
    }

    /**
     * トピック詳細取得
     *
     * @param  integer $topicId
     *
     * @return array   $dataTopic
     *
     */
    public function getTopicOne($topicId)
    {
        $dataTopic = array();

        $object = $this->Topic->getOne($topicId);
        if (is_object($object)) {
            $decodeObject = json_decode($object);
            foreach ($decodeObject as $key => $val) {
                $dataTopic[$key] = $val;
            }
        }

        return $dataTopic;
    }

    /**
     * トピック一覧取得
     *
     * @param  array $params
     *
     * @return array $list
     *
     */
    public function getList($params)
    {
        $search = $params;

        $search['status'] = config('forms.Topic.status'); // activeのトピックのみ

        $listTopic = $this->Topic->getList($search);

        return $listTopic;
    }

    /**
     * トピック一覧取得
     *
     * @param  array $params
     *
     * @return array $list
     *
     */
    public function getTopicList($params)
    {
        $search = $params;

        if (!empty($params['perPage'])) {
            $search['offset'] = $params['perPage'];
        } else {
            $search['offset'] = config('forms.Topic.offset');
        }

        $search['status'] = config('forms.Topic.status');

        $listTopic = $this->Topic->getList($search);

        $appends = array();
        if (!empty($params['community_id'])) {
            $appends['community_id'] = $params['community_id'];
        }
        if (!empty($params['id'])) {
            $appends['id'] = $params['id'];
        }
        if (!empty($params['title'])) {
            $appends['title'] = $params['title'];
        }
        if (!empty($params['user_id'])) {
            $appends['user_id'] = $params['user_id'];
        }
        if (!empty($params['perPage'])) {
            $appends['perPage'] = $params['perPage'];
        }

        $appends['page'] = 1;
        if (!empty($params['page'])) {
            $appends['page'] = $params['page'];
        }

        $listTopic->appends($appends);

        if ($listTopic->count() > 0) {
            $pagerViewParam = $this->getPagerView($listTopic, 5);
        } else {
            $pagerViewParam['from'] = 0;
            $pagerViewParam['to']   = 0;
        }

        $list = array(
            'listTopic'     => $listTopic,
            'pagerViewFrom' => $pagerViewParam['from'],
            'pagerViewTo'   => $pagerViewParam['to'],
        );

        return $list;
    }

    /**
     * トピック登録
     *
     * @param  array   $params
     *
     * @return boolean true
     *
     */
    public function addTopic($params)
    {
        $nowTime = date('Y-m-d H:i:s', now_stamp());

        $userId = '';
        $object = $this->Community->getOne($params['community_id']);
        if ($object) {
            $userId = $object->manager_user_id;
        }

        $data = array(
            'community_id'      => $params['community_id'],
            'title'             => $this->setConvertHankaku($params['title']),
            'description'       => $this->setConvertHankaku(except_html($params['description'])),
            'status'            => config('forms.Topic.status'),
            'create_user_id'    => $userId,
            'create_date'       => $nowTime,
            'is_only_developer' => $params['is_only_developer'],
        );

        $this->Topic->add($data);

        return true;
    }

    /**
     * トピック更新
     *
     * @param  array   $params
     *
     * @return boolean true
     *
     */
    public function editTopic($params)
    {
        $nowTime = date('Y-m-d H:i:s', now_stamp());

        $userId = '';
        $object = $this->Community->getOne($params['community_id']);
        if ($object) {
            $userId = $object->manager_user_id;
        }

        $data = array(
            'community_id'      => $params['community_id'],
            'title'             => $this->setConvertHankaku($params['title']),
            'description'       => $this->setConvertHankaku(except_html($params['description'])),
            'status'            => config('forms.Topic.status'),
            'update_user_id'    => $userId,
            'update_date'       => $nowTime,
            'is_only_developer' => $params['is_only_developer'],
        );

        $this->Topic->edit($params['id'], $data);

        return true;
    }

    /**
     * トピック削除
     *
     * @param  integer $id
     *
     * @return boolean true
     */
    public function delTopic($id)
    {
        $this->Topic->del($id);

        return true;
    }

    /**
     * コミュニティのデベロッパーかチェック
     *
     * @param  integer $communityId
     *
     * @return boolean
     *
     */
    public function checkStillDeveloper($communityId)
    {
        $devAppIds = $this->getDeveloperApplicationList(auth()->user()->id);

        if (count($devAppIds) > 0) {
            $params = ['id' => $communityId, 'type' => config('forms.Communities.type'), 'app_id' => $devAppIds];
            $object = $this->Community->getList($params);
            if ($object->count() > 0) {
                foreach ($object as $val) {
                    if (in_array($val->app_id, $devAppIds)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 全角英数字カナを半角英数字カナに変換
     *
     * @param  string $data
     *
     * @return string $dataConvert
     *
     */
    public function setConvertHankaku($data)
    {
        $dataConvert = $data;
        if ($dataConvert) {
               $dataConvert = mb_convert_kana($dataConvert, 'aK', 'UTF-8');
        }
        return $dataConvert;
    }

    /**
     * コミュニティ一覧取得
     *
     * @param  array $appIds
     *
     * @return array $listCommunities
     *
     */
    public function getCommunitiesList($appIds = '')
    {
        $listCommunities = array();

        $params = ['type' => config('forms.Communities.type'), 'app_id' => $appIds];
        $object = $this->Community->getList($params);
        if ($object->count() > 0) {
            foreach ($object as $val) {
                $listCommunities[$val->id] = $val->title;
            }
        }

        $listCommunities = array('' => '') + $listCommunities;

        return $listCommunities;
    }

    /**
     * 権限のあるアプリケーションID配列取得
     *
     * @param  integer $developerId
     *
     * @return array   $appIds
     *
     */
    public function getDeveloperApplicationList($developerId)
    {
        $appIds = array();

        $object = $this->DeveloperApplication->getListByDeveloperId($developerId);
        if ($object->count() > 0) {
            foreach ($object as $val) {
                $appIds[] = $val->app_id;
            }
        }

        return $appIds;
    }

    /**
     * 検索条件をセッションで保持
     *
     * @param  Request $search
     *
     * @return array   $search
     *
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('Topic.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'community_id',
            'id',
            'title',
            'user_id',
            'perPage',
            'page'
        ]);
        request()->session()->set('Topic.search', $search);
        return $search;
    }

    /**
     * ユーザー名称取得
     *
     * @param  integer $userId
     *
     * @return string  $userName
     *
     */
    public function getUserName($userId = '')
    {
        $userName = '';

        if ($userId) {
            $data = $this->User->getOne($userId);
            if (isset($data->nickname)) {
                $userName = $data->nickname;
            }
        }

        return $userName;
    }
}
