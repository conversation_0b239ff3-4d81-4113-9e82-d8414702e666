<?php

namespace App\Services;
use App\Models\FreegameDeveloper\ApplicationDivision;
use Exception;

/**
 * 定期購入：日別利用状況レポート
 */
class SubscriptionUtilizationReportDailyService extends CustomService
{
    /** @var ApplicationDivision */
    protected $applicationDivision;
    /** @var SubscriptionApiService */
    protected $subscriptionApiService;

    /**
     * コンストラクタ
     * 
     * @param SubscriptionApiService $subscriptionApiService
     */
    public function __construct(
        ApplicationDivision $applicationDivision,
        SubscriptionApiService $subscriptionApiService
    ) {
        $this->applicationDivision = $applicationDivision;
        $this->subscriptionApiService = $subscriptionApiService;
    }

    /**
     * フォームから送信されたreportでCSV用のリストを選定
     * 
     * @param array $condition リクエストパラメーターの配列
     * @param array 利用状況レポートの配列
     */
    public function getCsvList($condition = [])
    {
        // APIのクエリパラメーターに使用する値をまとめる
        $query = [
            'dateFormat' => 'day',
            'fromDate'   => date('Y-m-d', strtotime($condition['begin'])),
            'tillDate'   => date('Y-m-d', strtotime($condition['end']))
        ];
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-api.api_kind'),
        ];

        $devices = config('forms.ReportsDaily.subscriptionDevice');
        $list = [];
        foreach ($devices as $device) {
            // ループ中のデバイスが選択されていない場合は次のループへ
            if (!in_array($device, $condition['device'])) {
                continue;
            }
            // APIのパスパラメーターのデバイスを選択されたに使用する値をまとめる
            $path['device'] = $device;
            // 対象のタイトル、デバイス、期間でAPIの結果リストを取得する
            $response = $this->subscriptionApiService->getUtilizationReport($path, $query);
            if ($response['resultStatus']) {
               // appIdからゲームIDを取得して配列に設定
               $applicationDivision = $this->applicationDivision->getOneByAppId($response['response']['body']['appId']);                
               $response['response']['body']['gameId'] = $applicationDivision->game_id;
               // レポート作成
                $list = array_merge($list, $this->createReport($response['response']['body']));
                continue;
            }
            // 存在しない場合は、レスポンスの取得に失敗[404エラー]が返ってきてエラーになるため、404エラーの場合は次のループへ
            if ($response['resultCode'] === 404) {
                continue;
            }
            // エラーで404以外の場合は例外
            throw new Exception($response['resultMessage']);
        }
        return $list;
    }

    /**
     * フォームから送信されたreportでヘッダーを選定
     * 
     * @return array $header CSVファイルのヘッダー配列
     */
    public function getCsvHeader()
    {
        return [
            'date' => '年月日',
            'gameId' => 'ゲームID',
            'appId' => 'アプリID',
            'appTitle' => 'ゲーム名',
            'appDevice' => 'デバイス',
            'isAdult' => 'サイト情報',
            'title' => '商品名',
            'planSku' => '基本プランSKU',
            'paidPointsAmount' => '消費ポイント',
            'paidCreditAmount' => 'クレカ課金額',
            'paidPointsAndCreditAmount' => '定期購入課金額（ポイント+クレカ）',
            'paidStaffPointsAndCreditAmount' => '優待消費額（ポイント+クレカ）',
            'newSubscriptionsCount' => '定期購入新規加入数',
            'renewalSubscriptionsCount' => '定期購入継続数',
            'newAndRenewalSubscriptionsCount' => '定期購入利用数',
            'canceledSubscriptionsCount' => '定期購入解約数'
        ];
    }

    /**
     * レポートを生成する
     * 
     * @param  array $body   APIレスポンスのボディ
     * @return array $result 利用状況レポート
     */
    private function createReport($body)
    {
        $result = [];
        foreach ($body['reports'] as $report) {
            $result[] = [
                'date' => $report['date'],
                'gameId' => $body['gameId'],
                'appId' => $body['appId'],
                'appTitle' => $body['title'],
                'appDevice' => $body['device'],
                'isAdult' => $body['isAdult'] ? 'アダルト' : '一般',
                'title' => $report['title'],
                'planSku' => $report['planSku'],
                'paidPointsAmount' => $report['paidPointsAmount'],
                'paidCreditAmount' => $report['paidCreditAmount'],
                'paidPointsAndCreditAmount' => $report['paidPointsAndCreditAmount'],
                'paidStaffPointsAndCreditAmount' => $report['paidStaffPointsAndCreditAmount'],
                'newSubscriptionsCount' => $report['newSubscriptionsCount'],
                'renewalSubscriptionsCount' => $report['renewalSubscriptionsCount'],
                'newAndRenewalSubscriptionsCount' => $report['newAndRenewalSubscriptionsCount'],
                'canceledSubscriptionsCount' => $report['canceledSubscriptionsCount']
            ];
        }

        return $result;
    }
}
