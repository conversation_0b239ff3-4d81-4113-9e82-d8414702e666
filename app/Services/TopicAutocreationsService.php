<?php
namespace App\Services;

use App\Models\FreegameCommunity\Community;
use App\Models\FreegameCommunity\Topic;
use App\Models\FreegameCommunity\TopicAutocreations;
use App\Models\FreegameDeveloper\DeveloperApplication;

/**
 * 自動生成トピック管理
 */
class TopicAutocreationsService extends CustomService
{
    protected $Community;
    protected $Topic;
    protected $TopicAutocreations;
    protected $DeveloperApplication;

    public function __construct(
        Community            $Community,
        Topic                $Topic,
        TopicAutocreations   $TopicAutocreations,
        DeveloperApplication $DeveloperApplication
    ) {
        $this->Community            = $Community;
        $this->Topic                = $Topic;
        $this->TopicAutocreations   = $TopicAutocreations;
        $this->DeveloperApplication = $DeveloperApplication;
    }

    /**
     * 自動生成トピック詳細取得
     *
     * @param  array $params
     *
     * @return array $list
     *
     */
    public function getTopicAutocreationsOneByConditions($params)
    {
        $dataTopicAutocreations = array();

        $object = $this->TopicAutocreations->getOneByConditions($params);
        if (is_object($object)) {
            $decodeObject = json_decode($object);
            foreach ($decodeObject as $key => $val) {
                $dataTopicAutocreations[$key] = $val;
            }
        }

        return $dataTopicAutocreations;
    }

    /**
     * 自動生成トピック一覧取得
     *
     * @param  array $params
     *
     * @return array $list
     *
     */
    public function getTopicAutocreationsList($params)
    {
        $search = $params;

        if (!empty($params['perPage'])) {
            $search['offset'] = $params['perPage'];
        } else {
            $search['offset'] = config('forms.TopicAutocreations.offset');
        }

        $search['status'] = config('forms.TopicAutocreations.status');
        $search['type']   = config('forms.Communities.type');

        $listTopicAutocreations = $this->TopicAutocreations->getList($search);

        $appends = array();
        if (!empty($params['community_id'])) {
            $appends['community_id'] = $params['community_id'];
        }
        if (!empty($params['topic_title'])) {
            $appends['topic_title'] = $params['topic_title'];
        }
        if (!empty($params['perPage'])) {
            $appends['perPage'] = $params['perPage'];
        }

        $appends['page'] = 1;
        if (!empty($params['page'])) {
            $appends['page'] = $params['page'];
        }

        $listTopicAutocreations->appends($appends);

        if (count($listTopicAutocreations) > 0) {
            $pagerViewParam = $this->getPagerView($listTopicAutocreations, 5);

            foreach ($listTopicAutocreations as $val) {
                $val->next_title = preg_Replace('/{number}/', $val->next_number, $val->title_template);
            }
        } else {
            $pagerViewParam['from'] = 0;
            $pagerViewParam['to']   = 0;
        }

        $list = array(
            'listTopicAutocreations' => $listTopicAutocreations,
            'pagerViewFrom'          => $pagerViewParam['from'],
            'pagerViewTo'            => $pagerViewParam['to'],
        );

        return $list;
    }

    /**
     * 自動生成トピック登録
     *
     * @param  array   $params
     *
     * @return boolean true
     *
     */
    public function addTopicAutocreations($params)
    {
        $dataTopic = $this->getTopicOne($params['last_topic_id']);

        $data = array(
            'community_id'         => $dataTopic['community_id'],
            'last_topic_id'        => $params['last_topic_id'],
            'title_template'       => $params['title_template'],
            'next_number'          => $params['next_number'],
            'topic_create_comment' => $params['topic_create_comment'],
        );

        $this->TopicAutocreations->add($data);

        return true;
    }

    /**
     * 自動生成トピック削除
     *
     * @param  integer $id
     *
     * @return boolean true
     */
    public function delTopicAutocreations($id)
    {
        $this->TopicAutocreations->del($id);

        return true;
    }

    /**
     * コミュニティ一覧取得
     * $excludeStatusがtrueになると公開情報が公開/非公開区別なく全てのコミュニティ一覧が取得
     *
     * @param  array $appIds
     * @param  bool  $excludeStatus
     *
     * @return array $listCommunities
     *
     */
    public function getCommunitiesList($appIds = '', $excludeStatus = false)
    {
        $listCommunities = array();
        $params = [
            'type' => config('forms.Communities.type'),
            'app_id' => $appIds
        ];
        if (!$excludeStatus) {
            $params['status'] = 'active';
        }
        $object = $this->Community->getList($params);
        if (is_object($object)) {
            $decodeObject = json_decode($object);
            foreach ($decodeObject as $key => $val) {
                $val = (array)$val;
                $listCommunities[$val['id']] = $val['title'];
            }
        }

        return $listCommunities;
    }

    /**
     * トピック詳細取得
     *
     * @param  integer $topicId
     *
     * @return array   $dataTopic
     *
     */
    public function getTopicOne($topicId)
    {
        $dataTopic = array();

        $object = $this->Topic->getOne($topicId);
        if (is_object($object)) {
            $decodeObject = json_decode($object);
            foreach ($decodeObject as $key => $val) {
                $dataTopic[$key] = $val;
            }
        }

        return $dataTopic;
    }

    /**
     * トピック一覧取得
     * 自動生成トピック未作成のトピックのみ取得
     *
     * @param  array $params
     *
     * @return array $listTopic
     *
     */
    public function getTopicList($params)
    {
        $listTopic = array();

        $search = $params;

        $search['status']  = config('forms.Topic.status');
        $search['type']    = config('forms.Communities.type');
        $search['order'][] = ['community.title', 'asc'];
        $search['order'][] = ['topic.title', 'asc'];


        $object = $this->Topic->getListNoTopicAutocreation($search);
        if (is_object($object)) {
            $decodeObject = json_decode($object);
            foreach ($decodeObject as $key => $val) {
                $val = (array)$val;
                $listTopic[$val['id']] = $val['title'];
            }
        }

        return $listTopic;
    }

    /**
     * 権限のあるアプリケーションID配列取得
     *
     * @param  integer $developerId
     *
     * @return array   $appIds
     *
     */
    public function getDeveloperApplicationList($developerId)
    {
        $appIds = array();

        $object = $this->DeveloperApplication->getListByDeveloperId($developerId);
        if (is_object($object)) {
            $decodeObject = json_decode($object);
            foreach ($decodeObject as $key => $val) {
                $val = (array)$val;
                $appIds[] = $val['app_id'];
            }
        }

        return $appIds;
    }

    /**
     * 検索条件をセッションで保持
     *
     * @param  Request $search
     *
     * @return array   $search
     *
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('Topic.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'community_id',
            'topic_title',
            'perPage',
            'page'
        ]);
        request()->session()->set('Topic.search', $search);
        return $search;
    }

    /**
     * コミュニティのデベロッパーかチェック
     *
     * @param  integer $communityId
     *
     * @return boolean
     *
     */
    public function checkStillDeveloper($communityId)
    {
        $devAppIds = $this->getDeveloperApplicationList(auth()->user()->id);

        if (count($devAppIds) > 0) {
            $params = ['id' => $communityId, 'type' => config('forms.Communities.type'), 'app_id' => $devAppIds];
            $object = $this->Community->getList($params);
            if ($object->count() > 0) {
                foreach ($object as $val) {
                    if (in_array($val->app_id, $devAppIds)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
