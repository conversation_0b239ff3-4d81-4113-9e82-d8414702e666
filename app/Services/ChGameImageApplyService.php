<?php

namespace App\Services;

use App\Models\Freegame\ChApplication;
use App\Models\FreegameDeveloper\FreegameDeveloper;
use App\Models\FreegameDeveloper\ChApplicationImage;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\ApplicationTagRef;
use App\Models\FreegameDeveloper\DeveloperChApplication;
use Log;

class ChGameImageApplyService extends CustomService
{
    protected $chApplication;
    protected $chApplicationImage;
    protected $developerApplication;
    protected $infoService;
    protected $controllerName;
    protected $applicationTagRef;
    protected $developerChApplication;

    public function __construct(
        ChApplication $chApplication,
        chApplicationImage $chApplicationImage,
        DeveloperApplication $developerApplication,
        ChGameImageInfoService $infoService,
        ApplicationTagRef $applicationTagRef,
        DeveloperChApplication $developerChApplication
    ) {
        $this->chApplication = $chApplication;
        $this->chApplicationImage = $chApplicationImage;
        $this->developerApplication = $developerApplication;
        $this->infoService = $infoService;
        $this->applicationTagRef = $applicationTagRef;
        $this->developerChApplication = $developerChApplication;
    }

    /**
     * 詳細取得
     * @param  integer $imgId
     * @return array
     */
    public function getDetail($imgId)
    {
        $imgData = $this->chApplicationImage->getByIdAndDeveloper($imgId, auth_user_id());

        if ($imgData) {
            $appData = $this->getApplication($imgData->ch_app_id);
            if ($appData) {
                $this->setFileDetail($imgData);
                $this->setApplicationDetail($imgData);
            } else {
                $imgData->exists = false;
            }
        }
        return $imgData;
    }

    /**
     * ファイル情報設定
     * @param object $imgData
     */
    private function setFileDetail($imgData)
    {
        $imgData->file_url = $imgData->image_url;

        $path = pathinfo($imgData->file_url);
        $imgData->file_type = isset($path['extension']) ? $path['extension'] : '';
    }

    /**
     * アプリケーション情報設定
     * @param object $imgData
     */
    private function setApplicationDetail($imgData)
    {
        $appData = $this->getApplication($imgData->ch_app_id);

        $title = $appData->title;
        $type = [];

        if ($appData->general == 1) {
            $type[] = '一般';
        }

        if ($appData->adult   == 1) {
            $type[] = 'アダルト';
        }
        $imgData->app_title = $title;
        $imgData->app_type = implode('／', $type);
    }

    /**
     * ユーザタイトル取得
     * @param  integer $chAppId
     * @return object
     */
    public function getApplication($chAppId)
    {
        return $this->chApplication->getOne($chAppId);
    }

    /**
     * セレクトボックス用ユーザタイトル一覧取得
     * @return array;
     */
    public function getSelectApplicationList()
    {
        $chApplicationIdList = [];

        if (auth_is_sap()) {
            $chApplicationIdList = $this->developerChApplication->getListAppIdByDeveloperId(
                auth_user_id()
            )->toArray();
        }

        return $this->chApplication->getListTitleByDeveloperIdAndChAppIdList(
            auth_user_id(),
            $chApplicationIdList
        )->all();
    }

    /**
     * 登録情報表示用配列取得
     * @param  integer $chAppId
     * @return array
     */
    public function getViewInfoList($chAppId)
    {
        $keys = $this->infoService->getDataKeyList();
        $list = [];

        foreach ($keys as $key) {
            $info = $this->infoService->getData($chAppId, $key);
            $data = $info->toArrayViewData();

            if ($data) {
                $key = key($data);
                $list[$key][] = $data[$key];
            }
        }
        return $list;
    }

    /**
     * 登録情報セレクトボックス用配列取得
     * @param  integer $chAppId
     * @return array
     */
    public function getSelectInfoList($chAppId)
    {
        $keys = $this->infoService->getDataKeyList();
        $list = [];

        foreach ($keys as $key) {
            if (in_array($key, ['thumbnailPx80', 'thumbnailPx60'])) {
                continue;
            }

            // TODO : サムネイル（総合トップ等）を正式稼働する際に取り除く
            if ($key == 'overallRatedThumbnail') {
                continue;
            }

            $info = $this->infoService->getData($chAppId, $key);
            $data = $info->toArraySelectData();

            if ($data) {
                $key = key($data);
                $list[$key] = $data[$key];
            }
        }
        return $list;
    }

    /**
     * 登録中画像一覧取得
     * @param  integer $chAppId
     * @return array
     */
    public function getExamRegisterList($chAppId)
    {
        $param = [
            'ch_app_id' => $chAppId,
            'developer_id' => auth_user_id(),
        ];
        $imgDataList = $this->chApplicationImage->getExamRegisterList($param, 'id desc');

        foreach ($imgDataList as $imgData) {
            $this->setFileDetail($imgData);
            $this->setApplicationDetail($imgData);
        }
        return $imgDataList;
    }

    /**
     * 画像検索一覧取得
     * @param  array $param
     * @return array
     */
    public function getSearchList($param)
    {
        // 検索条件設定
        if (request()->has('search')) {
            $param = session("{$this->controllerName}.search", []);
            request()->merge($param);
        }
        if (!isset($param['perPage'])) {
            $param += ['perPage' => config('forms.ChGameImageApply.perPage')];
        }
        if (!isset($param['page'])) {
            $param += ['page' => 1];
        }
        $param = array_only($param, [
            'perPage',
            'request_date_from',
            'request_date_to',
            'ch_app_id',
            'image_type',
            'device',
            'examination_result',
            'examination_situation',
            'page'
        ]);

        $appends = $param;
        request()->session()->set("{$this->controllerName}.search", $param);

        // その他条件
        $param += ['developer_id' => auth_user_id()];

        // 取得・整形
        $imgDataList = $this->chApplicationImage->getList($param, 'request_date desc, id desc');
        $imgDataList->appends($appends);

        foreach ($imgDataList as $imgData) {
            $this->setFileDetail($imgData);
            $this->setApplicationDetail($imgData);
        }
        return $imgDataList;
    }

    /**
     * 画像登録
     * @param  array $param
     * @return boolean
     * @throws Exception
     */
    public function registerStore($param)
    {
        FreegameDeveloper::beginTransaction();
        try {
            $config = config('forms.ChGameImage');
            $chAppId = $param['ch_app_id'];
            $file = $param['upfile'];
            $type = explode('-', $param['type']);

            $param  = [
                'developer_id' => auth_user_id(),
                'ch_app_id' => $chAppId,
                'image_url' => '',
                'image_type' => $type[0],
                'image_size' => $type[1] ?: $config['none'],
                'device' => $type[2] ?: $config['deviceNone'],
                'examination_situation' => $config['register'],
                'examination_result' => $config['none'],
                'request_date' => timestamp_to_sqldate(now_stamp()),
                'examination_date' => '0000-00-00 00:00:00',
                'examination_developer_id' => 0,
                'sort_num' => 0,
            ];
            $imgData = $this->chApplicationImage->add($param);
            if ($imgData) {
                // 画像登録
                $upUrl = $this->uploadImage($imgData, $file->getRealPath(), $file->getMimeType());

                // URL更新
                $param = ['image_url' => $upUrl];
                $this->chApplicationImage->edit($imgData->id, $param);
            }
            FreegameDeveloper::commit();
        } catch (Exception $e) {
            Log::error('registerStoreError: ' . $e->getMessage());
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 画像審査設定
     * @param  array $imgDataList
     * @return boolean
     * @throws Exception
     */
    public function store($imgDataList)
    {
        FreegameDeveloper::beginTransaction();
        try {
            $param = [
                'examination_situation' => config('forms.ChGameImage.review'),
                'examination_result' => config('forms.ChGameImage.none'),
                'request_date' => timestamp_to_sqldate(now_stamp()),
            ];

            foreach ($imgDataList as $imgData) {
                $this->chApplicationImage->edit($imgData->id, $param);
            }

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            Log::error('storeError: ' . $e->getMessage());
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 削除
     * @param  object $imgData
     * @return boolean
     * @throws Exception
     */
    public function destroy($imgData)
    {
        FreegameDeveloper::beginTransaction();
        try {
            $imgId = $imgData->id;
            $this->chApplicationImage->del($imgId);

            // 画像削除
            $this->deleteImage($imgData);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            Log::error('destroyError: ' . $e->getMessage());
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 画像登録
     * @param  object  $imgData
     * @param  string $fileUrl
     * @param  string $fileType
     * @return string
     */
    private function uploadImage($imgData, $fileUrl, $fileType)
    {
        $imgId = $imgData->id;
        $upUrl = '';

        $info = $this->infoService->getDataByType(
            $imgData->ch_app_id,
            $imgData->image_type,
            $imgData->image_size,
            $imgData->device
        );

        if ($fileData = $info->toArrayUploadData($imgId, $fileType)) {
            $upUrl = $fileData['url'];
            $this->uploadFile($fileUrl, $fileData['name'], $fileData['dir']);
        }
        return $upUrl;
    }

    /**
     * 画像削除
     * @param  object   $imgData
     * @return string
     */
    private function deleteImage($imgData)
    {
        $imgId = $imgData->id;
        $fileType = $imgData->file_type;
        $upUrl = '';

        $info = $this->infoService->getDataByType(
            $imgData->ch_app_id,
            $imgData->image_type,
            $imgData->image_size,
            $imgData->device
        );

        if ($fileData = $info->toArrayUploadData($imgId, $fileType)) {
            $upUrl = $fileData['url'];
            $this->deleteFile($fileData['dir'] . $fileData['name']);
        }
        return $upUrl;
    }

    /**
     * 登録中か確認
     * @param  object $imgData
     * @return boolean
     */
    public function isEnableRegisterDelete($imgData)
    {
        if ($imgData->examination_situation == config('forms.ChGameImage.register')) {
            return true;
        }
        return false;
    }

    /**
     * 審査中か確認
     * @param  object $imgData
     * @return boolean
     */
    public function isEnableReviewDelete($imgData)
    {
        if ($imgData->examination_situation == config('forms.ChGameImage.review')) {
            return true;
        }
        return false;
    }

    /**
     * セレクトボックス用配列取得：画像種別
     * @return array
     */
    public function getSelectImageType()
    {
        $config = config('forms.ChGameImage');
        return [
            $config['thumbnail'] => $config['namesImageType'][$config['thumbnail']],
            $config['overallRatedThumbnail'] => $config['namesImageType'][$config['overallRatedThumbnail']],
            $config['rotateBanner'] => $config['namesImageType'][$config['rotateBanner']],
        ];
    }

    /**
     * セレクトボックス用配列取得：デバイス
     * @return array
     */
    public function getSelectDevice()
    {
        $config = config('forms.ChGameImage');

        return [
            $config['thumbnail'] => [
                $config['px60'] => $config['namesImageSize'][$config['px60']],
                $config['px80'] => $config['namesImageSize'][$config['px80']],
                $config['px200'] => $config['namesImageSize'][$config['px200']]
            ],
            $config['overallRatedThumbnail']         => [
                $config['px400'] => $config['namesImageSize'][$config['px400']]
            ],
            $config['rotateBanner'] => [
                $config['pc'] => $config['namesDevice'][$config['pc']],
                $config['sp'] => $config['namesDevice'][$config['sp']]
            ],
        ];
    }

    /**
     * セレクトボックス用配列取得：審査状況
     * @return array
     */
    public function getSelectExamSituation()
    {
        $config = config('forms.ChGameImage');

        return [
            $config['review'] => $config['namesExamSituation'][$config['review']],
            $config['completed'] => $config['namesExamSituation'][$config['completed']],
            $config['remand'] => $config['namesExamSituation'][$config['remand']],
            $config['delete'] => $config['namesExamSituation'][$config['delete']],
        ];
    }

    /**
     * セレクトボックス用配列取得：審査結果
     * @return array
     */
    public function getSelectExamResult()
    {
        $config = config('forms.ChGameImage');

        return [
            $config['ok'] => $config['namesExamResult'][$config['ok']],
            $config['ng'] => $config['namesExamResult'][$config['ng']],
        ];
    }

    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $applyConfig = config('forms.ChGameImageApply');
        $config = config('forms.ChGameImage');

        return [
            'formData' => [
                'screenName' => $applyConfig['screenName'],
                'breadcrumbs' => $applyConfig['breadcrumbsParent'],
            ],
            'selectImageType' => $this->getSelectImageType(),
            'selectDevice' => $this->getSelectDevice(),
            'selectExamSituation' => $this->getSelectExamSituation(),
            'selectExamResult' => $this->getSelectExamResult(),
            'namesImageType' => $config['namesImageType'],
            'namesImageSize' => $config['namesImageSize'],
            'namesDevice' => $config['namesDevice'],
            'namesExamSituation' => $config['namesExamSituation'],
            'namesExamResult' => $config['namesExamResult'],
        ];
    }

    /**
     * コントローラ名設定
     * @param string $controllerName
     */
    public function setControllerName($controllerName)
    {
        $this->controllerName = $controllerName;
    }

    /**
     * Get Girls Application
     * @return array
     */
    public function getGirlsAppList()
    {
        $config = config('forms.ChGameImageApply');
        return $this->applicationTagRef->getAppIdListByTagId($config['girlsAppTagId'])->all();
    }
}
