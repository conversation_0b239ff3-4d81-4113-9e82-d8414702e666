<?php

namespace App\Services;

use Exception;
use App\Libs\Apply\Release;
use App\Models\Freegame\ApplicationDevice;
use App\Models\Freegame\ChApplication;
use App\Models\FreegameDeveloper\ApplicationDetail;
use App\Models\FreegameDeveloper\ApplicationImage;
use App\Models\FreegameDeveloper\ChApplicationImage;
use App\Models\FreegameDeveloper\ClApplicationImage;

/**
 * リリース申請サービス
 */
class ApplyReleaseService
{
    /** @var ApplyReleaseApiService */
    private $applyReleaseApiService;

    /** @var ApplyExaminationFileApiService */
    private $applyExaminationFileApiService;
    
    /** @var CommunitiesService */
    protected $communitiesService;

    /** @var TopicService */
    protected $topicService;

    /** @var ApplicationDevice */
    protected $applicationDevice;

    /** @var ChApplication */
    protected $chApplication;

    /** @var ApplicationDetail */
    protected $applicationDetail;

    /** @var ApplicationImage */
    protected $applicationImage;

    /** @var ChApplicationImage */
    protected $chApplicationImage;

    /** @var ClApplicationImage */
    protected $clApplicationImage;

    /**
     * リリース申請サービスコンストラクタ
     *
     * @param ApplyReleaseApiService $applyReleaseApiService
     * @param ApplyExaminationFileApiService $applyExaminationFileApiService
     * @param ApplicationDevice $applicationDevice
     * @param ChApplication $chApplication
     * @param ApplicationDetail $applicationDetail
     * @param CommunitiesService $communitiesService
     * @param TopicService $topicService
     * @param ApplicationImage $applicationImage
     * @param ChApplicationImage $chApplicationImage
     * @param ClApplicationImage $clApplicationImage
     */
    public function __construct(
        ApplyReleaseApiService $applyReleaseApiService,
        ApplyExaminationFileApiService $applyExaminationFileApiService,
        ApplicationDevice $applicationDevice,
        ChApplication $chApplication,
        ApplicationDetail $applicationDetail,
        CommunitiesService $communitiesService,
        TopicService $topicService,
        ApplicationImage $applicationImage,
        ChApplicationImage $chApplicationImage,
        ClApplicationImage $clApplicationImage
    ) {
        $this->applyReleaseApiService = $applyReleaseApiService;
        $this->applyExaminationFileApiService = $applyExaminationFileApiService;
        $this->applicationDevice = $applicationDevice;
        $this->chApplication = $chApplication;
        $this->applicationDetail = $applicationDetail;
        $this->communitiesService = $communitiesService;
        $this->topicService = $topicService;
        $this->applicationImage = $applicationImage;
        $this->chApplicationImage = $chApplicationImage;
        $this->clApplicationImage = $clApplicationImage;
    }
    
    /**
     * リリース申請情報を取得する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return Release
     */
    public function getRelease($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $result = $this->applyReleaseApiService
            ->getRelease($appId , $kindAndDevice[0], $kindAndDevice[1]);
        
        return new Release($result['response']['body']);
    }
    
    /**
     * 審査用の画像を審査に提出する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return void
     */
    public function applyExaminationImages($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'examinationImages' => [
                'similarityCheckMaterials' => (object)[],
                'logicalCheckMaterials' => (object)[],
            ]
        ];
        
        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'examinationImages', $content);
    }
        
    /**
     * ゲーム紹介ページ：デザイン部分素材カテゴリを審査に提出する
     *
     * @return void
     */
    public function applyIntroductionImages(
        $appId, $device, 
        $hasCatchphraseImage, $catchphraseImageText,
        $isDesignated, $isCharacterPriorityDescriptionText,
        $isUseCopyright, $copyrightText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'introductionImages' => [
                'characterImages' => (object)[],
                'titleLogoImages' => (object)[],
                'backgroundImages' => (object)[],
                'screenshotImageDiagram' => (object)[],
                'catchphraseImageDiagram' => [
                    'hasCatchphraseImage' => ($hasCatchphraseImage == 'true'),
                    'catchphraseImageText' => $catchphraseImageText,
                ],
                'isCharacterPriorityNotes' => [
                    'isDesignated' => ($isDesignated == 'true'),
                    'isCharacterPriorityDescriptionText' => $isCharacterPriorityDescriptionText,
                ],
                'copyright' => [
                    'isUseCopyright' => ($isUseCopyright == 'true'),
                    'copyrightText' => $copyrightText,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }
        
        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'introductionImages', $content);
    }

    /**
     * 動作検証を審査に提出する:リリース申請
     *
     * @return void
     */
    public function applyVerification(
        $appId, $device,
        $apkUpload, $isBugCheckDone, $isGameTestingDone, $isFinishedProduct, $isExaminationCheckDone, $gameStartUrlText,
        $isOtherWorksUsedAndUnMoralityExpressionUnUsed,
        $isFollowingTermsDevelop, $isFollowingTermsManagement,
        $inGameVirtualCurrency, $isImplementsA,$usingInspectionApi, $inspectionApiVerificationText,
        $tags, $isSignedByDigitalSignature, $isUsingExternalInstaller, $isUsingModule,
        $hasTradeFeatureInGame, $hasGamechip, $ngwordVersionText, $hasNgwordCheck, $isMonthlyPaymentServiceOrSubscription

    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'verification' => [
                'productionVerification' => [
                    'isBugCheckDone' => $isBugCheckDone,
                    'isGameTestingDone' => $isGameTestingDone,
                    'isFinishedProduct' => $isFinishedProduct,
                    'isExaminationCheckDone' => $isExaminationCheckDone,
                ],
                'otherWorksUsedAndUnMoralityExpressionUnUsed' => [
                    'isOtherWorksUsedAndUnMoralityExpressionUnUsed'
                        => $isOtherWorksUsedAndUnMoralityExpressionUnUsed,
                ],
                'followingTermsDevelop' => [
                    'isFollowingTermsDevelop' => $isFollowingTermsDevelop,
                ],
                'followingTermsManagement' => [
                    'isFollowingTermsManagement' => $isFollowingTermsManagement,
                ],
                'gameVirtualCurrency' => [
                    'inGameVirtualCurrency' => $inGameVirtualCurrency,
                ],
                'gamesTags' => [
                    'tags' => $tags,
                ],
                'gameStartUrl' => [
                    'gameStartUrlText' => $gameStartUrlText,
                ],
                'usedInspectionApi' => [
                    'usingInspectionApi' => $usingInspectionApi,
                ],
                'inspectionApiVerification' => [
                    'inspectionApiVerificationText' => $inspectionApiVerificationText,
                ],
                'apkUpload' => (object)[],
                'implementsVirtualCurrency' => [
                    'isImplementsA' => $isImplementsA,
                ],
                'digitalSignature' => [
                    'isSignedByDigitalSignature' => $isSignedByDigitalSignature,
                ],
                'externalInstaller' => [
                    'isUsingExternalInstaller' => $isUsingExternalInstaller,
                ],
                'module' => [
                    'isUsingModule' => $isUsingModule,
                ],
                'tradeFeatureInGame' => [
                    'hasTradeFeatureInGame' => $hasTradeFeatureInGame,
                    'hasGamechip' => $hasGamechip,
                ],
                'ngwordVersion' => [
                    'ngwordVersionText' => $ngwordVersionText,
                ],
                'ngwordCheck' => [
                    'hasNgwordCheck' => $hasNgwordCheck,
                ],
                'monthlyPaymentServiceOrSubscription' => [
                    'isMonthlyPaymentServiceOrSubscription' => $isMonthlyPaymentServiceOrSubscription,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'verification', $content);
    }

    /**
     * プラットフォーム上に掲載される画像を審査に提出
     *
     * @return void
     */
    public function applyPlatformImages($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'platformImages' => [
                'thumbnailImage' => (object)[],
                'overallRatedThumbnailImage' => (object)[],
                'gameIntroductionImage' => [
                    'hasGameIntroductionImage' => (object)[],
                ],
                'android192x192' => [
                    'hasAndroid192x192' => (object)[],
                ],
                'android512x512' => [
                    'hasAndroid512x512' => (object)[],
                ],
                'appleTouchIcon' => [
                    'hasAppleTouchIcon' => (object)[],
                ],
                'gameIntroductionMovie' => [
                    'gameIntroductionMovie' => (object)[],
                ],
                'gameIntroductionThumbnail' => [
                    'gameIntroductionThumbnail' => (object)[],
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'platformImages', $content);
    }
    
    /**
     * 公式サイト検証を審査に提出する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return void
     */
    public function applyReleasesite(
        $appId, $device,
        $isFollowingTermsCreate, $meansVerificationText, $officialSiteUrlText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'releaseSite' => [
                'designDelivery' => (object)[],
                'createdOfficialSite' => [
                    'isFollowingTermsCreate' => $isFollowingTermsCreate,
                ],
                'meansVerification' => [
                    'meansVerificationText' => $meansVerificationText,
                ],
                'officialSiteDetail' => [
                    'officialSiteUrlText' => $officialSiteUrlText,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }
        
        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'releaseSite', $content);
    }
    
    /**
     * 審査用の画像を審査に提出する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return void
     */
    public function applyCommunity($appId, $device)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = [
            'community' => [
                'communityCreate' => (object)[],
                'topicCreate' => (object)[],
            ]
        ];
        
        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'community', $content);
    }

    /**
     * ゲーム内画像申請が行われているか確認する
     *
     * @param  mixed $appId
     * @param  mixed $kind
     * @param  mixed $imageType
     * @return void
     */
    public function isExaminationFileUpload($appId, $device, $imageType)
    {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $result = $this->applyExaminationFileApiService->getExaminationFileInfo($appId, $kindAndDevice[0], $imageType, 'release');

        return !empty($result['body']['uploadFileList']);
    }
    
    /**
     * コミュニティが作成されているか
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isCommunityCreate($appId)
    {
        $list = $this->communitiesService->getCommunitiesList($appId);
        $count = count($list);
        return $count > 0;
    }
    
    /**
     * トピックが作成されているか
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isTopicCreate($appId)
    {
        $list = $this->topicService->getList(['app_id' => $appId]);
        $count = count($list);
        return $count > 0;
    }
    
    /**
     * 指定デバイスが対象かどうかを確認する
     *
     * @param mixed $device
     * @return boolean
     */
    public function isTargetDevice($device)
    {
        $release = new Release();
        return $release->isTargetDevice($device);
    }

    /**
     * Olympus用の種別とデバイスを取得する
     *
     * @param  mixed $device
     * @return void
     */
    private function getKindAndDevice($device)
    {
        $result = null;
        switch ($device) {
            case 'pc':
                $result = ['application','pc'];
                break;
            
            case 'sp':
                $result = ['application','sp'];
                break;
            
            case 'android_app':
                $result = ['application','android_app'];
                break;
        
            case 'pc_channeling':
                $result = ['ch_application','pc'];
                break;
    
            case 'sp_channeling':
                $result = ['ch_application','sp'];
                break;

            case 'client':
                $result = ['cl_application', 'client'];
                break;
                
            default:
                throw new Exception($device.' is not target device.');
                break;
        }
        return $result;
    }

    /**
     * 問い合わせ先メールアドレスが設定されているか確認する
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isRegisteredContactMailAddress($appId)
    {
        $result = $this->applicationDetail->getApplicationDetail($appId);
        if (empty($result)) {
            return false;
        }

        return !empty($result->email);
    }

    /**
     * デバイス別ゲーム情報が設定されているか確認する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @param  mixed $target
     * @return bool
     */
    public function isRegisteredGameInfo($appId, $device, $target)
    {
        if (strpos($device, 'channeling') === false) {
            $result = $this->applicationDevice->getOne($appId, $device);
        } else {
            $result = $this->chApplication->getOne($appId);
        }
        if (empty($result)) {
            return false;
        }

        switch ($target) {
            case 'game_introduction':
                return !empty($result->description_middle);
            case 'channeling_game_introduction':
                return !empty($result->description_middle);
            case 'game_introduction_detail':
                return !empty($result->description);
            case 'supported_device':
                return !empty($result->restrictions);
            default:
                return false;
        }
    }

    /**
     * リリース申請のサムネイル画像が登録されているかを確認する
     *
     * @param mixed $appId
     * @param mixed $imageSize
     * @param mixed $device
     * @return bool
     */
    public function isThumbnailImageRegistered($appId, $imageSize, $device)
    {
        $gamesApplyDeviceTypeList = config('forms.Games.applyDeviceType');
        $chGamesApplyDeviceTypeList = config('forms.ChGames.applyDeviceType');
        $clGamesApplyDeviceTypeList = config('forms.ClGames.applyDeviceType');
        if (in_array($device, $gamesApplyDeviceTypeList)) {
            // ソーシャルゲームの場合
            $thumbnailList = $this->applicationImage->getPostThumbnailList($appId)->toArray();
        } else if (in_array($device, $chGamesApplyDeviceTypeList)) {
            // チャネリングゲームの場合
            $thumbnailList = $this->chApplicationImage->getPostThumbnailList($appId)->toArray();
        } else if (in_array($device, $clGamesApplyDeviceTypeList)) {
            // クライアントゲームの場合
            $thumbnailList = $this->clApplicationImage->getPostThumbnailList($appId)->toArray();
        } else {
            throw new Exception($device.' is not target device.');
        }

        $thumbnailSizes = array_column($thumbnailList, 'image_size');

        return in_array($imageSize, $thumbnailSizes);
    }

    /**
     * リリース申請のサムネイル（総合トップ等）画像が登録されているかを確認する
     *
     * @param mixed $appId
     * @param mixed $imageSize
     * @param mixed $device
     * @return bool
     */
    public function isOverallRatedThumbnailImageRegistered($appId, $imageSize, $device)
    {
        $gamesApplyDeviceTypeList = config('forms.Games.applyDeviceType');
        $chGamesApplyDeviceTypeList = config('forms.ChGames.applyDeviceType');
        $clGamesApplyDeviceTypeList = config('forms.ClGames.applyDeviceType');
        if (in_array($device, $gamesApplyDeviceTypeList)) {
            // ソーシャルゲームの場合
            $thumbnailList = $this->applicationImage->getPostThumbnailList($appId, config('forms.GameImage.overallRatedThumbnail'))->toArray();
        } else if (in_array($device, $chGamesApplyDeviceTypeList)) {
            // チャネリングゲームの場合
            $thumbnailList = $this->chApplicationImage->getPostThumbnailList($appId)->toArray();
        } else if (in_array($device, $clGamesApplyDeviceTypeList)) {
            // クライアントゲームの場合
            $thumbnailList = $this->clApplicationImage->getPostThumbnailList($appId)->toArray();
        } else {
            throw new Exception($device.' is not target device.');
        }

        $thumbnailSizes = array_column($thumbnailList, 'image_size');

        return in_array($imageSize, $thumbnailSizes);
    }

    /**
     * リリース申請のゲーム情報入力を審査に提出する
     *
     * @return void
     */
    public function applyGameInformation(
        $appId, $device,
        $recommendationAgeDivision, $taxIncludedPrice, $taxExcludedPrice, $clientGameIntroduction
    ) {

        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'gameInformation' => [
                'clientGameIntroduction' => ['clientGameIntroductionText' => $clientGameIntroduction],
                'contactMailAddress' => (object)[],
                'gameIntroduction' => (object)[],
                'channelingGameIntroduction' => (object)[],
                'gameIntroductionDetail' => (object)[],
                'supportedDevice' => (object)[],
                'recommendationAgeDivision' => [
                    'divisionValue' => $recommendationAgeDivision,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'gameInformation', $content);
    }

    /**
     * Linksmateを審査に提出する
     *
     * @return void
     */
    public function applyLinksmate(
        $appId, $device, 
        $linksmateCopyright, $linksmateCopyrightText
    ) {
        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);
        
        $content = $this->getTargetContent($device, [
            'linksmate' => [
                'linksmateTitleLogoImages' => (object)[],
                'linksmateCopyright' => [
                    'linksmateIsUseCopyright' => ($linksmateCopyright == 'true'),
                    'linksmateCopyrightText' => $linksmateCopyrightText,
                ],
            ],
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'linksmate', $content);
    }

    /**
     * 審査項目から指定デバイスで対象のものを返す
     *
     * @param  mixed $device
     * @param  mixed $content
     * @return array
     */
    private function getTargetContent($device, $content)
    {
        $release = new Release();
        return $release->getTargetContent($device, $content);
    }

    /**
     * 審査項目が空かを返す。
     *
     * @param  mixed $content
     * @return boolean
     */
    private function isEmptyContent($content)
    {
        foreach ($content as $items) {
            if (!empty($items)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Win対応環境を審査に提出する
     *
     * @return void
     */
    public function applyWindowsSupportedEnvironment(
        $appId, $device,
        $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
    ) {

        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'windowsSupportedEnvironment' => [
                'osVersion' => ['osVersionText' => $osVersionText],
                'processor' => ['processorText' => $processorText],
                'memory' => ['memorySize' => (int)$memorySize, 'memorySizeUnit' => $memorySizeUnit],
                'graphics' => ['graphicsText' => $graphicsText],
                'diskFreeSpace' => ['capacitySize' => (int)$capacitySize, 'capacitySizeUnit' => $capacitySizeUnit],
                'note' => ['noteText' => $noteText],
            ]
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'windowsSupportedEnvironment', $content);
    }

    /**
     * Mac対応環境を審査に提出する
     *
     * @return void
     */
    public function applyMacSupportedEnvironment(
        $appId, $device,
        $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
    ) {

        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'macSupportedEnvironment' => [
                'osVersion' => ['osVersionText' => $osVersionText],
                'processor' => ['processorText' => $processorText],
                'memory' => ['memorySize' => (int)$memorySize, 'memorySizeUnit' => $memorySizeUnit],
                'graphics' => ['graphicsText' => $graphicsText],
                'diskFreeSpace' => ['capacitySize' => (int)$capacitySize, 'capacitySizeUnit' => $capacitySizeUnit],
                'note' => ['noteText' => $noteText],
            ]
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'macSupportedEnvironment', $content);
    }

    /**
     * サイズユニットが定義内のものか確認
     *
     * @param  string
     * @return bool
     */
    public function ofSizeUnit($value)
    {
        $sizeUnit = config('forms.Games.sizeUnit');
        return in_array($value, $sizeUnit);
    }

    /**
     * CEROを審査に提出する
     *
     * @return void
     */
    public function applyCero(
        $appId, $device,
        $classificationText, $contentIcons
    ) {

        // $deviceをOlympusの種別とデバイスに変換する
        $kindAndDevice = $this->getKindAndDevice($device);

        $content = $this->getTargetContent($device, [
            'cero' => [
                'classification' => ['classificationText' => $classificationText],
                'contentIcons' => ['icons' => $contentIcons],
            ]
        ]);

        if ($this->isEmptyContent($content)) {
            throw new Exception($device.' is not target device.');
        }

        $this->applyReleaseApiService
            ->patchRelease(
                $appId , $kindAndDevice[0], $kindAndDevice[1], 
                'cero', $content);
    }

    /**
     * CERO区分が定義内のものか確認
     *
     * @param  string
     * @return bool
     */
    public function ofClassification($value)
    {
        $classification = config('forms.Games.CEROClassification');
        return in_array($value, $classification);
    }

    /**
     * CEROコンテンツアイコンが定義内のものか確認
     *
     * @param  array
     * @return bool
     */
    public function ofContentIcon($value)
    {
        $contentIcons = config('forms.Games.CEROContentIcons');
        foreach ($value as $icon) {
            if (!array_key_exists($icon, $contentIcons)) {
                return false;
            }
        }

        return true;
    }

    /**
     * radioボタンのtrue/falseをbool型に変換
     *
     * @param  string
     * @return bool
     */
    public function convertBool($value)
    {
        switch ($value) {
            case 'true':
                return true;
            case 'false':
                return false;
            default:
                return null;
        }
    }
}