<?php

namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\Freegame\ApplicationDevice;
use App\Models\FreegameSandbox\SandboxMember;
use App\Models\FreegameSandbox\User;
use App\Models\FreegameDeveloper\DeveloperApplication;
use Guz<PERSON>Http\Client as HttpClient;
use Illuminate\Contracts\Logging\Log;
use Exception;

/**
 * 定期購入：解約予約サービスクラス
 */
class SubscriptionCancelReservationService extends CustomService
{
    /** @var Application */
    protected $application;
    /** @var ApplicationDevice */
    protected $applicationDevice;
    /** @var DeveloperApplication */
    protected $developerApplication;
    /** @var SandboxMember */
    protected $sandboxMember;
    /** @var SubscriptionApiService */
    protected $subscriptionApiService;
    /** @var User */
    protected $user;

    /**
     * コンストラクタ
     * 
     * @param Application $application
     * @param ApplicationDevice $applicationDevice
     * @param DeveloperApplication $developerApplication
     * @param SandboxMember $sandboxMember
     * @param User $user
     */
    public function __construct(
        Application $application,
        ApplicationDevice $applicationDevice,
        DeveloperApplication $developerApplication,
        HttpClient $httpClient,
        Log $log,
        SandboxMember $sandboxMember,
        User $user
    ) {
        $this->application = $application;
        $this->applicationDevice = $applicationDevice;
        $this->developerApplication = $developerApplication;
        $this->sandboxMember = $sandboxMember;
        $this->subscriptionApiService = new SubscriptionApiService(true, $httpClient, $log);
        $this->user = $user;
    }

    /**
     * フォームに設定するデータを取得
     * 
     * @return array
     */
    public function getFormData()
    {
        return [
            'menuName' => config('forms.SubscriptionCancelReservation.menuName'),
            'screenName' => config('forms.SubscriptionCancelReservation.screenName'),
            'appTitleType' => $this->getApplicationTitleList(),
            'sandBoxIds' => $this->getSandboxIdList()
        ];
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        // 定期購入用デバイスリスト取得
        $deviceList = array_keys(config('forms.SubscriptionCancelReservation.device'));

        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap($deviceList);
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice($deviceList);
        }

        $opts = ['' => ['' => 'タイトルを選択してください']];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $this->convertDisplayTitle($data->title, $data->device);
        }

        return $opts;
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return [];
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }    

    /**
     * 画面表示用にアプリタイトルをコンバートする
     * 
     * @param string $title
     * @param string $device
     * @return string
     */
    private function convertDisplayTitle($title, $device)
    {
        $displayString = config('forms.SubscriptionCancelReservation.device');

        return $title . $displayString[$device];
    }

    /**
     * サンドボックスのアプリケーションリストを取得
     *
     * @return array
     */
    public function getSandboxIdList()
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $list = $this->sandboxMember->getIdListByDeveloperIdAndStatus(auth_user_id(), 'active');

        $opts = [];
        foreach ($list as $data) {
            $opts[] = $data->id;
        }

        return array_merge([0 => 'IDを選択してください'], $opts);
    }

    /**
     * ユーザーの購入済み定期購入アイテム一覧を取得
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array $result    ユーザーの購入済み定期購入アイテム一覧
     */
    public function getUserItems($condition)
    {
        $result = [
            'isSearched' => false,
            'items' => []
        ];
        if (!isset($condition['app_id'], $condition['id'], $condition['device'])) {
            return $result;
        }
        $user = $this->user->getOne($condition['id']);
        $path = [
            'openId' => $user['member_id']
        ];
        $response = $this->subscriptionApiService->getUserItems($path);
        if (!$response['resultStatus']) {
            throw new Exception($response['resultMessage']);
        }
        $result['isSearched'] = true;
        if (isset($response['response']['body'])) {
            $result['items'] = $this->createUserItemDetail($response['response']['body'], $condition['app_id'], $condition['device'], $user['member_id']);
        }

        return $result;
    }

    /**
     * 定期購入アイテムの解約予約を行う
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array $result    解約予約の結果
     */
    public function cancelReservation($condition)
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'openId' => $condition['memberId'],
            'purchaseToken' => $condition['purchaseToken']
        ];

        // ボディのパラメーターに使用する値をまとめる
        $body = [
            'canceledReasonId' => mt_rand(1, 5),
            'canceledReasonText' => 'Sandbox Subscription Cancel'
        ];

        $response = $this->subscriptionApiService->cancelReservation($path, $body);
        if (!$response['resultStatus']) {
            throw new Exception($response['resultMessage']);
        }

        return [
            'result' => [
                'status' => $response['response']['body']['status']
            ]
        ];
    }

    /**
     * 定期購入アイテムの解約予約を解除する
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array $result    解約予約の解除結果
     */
    public function resetReservation($condition)
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'openId' => $condition['memberId'],
            'purchaseToken' => $condition['purchaseToken']
        ];

        $response = $this->subscriptionApiService->resetReservation($path);
        if (!$response['resultStatus']) {
            throw new Exception($response['resultMessage']);
        }

        return [
            'result' => [
                'status' => $response['response']['body']['status']
            ]
        ];
    }

    /**
     * 定期購入アイテム詳細を成形
     * 
     * @param array  $body     APIレスポンスボディ
     * @param int    $appId    アプリID
     * @param string $device   デバイス
     * @param string $memberId メンバーID
     * @return array $result   定期購入アイテム詳細
     */
    private function createUserItemDetail($body, $appId, $device, $memberId)
    {
        $result = [];
        foreach ($body['userItems'] as $userItem) {
            if ($userItem['application']['appId'] != $appId || $userItem['application']['device'] != $device) {
                continue;
            }

            $result[] = [
                'memberId' => $memberId,
                'subsSku' => $userItem['subscription']['sku'],
                'planSku' => $userItem['basePlan']['sku'],
                'offerSku' => isset($userItem['offers'][0]['sku']) ? $userItem['offers'][0]['sku'] : '-',
                'status' => $userItem['status'],
                'purchaseToken' => $userItem['purchaseToken']
            ];
        }

        return $result;
    }
}
