<?php

namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\Freegame\ApplicationDevice;
use App\Models\FreegameDeveloper\DeveloperApplication;
use Carbon\Carbon;

/**
 * 定期購入
 */
class SubscriptionService extends CustomService
{
    /** @var Application */
    protected $application;
    /** @var ApplicationDevice */
    protected $applicationDevice;
    /** @var DeveloperApplication */
    protected $developerApplication;
    /** @var SubscriptionItemApiService */
    protected $subscriptionItemApiService;

    /**
     * コンストラクタ
     * 
     * @param Application $application
     * @param ApplicationDevice $applicationDevice
     * @param DeveloperApplication $developerApplication
     * @param SubscriptionItemApiService $subscriptionItemApiService
     */
    public function __construct(
        Application $application,
        ApplicationDevice $applicationDevice,
        DeveloperApplication $developerApplication,
        SubscriptionItemApiService $subscriptionItemApiService
    ) {
        $this->application = $application;
        $this->applicationDevice = $applicationDevice;
        $this->developerApplication = $developerApplication;
        $this->subscriptionItemApiService = $subscriptionItemApiService;
    }

    /**
     * フォームに設定するデータを取得
     * 
     * @return array
     */
    public function getFormData()
    {
        return [
            'menuName' => config('forms.Subscription.menuName'),
            'screenName' => config('forms.Subscription.screenName'),
            'subScreenName' => config('forms.Subscription.subScreenName'),
            'appTitleType' => $this->getApplicationTitleList()
        ];
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return [];
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }

    /**
     * 権限があるアプリケーションチェック
     * 
     * @param string $appId アプリID
     * @return bool $matched 判定結果
     */
    public function isDeveloperApplication($appId)
    {
        $matched = false;
        if (empty($appId)) {
            return $matched;
        }
        // ログイン情報を元に権限があるアプリケーションか判定する
        if (!auth_is_sap()) {
            return true;
        }
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);
        foreach ($developerApplicationList as $data) {
            if ($data->app_id == $appId) {
                $matched = true;
                break;
            }
        }

        return $matched;
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        // 定期購入用デバイスリスト取得
        $deviceList = config('subscription.device');

        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap($deviceList);
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice($deviceList);
        }

        $opts = ['' => ['' => 'タイトルを選択してください']];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $this->convertDisplayTitle($data->title, $data->device);
        }
        return $opts;
    }

    /**
     * 検索条件をセッションで保持
     *
     * @param  array $search
     * @return array
     *
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('Subscription.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'app_id',
            'device',
        ]);
        request()->session()->set('Subscription.search', $search);
        return $search;
    }

    /**
     * 定期購入一覧を取得する
     * 
     * @param array $condition
     * @return array
     * @throws \Exception
     */
    public function getSubscriptionList($condition)
    {
        if (empty($condition['app_id']) || empty($condition['device'])) {
            return [];
        }

        $pathParameters = [
            'appId'  => $condition['app_id'],
            'device' => $condition['device'],
            'kind' => config('subscription-item-api.api_kind'),
        ];

        // 定期購入一覧を取得する
        $getSubscriptions = $this->subscriptionItemApiService->getSubscriptions($pathParameters);
        if ($getSubscriptions['resultStatus']) {
            return $this->setSubscriptionItemCount($getSubscriptions['response']['body']['subscriptions']);
        }
        // タイトルに対応した定期購入がない場合
        if ($getSubscriptions['resultCode'] === 404) {
            return [];
        }

        throw new \Exception($getSubscriptions['resultMessage']);
    }

    /**
     * 定期購入を取得する
     * 
     * @param mixed $request
     * @return array
     * @throws \Exception
     */
    public function getSubscription($request)
    {
        if (empty($request['app_id']) || empty($request['device']) || empty($request['sku'])) {
            return [
                'resultMessage' => config('subscription.result_message.parameter_error'),
            ];
        }

        $pathParameters = [
            'appId'   => $request['app_id'],
            'device'  => $request['device'],
            'kind' => config('subscription-item-api.api_kind'),
            'subsSku' => $request['sku']
        ];

        // 定期購入情報を取得する
        $getSubscription = $this->subscriptionItemApiService->getSubscription($pathParameters);
        if ($getSubscription['resultStatus']) {
            return array_merge(
                $getSubscription['response']['body'],
                [
                    'app_id' => $request['app_id'],
                    'device' => $request['device'],
                ]
            );
        }

        throw new \Exception($getSubscription['resultMessage']);
    }

    /**
     * 定期購入を登録する
     * 
     * @param array $request
     * @return void
     * @throws \Exception
     */
    public function storeSubscription($request)
    {
        $pathParameters = [
            'appId' => $request['app_id'],
            'device' => $request['device'],
            'kind' => config('subscription-item-api.api_kind'),
            'subsSku' => $request['sku'],
        ];
        $bodyParameters = [
            'title' => $request['title'],
            'description' => $request['description'],
        ];

        $apiResult = $this->subscriptionItemApiService->storeSubscription($pathParameters, $bodyParameters);
        if (!$apiResult['resultStatus']) {
            // その他エラー
            throw new \Exception(config('subscription.result_message.fail_error'));
        }
    }   

    /**
     * 定期購入を更新する
     * 
     * @param array $request
     * @return void
     * @throws \Exception
     */
    public function updateSubscription($request)
    {
        $pathParameters = [
            'appId' => $request['app_id'],
            'device' => $request['device'],
            'kind' => config('subscription-item-api.api_kind'),
            'subsSku' => $request['sku'],
        ];
        $bodyParameters = [
            'title' => $request['title'],
            'description' => $request['description'],
        ];

        $apiResult = $this->subscriptionItemApiService->updateSubscription($pathParameters, $bodyParameters);
        if (!$apiResult['resultStatus']) {
            // その他エラー
            throw new \Exception(config('subscription.result_message.fail_error'));
        }
    }

    /**
     * 定期購入アイテム(プラン(アクティブ)と特典のカウント)を取得して配列に含める
     * 
     * @param array $subscriptions
     * @return array
     */
    private function setSubscriptionItemCount($subscriptions)
    {
        foreach ($subscriptions as $subscriptionIndex => $subscription) {
            $activeBasePlanCount = 0;
            $offerActiveCount = 0;
            foreach ($subscription['basePlans'] as $basePlan) {
                if ($basePlan['status'] === 'active') {
                    $activeBasePlanCount += 1;
                    foreach ($basePlan['offers'] as $offer) {
                        if ($offer['status'] === 'active') {
                            $offerActiveCount += 1;
                        }
                    }
                }
            }
            $subscriptions[$subscriptionIndex]['planActiveCount'] = $activeBasePlanCount;
            $subscriptions[$subscriptionIndex]['offerActiveCount'] = $offerActiveCount;
        }
        return $subscriptions;
    }

    /**
     * 画面表示用にアプリタイトルをコンバートする
     * 
     * @param string $title
     * @param string $device
     * @return string
     */
    private function convertDisplayTitle($title, $device)
    {
        $displayString = [
            'emulator' => '(エミュレーター)',
            'android_app' => '(アンドロイドAPP)',
            'ios_app' => '(iOS APP)',
            'pc' => '(PC)',
            'sp' => '(SmartPhone)',
        ];
        return $title . $displayString[$device];
    }

    /**
     * 登録済みデバイス情報の存在チェック
     * 
     * @param string $appId  アプリID
     * @param string $device デバイス
     * @return bool 判定結果
     */
    public function hasApplicationDevice($appId, $device)
    {
        if (empty($appId) || empty($device)) {
            return false;
        }
        $deviceInfo = $this->applicationDevice->getOne($appId, $device);

        return !empty($deviceInfo);
    }

    /**
     * 定期購入詳細情報取得
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array 定期購入詳細
     * @throws \Exception
     */
    public function getDetail($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku']
        ];

        $response = $this->subscriptionItemApiService->getSubscription($path);
        if (!isset($response['response']['body'])) {
            throw new \Exception($response['resultMessage']);
        }

        $detail = $this->formatDetail($response['response']['body']);
        $detail['subscriptionInfo']['title'] = $this->getApplicationTitle($condition['app_id']);

        return $detail;
    }

    /**
     * 基本プランを取得
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array 基本プラン
     * @throws \Exception
     */
    public function getBasePlan($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku'],
            'planSku' => $condition['planSku']
        ];

        $response = $this->subscriptionItemApiService->getBasePlan($path);
        if (!isset($response['response']['body'])) {
            throw new \Exception($response['resultMessage']);
        }
        
        return [
            'planSku' => $response['response']['body']['sku'],
            'price' => $response['response']['body']['priceAmountMicros'] * 0.000001,
            'duration' => $response['response']['body']['duration'],
            'status' => $response['response']['body']['status']
        ];
    }

    /**
     * 基本プランを登録
     * 
     * @param array $condition $condition リクエストパラメーターの配列
     * @throws \Exception
     */
    public function storeBasePlan($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku'],
            'planSku' => $condition['planSku']
        ];

        $body = [
            'priceAmountMicros' => $condition['price'] * 1000000,
            'duration' => $condition['duration'],
            'status' => $condition['status']
        ];

        $result = $this->subscriptionItemApiService->storeBasePlan($path, $body);

        if (!$result['resultStatus']) {
            throw new \Exception($result['resultMessage']);
        }
    }

    /**
     * 基本プランを更新
     * 
     * @param array $condition $condition リクエストパラメーターの配列
     * @throws \Exception
     */
    public function updateBasePlan($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku'],
            'planSku' => $condition['planSku']
        ];

        $body = [
            'duration' => $condition['duration'],
            'priceAmountMicros' => $condition['price'] * 1000000,
            'status' => $condition['status']
        ];

        $result = $this->subscriptionItemApiService->updateBasePlan($path, $body);

        if (!$result['resultStatus']) {
            throw new \Exception($result['resultMessage']);
        }
    }

    /**
     * 基本プランの一覧を取得する
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array            基本プランIDの配列
     * @throws \Exception
     */
    public function getBasePlanList($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku']
        ];

        $response = $this->subscriptionItemApiService->getSubscription($path);
        if (!isset($response['response']['body'])) {
            throw new \Exception($response['resultMessage']);
        }

        $result = [];
        foreach ($response['response']['body']['basePlans'] as $basePlan) {
            $result[] = $basePlan['sku'];
        }

        return array_merge([0 => 'プラン名'] + $result);
    }

    /**
     * 特典を登録
     * 
     * @param array $condition $condition リクエストパラメーターの配列
     * @throws \Exception
     */
    public function storeOffer($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku'],
            'planSku' => $condition['planSku'],
            'offerSku' => $condition['offerSku']
        ];
        // APIのボディパラメーターに使用する値をまとめる
        $body = [
            'status' => $condition['status'],
            'targeting' => config('forms.Subscription.targeting'),
            'acquisitionTargetingRule' => [
                'scope' => config('forms.Subscription.scope')
            ],
            'phases' => [
                [
                    'phaseType' => $condition['phaseType'],
                    'priceType' => config('forms.Subscription.priceType'),
                    'priceAmountMicros' => $condition['phaseType'] === 'singlePayment' ? $condition['price'] * 1000000 : 0,
                    'duration' => $condition['duration']
                ]
            ]
        ];

        $result = $this->subscriptionItemApiService->storeOffer($path, $body);

        if (!$result['resultStatus']) {
            throw new \Exception($result['resultMessage']);
        }
    }

    /**
     * 特典を取得
     * 
     * @param  array $condition リクエストパラメーターの配列
     * @return array 特典
     * @throws \Exception
     */
    public function getOffer($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku'],
            'planSku' => $condition['planSku'],
            'offerSku' => isset($condition['prevOfferSku']) ? $condition['prevOfferSku'] : $condition['offerSku']
        ];

        $response = $this->subscriptionItemApiService->getOffer($path);
        if (!isset($response['response']['body'])) {
            throw new \Exception($response['resultMessage']);
        }

        return [
            'duration' => $response['response']['body']['phases'][0]['duration'],
            'offerSku' => $response['response']['body']['sku'],
            'phaseType' => $response['response']['body']['phases'][0]['phaseType'],
            'price' => $response['response']['body']['phases'][0]['priceAmountMicros'] * 0.000001,
            'status' => $response['response']['body']['status']
        ];
    }

    /**
     * 特典を更新
     * 
     * @param array $condition $condition リクエストパラメーターの配列
     * @throws \Exception
     */
    public function updateOffer($condition = [])
    {
        // APIのパスパラメーターに使用する値をまとめる
        $path = [
            'appId' => $condition['app_id'],
            'kind' => config('subscription-item-api.api_kind'),
            'device' => $condition['device'],
            'subsSku' => $condition['subsSku'],
            'planSku' => $condition['planSku'],
            'offerSku' => $condition['offerSku']
        ];
        // APIのボディパラメーターに使用する値をまとめる
        $body = [
            'status' => $condition['status'],
            'targeting' => config('forms.Subscription.targeting'),
            'acquisitionTargetingRule' => [
                'scope' => config('forms.Subscription.scope')
            ],
            'phases' => [
                [
                    'phaseType' => $condition['phaseType'],
                    'priceType' => config('forms.Subscription.priceType'),
                    'priceAmountMicros' => $condition['phaseType'] === 'singlePayment' ? $condition['price'] * 1000000 : 0,
                    'duration' => $condition['duration'],
                ]
            ]
        ];

        $result = $this->subscriptionItemApiService->updateOffer($path, $body);

        if (!$result['resultStatus']) {
            throw new \Exception($result['resultMessage']);
        }
    }

    /**
     * アプリのタイトルを取得する
     * 
     * @param  int    $appId アプリID
     * @return string タイトル
     */
    public function getApplicationTitle($appId)
    {
        $appData = $this->application->getOne($appId);
        return $appData['title'];
    }

    /**
     * 定期購入詳細画面用パラメーターの構造を生成する
     * 
     * @param  array $body   APIレスポンスのボディ
     * @return array $result 表示用パラメーター配列
     */
    private function formatDetail($body)
    {
        // プラン情報を生成
        $displayStrings = config('forms.Subscription.status.list');
        foreach ($body['basePlans'] as $planKey => $basePlan) {
            $basePlanInfo = [
                'sku' => $basePlan['sku'],
                'planPriceAmount' => $basePlan['priceAmountMicros'] / 1000000,
                'planUpdateAt'    => Carbon::createFromTimestamp($basePlan['updatedAt'])->format('Y/m/d H:i:s'),
                'planDuration'    => config('forms.Subscription.duration.' . $basePlan['duration']),
                'planStatus'      => $displayStrings[$basePlan['status']],
                'offerPhaseType'  => '-',    
            ];
            if (!empty($body['basePlans'][$planKey]['offers'])) {
                // 基本プランに特典が設定されている場合は、特典情報を生成
                foreach ($body['basePlans'][$planKey]['offers'] as $offerKey => $offer) {
                    $offerUpdateAt = Carbon::createFromTimestamp($offer['updatedAt'])->format('Y/m/d H:i:s');
                    foreach ($offer['phases'] as $phaseKey => $phase) {
                        // 特典として表示する階層の情報を生成
                        $phaseInfo = [
                            'planSku'          => $basePlan['sku'],
                            'planPriceAmount'  => $basePlanInfo['planPriceAmount'],
                            'planStatus'       => $basePlanInfo['planStatus'],
                            'planDuration'     => $basePlanInfo['planDuration'],
                            'planUpdateAt'     => $basePlanInfo['planUpdateAt'],
                            'offerDuration'    => config('forms.Subscription.duration.' . $phase['duration']),
                            'offerPhaseType'   => config('forms.Subscription.phaseType.' . $phase['phaseType']),
                            'offerSku'         => $offer['sku'],
                            'offerPriceAmount' => $phase['priceAmountMicros'] / 1000000,
                            'offerStatus'      => $displayStrings[$offer['status']],
                            'offerUpdateAt'    => $offerUpdateAt,
                            'phaseUpdateAt'    => Carbon::createFromTimestamp($phase['updatedAt'])->format('Y/m/d H:i:s'),
                        ];
                        $basePlanInfo['offers'][$offerKey]['phases'][$phaseKey] = $phaseInfo;
                    }
                    $basePlanInfo['offerDuration'] = $phaseInfo['offerDuration'];
                    $basePlanInfo['offerPhaseType'] = $phaseInfo['offerPhaseType'];
                }
            } else {
                $basePlanInfo['offers'] = [];
            }
            $body['basePlans'][$planKey] = $basePlanInfo;
        }
        $result = [
            'basePlans' => $body['basePlans'],
        ];

        // 定期購入情報を生成
        $result['subscriptionInfo'] = [
            'skuTitle'    => $body['title'],
            'sku'         => $body['sku'],
            'description' => $body['description'],
        ];

        return $result;
    }
}
