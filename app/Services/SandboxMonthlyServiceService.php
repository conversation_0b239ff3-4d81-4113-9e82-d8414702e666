<?php
namespace App\Services;

use App\Models\FreegameDeveloper\DeveloperParentage;
use App\Models\FreegameSandbox\Application;
use App\Models\FreegameSandbox\MonthlyService;

class SandboxMonthlyServiceService extends CustomService
{
    protected $developerParentage;
    protected $application;
    protected $monthlyService;

    public function __construct(
        DeveloperParentage $developerParentage,
        Application $application,
        MonthlyService $monthlyService
    ) {
        $this->developerParentage = $developerParentage;
        $this->application = $application;
        $this->monthlyService = $monthlyService;
    }

    /**
     * Get values for form select box,radio box
     *
     * @return array
     */
    public function getFormData()
    {
        return config('forms.SandboxMonthlyService');
    }

    /**
     * 月額サービスのリスト取得
     * @param array $params
     * @return object
     */
    public function getSbxMonthlyServiceList($params)
    {
        // 表示件数の取得
        $offset = config('forms.SandboxMonthlyService.offset');
        if (! empty($params['perPage'])) {
            $offset = $params['perPage'];
        }

        $page = 1;
        if (! empty($params['page'])) {
            $page = $params['page'];
        }

        $appIdList = [];
        if (!auth_is_user_admin()) {
            // デベロッパidの配列取得
            $developerIdList[] = auth_user_id();
            // sapの場合は子idも取得
            if (auth_is_sap()) {
                $devIdList = $this->developerParentage->getDeveloperIdList(auth_user_id());
                foreach ($devIdList as $developer_id) {
                    $developerIdList[] = $developer_id;
                }
            }

            // app_idの配列取得
            $appList = $this->application->getListByDeveloperId($developerIdList);
            if ($appList->isEmpty()) {
                return collect();
            }
            foreach ($appList as $app) {
                if (! in_array($app->id, $appIdList)) {
                    $appIdList[] = $app->id;
                }
            }
        }

        $contditions['appId'] = $appIdList;
        $contditions['offset'] = $offset;

        // 月額サービスid配列の取得
        $monthlyServiceList = $this->monthlyService->getListByAppId($contditions);
        $monthlyServiceList->appends([
            'perPage' => $monthlyServiceList->perPage(),
            'page' => $page
        ]);
        return $monthlyServiceList;
    }

    /**
     * セレクトボックスに設定する為のアプリケーション取得
     *
     * @return array
     */
    public function getAppTitleOfSelectBox()
    {
        // sapの場合は子idも取得
        $developerIdList[] = auth_user_id();
        if (auth_is_sap()) {
            $devIdList = $this->developerParentage->getDeveloperIdList(auth_user_id());
            foreach ($devIdList as $developer) {
                $developerIdList[] = $developer;
            }
        }

        // セレクトボックスに設定するappidとタイトルの配列作成
        $appList = $this->application->getListByDeveloperId($developerIdList);
        $appTitleList = [];
        foreach ($appList as $app) {
            $appTitleList[$app['id']] = $app['title'];
        }
        return $appTitleList;
    }

    /**
     * developer_idの妥当性を確認後、applicationテーブルのデータを戻り値として返す
     * @param int app_id フォームのセレクトボックスで選択したアプリid
     * @return object $application application
     */
    public function getApplication($app_id)
    {
        // applicationに設定されたdeveloper_idであるかを確認
        $application = $this->application->getOne($app_id);
        if (empty($application)) {
            return ;
        }

        if (! empty($application) && $application->developer_id == auth_user_id()) {
            return $application;
        }

        // applicationに設定されていないdeveloper_idだった場合、子のdeveloper_idであるかを確認
        $isParentAndChild = $this->developerParentage->isParentAndChild(auth_user_id(), $application->developer_id);
        if ($isParentAndChild) {
            return $application;
        }
    }

    /**
     * monthly_serviceの登録
     * @param object $request
     * @return boolean
     */
        public function storeMonthlyService($request)
        {
            // アプリに権限があるか確認＆site設定
            $application = $this->getApplication($request->get('app_id'));
            if (empty($application)) {
                return false;
            }
            if ($application->general == 1) {
                $site = 'general';
            } else {
                $site = 'adult';
            }

            // monthly_serviceの登録
            $params['service_name'] = $request->get('service_name');
            $params['item_id'] = $request->get('item_id');
            $params['app_id'] = $request->get('app_id');
            $params['callback_url'] = $request->get('callback_url');
            $params['callback_url_withdrawal'] = $request->get('callback_url_withdrawal');
            $params['price'] = $request->get('price');
            $params['amount'] = $request->get('amount');
            $params['free_flg'] = $request->get('free_flg');
            $params['term'] = $request->get('term');
            $params['sell_begin_datetime'] = $this->convertDatetime($request->get('sell_begin_datetime'));
            $params['sell_end_datetime'] = $this->convertDatetime($request->get('sell_end_datetime'));
            $params['description'] = $request->get('description');
            $params['developer_id'] = auth_user_id();
            $params['site'] = $site;

            return $this->monthlyService->add($params);
        }

    /**
     * 月額サービスidからmonthly_sericeの対象レコードを取得<br>
     * 同時にアプリケーションに権限があるか確認
     * @param int $id 月額サービスid
     * @return object 月額サービス
     */
    public function getMonthlyService($id)
    {
        $monthlyService = $this->monthlyService->getOne($id);
        return $monthlyService;
    }

    /**
     * monthly_serviceの編集
     * @param object $request
     * @return boolean
     */
    public function updateMonthlyService($request)
    {
        // monthly_serviceの編集
        $params['service_name'] = $request->get('service_name');
        $params['callback_url'] = $request->get('callback_url');
        $params['callback_url_withdrawal'] = $request->get('callback_url_withdrawal');
        $params['term'] = $request->get('term');
        $params['sell_begin_datetime'] = $this->convertDatetime($request->get('sell_begin_datetime'));
        $params['sell_end_datetime'] = $this->convertDatetime($request->get('sell_end_datetime'));
        $params['description'] = $request->get('description');

        $this->monthlyService->edit($params, $request->get('id'));

        return true;
    }

    /**
     * monthly_serviceの削除
     * @param object $request
     * @return boolean
     */
    public function deleteMonthlyService($request)
    {
        // 指定したidでmonthly_serviceテーブルを確認
        $monthlyService = $this->getMonthlyService($request->get('id'));
        if (empty($monthlyService)) {
            return false;
        }

        // アプリに権限があるか確認
        $application = $this->getApplication($monthlyService->app_id);
        if (empty($application)) {
            return false;
        }

        // monthly_serviceの削除
        return $this->monthlyService->destroy($request->get('id'));
    }

    /**
     * 日時をDBに登録できる状態に変更する
     * @param string $datetime
     * @return string $datetime Y-m-d H:i:sの書式の日時(値なしは空文字)
     */
    public function convertDatetime($datetime)
    {
        // 日時の編集
        if (! empty($datetime)) {
            $datetime = date('Y-m-d H:i:s', strtotime($datetime . ':00'));
        } else {
            $datetime = '';
        }
        return $datetime;
    }

    /**
     * 検索条件セッション保持
     * @param array $search
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('SandboxMonthlyService.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'perPage',
            'page'
        ]);
        request()->session()->set('SandboxMonthlyService.search', $search);
        return $search;
    }
}
