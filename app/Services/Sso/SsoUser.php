<?php

namespace App\Services\Sso;

use Carbon\Carbon;
use App\Models\FreegameDeveloper\Developer;

/**
 * SSOログインユーザー向けのDeveloperクラスのラッパー
 */
class SsoUser
{
    const LOCK_INACTIVE_USER_MONTH_LIMIT = 3;

    /**
     * @var Developer
     */
    private $developer;

    /**
     * @var string
     */
    private $appEnv;

    /**
     * @param Developer $developer
     * @param $appEnv string
     */
    public function __construct(Developer $developer, $appEnv = null)
    {
        $this->developer = $developer;
        $this->appEnv = !empty($appEnv) ? $appEnv : env('APP_ENV');
    }

    /**
     * @return Developer
     */
    public function getDeveloper()
    {
        return $this->developer;
    }

    /**
     * @return string
     */
    public function getAppEnv()
    {
        return $this->appEnv;
    }

    /**
     * @return SsoUser
     */
    public static function createEmpty()
    {
        return new self(new Developer());
    }

    /**
     * @return bool
     */
    public function isEmpty()
    {
        return empty($this->developer->exists);
    }

    /**
     * @return bool
     */
    public function isLocked()
    {
        return (int)$this->developer->locked === 1;
    }

    /**
     * @return bool
     */
    public function isAdminUser()
    {
        return $this->developer->type === 'admin';
    }

    /**
     * @return bool
     */
    public function appEnvIsNotProduction()
    {
        return $this->getAppEnv() !== 'production';
    }

    /**
     * 長期間ログインしていないユーザーか確認する
     *
     * @return bool
     */
    public function isInactivateLimitOver()
    {
        if($this->appEnvIsNotProduction() || $this->isAdminUser()) {
            return false;
        }

        return $this->developer->last_login_date < Carbon::now()->subMonths(self::LOCK_INACTIVE_USER_MONTH_LIMIT);
    }

    /**
     * ログインの可否を判定
     *
     * @return bool
     */
    public function isUnableToLogin()
    {
        return $this->isLocked() || $this->isInactivateLimitOver();
    }

    /**
     * @param $developerModel Developer
     * @return bool
     */
    public function updateToLocked($developerModel)
    {
        if($this->isLocked()) {
            return true;
        }

        return $developerModel->edit(['locked' => true], $this->developer->id);
    }

    /**
     * @param $developerModel Developer
     * @return bool
     */
    public function updateLoginDate($developerModel)
    {
        return $developerModel->edit(['last_login_date' => Carbon::now()], $this->developer->id);
    }
}