<?php

namespace App\Services\Sso;

use Exception;
use Illuminate\Support\Facades\Auth;
use App\Libs\Sso\KeycloakClient;
use App\Models\FreegameDeveloper\Developer;
use Log;

class SsoService
{
    /**
     * @var KeycloakClient
     */
    private $keycloakClient;

    /**
     * @var SsoUserService
     */
    private $ssoUserService;

    /**
     * @var Developer
     */
    private $developer;

    /**
     * @param KeycloakClient $keycloakClient
     * @param SsoUserService $ssoUserService
     * @param Developer $developer
     */
    public function __construct(KeycloakClient $keycloakClient, SsoUserService $ssoUserService, Developer $developer)
    {
        $this->keycloakClient = $keycloakClient;
        $this->ssoUserService = $ssoUserService;
        $this->developer = $developer;
    }

    /**
     * Keycloakへの認証リクエストの実行
     *
     * @return SsoServiceResult|void
     */
    public function authRequest()
    {
        $requestResult = $this->keycloakClient->authRequest();
        if (!$requestResult) {
            Log::error(self::class . " Failed to request sso authentication.");
            return SsoServiceResult::createLocalLoginFailedForAuthServerError();
        }
        // 成功の場合リダイレクトされてるのでreturn無し
    }

    /**
     * devサイトのログインを実行する
     *
     * @return SsoServiceResult
     */
    public function localLogin()
    {
        // 認証サーバからのリダイレクトの検証、各種トークンの取得
        $authResult = $this->keycloakClient->authRedirect();
        if (!$authResult) {
            Log::error(self::class . ' localLogin Failed. Invalid redirect.');
            return SsoServiceResult::createLocalLoginFailed();
        }
        try {
            // devサイト上のユーザーを取得
            $userId = $this->keycloakClient->getUserId();
            $ssoUser = $this->ssoUserService->getUser($userId);
            if ($ssoUser->isEmpty()) {
                Log::error(self::class . ' localLogin Failed. Unable to get user.');
                return SsoServiceResult::createLocalLoginFailedForAuthServerError();
            }
            // ユーザーのロック状態の確認
            if ($ssoUser->isUnableToLogin()) {
                Log::error(self::class . ' localLogin Failed. user is unable to login. userId: ' . $userId);
                $ssoUser->updateToLocked($this->developer);
                return SsoServiceResult::createLocalLoginFailedForUserLocked();
            }

            $ssoUser->updateLoginDate($this->developer);
            $this->keycloakClient->putTokens();
            Auth::login($ssoUser->getDeveloper());
        } catch(Exception $e) {
            Log::error(self::class . ' localLogin Failed. message: ' . $e->getMessage());
            return SsoServiceResult::createLocalLoginFailedForAuthServerError();
        }

        return SsoServiceResult::createLocalLoginSuccess();
    }
}