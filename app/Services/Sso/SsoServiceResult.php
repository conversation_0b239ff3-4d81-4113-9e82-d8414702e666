<?php

namespace App\Services\Sso;

class SsoServiceResult
{
    /**
     * @var string
     */
    private $result;

    /**
     * @var string
     */
    private $message;

    const SUCCESS = 'SUCCESS';
    const FAILED = 'FAILED';

    /**
     * @param $result string
     * @param $message string
     */
    public function __construct($result, $message)
    {
        $this->result = $result;
        $this->message = $message;
    }

    /**
     * @return SsoServiceResult
     */
    public static function createAuthRequestSuccess()
    {
        return new self(
            self::SUCCESS,
            ''
        );
    }

    /**
     * @return SsoServiceResult
     */
    public static function createAuthRequestFailed()
    {
        return new self(
            self::FAILED,
            trans('validationmessage.MSG162')
        );
    }

    /**
     * @return SsoServiceResult
     */
    public static function createLocalLoginSuccess()
    {
        return new self(
            self::SUCCESS,
            ''
        );
    }

    /**
     * @return SsoServiceResult
     */
    public static function createLocalLoginFailed()
    {
        return new self(
            self::FAILED,
            trans('validationmessage.MSG162')
        );
    }

    /**
     * @return SsoServiceResult
     */
    public static function createLocalLoginFailedForAuthServerError()
    {
        return new self(
            self::FAILED,
            trans('validationmessage.MSG328')
        );
    }

    /**
     * @return SsoServiceResult
     */
    public static function createLocalLoginFailedForUserLocked()
    {
        return new self(
            self::FAILED,
            trans('validationmessage.MSG319')
        );
    }

    /**
     * @return string
     */
    public function getResult()
    {
        return $this->result;
    }

    /**
     * @return string
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * @return bool
     */
    public function isSuccess()
    {
        return $this->result === self::SUCCESS;
    }

    /**
     * @return bool
     */
    public function isFailed()
    {
        return $this->result === self::FAILED;
    }
}