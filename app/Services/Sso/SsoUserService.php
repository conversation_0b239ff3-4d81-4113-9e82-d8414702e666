<?php

namespace App\Services\Sso;

use Exception;
use App\Libs\Sso\KeycloakAdminClient;
use App\Models\FreegameDeveloper\Developer;
use App\Models\FreegameDeveloper\DeveloperAuthRef;
use Log;

class SsoUserService
{
    /**
     * @var KeycloakAdminClient
     */
    private $keycloakAdminClient;

    /**
     * @var Developer
     */
    private $developer;

    /**
     * @var DeveloperAuthRef
     */
    private $developerAuthRef;

    /**
     * @param KeycloakAdminClient $keycloakAdminClient
     * @param Developer $developer
     * @param DeveloperAuthRef $developerAuthRef
     */
    public function __construct(KeycloakAdminClient $keycloakAdminClient, Developer $developer, DeveloperAuthRef $developerAuthRef)
    {
        $this->keycloakAdminClient = $keycloakAdminClient;
        $this->developer = $developer;
        $this->developerAuthRef = $developerAuthRef;
    }

    /**
     * Keycloakのユーザーとdevサイトのユーザーを紐づけて、ログインするユーザーを取得する
     *
     * @param $userId string Keycloakのユーザーid
     * @return SsoUser
     */
    public function getUser($userId)
    {
        try {
            $loginId = $this->keycloakAdminClient->getDeveloperSiteLoginId($userId);

            /** @var  $user Developer */
            $user = $this->developer->getOneByLoginId($loginId);
            if(empty($user)) {
                Log::error(self::class . ' developer not exist. Keycloak userId: ' . $userId);
                return SsoUser::createEmpty();
            }

            $role = $this->developerAuthRef->getListForAuth(['developer_id' => $user->id]);
            if (empty($role)) {
                Log::error(self::class . ' developer role not exist. Keycloak userId: ' . $userId);
                return SsoUser::createEmpty();
            }
            $user->setAuthRole($role->toArray());

            return new SsoUser($user);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return SsoUser::createEmpty();
        }
    }
}