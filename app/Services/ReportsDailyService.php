<?php
namespace App\Services;

use App\Models\FreegameDeveloper\ReportUtilization;
use App\Models\FreegameDeveloper\ReportDmmUtilization;
use App\Models\FreegameDeveloper\GuestReportUtilization;
use App\Models\FreegameDeveloper\GuestReportDmmUtilization;
use App\Models\FreegameDeveloper\ReportGender;
use App\Models\FreegameDeveloper\ReportDmmGender;
use App\Models\FreegameDeveloper\ReportDuration;
use App\Models\FreegameReport\ReportMovieAuthAccessLogDaily;
use App\Models\Freegame\Application;
use App\Models\Freegame\ApplicationDevice;
use App\Models\Freegame\ApplicationInstallReport;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\MonthlyService;
use App\Models\FreegameDeveloper\MonthlyServiceReportDmmUtilization;
use App\Models\FreegameDeveloper\MonthlyServiceReportUtilization;
use App\Models\FreegameDeveloper\ApplicationDivision;
use App\Services\SubscriptionUtilizationReportDailyService;
use App\Services\ChargeCenterService;

/**
 * レポート：日別レポート
 */
class ReportsDailyService extends CustomService
{
    protected $reportUtilization;

    protected $reportDmmUtilization;

    protected $guestReportUtilization;

    protected $guestReportDmmUtilization;

    protected $reportGender;

    protected $reportDmmGender;

    protected $reportDuration;

    protected $reportMovieAuthAccessLogDaily;

    protected $application;

    protected $applicationDevice;

    protected $applicationInstallReport;

    protected $developerApplication;

    protected $appTitleType;

    protected $monthlyService;

    protected $monthlyServiceReportDmmUtilization;

    protected $monthlyServiceReportUtilization;

    protected $applicationDivision;

    protected $gameIdList;

    protected $subscriptionUtilizationReportDailyService;

    protected $chargeCenterService;

    public function __construct(
        ReportUtilization $reportUtilization,
        ReportDmmUtilization $reportDmmUtilization,
        GuestReportUtilization $guestReportUtilization,
        GuestReportDmmUtilization $guestReportDmmUtilization,
        ReportGender $reportGender,
        ReportDmmGender $reportDmmGender,
        ReportDuration $reportDuration,
        ReportMovieAuthAccessLogDaily $reportMovieAuthAccessLogDaily,
        Application $application,
        ApplicationDevice $applicationDevice,
        ApplicationInstallReport $applicationInstallReport,
        DeveloperApplication $developerApplication,
        MonthlyService $monthlyService,
        MonthlyServiceReportDmmUtilization $monthlyServiceReportDmmUtilization,
        MonthlyServiceReportUtilization $monthlyServiceReportUtilization,
        ApplicationDivision $applicationDivision,
        SubscriptionUtilizationReportDailyService $subscriptionUtilizationReportDailyService,
        ChargeCenterService $chargeCenterService
    ) {
        $this->reportUtilization = $reportUtilization;
        $this->reportDmmUtilization = $reportDmmUtilization;
        $this->guestReportUtilization = $guestReportUtilization;
        $this->guestReportDmmUtilization = $guestReportDmmUtilization;
        $this->reportGender = $reportGender;
        $this->reportDmmGender = $reportDmmGender;
        $this->reportDuration = $reportDuration;
        $this->reportMovieAuthAccessLogDaily = $reportMovieAuthAccessLogDaily;
        $this->application = $application;
        $this->applicationDevice = $applicationDevice;
        $this->applicationInstallReport = $applicationInstallReport;
        $this->developerApplication = $developerApplication;
        $this->monthlyService = $monthlyService;
        $this->monthlyServiceReportDmmUtilization = $monthlyServiceReportDmmUtilization;
        $this->monthlyServiceReportUtilization = $monthlyServiceReportUtilization;
        $this->applicationDivision = $applicationDivision;
        $this->subscriptionUtilizationReportDailyService = $subscriptionUtilizationReportDailyService;
        $this->chargeCenterService = $chargeCenterService;
    }

    /**
     * 経理用レポート権限判定
     * @return array boolean
     */
    protected function getIsAccountingAuth() {
        if (! auth_is_route('ReportsDaily.csvaccountingdownload')) {
            return false;
        }
        return true;
    }

    /**
     * 経理用レポートを指定しているか判定
     * @param  string $report
     * @return boolean
     */
    protected function getIsAccountingReport($report) {
        if (preg_match('/_Accounting/', $report)) {
            return true;
        }
        return false;
    }

    /**
     * フォームに設定するデータを取得
     * @return array
     */
    public function getFormData()
    {
        return [
            'menuName' => config('forms.ReportsDaily.menuName'),
            'screenName' => config('forms.ReportsDaily.screenName'),
            'periodType' => config('forms.ReportsDaily.periodType'),
            'deviceType' => config('forms.ReportsDaily.deviceType'),
            'reportType' => $this->getReportType(),
            'appTitleType' => $this->getAppTitleType()
        ];
    }

    /**
     * フォームの形式プルダウンのデータを取得
     * @return array $reportType
     */
    public function getReportType()
    {
        $reportType = [];
        if ($this->getIsAccountingAuth()) {
            // 経理用レポートの場合は形式に全て接尾語を追加
            foreach (config('forms.ReportsDaily.reportType') as $typeKey => $typeList) {
                foreach ($typeList as $reportKey => $report) {
                    $reportType[$typeKey][$reportKey] = $report;
                    $reportType[$typeKey][$reportKey . '_Accounting'] = $report . '【経理用】';
                }
            }
            foreach (config('forms.ReportsDaily.accountsOnlyReportType') as $typeKey => $typeList) {
                foreach ($typeList as $reportKey => $report) {
                    $reportType[$typeKey][$reportKey . '_Accounting'] = $report . '【経理用】';
                }
            }
        } else {
            // 経理用レポートではない場合
            $reportType = config('forms.ReportsDaily.reportType');
        }
        // デベロッパー用レポート
        foreach (config('forms.ReportsDaily.developerOnlyReportType') as $typeKey => $typeList) {
            foreach ($typeList as $reportKey => $report) {
                $reportType[$typeKey][$reportKey] = $report;
            }
        }
        return $reportType;
    }

    /**
     * 集計対象のアプリのapp_id、タイトルを取得
     * @return array $opts
     */
    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        if (auth_is_pf()) {
            $opts = config('forms.ReportsDaily.reportDmmType');
        }
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    /**
     * フォームから送信された検索条件を整理
     * @param array $condition
     * @return array $search
     */
    public function formatSearchQuery($search = [])
    {
        if (empty($search['begin'])) {
            $search['begin'] = '';
        } else {
            $search['begin'] = date('Y-m-d', strtotime($search['begin']));
        }
        if (empty($search['end'])) {
            $search['end'] = '';
        } else {
            $search['end'] = date('Y-m-d', strtotime($search['end']));
        }
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            if (auth_is_sap()) {
                $search['app_id'] = array_keys($appTitleType);
                if (empty($search['app_id'])) {
                    $search['app_id'] = '-1';
                }
            } else {
                $search['app_id'] = '';
            }
        }
        if (empty($search['device'])) {
            $search['device'] = '';
        }
        $search = array_only($search, [
            'begin',
            'end',
            'app_id',
            'attr',
            'device',
            'type',
            'select',
            'monthly_service_id',
            'kind'
        ]);
        return $search;
    }

    /**
     * ファイル名を生成
     * @param array $condition
     * @return string
     */
    public function getCsvFileName($condition = [])
    {
        if (empty($condition['begin'])) {
            $condition['begin'] = '1970/01/01';
        }
        if (empty($condition['end'])) {
            $condition['end'] = date('Y/m/d');
        }
        $report = preg_replace('/_Accounting/', '', $condition['report']);
        return sprintf(
            config('forms.ReportsDaily.CsvFileName'),
            $report,
            $condition['app_id'],
            date('Y-m-d', strtotime($condition['begin'])),
            date('Y-m-d', strtotime($condition['end']))
        );
    }

    /**
     * フォームから送信されたreportでヘッダーを選定
     * @param array $condition
     * @return array $header
     */
    public function getCsvHeader($condition = [])
    {
        if (empty($condition['report'])) {
            return [];
        }

        // 経理用レポートの接尾語削除
        $report = preg_replace('/_Accounting/', '', $condition['report']);

        $method = camel_case('get_' . $report . '_header');
        if (method_exists($this, $method)) {
            return $this->{$method}($condition);
        }
        return [];
    }

    /**
     * ヘッダー：利用状況レポート
     * @param array $condition
     * @return array $header
     */
    public function getReportHeader($condition = [])
    {
        $header['format_date'] = '年月日';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
            $header['app_id'] = 'アプリID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'active_user' => '登録者数',
            'download_user' => 'ダウンロード数',
            'regist_user' => '新規登録者数',
            'upgrade_user' => 'アップグレード数',
            'suspend_user' => '新規停止者数',
            'pv' => 'PV',
            'dau' => 'DAU',
            'average_pv' => '平均PV',
            'use_point_user' => '課金UU',
            'charging_rate' => '課金率',
            'use_point' => '消費ポイント',
            'use_point_pay' => '有料ポイント',
            'use_point_free' => '無料ポイント',
            'discount_price' => 'クーポン値引ポイント',
            'arpu' => 'ARPU',
            'arppu' => 'ARPPU',
            'arpdau' => 'ARPDAU'
        ];
        // 利用状況レポート以外の場合、クーポン値引ポイントをヘッダーから削除
        if(!in_array($condition['report'], ['Report', 'Report_Accounting'])){
            unset($header['discount_price']);
        }
        if (is_numeric($condition['app_id'])) {
            $header += [
                'use_point_staff' => '優待消費ポイント'
            ];
        } else {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：利用状況レポート（ゲスト）
     * @param array $condition
     * @return array $header
     */
    public function getGuestReportHeader($condition = [])
    {
        $header['format_date'] = '年月日';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'active_user' => 'ゲストプレイ人数',
            'regist_user' => '新規ゲストプレイ人数',
            'upgrade_user' => 'アップグレード数',
            'dau' => 'DAU',
            'pv' => 'PV',
            'average_pv' => '平均PV',
            'use_point_user' => '課金UU',
            'use_point_pay' => '消費ポイント',
            'arppu' => 'ARPPU'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：男女別　利用者レポート
     * @param array $condition
     * @return array $header
     */
    public function getUserReportHeader($condition = [])
    {
        $header['format_date'] = '年月日';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'male_18_19' => '男性（18～19）',
            'male_20_24' => '男性（20～24）',
            'male_25_29' => '男性（25～29）',
            'male_30_34' => '男性（30～34）',
            'male_35_39' => '男性（35～39）',
            'male_40_49' => '男性（40～49）',
            'male_50' => '男性（50～）',
            'female_18_19' => '女性（18～19）',
            'female_20_24' => '女性（20～24）',
            'female_25_29' => '女性（25～29）',
            'female_30_34' => '女性（30～34）',
            'female_35_39' => '女性（35～39）',
            'female_40_49' => '女性（40～49）',
            'female_50' => '女性（50～）'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：男女別　PVレポート
     * @param array $condition
     * @return array $header
     */
    public function getPVReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：男女別　DAUレポート
     * @param array $condition
     * @return array $header
     */
    public function getAUReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：男女別　課金UUレポート
     * @param array $condition
     * @return array $header
     */
    public function getPUReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：男女別　消費ポイントレポート
     * @param array $condition
     * @return array $header
     */
    public function getPointReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：経過日数別　利用率
     * @param array $condition
     * @return array $header
     */
    public function getProgressAUReportHeader($condition = [])
    {
        $header['format_date'] = '年月日';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'day_1st' => '1日目',
            'day_2nd' => '2日目',
            'day_3rd' => '3日目',
            'day_4th' => '4日目',
            'day_5th' => '5日目',
            'day_6th' => '6日目',
            'day_7th' => '7日目',
            'day_14th' => '14日目',
            'day_30th' => '30日目'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：経過日数別　消費ポイント
     * @param array $condition
     * @return array $header
     */
    public function getProgressPointReportHeader($condition = [])
    {
        return $this->getProgressAUReportHeader($condition);
    }

    /**
     * ヘッダー：経過日数別　消費率（課金率）
     * @param array $condition
     * @return array $header
     */
    public function getProgressPUPAUReportHeader($condition = [])
    {
        return $this->getProgressAUReportHeader($condition);
    }

    /**
     * ヘッダー：経過日数別　ARPU
     * @param array $condition
     * @return array $header
     */
    public function getProgressARPUReportHeader($condition = [])
    {
        return $this->getProgressAUReportHeader($condition);
    }

    /**
     * ヘッダー：経過日数別　ARPPU
     * @param array $condition
     * @return array $header
     */
    public function getProgressARPPUReportHeader($condition = [])
    {
        return $this->getProgressAUReportHeader($condition);
    }

    /**
     * ヘッダー：経過日数別　ARPDAU
     * @param array $condition
     * @return array $header
     */
    public function getProgressARPDAUReportHeader($condition = [])
    {
        return $this->getProgressAUReportHeader($condition);
    }

    /**
     * ヘッダー：経過日数別　アンインストール数
     * @param array $condition
     * @return array $header
     */
    public function getProgressUninstallReportHeader($condition = [])
    {
        $header['format_date'] = '年月日';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'day_1st' => '1日目',
            'day_2nd' => '2日目',
            'day_3rd' => '3日目',
            'day_4th' => '4日目',
            'day_5th' => '5日目',
            'day_6th' => '6日目',
            'day_14th' => '7～13日目',
            'day_30th' => '14～30日目'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：動画視聴状況レポート
     * @param array $condition
     * @return array $header
     */
    public function getMovieReportHeader($condition = [])
    {
        $header['format_date'] = '年月日';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'movie_id' => 'プロダクトID',
            'auth_count' => '視聴回数',
            'unique_user' => 'ユニーク視聴回数'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：月額サービス用
     * @param array $condition
     */
    public function getMonthlyServiceReportHeader($condition = [])
    {
        $header  = $this->getReportHeader($condition);
        $header += [
            'm_title'           => '月額サービス名',
            'm_price'           => '月額課金',
            'm_regist_user'     => '月額新規入会者数',
            'm_continue_user'   => '月額継続者数',
            'm_active_user'     => '月額利用者数',
            'm_withdrawal_user' => '月額解約者数',
            'm_eviction_user'   => '月額強制解約者数',
            'm_total_use'       => '合計課金額(消費ポイント・月額課金)',
            'm_use_unique_user' => '課金UU(月額課金者とポイント消費者)',
            'm_total_arpu'      => 'ARPU(消費ポイント＋月額課金)',
            'm_total_arppu'     => 'ARPPU(消費ポイント＋月額課金)',
            'm_credit_price'    => 'クレジット課金額',
            'm_point_price'     => 'ポイント消費'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['m_title'] = '月額出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：登録ユーザー用
     * @param array $condition
     */
    public function getRegisterReportHeader($condition = [])
    {
        $header = [
            'app_id' => 'アプリID',
            'title' => 'タイトル',
            'device' => 'デバイス',
            'begin_date' => '利用開始日時',
            'user_id' => 'GAMES ID',
            'nickname' => 'ニックネーム',
        ];

        return $header;
    }

    /**
     * フォームから送信されたreportでCSV用のリストを選定
     * @param array $condition
     * @param array 
     */
    public function getCsvList($condition = [])
    {
        if (empty($condition['report'])) {
            return [];
        }

        // 経理用レポートの接尾語削除
        $report = preg_replace('/_Accounting/', '', $condition['report']);

        $method = camel_case('get_' . $report);

        // app_id=>game_idの配列生成()
        $this->gameIdList = [];
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $this->gameIdList = $this->applicationDivision->getGameIdByAppIdList();
        }

        if (method_exists($this, $method)) {
            return $this->{$method}($condition);
        }
        return [];
    }

    /**
     * 利用状況レポート
     * @param array $condition
     * @param array $list
     */
    public function getReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmUtilization->getList($this->formatSearchQuery($condition));
        }
        $subList = $this->getGuestReport($condition);
        $addUpgradeUsers = [];
        foreach ($subList as $subData) {
            if (is_numeric($condition['app_id'])) {
                $addUpgradeUsers
                    [$subData->date->format('Ymd') . '_' . $subData->app_id . '_' . $subData->device] =
                        $subData->upgrade_user;
            } else {
                $addUpgradeUsers
                    [$subData->date->format('Ymd') . '_' . $subData->attr . '_' . $subData->device] =
                        $subData->upgrade_user;
            }
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
                $addKey = $data->date->format('Ymd') . '_' . $data->app_id . '_' . $data->device;
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
                $addKey = $data->date->format('Ymd') . '_' . $data->attr . '_' . $data->device;
            }
            $data->upgrade_user = array_get($addUpgradeUsers, $addKey, 0);
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
            if (empty($data->pv) || empty($data->dau)) {
                $data->average_pv = 0;
            } else {
                $data->average_pv = round($data->pv / $data->dau, 3);
            }
            if (empty($data->use_point_user) || empty($data->dau)) {
                $data->charging_rate = 0;
            } else {
                $data->charging_rate = round($data->use_point_user / $data->dau, 3);
            }
            if (empty($data->use_point) || empty($data->active_user)) {
                $data->arpu = 0;
            } else {
                $data->arpu = round($data->use_point / $data->active_user, 3);
            }
            if (empty($data->use_point) || empty($data->use_point_user)) {
                $data->arppu = 0;
            } else {
                $data->arppu = round($data->use_point / $data->use_point_user, 3);
            }
            if (empty($data->use_point) || empty($data->dau)) {
                $data->arpdau = 0;
            } else {
                $data->arpdau = round($data->use_point / $data->dau, 3);
            }
            $data->regist_user = $data->regist_user - $data->upgrade_user;
        }
        return $list;
    }

    /**
     * 利用状況レポート（ゲスト）
     * @param array $condition
     * @param array $list
     */
    public function getGuestReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->guestReportUtilization->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->guestReportDmmUtilization->getList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
            if (empty($data->pv) || empty($data->dau)) {
                $data->average_pv = 0;
            } else {
                $data->average_pv = round($data->pv / $data->dau, 3);
            }
            if (empty($data->use_point_pay) || empty($data->use_point_user)) {
                $data->arppu = 0;
            } else {
                $data->arppu = round($data->use_point_pay / $data->use_point_user, 3);
            }
        }
        return $list;
    }

    /**
     * 男女別　利用者レポート
     * @param array $condition
     * @param array $list
     */
    public function getUserReport($condition = [])
    {
        $condition['type'] = 'user';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmGender->getList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　PVレポート
     * @param array $condition
     * @param array $list
     */
    public function getPVReport($condition = [])
    {
        $condition['type'] = 'pv';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmGender->getList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　DAUレポート
     * @param array $condition
     * @param array $list
     */
    public function getAUReport($condition = [])
    {
        $condition['type'] = 'dau';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmGender->getList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　課金UUレポート
     * @param array $condition
     * @param array $list
     */
    public function getPUReport($condition = [])
    {
        $condition['type'] = 'use_point_user';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmGender->getList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　消費ポイントレポート
     * @param array $condition
     * @param array $list
     */
    public function getPointReport($condition = [])
    {
        $condition['type'] = 'use_point';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmGender->getList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 経過日数別　利用率
     * @param array $condition
     * @param array $list
     */
    public function getProgressAUReport($condition = [])
    {
        $condition['type'] = 'utilization';
        $appTitleType = $this->getAppTitleType();
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
            $day_1st = $data->day_1st;
            if ($day_1st != '0') {
                $data->day_1st = number_format($data->day_1st / $day_1st * 100, 3);
                $data->day_2nd = number_format($data->day_2nd / $day_1st * 100, 3);
                $data->day_3rd = number_format($data->day_3rd / $day_1st * 100, 3);
                $data->day_4th = number_format($data->day_4th / $day_1st * 100, 3);
                $data->day_5th = number_format($data->day_5th / $day_1st * 100, 3);
                $data->day_6th = number_format($data->day_6th / $day_1st * 100, 3);
                $data->day_7th = number_format($data->day_7th / $day_1st * 100, 3);
                $data->day_14th = number_format($data->day_14th / $day_1st * 100, 3);
                $data->day_30th = number_format($data->day_30th / $day_1st * 100, 3);
            }
        }
        return $list;
    }

    /**
     * 経過日数別　消費ポイント
     * @param array $condition
     * @param array $list
     */
    public function getProgressPointReport($condition = [])
    {
        $condition['type'] = 'use_point';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 経過日数別　消費率（課金率）
     * @param array $condition
     * @param array $list
     */
    public function getProgressPUPAUReport($condition = [])
    {
        $condition['type'] = 'use_percentage';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 経過日数別　ARPU
     * @param array $condition
     * @param array $list
     */
    public function getProgressARPUReport($condition = [])
    {
        $condition['type'] = 'arpu';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 経過日数別　ARPPU
     * @param array $condition
     * @param array $list
     */
    public function getProgressARPPUReport($condition = [])
    {
        $condition['type'] = 'arppu';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 経過日数別　アンインストール数
     * @param array $condition
     * @param array $list
     */
    public function getProgressUninstallReport($condition = [])
    {
        $condition['type'] = 'suspend';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 経過日数別　ARPDAU
     * @param array $condition
     * @return array $list
     */
    public function getProgressARPDAUReport($condition = [])
    {
        if (!is_numeric($condition['app_id'])) {
            return [];
        }

        $condition['type'] = 'arpdau';
        $appTitleType = $this->getAppTitleType();
        $list = $this->reportDuration->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsDaily.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 動画視聴状況レポート
     * @param array $condition
     * @param array $list
     */
    public function getMovieReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportMovieAuthAccessLogDaily->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_date->format('Y/m/d');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }
        }
        return $list;
    }

    /**
     * 月額サービス用
     * @param array $condition
     * @return unknown
     */
    public function getMonthlyServiceReport($condition = [])
    {
        $result            = [];
        $condition['kind'] = 'social';

        if (is_numeric($condition['app_id'])) {
            if (auth_is_sap() && $condition['app_id'] == '0') {
                // sapアカウントかつゲーム全体なら管理アプリのみ表示する
                $devAppList = $this->developerApplication->getApplicationAppIdList([
                    'developer_id' => auth_user_id()
                ]);

                $monthlyList = collect();
                $param['kind'] = 'social';
                foreach($devAppList as $devApp) {
                    $param['app_id'] = $devApp['app_id'];
                    $monthlyList = $monthlyList->merge($this->monthlyService->getList($param));
                }
            } else {
                // pfアカウントもしくはアプリ指定なら通常通り表示
                $monthlyList = $this->monthlyService->getList($condition);
            }
            foreach ($monthlyList as $monthly) {
                $condition['app_id']             = $monthly['app_id'];
                $condition['monthly_service_id'] = $monthly['id'];
                $addMonthlyService               = [];
                $list    = $this->getReport($condition);
                $subList = $this->monthlyServiceReportUtilization->getList($this->formatSearchQueryMonthly($condition));
                if ($this->hasApkCloud($monthly['app_id'])) {
                    $this->ajusMonthlyServiceDevice($subList);
                }

                foreach ($subList as $subData) {
                    $addKey =  "{$subData->date->format('Ymd')}"
                            . "_{$subData->app_id}"
                            . "_{$subData->device}"
                            . "_{$subData->monthly_service_id}";
                    $addMonthlyService[$addKey] = $subData;
                }

                foreach ($list as $data) {
                    $data->m_title = $monthly['service_name'];
                    $result[]      = $this->getMonthlyServiceReportCalc($data, $addMonthlyService, $monthly['id']);
                }
            }
        } else {
            $condition['attr'] = $condition['app_id'];
            $addMonthlyService = [];
            $list    = $this->getReport($condition);
            $subList = $this->monthlyServiceReportDmmUtilization->getList($this->formatSearchQuery($condition));

            foreach ($subList as $subData) {
                $addKey =  "{$subData->date->format('Ymd')}"
                        . "_{$subData->attr}"
                        . "_{$subData->device}";
                $addMonthlyService[$addKey] = $subData;
            }

            foreach ($list as $data) {
                $data->m_title = $data->title;
                $result[]      = $this->getMonthlyServiceReportCalc($data, $addMonthlyService);
            }
        }
        return $result;
    }

    /**
     * TODO : APKクラウドの月額サービスにて正しいデバイス値が保存されるようになった際、以下の関数を調整する。
     * ・formatSearchQueryMonthly() 削除し、呼び出しは formatSearchQuery() に戻す。
     * ・hasApkCloud() 削除。
     * ・ajusMonthlyServiceDevice() 削除。
     * また $this->applicationDevice も上記に伴って不要になるようであれば関連コードを除去する。
     */

    /**
     * フォームから送信された検索条件を整理
     * APKクラウドの月額サービス用に値を調整する
     * @param array $condition
     * @return array $search
     */
    private function formatSearchQueryMonthly($search = [])
    {
        $query = $this->formatSearchQuery($search);
    
        if (in_array('apk_cloud', $query['device']) && !in_array('sp', $query['device'])) {
            foreach ($query['device'] as $key => $device) {
                if ($device == 'apk_cloud') {
                    $query['device'][$key] = 'sp';
                }
            }    
        }

        return $query;
    }

    /**
     * 指定されたコンテンツにAPKクラウドのデバイスが存在するか確認する。
     * @param integer $appId
     * @return boolean
     */
    private function hasApkCloud($appId)
    {
        $device = $this->applicationDevice->getOne($appId, 'apk_cloud');

        return !empty($device);
    }

    /**
     * 月額サービスのレポートにて apk_cloud の場合 sp として保存されてしまうため、
     * 暫定対応としてこれを apk_cloud に置き換える。
     * @param array &$monthlyServiceReport
     */
    private function ajusMonthlyServiceDevice(&$monthlyServiceReport)
    {
        foreach ($monthlyServiceReport as $key => $data) {
            if ($data->device == 'sp') {
                $monthlyServiceReport[$key]->device = 'apk_cloud';
            }
        }
    }

    /**
     * 月額サービス計算用
     * @param object  $data
     * @param array   $addMonthlyService
     * @param integer $monthlyId
     * @return unknown
     */
    public function getMonthlyServiceReportCalc($data, $addMonthlyService, $monthlyId = null)
    {
        if ($monthlyId) {
            $addKey =  "{$data->date->format('Ymd')}"
                    . "_{$data->app_id}"
                    . "_{$data->device}"
                    . "_{$monthlyId}";
        } else {
            $addKey =  "{$data->date->format('Ymd')}"
                    . "_{$data->attr}"
                    . "_{$data->device}";
        }
        $monthlyService          = array_get($addMonthlyService, $addKey, 0);
        $usePoint                = $data->use_point      ?: 0;
        $usePointUser            = $data->use_point_user ?: 0;
        $useMonthlyServiceUser   = $monthlyService ? ($monthlyService->use_monthly_service_user ?: 0) : 0;
        $useDuplicateUser        = $monthlyService ? ($monthlyService->use_duplicate_user       ?: 0) : 0;
        $mPrice                  = $monthlyService ? ($monthlyService->price                    ?: 0) : 0;
        $mRegistUser             = $monthlyService ? ($monthlyService->regist_user              ?: 0) : 0;
        $mContinueUser           = $monthlyService ? ($monthlyService->continue_user            ?: 0) : 0;
        $mActiveUser             = $monthlyService ? ($monthlyService->active_user              ?: 0) : 0;
        $mWithdrawalUser         = $monthlyService ? ($monthlyService->withdrawal_user          ?: 0) : 0;
        $mEvictionUser           = $monthlyService ? ($monthlyService->eviction_user            ?: 0) : 0;
        $mCreditPrice            = $monthlyService ? ($monthlyService->credit_price             ?: 0) : 0;
        $mPointPrice             = $monthlyService ? ($monthlyService->point_price              ?: 0) : 0;

        $data->m_price           = $mPrice;
        $data->m_regist_user     = $mRegistUser;
        $data->m_continue_user   = $mContinueUser;
        $data->m_active_user     = $mActiveUser;
        $data->m_withdrawal_user = $mWithdrawalUser;
        $data->m_eviction_user   = $mEvictionUser;
        $data->m_total_use       = $usePoint     + $mPrice;
        $data->m_use_unique_user = $usePointUser + $useMonthlyServiceUser - $useDuplicateUser;
        $data->m_credit_price    = $mCreditPrice;
        $data->m_point_price     = $mPointPrice;

        if (empty($data->m_total_use) || empty($data->active_user)) {
            $data->m_total_arpu  = 0;
        } else {
            $data->m_total_arpu  = round($data->m_total_use / $data->active_user, 3);
        }
        if (empty($data->m_total_use) || empty($data->m_use_unique_user)) {
            $data->m_total_arppu = 0;
        } else {
            $data->m_total_arppu = round($data->m_total_use / $data->m_use_unique_user, 3);
        }
        return $data;
    }

    /**
     * 登録ユーザー用
     * @param array $condition
     * @return unknown
     */
    public function getRegisterReport($condition = [])
    {
        if (!is_numeric($condition['app_id'])) {
            return [];
        }

        $list = $this->applicationInstallReport->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->begin_date = str_replace('-', '/', $data->begin_date);
        }

        return $list;
    }

    /**
     * 利用状況レポート(定期購入)ヘッダー取得
     * 
     * @param array $condition CSV出力メソッド共通によって渡されるが、現在は条件によって制御はしていない
     */
    public function getSubscriptionUsageReportHeader($condition = [])
    {
        return $this->subscriptionUtilizationReportDailyService->getCsvHeader();
    }

    /**
     * 利用状況レポート(定期購入)取得
     * 
     * @param array $condition 検索条件
     */
    public function getSubscriptionUsageReport($condition = [])
    {
        return $this->subscriptionUtilizationReportDailyService->getCsvList($condition);
    }

    /**
     * 利用状況レポート(アプリ外課金)ヘッダー取得
     * 
     * @param array $condition 検索条件
     */
    public function getChargeCenterReportHeader($condition = [])
    {
        return $this->chargeCenterService->getReportsDailyHeader($condition);
    }

    /**
     * 利用状況レポート(アプリ外課金)取得
     * 
     * @param array $condition 検索条件
     */
    public function getChargeCenterReport($condition = [])
    {
        return $this->chargeCenterService->getReportsDaily(
            $condition,
            $this->gameIdList
        );
    }
}
