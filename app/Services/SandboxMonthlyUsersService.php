<?php

namespace App\Services;

use App\Models\FreegameSandbox\FreegameSandbox;
use App\Models\FreegameDeveloper\DeveloperParentage;
use App\Models\FreegameSandbox\Application;
use App\Models\FreegameSandbox\MonthlyService;
use App\Models\FreegameSandbox\MonthlyServiceUser;
use App\Models\FreegameSandbox\ApplicationInstall;
use App\Models\FreegameSandbox\User;
// OAuth
use OAuth\Common\Http\Uri\Uri;
use OAuth\Common\Consumer\Credentials;
use OAuth\OAuth1\Token\StdOAuth1Token;
use OAuth\OAuth1\Signature\Signature;
use OAuth\Common\Storage\Memory;
use App\Libs\OAuth\CurlClient;
use App\Libs\OAuth\MonthlyServiceOAuth;

use Log;

class SandboxMonthlyUsersService extends CustomService
{
    protected $developerParentage;
    protected $application;
    protected $monthlyService;
    protected $monthlyServiceUser;
    protected $applicationInstall;
    protected $user;

    public function __construct(
        DeveloperParentage $developerParentage,
        Application $application,
        MonthlyService $monthlyService,
        MonthlyServiceUser $monthlyServiceUser,
        ApplicationInstall $applicationInstall,
        User $user
    ) {
        $this->developerParentage = $developerParentage;
        $this->application = $application;
        $this->monthlyService = $monthlyService;
        $this->monthlyServiceUser = $monthlyServiceUser;
        $this->applicationInstall = $applicationInstall;
        $this->user = $user;
    }

    /**
     * Get values for form select box,radio box
     *
     * @return array
     */
    public function getFormData()
    {
        return config('forms.SandboxMonthlyUsers');
    }

    /**
     * 月額サービスのリスト取得
     * @param array $params
     * @return object
     */
    public function getSbxMonthlyUsersList($params)
    {
        // 表示件数の取得
        $offset = config('forms.SandboxMonthlyUsers.offset');
        if (! empty($params['perPage'])) {
            $offset = $params['perPage'];
        }

        $page = 1;
        if (! empty($params['page'])) {
            $page = $params['page'];
        }

        // デベロッパidの配列取得
        $developerIdList[] = auth_user_id();
        if (auth_is_sap()) {
            $devIdList = $this->developerParentage->getDeveloperIdList(auth_user_id());
            foreach ($devIdList as $developer_id) {
                $developerIdList[] = $developer_id;
            }
        }

        // app_idの配列取得
        $appList = $this->application->getListByDeveloperId($developerIdList);
        if ($appList->isEmpty()) {
            return collect();
        }
        $appIdList = [];
        foreach ($appList as $app) {
            if (! in_array($app->id, $appIdList)) {
                $appIdList[] = $app->id;
            }
        }

        $contditions['appId'] = $appIdList;
        $contditions['offset'] = $offset;

        // 月額サービスid配列の取得
        $monthlyServiceList = $this->monthlyService->getListByAppId($contditions);
        $monthlyServiceList->appends([
            'perPage' => $monthlyServiceList->perPage(),
            'page' => $page
        ]);
        return $monthlyServiceList;
    }

    /**
     * developer_idの妥当性を確認後、applicationテーブルのデータを戻り値として返す
     * @param int app_id アプリid
     * @return object $application application
     */
    public function getApplication($app_id)
    {
        // applicationに設定されたdeveloper_idであるかを確認
        $application = $this->application->getOne($app_id);
        if (empty($application)) {
            return ;
        }

        if (! empty($application) && $application->developer_id == auth_user_id()) {
            return $application;
        }

        // applicationに設定されていないdeveloper_idだった場合、子のdeveloper_idであるかを確認
        $isParentAndChild = $this->developerParentage->isParentAndChild(auth_user_id(), $application->developer_id);
        if ($isParentAndChild) {
            return $application;
        }
    }

    /**
     * 月額サービスidからmonthly_sericeの対象レコードを取得<br>
     * 同時にアプリケーションに権限があるか確認
     * @param int $id 月額サービスid
     * @return object 月額サービス
     */
    public function getMonthlyService($id)
    {
        $monthlyService = $this->monthlyService->getOne($id);
        return $monthlyService;
    }

    /**
     * 契約中ユーザを取得
     * @param int $monthlyServiceId 月額サービスid
     * @return object 契約中ユーザのリスト
     */
    public function getUserList($monthlyServiceId)
    {
        return $this->monthlyServiceUser->getListByMonthlyServiceId($monthlyServiceId);
    }

    /**
     * 成功通知
     * @param object $request request
     * @return 
     */
    public function entry($request)
    {
        // idから月額サービスを取得
        $monthlyService = $this->getMonthlyService($request->get('id'));
        if (empty($monthlyService)) {
            $result[] = preg_replace('/:attribute/', 'データ', trans('validationmessage.MSG011'));
            return $result;
        }

        // applicationのデータを取得。
        $application = $this->getApplication($monthlyService->app_id);
        if (empty($application)) {
            $result[] = preg_replace('/:attribute/', 'データ', trans('validationmessage.MSG011'));
            return $result;
        }

        // application_installのデータを取得。
        $conditions['user_id'] = $request->get('user_id');
        $conditions['app_id'] = $monthlyService->app_id;
        $conditions['status'] = 'active';

        $applicationInstall = $this->applicationInstall->getOneByConditions($conditions);
        if (empty($applicationInstall)) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        }

        // 契約有効期間内のデータ登録済みかを確認
        $conditions['monthly_service_id'] = $request->get('id');
        $conditions['monthly_service_user_id'] = $request->get('user_id');

        $monthlyServiceUser = $this->monthlyServiceUser->getEntryNow($conditions);
        if (count($monthlyServiceUser) > 1) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        } elseif (count($monthlyServiceUser) == 1) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        }

        // ユーザ情報取得
        $member = $this->user->getOneByUidWithMember($request->get('user_id'));
        if (empty($member)) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        }

        $date = date('Y-m-d H:i:s');
        $expireDatetime = date('Y-m-d H:i:s', strtotime($date . " +". $monthlyService->term . "day"));

        // monthly_service_user登録データ設定
        $params['monthly_service_user_id'] = $request->get('user_id');
        $params['member_id'] = $member->member_id;
        $params['monthly_service_id'] = $request->get('id');
        $params['status'] = 'active';
        $params['begin_datetime'] = date('Y-m-d H:i:s');
        $params['expire_datetime'] = $expireDatetime;

        // OAuth通知データ設定
        $paymentId = sprintf('%010d', $request->get('user_id')) . '-' . sprintf('%020d', $request->get('user_id')). '-' . date('YmdHis');
        $transactionId = date('YmdHis'). sprintf('%010d', $request->get('id')). sprintf('%020d', $request->get('user_id'));
        $oauth['status'] = 'success';
        $oauth['monthly_service_user_id'] = $request->get('user_id');
        $oauth['app_id'] = $monthlyService->app_id;
        $oauth['consumer_key'] = $application->consumer_key;
        $oauth['consumer_secret'] = $application->consumer_secret;
        $oauth['oauth_token'] = $applicationInstall->oauth_token;
        $oauth['oauth_token_secret'] = $applicationInstall->oauth_token_secret;
        $oauth['url'] = $monthlyService->callback_url;
        $oauth['payment_id'] = $paymentId;
        $oauth['date'] = $date;
        $oauth['expire_datetime'] = $expireDatetime;
        $oauth['amount'] = $monthlyService->amount;
        $oauth['product'] = $monthlyService->item_id;
        $oauth['monthly_service_id'] = $request->get('id');
        $oauth['vendor_id'] = $monthlyService->vendor_id;
        $oauth['transaction_id'] = $transactionId;

        // トランザクション開始
        FreegameSandbox::beginTransaction();
        try {
            // monthly_service_user登録
            $isSucccess = $this->monthlyServiceUser->add($params);
            if (! $isSucccess) {
                $result[] = trans('validationmessage.MSG162');
                FreegameSandbox::rollback();
                return $result;
            }

            // OAuth通知
            if (! $this->oauth($oauth)) {
                $result[] = preg_replace('/:attribute/', '通知', trans('validationmessage.MSG071'));
                FreegameSandbox::rollback();
                return $result;
            }

            // 登録(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }
    }

    /**
     * 解約通知
     * @param object $request request
     * @return 
     */
    public function cancel($request)
    {
        // idから月額サービスを取得
        $monthlyService = $this->getMonthlyService($request->get('id'));
        if (empty($monthlyService)) {
            $result[] = preg_replace('/:attribute/', 'データ', trans('validationmessage.MSG011'));;
            return $result;
        }

        // applicationのデータを取得。
        $application = $this->getApplication($monthlyService->app_id);
        if (empty($application)) {
            $result[] = preg_replace('/:attribute/', 'データ', trans('validationmessage.MSG011'));
            return $result;
        }

        // application_installのデータを取得。
        $conditions['user_id'] = $request->get('user_id');
        $conditions['app_id'] = $monthlyService->app_id;
        $conditions['status'] = 'active';

        $applicationInstall = $this->applicationInstall->getOneByConditions($conditions);
        if (empty($applicationInstall)) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        }

        // 契約有効期間内のデータ登録済みかを確認
        $conditions['monthly_service_id'] = $request->get('id');
        $conditions['monthly_service_user_id'] = $request->get('user_id');

        $monthlyServiceUser = $this->monthlyServiceUser->getEntryNow($conditions);
        if (count($monthlyServiceUser) > 1) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        }
        if (count($monthlyServiceUser) < 1) {
            $result[] = preg_replace('/:attribute/', 'ユーザID', trans('validationmessage.MSG011'));
            return $result;
        }

        // monthly_service_user変更(解約)データ設定
        $id = $monthlyServiceUser[0]->id;
        $params['status'] = 'withdrawal';
        $params['expire_datetime'] = date('Y-m-d H:i:s');

        // OAuth解約通知データ設定
        $transactionId = date('YmdHis'). sprintf('%010d', $request->get('id')). sprintf('%020d', $request->get('user_id'));
        $oauth['status'] = 'withdrawal';
        $oauth['monthly_service_user_id'] = $request->get('user_id');
        $oauth['app_id'] = $monthlyService->app_id;
        $oauth['consumer_key'] = $application->consumer_key;
        $oauth['consumer_secret'] = $application->consumer_secret;
        $oauth['oauth_token'] = $applicationInstall->oauth_token;
        $oauth['oauth_token_secret'] = $applicationInstall->oauth_token_secret;
        $oauth['url'] = $monthlyService->callback_url_withdrawal;
        if (empty($oauth['url'])) {
            $oauth['url'] = $monthlyService->callback_url;
        }
        $oauth['date'] = date('Y-m-d H:i:s');
        $oauth['expire_datetime'] = $oauth['date'];
        $oauth['amount'] = $monthlyService->amount;
        $oauth['product'] = $monthlyService->item_id;
        $oauth['monthly_service_id'] = $request->get('id');
        $oauth['vendor_id'] = $monthlyService->vendor_id;
        $oauth['transaction_id'] = $transactionId;

        // トランザクション開始
        FreegameSandbox::beginTransaction();
        try {
            // monthly_service_user変更(解約)
            $isSucccess = $this->monthlyServiceUser->edit($id, $params);
            if (! $isSucccess) {
                $result[] = trans('validationmessage.MSG162');
                FreegameSandbox::rollback();
                return $result;
            }
            // OAuth通知
            if (! $this->oauth($oauth)) {
                $result[] = preg_replace('/:attribute/', '通知', trans('validationmessage.MSG071'));
                FreegameSandbox::rollback();
                return $result;
            }

            // 登録(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }
    }

    /**
     * OAuth通知(メソッドはGET)
     * @param object $oauth 認証の為のデータ
     * @return 
     */
    public function oauth($oauth)
    {
        $orderdDatetime = str_replace(' ', 'T', $oauth['date']);
        $signatureHash = base64_encode(hash_hmac('sha1', $orderdDatetime, 'dmmgame'));
        $expireDatetime = str_replace(' ', 'T', $oauth['expire_datetime']);

        // url設定、最後尾に? or &があるか
        $urlPath = $oauth['url'];
        $parse_data  = parse_url($urlPath);
        if (!empty($parse_data['query'])) {
            $amp  = (substr($parse_data['query'], -1, 1) == '&') ? '' : '&';
            $urlPath = $parse_data['scheme']. "://" . $parse_data['host'] . $parse_data['path'] . "?" . $parse_data['query'] . $amp;
        } else if (substr($urlPath, -1, 1) != '?') {
            $urlPath = $urlPath . '?';
        } else {
            $urlPath = $urlPath;
        }

        // GETパラメータ
        $urlPath .= 'status=' . urlencode($oauth['status']);
        $urlPath .= '&ordered_datetime=' . urlencode($orderdDatetime);
        $urlPath .= '&amount=' . urlencode($oauth['amount']);
        $urlPath .= '&product=' . urlencode($oauth['product']);
        $urlPath .= '&vendor_id=' . urlencode($oauth['vendor_id']);
        $urlPath .= '&transaction_id=' . urlencode($oauth['transaction_id']);
        $urlPath .= '&signature_hash=' . urlencode($signatureHash);
        $urlPath .= '&user_id=' . urlencode($oauth['monthly_service_user_id']);
        $urlPath .= '&monthly_service_id=' . urlencode($oauth['monthly_service_id']);
        $urlPath .= '&expire_datetime=' . urlencode($expireDatetime);
        $urlPath .= '&opensocial_owner_id=' . urlencode($oauth['monthly_service_user_id']);
        $urlPath .= '&opensocial_viewer_id=' . urlencode($oauth['monthly_service_user_id']);
        $urlPath .= '&opensocial_app_id=' . urlencode($oauth['app_id']);
        if (isset($oauth['payment_id'])) {
            $urlPath .= '&payment_id=' . urlencode($oauth['payment_id']);
        }

        // OAuth
        $consumer = new Credentials(
            $oauth['consumer_key'], 
            $oauth['consumer_secret'],
            null
        );
        try {
            $uri = new Uri($urlPath);
            $accessToken = new StdOAuth1Token($oauth['oauth_token']);
            $accessToken->setAccessTokenSecret($oauth['oauth_token_secret']);
            $signature = new Signature($consumer);
            $httpClient = new CurlClient();
            if (empty($httpClient)) {
                return false;
            }
            $storage = new Memory();
            $monthlyService = new MonthlyServiceOAuth($consumer, $httpClient, $storage, $signature, $uri);
            $return = $monthlyService->requestGet($uri, null, array(), $accessToken);
            Log::error(var_export($return, true));
            if ($return['code'] != 200) {
                return false;
            }
        } catch (Exception $e) {
            return false;
        }
        return true;
    }

    /**
     * 検索条件セッション保持
     * @param array $search
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('SandboxMonthlyUsers.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'perPage',
            'page'
        ]);
        request()->session()->set('SandboxMonthlyUsers.search', $search);
        return $search;
    }
}
