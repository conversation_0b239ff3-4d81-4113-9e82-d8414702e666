<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;

class ReceiptInquiryService extends CustomService
{
    /** @var Application */
    protected $application;
    /** @var DeveloperApplication */
    protected $developerApplication;
    /** @var ReceiptApiService */
    protected $receiptApiService;

    // アプリケーションタイトル一覧
    protected $applicationTitleList;

    // ロケール
    const LOCALE_JP = 'ja-JP';
    // 取得したマイクロ単位の価格をローカライズするための値
    const AMOUNT_LOCALIZE_JP = 1000000;

    /**
     * ReceiptInquiryService constructor.
     *
     * @param Application $application
     * @param DeveloperApplication $developerApplication
     * @param ReceiptApiService $receiptApiService
     */
    public function __construct(
        Application $application,
        DeveloperApplication $developerApplication,
        ReceiptApiService $receiptApiService
    ) {
        $this->application = $application;
        $this->developerApplication = $developerApplication;
        $this->receiptApiService = $receiptApiService;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $list = [
            'menuName' => config('forms.ReceiptInquiry.menuName'),
            'screenName' => config('forms.ReceiptInquiry.screenName'),
            'subScreenName' => config('forms.ReceiptInquiry.subScreenName')
        ];
        return $list;
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        $deviceList = $this->receiptApiService->getSupportedDevice();

        if (isset($this->applicationTitleList)) {
            return $this->applicationTitleList;
        }

        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap($deviceList);
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice($deviceList);
        }

        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $this->receiptApiService->convertDisplayTitle($data->title, $data->device);
        }
        $this->applicationTitleList = ['' =>  ['' => '選択してください']] + $opts;

        return $this->applicationTitleList;
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return [];
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }

    /**
     * IDの長さで実行するAPIを判別
     *
     * @param string $receiptId
     * @param string $appId
     * @param string $device
     * @return array
     */
    public function getReceiptInfo($receiptId, $appId, $device)
    {
        // 桁数を取得
        $longs = strlen($receiptId);

        // レシート用アプリケーションID取得
        $receiptApplication = $this->receiptApiService->getReceiptApplication($appId, $device);

        // エラー処理対応
        if (!array_key_exists('resultData', $receiptApplication)) {
            return $this->setResponseErrorResult();
        }
        if (!array_key_exists('id', $receiptApplication['resultData'])) {
            return $this->setResponseErrorResult();
        }

        // 36桁の場合はreceiptId
        if ($longs == 36) {
            return $this->getReceiptData($receiptId, $receiptApplication['resultData']['id']);
        // 12桁の場合はinquiryId
        } elseif ($longs == 12) {
            return $this->getInquiryData($receiptId, $receiptApplication['resultData']['id']);
        }

        return $this->setResponseErrorResult();
    }

    /**
     * 36桁レシートIDでの検索の場合
     *
     * @param $receiptId
     * @param $receiptApplicationId
     * @return array
     */
    private function getReceiptData($receiptId, $receiptApplicationId)
    {
        // 初期化
        $receiptInfo[0] = [
            'resultStatus'  => null,
            'resultMessage' => null,
            'receiptId'     => null,
            'sku'           => null,
            'title'         => null,
            'price'         => null,
            'status'        => null,
            'placedAt'      => null,
            'fulfilledAt'   => null,
            'payload'       => null,
        ];

        // レシートID用のAPI実行
        $receiptData = $this->receiptApiService->getReceiptData($receiptId, $receiptApplicationId);

        // レシートID用整形
        if (array_key_exists('resultStatus', $receiptData)) {
            $receiptInfo[0]['resultStatus'] = $receiptData['resultStatus'];
        }
        if (array_key_exists('resultMessage', $receiptData)) {
            $receiptInfo[0]['resultMessage'] = $receiptData['resultMessage'];
        }

        // エラー処理対応
        if (!array_key_exists('response', $receiptData)) {
            return $this->setResponseErrorResult();
        }
        if (!$receiptData['response']) {
            return $this->setItemErrorResult();
        }

        if (array_key_exists('receiptId', $receiptData['response'])) {
            $receiptInfo[0]['receiptId'] = $receiptData['response']['receiptId'];
        }
        if (array_key_exists('sku', $receiptData['response'])) {
            $receiptInfo[0]['sku'] = $receiptData['response']['sku'];
        }
        if (array_key_exists('title', $receiptData['response'])) {
            $receiptInfo[0]['title'] = $receiptData['response']['title'];
        }
        if (array_key_exists('price', $receiptData['response']) && array_key_exists('locale', $receiptData['response'])) {
            $receiptInfo[0]['price'] = $this->createDisplayAmount($receiptData['response']['price'], $receiptData['response']['locale']);
        }
        if (array_key_exists('status', $receiptData['response'])) {
            $receiptInfo[0]['status'] = $this->createDisplayStatus($receiptData['response']['status']);
        }
        if (array_key_exists('placedAt', $receiptData['response'])) {
            $receiptInfo[0]['placedAt'] = $this->createDisplayTime($receiptData['response']['placedAt']);
        }
        if (array_key_exists('fulfilledAt', $receiptData['response'])) {
            $receiptInfo[0]['fulfilledAt'] = $this->createDisplayTime($receiptData['response']['fulfilledAt']);
        }
        if (array_key_exists('payload', $receiptData['response'])) {
            $receiptInfo[0]['payload'] = $receiptData['response']['payload'];
        }
        return $receiptInfo;
    }

    /**
     * 12桁お問い合わせIDでの検索の場合
     *
     * @param $inquiryId
     * @param $receiptApplicationId
     * @return array
     */
    private function getInquiryData($inquiryId, $receiptApplicationId)
    {
        // 初期化
        $receiptInfo[0] = [
            'resultStatus'  => null,
            'resultMessage' => null,
            'receiptId'     => null,
            'sku'           => null,
            'title'         => null,
            'price'         => null,
            'status'        => null,
            'placedAt'      => null,
            'fulfilledAt'   => null,
            'payload'       => null,
        ];

        // 問い合わせID用のAPI実行
        $inquiryData = $this->receiptApiService->getInquiryData($inquiryId, $receiptApplicationId);

        // エラー処理対応
        if (!array_key_exists('response', $inquiryData)) {
            return $this->setResponseErrorResult();
        }
        if (!array_key_exists('inquiries', $inquiryData['response'])) {
            return $this->setResponseErrorResult();
        }

        // 問い合わせID用整形
        foreach ($inquiryData['response']['inquiries'] as $key => $data) {
            if (array_key_exists('resultStatus', $inquiryData)) {
                $receiptInfo[$key]['resultStatus'] = $inquiryData['resultStatus'];
            }
            // エラー処理対応
            if (array_key_exists('resultMessage', $inquiryData)) {
                return $this->setResponseErrorResult();
            }
            if (array_key_exists('receiptId', $data)) {
                $receiptInfo[$key]['receiptId'] = $data['receiptId'];
            }
            if (array_key_exists('sku', $data)) {
                $receiptInfo[$key]['sku'] = $data['sku'];
            }
            if (array_key_exists('title', $data)) {
                $receiptInfo[$key]['title'] = $data['title'];
            }
            if (array_key_exists('price', $data) && array_key_exists('locale', $data)) {
                $receiptInfo[$key]['price'] = $this->createDisplayAmount($data['price'], $data['locale']);
            }
            if (array_key_exists('status', $data)) {
                $receiptInfo[$key]['status'] = $this->createDisplayStatus($data['status']);
            }
            if (array_key_exists('placedAt', $data)) {
                $receiptInfo[$key]['placedAt'] = $this->createDisplayTime($data['placedAt']);
            }
            if (array_key_exists('fulfilledAt', $data)) {
                $receiptInfo[$key]['fulfilledAt'] = $this->createDisplayTime($data['fulfilledAt']);
            }
            if (array_key_exists('payload', $data)) {
                $receiptInfo[$key]['payload'] = $data['payload'];
            }
        }

        // エラー処理対応
        if ($inquiryData['resultStatus'] == true && empty($inquiryData['response']['inquiries'])) {
            return $this->setItemErrorResult();
        }
        if ($inquiryData['resultStatus'] == false) {
            return $this->setResponseErrorResult();
        }
        return $receiptInfo;
    }

    /**
     * unix timestamp変換
     *
     * @param int $time
     * @return string
     */
    private function createDisplayTime($time)
    {
        $displayTime = '';
        if ($time) {
            $displayTime = date("Y/m/d H:i:s", $time);
        }
        return $displayTime;
    }

    /**
     * 表示用の価格情報を生成する
     * レシート課金側で登録されている価格情報は、マイクロ単位の価格になっているので、
     * 表示する際は特定の通貨にローカライズする必要があります。
     *
     * @param int $amount
     * @param string $locale
     * @return string
     */
    private function createDisplayAmount($amount, $locale)
    {
        $displayAmount = '';
        if (is_null($amount)) {
            $displayAmount = '-';
        } else if ($locale === self::LOCALE_JP) {
            $displayAmount = number_format($amount / self::AMOUNT_LOCALIZE_JP);
        }
        return $displayAmount;
    }

    /**
     * ステータスを英語から日本語に変換
     *
     * @param string $status
     * @return string
     */
    private function createDisplayStatus($status)
    {
        $displayStatus = '';
        foreach (config('forms.ReceiptInquiry.status') as $key => $value) {
            if ($key == $status) {
                $displayStatus = $value;
            }
        }
        return $displayStatus;
    }

    /**
     * エラー処理対応、条件にヒットするアイテムが見つかりませんでした
     *
     * @return array
     */
    private function setItemErrorResult()
    {
        $receiptInfo[0] = [
            'resultStatus'  => true,
            'resultMessage' => null,
            'receiptId'     => null,
            'sku'           => null,
            'title'         => null,
            'price'         => null,
            'status'        => null,
            'placedAt'      => null,
            'fulfilledAt'   => null,
            'payload'       => null,
        ];
        return $receiptInfo;
    }

    /**
     * エラー処理対応、サーバから応答がありません
     *
     * @return array
     */
    private function setResponseErrorResult()
    {
        $receiptInfo[0] = [
            'resultStatus'  => false,
            'resultMessage' => config('receipt-api.resultMessage.no_api_response'),
            'receiptId'     => null,
            'sku'           => null,
            'title'         => null,
            'price'         => null,
            'status'        => null,
            'placedAt'      => null,
            'fulfilledAt'   => null,
            'payload'       => null,
        ];
        return $receiptInfo;
    }
}
