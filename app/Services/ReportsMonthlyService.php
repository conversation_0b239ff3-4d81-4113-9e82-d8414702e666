<?php
namespace App\Services;

use App\Models\FreegameDeveloper\ReportUtilization;
use App\Models\FreegameDeveloper\ReportDmmMonthlyUtilization;
use App\Models\FreegameDeveloper\GuestReportUtilization;
use App\Models\FreegameDeveloper\GuestReportMonthlyUtilization;
use App\Models\FreegameDeveloper\GuestReportDmmMonthlyUtilization;
use App\Models\FreegameDeveloper\ReportGender;
use App\Models\FreegameDeveloper\ReportMonthlyGender;
use App\Models\FreegameDeveloper\ReportDmmMonthlyGender;
use App\Models\FreegameReport\ReportMovieAuthAccessLogMonthly;
use App\Models\FreegameDeveloper\TaxRate;
use App\Models\Freegame\Application;
use App\Models\Freegame\ApplicationDevice;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\MonthlyService;
use App\Models\FreegameDeveloper\MonthlyServiceReportDmmMonthlyUtilization;
use App\Models\FreegameDeveloper\MonthlyServiceReportMonthlyUtilization;
use App\Models\FreegameDeveloper\MonthlyServiceReportUtilization;
use App\Models\FreegameDeveloper\ApplicationDivision;
use App\Models\FreegameDeveloper\ReportDmmUtilizationByFloor;
use App\Models\FreegameDeveloper\ReportUtilizationByFloor;
use App\Models\Freegame\ApplicationLanguageGroup;
use App\Models\FreegameSetting\Language;
use App\Services\Accessory\LanguageSupport;
use App\Services\SubscriptionUtilizationReportMonthlyService;
use App\Services\ChargeCenterService;

use Exception;

/**
 * レポート：月別レポート
 */
class ReportsMonthlyService extends CustomService
{
    use LanguageSupport;

    protected $reportUtilization;

    protected $reportDmmMonthlyUtilization;

    protected $guestReportUtilization;

    protected $guestReportMonthlyUtilization;

    protected $guestReportDmmMonthlyUtilization;

    protected $reportGender;

    protected $reportMonthlyGender;

    protected $reportDmmMonthlyGender;

    protected $reportMovieAuthAccessLogMonthly;

    protected $taxRate;

    protected $application;

    protected $applicationDevice;

    protected $developerApplication;

    protected $appTitleType;

    protected $monthlyService;

    protected $monthlyServiceReportDmmMonthlyUtilization;

    protected $monthlyServiceReportMonthlyUtilization;

    protected $monthlyServiceReportUtilization;

    protected $applicationDivision;

    protected $applicationLanguageGroup;

    protected $reportDmmUtilizationByFloor;

    protected $reportUtilizationByFloor;

    protected $language;

    protected $gameIdList;
    protected $siteList;
    protected $subscriptionUtilizationReportMonthlyService;

    protected $chargeCenterService;

    public function __construct(
        ReportUtilization $reportUtilization,
        ReportDmmMonthlyUtilization $reportDmmMonthlyUtilization,
        GuestReportUtilization $guestReportUtilization,
        GuestReportMonthlyUtilization $guestReportMonthlyUtilization,
        GuestReportDmmMonthlyUtilization $guestReportDmmMonthlyUtilization,
        ReportGender $reportGender,
        ReportMonthlyGender $reportMonthlyGender,
        ReportDmmMonthlyGender $reportDmmMonthlyGender,
        ReportMovieAuthAccessLogMonthly $reportMovieAuthAccessLogMonthly,
        TaxRate $taxRate,
        Application $application,
        ApplicationDevice $applicationDevice,
        DeveloperApplication $developerApplication,
        MonthlyService $monthlyService,
        MonthlyServiceReportDmmMonthlyUtilization $monthlyServiceReportDmmMonthlyUtilization,
        MonthlyServiceReportMonthlyUtilization $monthlyServiceReportMonthlyUtilization,
        MonthlyServiceReportUtilization $monthlyServiceReportUtilization,
        ApplicationDivision $applicationDivision,
        ApplicationLanguageGroup $applicationLanguageGroup,
        Language $language,
        ReportDmmUtilizationByFloor $reportDmmUtilizationByFloor,
        ReportUtilizationByFloor $reportUtilizationByFloor,
        SubscriptionUtilizationReportMonthlyService $subscriptionUtilizationReportMonthlyService,
        ChargeCenterService $chargeCenterService
    ) {
        $this->reportUtilization = $reportUtilization;
        $this->reportDmmMonthlyUtilization = $reportDmmMonthlyUtilization;
        $this->guestReportUtilization = $guestReportUtilization;
        $this->guestReportMonthlyUtilization = $guestReportMonthlyUtilization;
        $this->guestReportDmmMonthlyUtilization = $guestReportDmmMonthlyUtilization;
        $this->reportGender = $reportGender;
        $this->reportMonthlyGender = $reportMonthlyGender;
        $this->reportDmmMonthlyGender = $reportDmmMonthlyGender;
        $this->reportMovieAuthAccessLogMonthly = $reportMovieAuthAccessLogMonthly;
        $this->taxRate = $taxRate;
        $this->application = $application;
        $this->applicationDevice = $applicationDevice;
        $this->developerApplication = $developerApplication;
        $this->monthlyService = $monthlyService;
        $this->monthlyServiceReportDmmMonthlyUtilization = $monthlyServiceReportDmmMonthlyUtilization;
        $this->monthlyServiceReportMonthlyUtilization = $monthlyServiceReportMonthlyUtilization;
        $this->monthlyServiceReportUtilization = $monthlyServiceReportUtilization;
        $this->applicationDivision = $applicationDivision;
        $this->applicationLanguageGroup = $applicationLanguageGroup;
        $this->language = $language;
        $this->reportDmmUtilizationByFloor = $reportDmmUtilizationByFloor;
        $this->reportUtilizationByFloor = $reportUtilizationByFloor;
        $this->subscriptionUtilizationReportMonthlyService = $subscriptionUtilizationReportMonthlyService;
        $this->chargeCenterService = $chargeCenterService;
    }

    /**
     * 経理用レポート権限判定
     * @return array boolean
     */
    protected function getIsAccountingAuth() {
        if (! auth_is_route('ReportsMonthly.csvaccountingdownload')) {
            return false;
        }
        return true;
    }

    /**
     * 経理用レポートを指定しているか判定
     * @param  string $report
     * @return boolean
     */
    protected function getIsAccountingReport($report) {
        if (preg_match('/_Accounting/', $report)) {
            return true;
        }
        return false;
    }

    /**
     * フォームに設定するデータを取得
     * @return array
     */
    public function getFormData()
    {
        $reportType = config('forms.ReportsMonthly.reportType');
        if (!auth_is_route('ReportsMonthly.csvnotaxdownload')) {
            unset($reportType['utilization']['NoTax_Report']);
        }

        return [
            'menuName' => config('forms.ReportsMonthly.menuName'),
            'screenName' => config('forms.ReportsMonthly.screenName'),
            'periodType' => config('forms.ReportsMonthly.periodType'),
            'deviceType' => config('forms.ReportsMonthly.deviceType'),
            'reportType' => $this->getReportType(),
            'appTitleType' => $this->getAppTitleType()
        ];
    }

    /**
     * フォームの形式プルダウンのデータを取得
     * @return array $reportType
     */
    public function getReportType()
    {
        $reportType = [];
        if ($this->getIsAccountingAuth()) {
            // 経理用レポートの場合は形式に全て接尾語を追加
            foreach (config('forms.ReportsMonthly.reportType') as $typeKey => $typeList) {
                foreach ($typeList as $reportKey => $report) {
                    $reportType[$typeKey][$reportKey] = $report;
                    $reportType[$typeKey][$reportKey . '_Accounting'] = $report . '【経理用】';
                }
            }
            foreach (config('forms.ReportsMonthly.accountsOnlyReportType') as $typeKey => $typeList) {
                foreach ($typeList as $reportKey => $report) {
                    $reportType[$typeKey][$reportKey . '_Accounting'] = $report . '【経理用】';
                }
            }
        } else {
            // 経理用レポートではない場合
            $reportType = config('forms.ReportsMonthly.reportType');
        }
        // 定期購入利用状況レポート
        foreach (config('forms.ReportsMonthly.developerOnlyReportType') as $typeKey => $typeList) {
            foreach ($typeList as $reportKey => $report) {
                $reportType[$typeKey][$reportKey] = $report;
            }
        }
        return $reportType;
    }

    /**
     * 集計対象のアプリのapp_id、タイトルを取得
     * @return array $opts
     */
    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        if (auth_is_pf()) {
            $opts = config('forms.ReportsMonthly.reportDmmType');
        }
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    /**
     * フォームから送信された検索条件を整理
     * @param array $condition
     * @return array $search
     */
    public function formatSearchQuery($search = [])
    {
        if (empty($search['begin'])) {
            $search['begin'] = '';
        } else {
            $search['begin'] = date('Y-m-01', strtotime($search['begin']));
        }
        if (empty($search['end'])) {
            $search['end'] = '';
        } else {
            $search['end'] = date('Y-m-t', strtotime($search['end']));
        }
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            if (auth_is_sap()) {
                $search['app_id'] = array_keys($appTitleType);
                if (empty($search['app_id'])) {
                    $search['app_id'] = '-1';
                }
            } else {
                $search['app_id'] = '';
            }
        }
        if (empty($search['device'])) {
            $search['device'] = '';
        }
        $search = array_only($search, [
            'begin',
            'end',
            'app_id',
            'attr',
            'device',
            'type',
            'select',
            'monthly_service_id',
            'kind'
        ]);
        return $search;
    }

    /**
     * ファイル名を生成
     * @param array $condition
     * @return string
     */
    public function getCsvFileName($condition = [])
    {
        if (empty($condition['begin'])) {
            $condition['begin'] = '1970/01/01';
        }
        if (empty($condition['end'])) {
            $condition['end'] = date('Y/m/d');
        }
        $report = preg_replace('/_Accounting/', '', $condition['report']);
        return sprintf(
            config('forms.ReportsMonthly.CsvFileName'),
            $report,
            $condition['app_id'],
            date('Y-m', strtotime($condition['begin'])),
            date('Y-m', strtotime($condition['end']))
        );
    }

    /**
     * フォームから送信されたreportでヘッダーを選定
     * @param array $condition
     * @return array $header
     */
    public function getCsvHeader($condition = [])
    {
        if (empty($condition['report'])) {
            return [];
        }

        // 経理用レポートの接尾語削除
        $report = preg_replace('/_Accounting/', '', $condition['report']);
        $method = camel_case('get_' . $report . '_header');
        if (method_exists($this, $method)) {
            return $this->{$method}($condition);
        }
        return [];
    }

    /**
     * ヘッダー：利用状況レポート
     * @param array $condition
     * @return array $header
     */
    public function getReportHeader($condition = [])
    {
        $header['format_date'] = '年月';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
            $header['app_id'] = 'アプリID';
        }
        $header['title'] = 'タイトル';
        $header['format_device'] = 'デバイス';

        if ($this->getIsAccountingAuth() && ($condition['report'] == 'Report_Accounting'  || $condition['report'] == 'Monthly_Service_Report_Accounting')) {
            if(empty($condition['app_id'])) {
                $header['site'] = 'サイト';
            }
        }
        if ($this->getIsAccountingAuth()) {
            $header['language'] = '言語';
        }

        $header['active_user'] = '登録者数';
        if (! is_numeric($condition['app_id'])) {
            $header ['active_user_all'] = '全体登録者数';
        }
        $header += [
            'download_user' => 'ダウンロード数',
            'regist_user' => '新規登録者数',
            'upgrade_user' => 'アップグレード数',
            'suspend_user' => '新規停止者数',
            'pv' => 'PV',
            'mau' => 'MAU',
            'average_pv' => '平均PV',
            'use_point_user' => '課金UU',
            'charging_rate' => '課金率',
            'use_point' => '消費ポイント',
            'use_point_pay' => '有料ポイント',
            'use_point_free' => '無料ポイント',
            'discount_price' => 'クーポン値引ポイント',
            'arpu' => 'ARPU',
            'arppu' => 'ARPPU'
        ];
        // 利用状況レポート以外の場合、クーポン値引ポイントをヘッダーから削除
        if(!in_array($condition['report'], ['Report', 'Report_Accounting'])){
            unset($header['discount_price']);
        }
        if (is_numeric($condition['app_id'])) {
            $header += [
                'use_point_staff' => '優待消費ポイント'
            ];
        } else {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：利用状況レポート（ゲスト）
     * @param array $condition
     * @return array $header
     */
    public function getGuestReportHeader($condition = [])
    {
        $header['format_date'] = '年月';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'active_user' => 'ゲストプレイ人数',
            'active_user_all' => '総ゲストプレイ数',
            'regist_user' => '新規ゲストプレイ人数',
            'upgrade_user' => 'アップグレード数',
            'mau' => 'MAU',
            'pv' => 'PV',
            'average_pv' => '平均PV',
            'use_point_user' => '課金UU',
            'use_point_pay' => '消費ポイント'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：税抜価格レポート
     * @param array $condition
     * @return array $header
     */
    public function getNoTaxReportHeader($condition = [])
    {
        $header['format_date'] = '年月';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス'
        ];
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report']) && empty($condition['app_id'])) {
            $header['site'] = 'サイト';
        }

        if ($this->getIsAccountingAuth() && $condition['report'] === 'NoTax_Report_Accounting') {
            $header['language'] = '言語';
        }

        $header += [
            'use_point' => '消費ポイント（税抜）',
            'use_point_free' => '無料消費ポイント（税抜）'
        ];
        if (is_numeric($condition['app_id'])) {
            $header += [
                'use_point_staff' => '優待消費ポイント（税抜）'
            ];
        } else {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：男女別　利用者レポート
     * @param array $condition
     * @return array $header
     */
    public function getUserReportHeader($condition = [])
    {
        $header['format_date'] = '年月';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'format_device' => 'デバイス',
            'male_18_19' => '男性（18～19）',
            'male_20_24' => '男性（20～24）',
            'male_25_29' => '男性（25～29）',
            'male_30_34' => '男性（30～34）',
            'male_35_39' => '男性（35～39）',
            'male_40_49' => '男性（40～49）',
            'male_50' => '男性（50～）',
            'female_18_19' => '女性（18～19）',
            'female_20_24' => '女性（20～24）',
            'female_25_29' => '女性（25～29）',
            'female_30_34' => '女性（30～34）',
            'female_35_39' => '女性（35～39）',
            'female_40_49' => '女性（40～49）',
            'female_50' => '女性（50～）'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：男女別　PVレポート
     * @param array $condition
     * @return array $header
     */
    public function getPVReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：男女別　MAUレポート
     * @param array $condition
     * @return array $header
     */
    public function getAUReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：男女別　課金UUレポート
     * @param array $condition
     * @return array $header
     */
    public function getPUReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：男女別　消費ポイントレポート
     * @param array $condition
     * @return array $header
     */
    public function getPointReportHeader($condition = [])
    {
        return $this->getUserReportHeader($condition);
    }

    /**
     * ヘッダー：動画視聴状況レポート
     * @param array $condition
     * @return array $header
     */
    public function getMovieReportHeader($condition = [])
    {
        $header['format_date'] = '年月';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
        }
        $header += [
            'title' => 'タイトル',
            'movie_id' => 'プロダクトID',
            'auth_count' => '視聴回数',
            'unique_user' => 'ユニーク視聴回数'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * 月額サービス用
     * @param array $condition
     */
    public function getMonthlyServiceReportHeader($condition = [])
    {
        $header  = $this->getReportHeader($condition);
        $header += [
            'download_user' => 'ダウンロード数',
            'regist_user' => '新規登録者数',
            'upgrade_user' => 'アップグレード数',
            'suspend_user' => '新規停止者数',
            'pv' => 'PV',
            'mau' => 'MAU',
            'average_pv' => '平均PV',
            'use_point_user' => '課金UU',
            'charging_rate' => '課金率',
            'use_point' => '消費ポイント',
            'use_point_pay' => '有料ポイント',
            'use_point_free' => '無料ポイント',
            'arpu' => 'ARPU',
            'arppu' => 'ARPPU'
        ];
        if (is_numeric($condition['app_id'])) {
            $header += [
                'use_point_staff' => '優待消費ポイント'
            ];
        } else {
            $header['title'] = '出力範囲';
        }
        $header += [
            'm_title'           => '月額サービス名',
            'm_price'           => '月額課金',
            'm_regist_user'     => '月額新規入会者数',
            'm_continue_user'   => '月額継続者数',
            'm_active_user'     => '月額利用者数',
            'm_withdrawal_user' => '月額解約者数',
            'm_eviction_user'   => '月額強制解約者数',
            'm_total_use'       => '合計課金額(消費ポイント・月額課金)',
            'm_use_unique_user' => '課金UU(月額課金者とポイント消費者)',
            'm_total_arpu'      => 'ARPU(消費ポイント＋月額課金)',
            'm_total_arppu'     => 'ARPPU(消費ポイント＋月額課金)',
            'm_credit_price'    => 'クレジット課金額',
            'm_point_price'     => 'ポイント消費'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['m_title'] = '月額出力範囲';
        }
        return $header;
    }

    /**
     * ヘッダー：費用負担元別利用状況レポート（経理）
     * @param array $condition
     * @return array $header
     */
    public function getCostBearerReportHeader($condition = [])
    {
        $header['format_date'] = '年月';
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $header['game_id'] = 'ゲームID';
            $header['app_id'] = 'アプリID';
        }
        $header['title'] = 'タイトル';
        $header['format_device'] = 'デバイス';

        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            if(empty($condition['app_id'])) {
                $header['site'] = 'サイト';
            }
        }
        if ($this->getIsAccountingAuth()) {
            $header['language'] = '言語';
        }

        $header += [
            'publisher_floor_id' => '費用負担元（フロアID）',
            'floor_name' => '費用負担元名',
            'use_point' => '消費ポイント',
            'use_point_pay' => '有料ポイント',
            'use_point_free' => '無料ポイント',
            'discount_price' => 'クーポン値引ポイント',
            'use_discount_price' => 'クーポン値引後消費ポイント'
        ];
        if (! is_numeric($condition['app_id'])) {
            $header['title'] = '出力範囲';
        }
        return $header;
    }

    /**
     * フォームから送信されたreportでCSV用のリストを選定
     * @param array $condition
     * @param array 
     */
    public function getCsvList($condition = [])
    {
        if (empty($condition['report'])) {
            return [];
        }
        // 経理用レポートの接尾語削除
        $report = preg_replace('/_Accounting/', '', $condition['report']);
        $method = camel_case('get_' . $report);

        // app_id=>game_idの配列生成
        $this->gameIdList = [];
        if ($this->getIsAccountingAuth() && $this->getIsAccountingReport($condition['report'])) {
            $this->gameIdList = $this->applicationDivision->getGameIdByAppIdList();
        }

        // app_id=>サイト(一般orR18)の配列生成
        $this->siteList = [];
        if (($this->getIsAccountingAuth() && $condition['app_id'] == 0)
            && $this->getIsAccountingReport($condition['report'])
        ) {
            $appAllList = $this->application->getListAll();
            foreach ($appAllList as $app) {
                if ($app['general'] == 1) {
                    $this->siteList[$app['id']] = '一般';
                } else {
                    $this->siteList[$app['id']] = 'R18';
                }
            }
        }

        if (method_exists($this, $method)) {
            return $this->{$method}($condition);
        }
        return [];
    }

    /**
     * 利用状況レポート
     * @param array $condition
     * @return object $list
     */
    public function getReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        $addActiveUsers = [];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
            $subList = $this->reportUtilization->getList($this->formatSearchQuery($condition));
            foreach ($subList as $subData) {
                $addActiveUsers
                    [$subData->date->format('Ym') . '_' . $subData->app_id . '_' . $subData->device] =
                        $subData->active_user;
            }
            $subList = $this->guestReportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
            $subList = $this->guestReportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $addUpgradeUsers = [];
        foreach ($subList as $subData) {
            if (is_numeric($condition['app_id'])) {
                $addUpgradeUsers
                    [$subData->date->format('Ym') . '_' . $subData->app_id . '_' . $subData->device] =
                        $subData->upgrade_user;
            } else {
                $addUpgradeUsers
                    [$subData->date->format('Ym') . '_' . $subData->attr . '_' . $subData->device] =
                        $subData->upgrade_user;
            }
        }
        $addUsePointUsers = [];
        if ($condition['app_id'] != 'spt') {
            $subList = $this->getPUReport($condition, true);
            foreach ($subList as $subData) {
                $sum = $subData->male_18_19
                    + $subData->male_20_24 + $subData->male_25_29
                    + $subData->male_30_34 + $subData->male_35_39
                    + $subData->male_40_49 + $subData->male_50
                    + $subData->female_18_19
                    + $subData->female_20_24 + $subData->female_25_29
                    + $subData->female_30_34 + $subData->female_35_39
                    + $subData->female_40_49 + $subData->female_50;
                if (is_numeric($condition['app_id'])) {
                    $addUsePointUsers
                        [$subData->date->format('Ym') . '_' . $subData->app_id . '_' . $subData->device] =
                            $sum;
                } else {
                    $addUsePointUsers
                        [$subData->date->format('Ym') . '_' . $subData->attr . '_' . $subData->device] =
                            $sum;
                }
            }
        }
        $subList = $this->getAUReport($condition, true);
        $addMaus = [];
        foreach ($subList as $subData) {
            $sum = $subData->male_18_19
                + $subData->male_20_24 + $subData->male_25_29
                + $subData->male_30_34 + $subData->male_35_39
                + $subData->male_40_49 + $subData->male_50
                + $subData->female_18_19
                + $subData->female_20_24 + $subData->female_25_29
                + $subData->female_30_34 + $subData->female_35_39
                + $subData->female_40_49 + $subData->female_50;
            if (is_numeric($condition['app_id'])) {
                $addMaus[$subData->date->format('Ym') . '_' . $subData->app_id . '_' . $subData->device] = $sum;
            } else {
                $addMaus[$subData->date->format('Ym') . '_' . $subData->attr . '_' . $subData->device] = $sum;
            }
        }
        $thisMonth = date('Y-m-01 00:00:00');

        // 言語リスト取得
        $language_list = $this->getApplicationLanguageList($this->formatSearchQuery($condition));

        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
                $addKey = $data->date->format('Ym') . '_' . $data->app_id . '_' . $data->device;
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
                $addKey = $data->date->format('Ym') . '_' . $data->attr . '_' . $data->device;
            }
            $data->active_user = array_get($addActiveUsers, $addKey, $data->active_user);
            $data->upgrade_user = array_get($addUpgradeUsers, $addKey, 0);

            $data->use_point_user = array_get($addUsePointUsers, $addKey, 0);
            $data->mau = array_get($addMaus, $addKey, 0);

            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
            if (empty($data->pv) || empty($data->mau)) {
                $data->average_pv = 0;
            } else {
                $data->average_pv = round($data->pv / $data->mau, 3);
            }

            // サイト
            if (isset($this->siteList[$data->app_id])) {
                $data->site = $this->siteList[$data->app_id];
            } else {
                $data->site = '';
            }

            // 言語
            $data->language = $this->getLanguage($language_list, $data->app_id);

            if (empty($data->use_point_user) || empty($data->mau)) {
                $data->charging_rate = 0;
            } else {
                $data->charging_rate = round($data->use_point_user / $data->mau, 3);
            }
            if (empty($data->use_point) || empty($data->active_user)) {
                $data->arpu = 0;
            } else {
                $data->arpu = round($data->use_point / $data->active_user, 3);
            }
            if (empty($data->use_point) || empty($data->use_point_user)) {
                $data->arppu = 0;
            } else {
                $data->arppu = round($data->use_point / $data->use_point_user, 3);
            }
            $data->regist_user = $data->regist_user - $data->upgrade_user;
        }
        return $list;
    }

    /**
     * 利用状況レポート（ゲスト）
     * @param array $condition
     * @return object $list
     */
    public function getGuestReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->guestReportMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->guestReportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
            if (empty($data->pv) || empty($data->mau)) {
                $data->average_pv = 0;
            } else {
                $data->average_pv = round($data->pv / $data->mau, 3);
            }
            if (empty($data->use_point_pay) || empty($data->use_point_user)) {
                $data->arppu = 0;
            } else {
                $data->arppu = round($data->use_point_pay / $data->use_point_user, 3);
            }
        }
        return $list;
    }

    /**
     * 税抜価格レポート
     * @param array $condition
     * @return object $list
     */
    public function getNoTaxReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $taxList = $this->taxRate->getList();
        $languageList = $this->getApplicationLanguageList($this->formatSearchQuery($condition));

        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');

            // サイト
            if (isset($this->siteList[$data->app_id])) {
                $data->site = $this->siteList[$data->app_id];
            } else {
                $data->site = '';
            }
            
            // 言語
            $data->language = $this->getLanguage($languageList, $data->app_id);

            if (! empty($data->use_point) || ! empty($data->use_point_free) || ! empty($data->use_point_staff)) {
                $tax = 0;
                foreach ($taxList as $taxData) {
                    if ($data->date->between($taxData->begin_date, $taxData->end_date)) {
                        $tax = ($taxData->tax_rate + 100) / 100;
                        break;
                    }
                }
                if ($tax != '0') {
                    if (! empty($data->use_point)) {
                        $data->use_point = floor(sprintf('%.3f', $data->use_point / $tax));
                    }
                    if (! empty($data->use_point_free)) {
                        $data->use_point_free = floor(sprintf('%.3f', $data->use_point_free / $tax));
                    }
                    if (! empty($data->use_point_staff)) {
                        $data->use_point_staff = floor(sprintf('%.3f', $data->use_point_staff / $tax));
                    }
                }
            }
        }
        return $list;
    }

    /**
     * 男女別　利用者レポート
     * @param array $condition
     * @return object $list
     */
    public function getUserReport($condition = [])
    {
        $condition['type'] = 'user';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　PVレポート
     * @param array $condition
     * @return object $list
     */
    public function getPVReport($condition = [])
    {
        $condition['type'] = 'pv';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　MAUレポート
     * @param array $condition
     * @return object $list
     */
    public function getAUReport($condition = [], $resultonly = false)
    {
        $condition['type'] = 'mau';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        if ($resultonly) {
            return $list;
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　課金UUレポート
     * @param array $condition
     * @return object $list
     */
    public function getPUReport($condition = [], $resultonly = false)
    {
        $condition['type'] = 'use_point_user';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        if ($resultonly) {
            return $list;
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 男女別　消費ポイントレポート
     * @param array $condition
     * @return object $list
     */
    public function getPointReport($condition = [])
    {
        $condition['type'] = 'use_point';
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
            }
            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');
        }
        return $list;
    }

    /**
     * 動画視聴状況レポート
     * @param array $condition
     * @return object $list
     */
    public function getMovieReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportMovieAuthAccessLogMonthly->getList($this->formatSearchQuery($condition));
        } else {
            $list = [];
        }
        foreach ($list as &$data) {
            $data->title = array_get($appTitleType, $data->app_id, '---');
            $data->format_date = $data->report_month->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

        }
        return $list;
    }

    /**
     * 月額サービス用
     * @param array $condition
     * @return unknown
     */
    public function getMonthlyServiceReport($condition = [])
    {
        $result            = [];
        $condition['kind'] = 'social';

        if (is_numeric($condition['app_id'])) {
            $monthlyList   = $this->monthlyService->getList($this->formatSearchQuery($condition));

            foreach ($monthlyList as $monthly) {
                $condition['app_id']             = $monthly['app_id'];
                $condition['monthly_service_id'] = $monthly['id'];
                $addMonthlyService               = [];
                $addActiveUsers                  = [];
                $list    = $this->getReport($condition);
                $subList = $this->monthlyServiceReportMonthlyUtilization->getMonthlyList($this->formatSearchQueryMonthly($condition));
                $tmpList = $this->monthlyServiceReportUtilization->getList($this->formatSearchQueryMonthly($condition));
                if ($this->hasApkCloud($monthly['app_id'])) {
                    $this->ajusMonthlyServiceDevice($subList);
                    $this->ajusMonthlyServiceDevice($tmpList);
                }

                foreach ($tmpList as $tmpData) {
                    $addKey =  "{$tmpData->date->format('Ym')}"
                            . "_{$tmpData->app_id}"
                            . "_{$tmpData->device}"
                            . "_{$tmpData->monthly_service_id}";
                    $addActiveUsers[$addKey] = $tmpData->active_user;
                }

                foreach ($subList as $subData) {
                    $addKey =  "{$subData->subdate->format('Ym')}"
                            . "_{$subData->app_id}"
                            . "_{$subData->device}"
                            . "_{$subData->monthly_service_id}";
                    $subData->active_user       = array_get($addActiveUsers, $addKey, $subData->active_user);
                    $addMonthlyService[$addKey] = $subData;
                }

                foreach ($list as $data) {
                    $data->m_title = $monthly['service_name'];
                    $result[]      = $this->getMonthlyServiceReportCalc($data, $addMonthlyService, $monthly['id']);
                }
            }
        } else {
            $condition['attr'] = $condition['app_id'];
            $addMonthlyService = [];
            $condition         = $this->formatSearchQuery($condition);
            $list    = $this->getReport($condition);
            $subList = $this->reportDmmMonthlyUtilization->getMonthlyServiceList($condition);

            if ($condition['attr'] == 'spt') {
                foreach ($subList as $subData) {
                    // 月額サービスの場合、DMM全体サポート調整含(spt)がなくDMM全体(all)と同じデータを登録するためキーの値に注意
                    $addKey =  "{$subData->date->format('Ym')}_{$condition['attr']}_{$subData->device}";
                    $addMonthlyService[$addKey] = $subData;
                }
            } else {
                foreach ($subList as $subData) {
                    $addKey =  "{$subData->date->format('Ym')}_{$subData->attr}_{$subData->device}";
                    $addMonthlyService[$addKey] = $subData;
                }
            }

            foreach ($list as $data) {
                $data->m_title = $data->title;
                $result[]      = $this->getMonthlyServiceReportCalc($data, $addMonthlyService);
            }
        }
        return $result;
    }

    /**
     * TODO : APKクラウドの月額サービスにて正しいデバイス値が保存されるようになった際、以下の関数を調整する。
     * ・formatSearchQueryMonthly() 削除し、呼び出しは formatSearchQuery() に戻す。
     * ・hasApkCloud() 削除。
     * ・ajusMonthlyServiceDevice() 削除。
     * また $this->applicationDevice も上記に伴って不要になるようであれば関連コードを除去する。
     */

    /**
     * フォームから送信された検索条件を整理
     * APKクラウドの月額サービス用に値を調整する
     * @param array $condition
     * @return array $search
     */
    private function formatSearchQueryMonthly($search = [])
    {
        $query = $this->formatSearchQuery($search);
    
        if (in_array('apk_cloud', $query['device']) && !in_array('sp', $query['device'])) {
            foreach ($query['device'] as $key => $device) {
                if ($device == 'apk_cloud') {
                    $query['device'][$key] = 'sp';
                }
            }    
        }

        return $query;
    }

    /**
     * 指定されたコンテンツにAPKクラウドのデバイスが存在するか確認する。
     * @param integer $appId
     * @return boolean
     */
    private function hasApkCloud($appId)
    {
        $device = $this->applicationDevice->getOne($appId, 'apk_cloud');

        return !empty($device);
    }

    /**
     * 月額サービスのレポートにて apk_cloud の場合 sp として保存されてしまうため、
     * 暫定対応としてこれを apk_cloud に置き換える。
     * @param array &$monthlyServiceReport
     */
    private function ajusMonthlyServiceDevice(&$monthlyServiceReport)
    {
        foreach ($monthlyServiceReport as $key => $data) {
            if ($data->device == 'sp') {
                $monthlyServiceReport[$key]->device = 'apk_cloud';
            }
        }
    }

    /**
     * 月額サービス計算用
     * @param object  $data
     * @param array   $addMonthlyService
     * @param integer $monthlyId
     * @return unknown
     */
    public function getMonthlyServiceReportCalc($data, $addMonthlyService, $monthlyId = null)
    {
        if ($monthlyId) {
            $addKey =  "{$data->date->format('Ym')}"
                    . "_{$data->app_id}"
                    . "_{$data->device}"
                    . "_{$monthlyId}";
        } else {
            $addKey =  "{$data->date->format('Ym')}"
                    . "_{$data->attr}"
                    . "_{$data->device}";
        }
        $monthlyService          = array_get($addMonthlyService, $addKey, 0);
        $usePoint                = $data->use_point      ?: 0;
        $usePointUser            = $data->use_point_user ?: 0;
        $useMonthlyServiceUser   = $monthlyService ? ($monthlyService->m_use_monthly_service_user ?: 0) : 0;
        $useDuplicateUser        = $monthlyService ? ($monthlyService->m_use_duplicate_user       ?: 0) : 0;
        $mPrice                  = $monthlyService ? ($monthlyService->m_price                    ?: 0) : 0;
        $mRegistUser             = $monthlyService ? ($monthlyService->m_regist_user              ?: 0) : 0;
        $mContinueUser           = $monthlyService ? ($monthlyService->m_continue_user            ?: 0) : 0;
        $mActiveUser             = $monthlyService ? ($monthlyService->m_active_user              ?: 0) : 0;
        $mWithdrawalUser         = $monthlyService ? ($monthlyService->m_withdrawal_user          ?: 0) : 0;
        $mEvictionUser           = $monthlyService ? ($monthlyService->m_eviction_user            ?: 0) : 0;
        $mCreditPrice            = $monthlyService ? ($monthlyService->m_credit_price             ?: 0) : 0;
        $mPointPrice             = $monthlyService ? ($monthlyService->m_point_price              ?: 0) : 0;

        $data->m_price           = $mPrice;
        $data->m_regist_user     = $mRegistUser;
        $data->m_continue_user   = $mContinueUser;
        $data->m_active_user     = $mActiveUser;
        $data->m_withdrawal_user = $mWithdrawalUser;
        $data->m_eviction_user   = $mEvictionUser;
        $data->m_total_use       = $usePoint     + $mPrice;
        $data->m_use_unique_user = $usePointUser + $useMonthlyServiceUser - $useDuplicateUser;
        $data->m_credit_price    = $mCreditPrice;
        $data->m_point_price     = $mPointPrice;

        if (empty($data->m_total_use) || empty($data->active_user)) {
            $data->m_total_arpu  = 0;
        } else {
            $data->m_total_arpu  = round($data->m_total_use / $data->active_user, 3);
        }
        if (empty($data->m_total_use) || empty($data->m_use_unique_user)) {
            $data->m_total_arppu = 0;
        } else {
            $data->m_total_arppu = round($data->m_total_use / $data->m_use_unique_user, 3);
        }
        return $data;
    }

    /**
     * 費用負担元別利用状況レポート（経理）
     * @param array $condition
     * @return object $list
     */
    public function getCostBearerReport($condition = [])
    {
        $appTitleType = $this->getAppTitleType();
        $addActiveUsers = [];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilizationByFloor->getMonthlyList($this->formatSearchQuery($condition));
            $subList = $this->reportUtilizationByFloor->getList($this->formatSearchQuery($condition));
            foreach ($subList as $subData) {
                $addActiveUsers
                    [$subData->date->format('Ym') . '_' . $subData->app_id . '_' . $subData->device] =
                        $subData->active_user;
            }
            $subList = $this->guestReportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmUtilizationByFloor->getMonthlyList($this->formatSearchQuery($condition));
            $subList = $this->guestReportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $thisMonth = date('Y-m-01 00:00:00');

        // 言語リスト取得
        $language_list = $this->getApplicationLanguageList($this->formatSearchQuery($condition));

        // publisher_floor_id と floor_name のマッピングを config より読み込みする 
        $floor_detail_list = config('forms.ReportsMonthly.floorIdMappingList');

        foreach ($list as &$data) {
            if (is_numeric($condition['app_id'])) {
                $data->title = array_get($appTitleType, $data->app_id, '---');
                $addKey = $data->date->format('Ym') . '_' . $data->app_id . '_' . $data->device;
            } else {
                $data->title = array_get($appTitleType, $data->attr, '---');
                $addKey = $data->date->format('Ym') . '_' . $data->attr . '_' . $data->device;
            }

            $data->format_date = $data->date->format('Y/m');

            // ゲームID
            if (isset($this->gameIdList[$data->app_id])) {
                $data->game_id = $this->gameIdList[$data->app_id];
            } else {
                $data->game_id = '';
            }

            $data->format_device = config('forms.ReportsMonthly.deviceType.' . $data->device, '---');

            // サイト
            if (isset($this->siteList[$data->app_id])) {
                $data->site = $this->siteList[$data->app_id];
            } else {
                $data->site = '';
            }

            // 言語
            $data->language = $this->getLanguage($language_list, $data->app_id);

            // クーポン割引後消費ポイント
            $data->use_discount_price = $data->use_point - $data->discount_price;

            // 費用負担元情報取得
            if (!empty($data->publisher_floor_id)) {

                // 費用負担元（フロアID）が想定した値であれば、費用負担元名をセット
                if (array_key_exists($data->publisher_floor_id, $floor_detail_list)) {

                    $data->floor_name = $floor_detail_list[$data->publisher_floor_id];

                }
            }
        }

        return $list;
    }

    /**
     * 利用状況レポート(定期購入)ヘッダー取得
     * 
     * @param array $condition CSV出力メソッド共通によって渡されるが、現在は条件によって制御はしていない
     */
    public function getSubscriptionUsageReportHeader($condition = [])
    {
        return $this->subscriptionUtilizationReportMonthlyService->getCsvHeader();
    }

    /**
     * 利用状況レポート(定期購入)取得
     *
     * @param array $condition 検索条件
     */
    public function getSubscriptionUsageReport($condition = [])
    {
        return $this->subscriptionUtilizationReportMonthlyService->getCsvList($condition);
    }

    /**
     * 利用状況レポート(アプリ外課金)ヘッダー取得
     * 
     * @param array $condition 検索条件
     */
    public function getChargeCenterReportHeader($condition = [])
    {
        return $this->chargeCenterService->getReportsMonthlyHeader($condition);
    }

    /**
     * 利用状況レポート(アプリ外課金)取得
     * 
     * @param array $condition 検索条件
     */
    public function getChargeCenterReport($condition = [])
    {
        $language_list = $this->getApplicationLanguageList($this->formatSearchQuery($condition));

        return $this->chargeCenterService->getReportsMonthly(
            $condition,
            $this->gameIdList,
            $language_list
        );
    }
}
