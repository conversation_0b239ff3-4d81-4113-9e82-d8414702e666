table,models,primary_services_controllers,joins_or_relations,notes
"application","App\\Models\\Freegame\\Application; App\\Models\\FreegameSandbox\\Application","Services: app/Services/GamesService.php; app/Services/SbxApplicationsService.php","Joined by: application_device (app/Models/Freegame/Application.php); unit_application (app/Models/Freegame/UnitApplication.php)","Core social applications. CRUD via GamesService; sandbox create/update via SbxApplicationsService."
"application_protocol","App\\Models\\Freegame\\ApplicationProtocol; App\\Models\\FreegameSandbox\\ApplicationProtocol","Services: app/Services/GamesService.php (basicUpdate forces SSL); app/Services/SbxApplicationsService.php (create/update)","FK: app_id to application.id","SSL flag for gadget endpoints when PC/SP present."
"application_device","App\\Models\\Freegame\\ApplicationDevice; App\\Models\\FreegameSandbox\\ApplicationDevice","Services: app/Services/GamesService.php (getDevice, deviceUpdate); app/Services/SbxApplicationsService.php (create/update/delete)","Joins: application (app/Models/Freegame/Application.php); unit_application.getListWithApplication();","Per-app device rows with status window and attributes (browser_sdk, guest_play, etc.)."
"application_division","App\\Models\\FreegameDeveloper\\ApplicationDivision","Controllers/Services: referenced via model only","Maps: app_id→game_id","Used for developer/game mapping; referenced in validation messages."
"ch_application","App\\Models\\Freegame\\ChApplication","Controllers: app/Http/Controllers/ChGamesController.php; Services: Apply*, BoardService, PresentService, Reports*","unit_application joins to ch_application (app/Models/Freegame/UnitApplication.php)","Channeling applications."
"ch_application_device","App\\Models\\Freegame\\ChApplicationDevice","Controllers/Services: used via model in device contexts","Maps: app_id+device","Device mapping for channeling apps."
"ch_application_division","App\\Models\\FreegameDeveloper\\ChApplicationDivision","Controllers/Services: used via model","Maps: ch_app_id→game_id","Channeling app-to-game mapping."
"unit_application","App\\Models\\Freegame\\UnitApplication","Services: PresentService; PfOwnerReports*; PfManagementContentsReports*; ChargeCenterService","Joins: application; ch_application; cl_application (helper methods)","Bridge entries pointing to app id plus app_type (application/ch_application/cl_application)."
"cl_application","App\\Models\\Freegame\\ClApplication","Controllers: app/Http/Controllers/ClGamesController.php; Services: PresentService, Reports*","unit_application joins to cl_application (app/Models/Freegame/UnitApplication.php)","Client applications."
"cl_application_division","App\\Models\\FreegameDeveloper\\ClApplicationDivision","Controllers/Services: used via model","Maps: cl_app_id→game_id","Client app-to-game mapping."
"cl_application_cloud","(none)","(none)","(none)","No references found; appears unused in this repo."
"application_genre_ref","App\\Models\\Freegame\\ApplicationGenreRef","Services: app/Services/GamesService.php (basicUpdate add/del; getList loads)","Joins: application_main_genre; application_sub_genre","Stores main/sub genre per app; social/channel variants via kind."
"application_apk","App\\Models\\Freegame\\ApplicationApk; App\\Models\\FreegameSandbox\\ApplicationApk","Services: app/Services/SbxApplicationsService.php (create/update); app/Services/GamesService.php (display/timestamps)","FK: app_id to application.id","Android APK metadata and filename; sandbox-only creation when AndroidApp-only device."
"application_apk_cloud","App\\Models\\Freegame\\ApplicationApkCloud","Services: app/Services/GamesService.php (apk_cloud device get/update)","FK: app_id to application.id","APK Cloud maintenance window per app; shown on apk_cloud device settings."
