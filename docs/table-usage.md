# Table Usage Summary

This document summarizes how the key application-related tables are used in the project. For a machine-readable version, see docs/table-usage.csv.

- application
  - Models: [app/Models/Freegame/Application.php](../app/Models/Freegame/Application.php), [app/Models/FreegameSandbox/Application.php](../app/Models/FreegameSandbox/Application.php)
  - Primary usage: [app/Services/GamesService.php](../app/Services/GamesService.php), [app/Services/SbxApplicationsService.php](../app/Services/SbxApplicationsService.php)
  - Joins: `application_device`, `unit_application`

- application_protocol
  - Models: [app/Models/Freegame/ApplicationProtocol.php](../app/Models/Freegame/ApplicationProtocol.php), [app/Models/FreegameSandbox/ApplicationProtocol.php](../app/Models/FreegameSandbox/ApplicationProtocol.php)
  - Usage: set/force SSL in [SbxApplicationsService](../app/Services/SbxApplicationsService.php) and [GamesService::basicUpdate](../app/Services/GamesService.php)

- application_device
  - Models: [app/Models/Freegame/ApplicationDevice.php](../app/Models/Freegame/ApplicationDevice.php), [app/Models/FreegameSandbox/ApplicationDevice.php](../app/Models/FreegameSandbox/ApplicationDevice.php)
  - Usage: device hydration and updates in [GamesService](../app/Services/GamesService.php); lifecycle in [SbxApplicationsService](../app/Services/SbxApplicationsService.php)
  - Joins: with `application` and `unit_application`

- application_division
  - Model: [app/Models/FreegameDeveloper/ApplicationDivision.php](../app/Models/FreegameDeveloper/ApplicationDivision.php)
  - Maps `app_id` to `game_id`

- ch_application / ch_application_device / ch_application_division
  - Models: [ChApplication](../app/Models/Freegame/ChApplication.php), [ChApplicationDevice](../app/Models/Freegame/ChApplicationDevice.php), [ChApplicationDivision](../app/Models/FreegameDeveloper/ChApplicationDivision.php)
  - Usage: channeling flows ([ChGamesController](../app/Http/Controllers/ChGamesController.php)), unit joins ([UnitApplication](../app/Models/Freegame/UnitApplication.php))

- unit_application
  - Model: [app/Models/Freegame/UnitApplication.php](../app/Models/Freegame/UnitApplication.php)
  - Usage: reporting and present services; helper joins to application/ch_application/cl_application

- cl_application / cl_application_division
  - Models: [ClApplication](../app/Models/Freegame/ClApplication.php), [ClApplicationDivision](../app/Models/FreegameDeveloper/ClApplicationDivision.php)
  - Usage: client app flows ([ClGamesController](../app/Http/Controllers/ClGamesController.php)), unit joins

- cl_application_cloud
  - No usage found in repository

- application_genre_ref
  - Model: [app/Models/Freegame/ApplicationGenreRef.php](../app/Models/Freegame/ApplicationGenreRef.php)
  - Usage: join to main/sub genre; managed in [GamesService::basicUpdate](../app/Services/GamesService.php)

- application_apk
  - Models: [app/Models/Freegame/ApplicationApk.php](../app/Models/Freegame/ApplicationApk.php), [app/Models/FreegameSandbox/ApplicationApk.php](../app/Models/FreegameSandbox/ApplicationApk.php)
  - Usage: created/updated in sandbox service; displayed in [GamesService](../app/Services/GamesService.php)

- application_apk_cloud
  - Model: [app/Models/Freegame/ApplicationApkCloud.php](../app/Models/Freegame/ApplicationApkCloud.php)
  - Usage: apk_cloud device maintenance windows in [GamesService](../app/Services/GamesService.php)
