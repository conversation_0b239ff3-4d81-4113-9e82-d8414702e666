{{--*/ $info = $infoList[0]; /*--}}
@include('partials.errormessage', ['errors' => collect($overallErrors)])
<table class="c-table info-table v-top info-table-image mg-b12">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr>
            <th class="tx-12">
                <p class="tx-14 mg-b6">審査完了画像</p>
                <span class="fw-normal">
                    <p>掲載可能枚数{{ $info['postMaxNum'] }}枚</p>
                    <p>登録可能枚数{{ $info['examMaxNum'] }}枚</p>
                </span>
            </th>
            <td>
                <ul>
                    {{-- 審査済みイメージデータのリスト = $info['examOkList'] --}}
                    @for ($num = 0; $num < $info['examMaxNum']; $num++) 
                        @if (isset($info['examOkList'][$num])) {{-- イメージの情報 = $info['examOkList'][$num] --}} 
                            <li class="clearfix pd-b10 item-image">
                                <div class="float-l mg-r10 pic-image pic-image-manifest-l">
                                    <a href="{{ URL::route('GameImage.show', ['id' => $info['examOkList'][$num]->id])}}" class="fn-fancybox iframe">
                                        <img src="{{ $info['examOkList'][$num]->image_url}}" class="{{$info['examOkList'][$num]->id}}">
                                    </a>
                                </div>
                                <div class="float-l">
                                    {{--アップロード済み--}}
                                    @if ($info['examOkList'][$num]->post_id == 1)
                                        <p class="mg-b10"><a href="" class="button is-primary is-disabled">画像使用中</a></p>
                                    @else
                                        {{--アップロード可能--}}
                                        <p class="mg-b10"><a href="javascript:void(0);" class="button is-primary confirm-button" data-form="poststore-form" data-id="{{$info['examOkList'][$num]->id}}">使用する</a></p>
                                    @endif
                                    <p>
                                        <a href="" class="button confirm-button mg-b10" data-form="postupdate-form" data-id="{{$info['examOkList'][$num]->id}}">
                                            <i class="fa fa-refresh"></i>更新
                                        </a>
                                    </p>
                                    <p>
                                        <a href="{{ URL::route('GameImage.deleteconfirm', $info['examOkList'][$num]->id) }}" class="button is-danger fn-fancybox iframe mg-b10">
                                            <i class="fa fa-trash-o"></i>削除
                                        </a>
                                    </p>
                                </div>
                            </li>
                        @else
                        {{--未登録--}}
                        <li class="clearfix pd-t10 pd-b10">
                            <div class="float-l">
                                <p class="c-notregist">画像<br>未登録</p>
                            </div>
                        </li>
                        {{--@if (isset($info['examOkList'][$num]))--}}
                        @endif
                    @endfor
                </ul>
            </td>
        </tr>
    </tbody>
</table>
