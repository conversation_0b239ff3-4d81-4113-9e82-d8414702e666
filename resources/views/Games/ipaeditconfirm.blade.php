@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', ['breadcrumbs' => array_merge($menuName, [[$screenName, 'Games.index', ['search' => 'on']], $subScreenName['ipa'], '編集確認'])])
    <!-- [#gnavi] -->
</header>

@include('partials.errormessage', ['errors' => $errors])

{!! Form::open(['route' => 'Games.ipa.update', 'id' => 'ipaupdate']) !!}

{!! Form::hidden('id', $data->app_id) !!}
{!! Form::hidden('adp_id', $data->import_app['adp_id']) !!}
{!! Form::hidden('adp_ver_id', $data->import_app['adp_ver_id']) !!}

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $subScreenName['ipa'] }}：公開確認</h2>
        <p class="mg-b10 tx-16 bold">▼{{ $appTitleType[$data->app_id] }}</p>
        <p class="mg-b10 tx-14 bold">{{{ $subScreenName['ipa'] }}}</p>
        <div class="c-searchform down_triangle">
            <p class="mg-b10 tx-14"><span class="bold">元ファイル</span>（最終更新日時：{{ empty($data->public_app['published_at']) ? 'なし' : $data->public_app['published_at'] }}）</p>
            <table class="c-table">
                <colgroup>
                    <col class="col-m">
                    <col class="null">
                </colgroup>
                <tbody>
                    <tr>
                        <th>バンドルバージョン</th>
                        <td>{{ empty($data->public_app['bundle_version']) ? '' : $data->public_app['bundle_version'] }}</td>
                    </tr>
                    <tr>
                        <th>バージョンコード</th>
                        <td>{{ empty($data->public_app['short_version']) ? '' : $data->public_app['short_version'] }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="c-searchform">
            <p class="mg-b10 tx-14"><span class="bold">次公開ファイル</span>（取込日時：{{ empty($data->import_app['import_at']) ? 'なし' : $data->import_app['import_at'] }}）</p>
            <table class="c-table">
                <colgroup>
                    <col class="col-m">
                    <col class="null">
                </colgroup>
                <tbody>
                <tr>
                    <tr>
                        <th>バンドルバージョン</th>
                        <td>{{ empty($data->import_app['bundle_version']) ? '' : $data->import_app['bundle_version'] }}</td>
                    </tr>
                    <tr>
                        <th>バージョンコード</th>
                        <td>{{ empty($data->import_app['short_version']) ? '' : $data->import_app['short_version'] }}</td>
                    </tr>
                    <tr>
                        <th>ステータス</th>
                        <td>
                            @if ($data->enabledPublish)
                                公開可能
                            @else
                                公開不可
                            @endif
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        @if ($data->enabledPublish)
        <div class="center mg-b10">
            <input type="checkbox" id="confirmEditedIpaResults" name="checkbox" value="">
            <label for="confirmEditedIpaResults">公開内容を確認しました</label>
        </div>
        <div class="center">
            <a href="{{ URL::route('Games.ipa.edit', ['id' => $data->app_id]) }}" class="button is-medium"><i class="fa fa-reply"></i>戻る</a>
            <button type='button' id="ipaSubmitButton" class="button is-primary is-medium is-disabled"><i class="fa fa-circle-o"></i>公開</button>
        </div>
        @else
        <div class="center">
            <a href="{{ URL::route('Games.ipa.edit', ['id' => $data->app_id]) }}" class="button is-medium"><i class="fa fa-reply"></i>戻る</a>
        </div>
        @endif
    </div>
</section>

{!! Form::close() !!}

@endsection
