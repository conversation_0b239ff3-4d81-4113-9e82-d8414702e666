@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', ['breadcrumbs' => array_merge($menuName, [[$screenName, 'Games.index', ['search' => 'on']], $subScreenName['ipa'], '編集'])])
    <!-- [#gnavi] -->
</header>

@include('partials.errormessage', ['errors' => $errors])

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $subScreenName['ipa'] }}：編集</h2>
        <p class="mg-b10 tx-16 bold">▼{{ $appTitleType[$data->app_id] }}</p>
        <p class="mg-b10 tx-14"><span class="bold">{{{ $subScreenName['ipa'] }}}</span>（最終更新日時：{{ empty($data->public_app['published_at']) ? 'なし' : $data->public_app['published_at'] }}）</p>
        <table class="c-table">
            <colgroup>
                <col class="col-xxs">
                <col class="col-s">
                <col style="width:100%;">
            </colgroup>
            <tbody>
                <tr>
                    <th colspan="2">バンドルID</th>
                    <td>{{ $data->bundle_id }}</td>
                </tr>
                <tr>
                    <th colspan="2">バンドルバージョン</th>
                    <td>{{ empty($data->public_app['bundle_version']) ? '' : $data->public_app['bundle_version'] }}</td>
                </tr>
                <tr>
                    <th colspan="2">バージョンコード</th>
                    <td>{{ empty($data->public_app['short_version']) ? '' : $data->public_app['short_version'] }}</td>
                </tr>
                <tr>
                    <th rowspan="4">次公開</th>
                    <th>バンドルバージョン</th>
                    <td>{{ empty($data->import_app['bundle_version']) ? '' : $data->import_app['bundle_version'] }}</td>
                </tr>
                <tr>
                    <th>バージョンコード</th>
                    <td>{{ empty($data->import_app['short_version']) ? '' : $data->import_app['short_version'] }}</td>
                </tr>
                <tr>
                    <th>取込日時</th>
                    <td>{{ empty($data->import_app['import_at']) ? '' : $data->import_app['import_at'] }}</td>
                </tr>
                <tr>
                    <th>ステータス</th>
                    <td>
                        @if ($data->enabledPublish)
                            公開可能
                        @else
                            公開不可
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="c-searchform">
            <p class="tx-14 mg-b10">iOS IPAファイル公開</p>
            <p class="tx-14 mg-b10 tx-gray">・取り込み済みの最新IPAを公開します。</p>
            <p class="tx-14 mg-b10 tx-gray">・次画面の内容を確認の上、公開してください。</p>
            <a href="{{ URL::route('Games.ipa.editconfirm', ['id' => $data->app_id]) }}" class="button">
                公開確認
            </a>
            <hr>
            <p class="tx-14 mg-b10"> Marketplace Token 発行</p>
            <p class="tx-14 mg-b10 tx-gray">・Marketplace Token を発行します。</p>
            {!! Form::open(['route' => ['Games.ipa.edit', $data->app_id], 'id' => 'ipaedit']) !!}
                {!! csrf_field() !!}
                {!! Form::hidden('id', $data->id) !!}
                <div class="mg-l10">
                    Apple Developer ID：{!! Form::text('appleDeveloperId', null, ['class' => 'w50', 'maxlength' => '45']) !!}
                </div>
                <a href="#" class="button mg-t10" data-form="ipaedit">発行</a>
            {!! Form::close() !!}
            @if (!empty($marketplaceToken))
                <div class="c-searchform mg-t10">
                    @if (empty($marketplaceToken['error']))
                        <table class="c-table mg-b0">
                            <colgroup>
                                <col class="col-xs">
                                <col class="null">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <th>Marketplace Token</th>
                                    <td>{{ $marketplaceToken['marketplace_token'] }}</td>
                                </tr>
                                <tr>
                                    <th>有効期限</th>
                                    <td>{{ $marketplaceToken['expire'] }}</td>
                                </tr>
                            </tbody>
                        </table>
                    @else
                        発行に失敗しました({{ $marketplaceToken['error']['code'] }})。
                    @endif
                </div>
            @endif
        </div>
        <div class="center">
            <a href="{{ URL::route('Games.index', ['search' => 'on']) }}" class="button is-medium"><i class="fa fa-reply"></i>戻る</a>
        </div>
    </div>
</section>

@endsection
