<link rel="stylesheet" href="{{ cache_asset('/css/board/board-edit-modal.css') }}">

<div style="display: none;">
    <section id="editCommentModal" class="board-edit-modal">
        <div class="board-edit-modal__content fancybox-modal-container">
            <!-- ヘッダー -->
            <div class="board-edit-modal__header">
                <div class="board-edit-modal__header-title">
                    <img src="{{ asset('images/board/fa-file-text-o.svg') }}" alt="掲示板アイコン" width="21" height="21">
                    <span>掲示板 - {{ $data->title }}</span>
                </div>
                <div class="board-edit-modal__header-line"></div>
            </div>

            <div class="board-edit-modal__input-section">
                <div id="replyPreview" class="existing-post d-none">
                    <div class="post-item">
                        <div class="post-header">
                            <span id="replyPreviewNumber" class="post-number">1</span>
                            <i class="fa fa-user"></i>
                            <span id="replyPreviewAuthor" class="post-author"></span>
                            <span id="replyPreviewDate" class="post-date"></span>
                        </div>
                        <div class="post-content"><p id="replyPreviewBody"></p></div>
                        <div id="replyPreviewFiles" class="post-attachments-container"></div>
                    </div>
                </div>
                <form id="editCommentForm" class="board-edit-modal__form">
                    <div id="editCommentError" class="board-edit-modal__error d-none"></div>
                    <div class="board-edit-modal__input-group">
                        <div class="board-edit-modal__input-container">
                            <textarea id="editCommentContent" class="board-edit-modal__textarea" placeholder="コメント内容を入力してください。"></textarea>
                        </div>

                        <div class="board-edit-modal__attachments" id="allFilesSection">
                            <div id="allFilesList" class="attachment-display">
                                <!-- Existing and new files render here -->
                            </div>
                        </div>
                    </div>

                    <input type="file" id="fileInput" style="display: none;" multiple>
                </form>
            </div>

            <div class="board-edit-modal__button-section">
                <div class="board-edit-modal__left-buttons">
                    <button type="button" class="board-edit-modal__button board-edit-modal__button--secondary" id="selectFileBtn">
                        <i class="fa fa-file"></i>
                        <span>ファイルを選択</span>
                    </button>
                    <button type="button" class="board-edit-modal__button board-edit-modal__button--secondary" id="quoteInsertBtn" style="display: none;">
                        <i class="fa fa-quote-right"></i>
                        <span>引用</span>
                    </button>
                </div>
                <div class="board-edit-modal__right-buttons">
                    <button type="button" class="board-edit-modal__button board-edit-modal__button--secondary" onclick="parent.jQuery.fancybox.close();">キャンセル</button>
                    <button type="button" class="board-edit-modal__button board-edit-modal__button--primary edit-comment-btn" id="saveCommentBtn">保存</button>
                </div>
            </div>
        </div>
    </section>
</div>
