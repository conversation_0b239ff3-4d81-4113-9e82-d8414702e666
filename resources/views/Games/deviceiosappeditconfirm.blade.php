@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', ['breadcrumbs' => array_merge($menuName, [[$screenName, 'Games.index', ['search' => 'on']], $subScreenName['android_app'], '編集確認'])])
    <!-- [#gnavi] -->
</header>

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $screenName . $subScreenName['ios_app'] }}：編集確認</h2>
        <p class="mg-b10 tx-16 bold">▼{{ $appTitleType[request('id')] }}</p>
        <p class="mg-b10 tx-14 bold">{{{ $subScreenName['ios_app'] }}}</p>
        <table class="c-table">
            <colgroup>
                <col class="col-s">
                <col class="null">
            </colgroup>
            <tbody>
                <tr>
                    <th>紹介文</th>
                    <td>
                        {!! convert_html(request('description')) !!}
                    </td>
                </tr>
                <tr>
                    <th>紹介文（20文字）</th>
                    <td>
                        {!! convert_html(request('description_middle')) !!}
                    </td>
                </tr>
                <tr>
                    <th>ゲームの遊び方</th>
                    <td>
                        {!! convert_html(request('how_to_play')) !!}
                    </td>
                </tr>
                <tr>
                    <th>対応機種</th>
                    <td>
                        {!! nl2br(e(request('restrictions'))) !!}
                    </td>
                </tr>
            </tbody>
            <!-- /.c-table -->
        </table>

        <div class="center">
            <a href="#" class="button is-medium" data-form="deviceiosappedit"><i class="fa fa-reply"></i>修正</a>
            <a href="#" class="button is-primary is-medium is-success" data-form="deviceiosappupdate"><i class="fa fa-circle-o"></i>確定</a>
        </div>

        <!-- /.c-area -->
    </div>
</section>

{!! Form::open(['route' => 'Games.device.ios.app.update', 'id' => 'deviceiosappupdate']) !!}

{!! Form::hidden('id', request('id')) !!}
{!! Form::hidden('description', request('description')) !!}
{!! Form::hidden('description_middle', request('description_middle')) !!}
{!! Form::hidden('how_to_play', request('how_to_play')) !!}
{!! Form::hidden('restrictions', request('restrictions')) !!}

{!! Form::close() !!}

{!! Form::open(['route' => ['Games.device.ios.app.edit', request('id')], 'id' => 'deviceiosappedit']) !!}

{!! Form::hidden('id', request('id')) !!}
{!! Form::hidden('description', request('description')) !!}
{!! Form::hidden('description_middle', request('description_middle')) !!}
{!! Form::hidden('how_to_play', request('how_to_play')) !!}
{!! Form::hidden('restrictions', request('restrictions')) !!}

{!! Form::close() !!}

@endsection
