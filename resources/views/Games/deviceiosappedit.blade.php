@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', ['breadcrumbs' => array_merge($menuName, [[$screenName, 'Games.index', ['search' => 'on']], $subScreenName['ios_app'], '編集'])])
    <!-- [#gnavi] -->
</header>

@include('partials.errormessage', ['errors' => $errors])

{!! Form::open(['route' => 'Games.device.ios.app.editconfirm', 'id' => 'deviceiosappeditconfirm']) !!}
{!! csrf_field() !!}
{!! Form::hidden('id', $data->app_id) !!}

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $screenName . $subScreenName['ios_app'] }}：編集</h2>
        <p class="mg-b10 tx-16 bold">▼{{ $appTitleType[$data->app_id] }}</p>
        <p class="mg-b10 tx-14 bold">{{{ $subScreenName['ios_app'] }}}</p>
        <table class="c-table">
            <colgroup>
                <col class="col-s">
                <col class="null">
            </colgroup>
            <tbody>
                <tr {!! error_class('description') !!}>
                    <th>紹介文<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::textarea('description', request('description', $data->description), ['id' => 'description', 'rows' => '8', 'placeholder' => '紹介文を入力', 'maxlength' => '6000']) !!}
                    </td>
                </tr>
                <tr {!! error_class('description_middle') !!}>
                    <th>紹介文（20文字）<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::text('description_middle', request('description_middle', $data->description_middle), ['id' => 'description_middle', 'class' => 'w80', 'placeholder' => '紹介文（20文字）を入力', 'maxlength' => '400']) !!}
                    </td>
                </tr>
                <tr {!! error_class('how_to_play') !!}>
                    <th>ゲームの遊び方</th>
                    <td>
                        {!! Form::textarea('how_to_play', request('how_to_play', $data->how_to_play), ['rows' => '8', 'placeholder' => 'ゲームの遊び方を入力', 'maxlength' => '800']) !!}
                    </td>
                </tr>
                <tr {!! error_class('restrictions') !!}>
                    <th>対応機種<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::textarea('restrictions', request('restrictions', $data->restrictions), ['rows' => '8', 'placeholder' => '対応機種を入力', 'maxlength' => '5000']) !!}
                    </td>
                </tr>
            </tbody>
            <!-- /.c-table -->
        </table>

        <div class="center">
            <a href="{{ URL::route('Games.index', ['search' => 'on']) }}" class="button is-medium"><i class="fa fa-reply"></i>戻る</a>
            <a href="#" class="button is-warning is-medium is-warning" data-form="deviceiosappeditconfirm"><i class="fa fa-check"></i>確認</a>
        </div>

        <!-- /.c-area -->
    </div>
</section>

{!! Form::close() !!}

@endsection
