@include('Games.partials.components.common.dueDateHeader', [
    'title' => 'ゲーム情報入力',
    'dueDate' => $preregistration->getGameInformation('completionDate', null, '未設定'),
    'scrollId' => 'gameInfoForm',
])

<section>
    <div class="c-area is-error" id="gameinformation-errors" style="display: none;">
        <p class="error-title"><i class="fa fa-exclamation-triangle"></i>エラー</p>
    </div>
</section>

{!! Form::open(['route' => ['Games.apply.preregistration.gameinformation',$data->id, $device], 'id' => 'gameinformation-form', 'class' => 'fn-form-check']) !!}
<table class="c-table info-table">
    <colgroup>
        <col class="col-xxl">
        <col class="null">
        <col class="col-xxs">
    </colgroup>

    <tbody>
    @if($preregistration->isTarget($device, 'gameInformation', 'contactMailAddress'))
    <tr data-errors-keys="contact_mail_address">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '問い合わせ先メールアドレス',
                'isRequired' => true,
                'tooltipText' => $preregistration->getTooltipMsg($tab, $device, 'gameInformation', 'contactMailAddress'),
            ])
        </th>
        <td class="fn-contactMailAddress-input">
            {!! Form::hidden('contact_mail_address', 'true') !!}
            @include('Games.partials.components.common.editPageButton', ['url' => $preregistration->makeMailEditUrl($data->id)])
        </td>
        <td class="fn-contactMailAddress-status" data-status="{{ $preregistration->getGameInformation('contactMailAddress','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'gameIntroduction'))
    <tr data-errors-keys="game_introduction">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '紹介文（20文字）',
                'isRequired' => true,
            ])
        </th>
        <td class="fn-gameIntroduction-input">
            {!! Form::hidden('game_introduction', 'true') !!}
            @include('Games.partials.components.common.editPageButton', ['url' => $preregistration->makeGameInfoEditUrl($data->id, $device)])
        </td>
        <td class="fn-gameIntroduction-status" data-status="{{ $preregistration->getGameInformation('gameIntroduction','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'channelingGameIntroduction'))
    <tr data-errors-keys="channeling_game_introduction">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '紹介文（20文字）',
                'isRequired' => true,
            ])
        </th>
        <td class="fn-channelingGameIntroduction-input">
            {!! Form::hidden('channeling_game_introduction', 'true') !!}
            @include('Games.partials.components.common.editPageButton', ['url' => $preregistration->makeGameInfoEditUrl($data->id, $device)])
        </td>
        <td class="fn-channelingGameIntroduction-status" data-status="{{ $preregistration->getGameInformation('channelingGameIntroduction','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'gameIntroductionDetail'))
    <tr data-errors-keys="game_introduction_detail">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '紹介文の詳細',
                'isRequired' => true,
            ])
        </th>
        <td class="fn-gameIntroductionDetail-input">
            {!! Form::hidden('game_introduction_detail', 'true') !!}
            @include('Games.partials.components.common.editPageButton', ['url' => $preregistration->makeGameInfoEditUrl($data->id, $device)])
        </td>
        <td class="fn-gameIntroductionDetail-status" data-status="{{ $preregistration->getGameInformation('gameIntroductionDetail','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'supportedDevice'))
    <tr data-errors-keys="supported_device">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '対応機種',
                'isRequired' => true,
            ])
        </th>
        <td class="fn-supportedDevice-input">
            {!! Form::hidden('supported_device', 'true') !!}
            @include('Games.partials.components.common.editPageButton', ['url' => $preregistration->makeGameInfoEditUrl($data->id, $device)])
        </td>
        <td class="fn-supportedDevice-status" data-status="{{ $preregistration->getGameInformation('supportedDevice','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'clientGameIntroduction'))
    <tr data-errors-keys="client_game_introduction_text">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '製品紹介',
                'isRequired' => true,
                'tooltipText' => $preregistration->getTooltipMsg($tab, $device, 'gameInformation', 'clientGameIntroduction'),
            ])
        </th>
        <td class="fn-clientGameIntroduction-input">
            {!! Form::hidden('client_game_introduction_text', '') !!}
            <textarea name="client_game_introduction_text"
                      maxlength="2048"
                      rows="8"
                      cols="50"
                      class="signature w100">{{ $preregistration->getGameInformation('clientGameIntroduction','clientGameIntroductionText','') }}</textarea>
        </td>
        <td class="fn-clientGameIntroduction-status" data-status="{{ $preregistration->getGameInformation('clientGameIntroduction','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'preReleaseGameAnnouncement'))
    <tr data-errors-keys="pre_release_game_announcement">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => 'リリース直前ゲームとして公表',
                'isRequired' => true,
                'tooltipText' => $preregistration->getTooltipMsg($tab, $device, 'gameInformation', 'preReleaseGameAnnouncement'),
            ])
        </th>
        <td class="fn-preReleaseGameAnnouncement-input">
            {!! Form::hidden('pre_release_game_announcement', '') !!}
            <input type="radio" id="preReleaseGameAnnouncement_true" name="pre_release_game_announcement" value="true" {{$preregistration->getGameInformation('preReleaseGameAnnouncement','isPublishing') === true ? 'checked' : ''}} >
            <label for="preReleaseGameAnnouncement_true">公表する</label>
            <input type="radio" id="preReleaseGameAnnouncement_false" name="pre_release_game_announcement" value="false" {{$preregistration->getGameInformation('preReleaseGameAnnouncement','isPublishing') === false ? 'checked' : ''}} >
            <label for="preReleaseGameAnnouncement_false">公表しない</label>
        </td>
        <td class="fn-preReleaseGameAnnouncement-status" data-status="{{ $preregistration->getGameInformation('preReleaseGameAnnouncement','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'gameInformation', 'recommendationAgeDivision'))
    <tr data-errors-keys="recommendation_age_division">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '推奨年齢区分',
                'isRequired' => true,
                'tooltipText' => $preregistration->getTooltipMsg($tab, $device, 'gameInformation', 'recommendationAgeDivision'),
            ])
        </th>
        <td class="fn-recommendationAgeDivision-input">
            {!! Form::hidden('recommendation_age_division', '') !!}
            <input type="radio" id="ageDivision_4" name="recommendation_age_division" value="4+" {{$preregistration->getGameInformation('recommendationAgeDivision','divisionValue') === '4+' ? 'checked' : ''}} >
            <label for="ageDivision_4">4+</label>
            <input type="radio" id="ageDivision_9" name="recommendation_age_division" value="9+" {{$preregistration->getGameInformation('recommendationAgeDivision','divisionValue') === '9+' ? 'checked' : ''}} >
            <label for="ageDivision_9">9+</label>
            <input type="radio" id="ageDivision_12" name="recommendation_age_division" value="12+" {{$preregistration->getGameInformation('recommendationAgeDivision','divisionValue') === '12+' ? 'checked' : ''}} >
            <label for="ageDivision_12">12+</label>
            <input type="radio" id="ageDivision_17" name="recommendation_age_division" value="17+" {{$preregistration->getGameInformation('recommendationAgeDivision','divisionValue') === '17+' ? 'checked' : ''}} >
            <label for="ageDivision_17">17+</label>
        </td>
        <td class="fn-recommendationAgeDivision-status" data-status="{{ $preregistration->getGameInformation('recommendationAgeDivision','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    </tbody>
</table>
{!! csrf_field()  !!}
{!! Form::close() !!}

@include('Games.partials.components.common.submitForReviewButton', ['id' => 'gameinformation'])
