@if($device == 'pc' || $device == 'sp')

@include('Games.partials.components.common.dueDateHeader', [
    'title' => '【検証】サンドボックス環境 事前登録検証',
    'dueDate' => $preregistration->getSandboxVerification('completionDate', null, '未設定'),
    'scrollId' => 'performanceCheck',
])

<section>
    <div class="c-area is-error" id="sandboxverification-errors" style="display: none;">
        <p class="error-title"><i class="fa fa-exclamation-triangle"></i>エラー</p>
    </div>
</section>

{!! Form::open(['route' => ['Games.apply.preregistration.sandboxverification',$data->id, $device], 'id' => 'sandboxverification-form', 'class' => 'fn-form-check']) !!}
<table class="c-table info-table">
    <colgroup>
        <col class="col-xxl">
        <col class="null">
        <col class="col-xxs">
    </colgroup>

    <tbody>
    @if($preregistration->isTarget($device, 'sandboxVerification', 'sandboxPreRegistrationVerifications'))
    <tr data-errors-keys="is_user_type_special_processing_done">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => 'サンドボックス環境への繋ぎ込み',
                'isRequired' => true,
            ])
        </th>
        <td class="fn-sandboxPreRegistrationVerifications-input">
            {!! Form::hidden('is_user_type_special_processing_done', '') !!}
            <input type="checkbox" id="is_user_type_special_processing_done" name="is_user_type_special_processing_done" value="true" {{$preregistration->getSandboxVerification('sandboxPreRegistrationVerifications','isUserTypeSpecialProcessingDone') === true ? 'checked' : ''}}>
            <label for="is_user_type_special_processing_done">userTypeによる判別処理の動作を確認した</label>
        </td>
        <td class="fn-sandboxPreRegistrationVerifications-status" data-status="{{ $preregistration->getSandboxVerification('sandboxPreRegistrationVerifications','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    @if($preregistration->isTarget($device, 'sandboxVerification', 'sandboxTestGameAppId'))
    <tr data-errors-keys="sandbox_test_game_app_id_text">
        <th>
            @include('Games.partials.components.common.itemTitle', [
                'title' => '繋ぎ込んだテストゲームのアプリID',
                'isRequired' => true,
            ])
        </th>
        <td class="fn-sandboxTestGameAppId-input">
            {!! Form::hidden('sandbox_test_game_app_id_text', '') !!}
            <input type="text" name="sandbox_test_game_app_id_text" value="{{ $preregistration->getSandboxVerification('sandboxTestGameAppId','sandboxTestGameAppIdText','') }}" placeholder="テキスト内容をここに入力してください。" class="w100" maxlength="11"/>
        </td>
        <td class="fn-sandboxTestGameAppId-status" data-status="{{ $preregistration->getSandboxVerification('sandboxTestGameAppId','applyStatus','unapplied') }}">
            @include('Games.partials.components.common.reviewStatusLabel')
        </td>
    </tr>
    @endif
    </tbody>
</table>
{!! csrf_field()  !!}
{!! Form::close() !!}

@include('Games.partials.components.common.submitForReviewButton', ['id' => 'sandboxverification'])

@endif