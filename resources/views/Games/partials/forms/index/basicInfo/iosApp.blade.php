<p class="mg-b10 tx-14 bold" data-scroll-id="{{ $application->id }}.ios_app">
    {{ $title }}
</p>

<table class="c-table info-table">
    <colgroup>
        <col class="col-s">
        <col class="null">
    </colgroup>

    <tbody>
    <tr>
        <th>URL</th>
        <td>
            @if(!empty($application->general))
                {{-- */ $url = env('HTTP_GAMES_GENERAL_URL') /* --}}
            @elseif(!empty($application->adult))
                {{-- */ $url = env('HTTP_GAMES_ADULT_URL') /* --}}
            @else
                {{-- */ $url = 'http://localhost' /* --}}
            @endif
            {{-- */ $url .= '/detail/' . $application->title_id /* --}}
            <a href="{{ $url }}" target="_blank">{{ $url }}</a>
        </td>
    </tr>
    <tr>
        <th>紹介文</th>
        <td>{!! convert_html($application->device->ios_app->description) !!}</td>
    </tr>
    <tr>
        <th>紹介文（20文字）</th>
        <td>{!! convert_html($application->device->ios_app->description_middle) !!}</td>
    </tr>
    <tr>
        <th>ゲームの遊び方</th>
        <td>{!! nl2br(e($application->device->ios_app->how_to_play)) !!}</td>
    </tr>
    <tr>
        <th>ゲーム画像</th>
        <td>
            @if($application->device->ios_app->image->count())
                @foreach($application->device->ios_app->image as $subData)
                    {{-- */ $img = env('HTTP_PICS_GENERAL_URL') . env('DOCUMENT_ROOT_PICS_FREEGAME', '/freegame') . sprintf('/app/%s/%ssp_%02d.jpg', $application->id, $application->id, $subData->post_id) /* --}}
                    <img src="{{ $img }}" alt="{{ $application->title }}" width="200">
                @endforeach
            @else
                未設定
            @endif
        </td>
    </tr>
    <tr>
        <th>対応機種</th>
        <td>{!! nl2br(e($application->device->ios_app->restrictions)) !!}</td>
    </tr>
    <tr>
        <th>QRコード</th>
        <td>
            <div class="qrcode" data-text="{{ $url . $qrCodeQuery }}"></div>
        </td>
    </tr>
    </tbody>
    <!-- /.c-table -->
</table>

<div class="clearfix mg-b10">
    @if(auth_is_sap())
        <div class="float-r">
            <a href="{{ URL::route('Games.device.ios.app.edit', ['id' => $application->id]) }}" class="button">
                <i class="fa fa-pencil"></i>編集
            </a>
        </div>
    @endif
</div>
