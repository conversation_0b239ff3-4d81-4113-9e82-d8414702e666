<p class="mg-b10 tx-14" data-scroll-id="{{ $application->id }}.ipa">
    <span class='bold'>{{ $title }}</span>
    （最終更新日時：{{ empty($application->ipa->public_app['published_at']) ? 'なし' : $application->ipa->public_app['published_at'] }}）
</p>

<table class="c-table info-table mg-b30">
    <colgroup>
        <col class="col-xxxs">
        <col class="col-xs">
        <col class="null">
    </colgroup>

    <tbody>
    <tr>
        <th colspan="2">バンドルID</th>
        <td>{{ $application->ipa->bundle_id }}</td>
    </tr>
    <tr>
        <th colspan="2">バンドルバージョン</th>
        <td>{{ empty($application->ipa->public_app['bundle_version']) ? '' : $application->ipa->public_app['bundle_version'] }}</td>
    </tr>
    <tr>
        <th colspan="2">バージョンコード</th>
        <td>{{ empty($application->ipa->public_app['short_version']) ? '' : $application->ipa->public_app['short_version'] }}</td>
    </tr>
    <tr>
        <th rowspan="4">次公開</th>
        <th>バンドルバージョン</th>
        <td>{{ empty($application->ipa->import_app['bundle_version']) ? '' : $application->ipa->import_app['bundle_version'] }}</td>
    </tr>
    <tr>
        <th>バージョンコード</th>
        <td>{{ empty($application->ipa->import_app['short_version']) ? '' : $application->ipa->import_app['short_version'] }}</td>
    </tr>
    <tr>
        <th>取込日時</th>
        <td>{{ empty($application->ipa->import_app['import_at']) ? '' : $application->ipa->import_app['import_at'] }}</td>
    </tr>
    <tr>
        <th>ステータス</th>
        <td>
            @if ($application->ipa->enabledPublish)
                公開可能
            @else
                公開不可
            @endif
        </td>
    </tr>
    </tbody>
    <!-- /.c-table -->
</table>

<div class="clearfix mg-b10">
    @if(auth_is_sap())
        <div class="float-r">
            <a href="{{ URL::route('Games.ipa.edit', ['id' => $application->id]) }}" class="button">
                <i class="fa fa-pencil"></i>編集
            </a>
        </div>
    @endif
</div>
