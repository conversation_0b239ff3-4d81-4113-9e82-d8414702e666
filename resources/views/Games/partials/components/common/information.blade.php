<div class="c-searchform mg-b20">
    <div class="d-flex ai-center jc-between mg-b12">
        <div class="tx-14 bold">
            掲示板・トピック
        </div>

        <div class="right">
            <a href="{{ $applyCommon->makeGameBoardUrl($appId, $device) }}" class="button is-primary">
                <i class="fa fa-angle-double-right"></i>掲示板
            </a>
        </div>
    </div>

    <table class="c-table mg-0">
        <colgroup>
            <col class="col-xxxxs">
            <col class="col-xxxs">
            <col class="null">
            <col class="col-xs">
        </colgroup>

        <tbody>
        @forelse($informationTopics as $topic)
            <tr>
                <td><i class="fa fa-thumb-tack"></i></td>
                <td class="center">{{ $topic['messageNumber'] }}</td>
                <td class="nowrap">
                    <a href="{{ $applyCommon->makeGameBoardUrl($appId, $device) }}">
                        {!! nl2br($topic['contentBody']) !!}
                    </a>
                </td>
                <td class="nowrap tx-lightgray right">
                    {{ $topic['registerDatetime'] }}
                </td>
            </tr>
        @empty
            <p class="center">現在、ピン留めされたトピックはありません。</p>
        @endforelse
        </tbody>
    </table>
</div>
