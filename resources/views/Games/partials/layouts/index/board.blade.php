<link rel="stylesheet" href="{{ cache_asset('/css/board/board-inline.css') }}">

<div class="bg-white pd-t20 pd-b20 pd-l10 pd-r10">
    <div class="d-flex fd-col gap-20">
        @if($releaseStatus === 'released')
            <div class="center tx-red">
                ※DMMへの問い合わせは
                <a href="{{ env('HTTP_DEVELOPER_PORTAL_URL') }}" target="_blank">Developer Portal</a>
                のその他のツールより、「お問い合わせ」からお願いいたします。
            </div>
        @endif

        @include('Games.partials.components.common.board',
        ['appId' => $data->id, 'informationTopics' => $informationTopics, 'baseRoute' => $baseRoute])
        <!-- コメント一覧 -->
        <div class="comments-section">
            <h3>コメント一覧</h3>

            @if ($board['paginator']->count())
                @foreach($board['paginator'] as $value)
                    <?php
                        $commentAuthor = $value['isAdminRegistered'] ? 'DMM GAMES 担当者' : $data->title.' 担当者';
                        $commentContentAttr = htmlspecialchars($value['contentBody'], ENT_QUOTES, 'UTF-8');
                        $existingFilesJson = htmlspecialchars(json_encode(empty($value['uploadFileList']) ? [] : $value['uploadFileList'], JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8');
                        $commentAuthorAttr = htmlspecialchars($commentAuthor, ENT_QUOTES, 'UTF-8');
                        $commentDateAttr = htmlspecialchars($value['createdAtFormatted'], ENT_QUOTES, 'UTF-8');
                    ?>
                <div class="comment-item">
                    <div class="comment-header">
                        <i class="fa fa-thumb-tack pin-icon on" 
                            @if(!$value['isPin']) style="color: #dce1e4" @endif 
                            onclick="togglePin(this)">
                        </i>
                        <span class="comment-number">{{ $value['messageNumber'] }}</span>
                        <i class="fa fa-user"></i>
                        <span class="commenter">{{ $commentAuthor }}</span>
                        <span class="comment-date">{{ $value['createdAtFormatted'] }}</span>
                        <span class="comment-status">@if($value['updatedAt'] !== $value['createdAt']) 編集済み @endif</span>
                    </div>
                    <div class="comment-content">
                        {!! nl2br($value['contentBody']) !!}
                    </div>
                    @if($value['uploadFileList'] && count($value['uploadFileList']))
                        <div class="attachment-display">
                            @foreach($value['uploadFileList'] as $file)
                                <div class="attachment-item">
                                    <i class="fa fa-file mg-0"></i>
                                    <a href="javascript:void(0)"
                                        class="file-link fn-file-download"
                                        data-download-url="{{ $file['signedUrl'] }}"
                                        data-file-id="{{ $file['fileId'] }}"
                                        data-file-name="{{ $file['fileName'] }}"
                                    >
                                        {{ $file['fileName'] }}
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif
                    <div class="comment-action-container">
                    <!-- 返信/編集 -->
                    @if( ($value['isAdminRegistered'] && auth_is_pf() ) || !$value['isAdminRegistered'] && !auth_is_pf())
                        <a href="#editCommentModal"
                            class="comment-action-btn fn-fancybox comment-edit-btn"
                            data-mode="edit"
                            data-comment-id="{{ $value['messageId'] }}"
                            data-update-url="{{ route($baseRoute.'.board.comment.update', ['app_id' => $data->id, 'comment_id' => $value['messageId']]) }}"
                            data-comment-content="{{ $commentContentAttr }}"
                            data-existing-files="{{ $existingFilesJson }}">
                            編集
                        </a>
                    @endif
                        <a href="#editCommentModal"
                            class="comment-action-btn fn-fancybox comment-reply-btn"
                            data-mode="reply"
                            data-comment-id="{{ $value['messageId'] }}"
                            data-message-number="{{ $value['messageNumber'] }}"
                            data-reply-url="{{ route($baseRoute.'.board.comment.child', ['app_id' => $data->id, 'comment_id' => $value['messageId']]) }}"
                            data-original-comment="{{ $commentContentAttr }}"
                            data-original-files="{{ $existingFilesJson }}"
                            data-original-author="{{ $commentAuthorAttr }}"
                            data-original-date="{{ $commentDateAttr }}"
                            data-parent-message-id="{{ $value['messageId'] }}">
                            返信
                        </a>
                    </div>
                            
                    @if($value['threadPostCount'] )
                        <div
                            id="reply-summary-{{ $value['messageNumber'] }}"
                            class="reply-summary"
                            data-message-number="{{ $value['messageNumber'] }}"
                            data-app-id="{{ $data->id }}"
                            data-parent-message-id="{{ $value['messageId'] }}">
                            <i class="fa fa-caret-down"></i>
                            <span>{{ $value['threadPostCount'] }}件の返信</span>
                            <span class="reply-date">{{ $value['registerDatetime'] }}</span>
                        </div>

                        <!-- 返信コメント一覧 -->
                        <div id="reply-comments-{{ $value['messageNumber'] }}" class="reply-comments">
                        </div>
                    @endif
                        </div>
                @endforeach
            @else
                <div class="d-flex jc-center ai-center bg-lightgrey tx-16 pd-t20 pd-b20">
                    <i class="fa fa-close"></i>投稿されたコメントはありません
                </div>
            @endif
        </div>

        <div class="d-flex jc-center mg-t20">
            <a href="#editCommentModal" class="button is-primary @if($releaseStatus === 'released') is-disabled @endif comment-post-btn fn-fancybox">
                コメント投稿
            </a>
        </div>

        @if($board['paginator']->count())
            <div class="c-pager">
                <div class="d-flex fd-row jc-between">
                    <div>
                        @include('partials.paginatorperpage', ['paginator' => $board['paginator']])
                    </div>
                    <div>
                        @include('partials.paginator', ['paginator' => $board['paginator'], 'from' => $board['pagerView']['from'], 'to' => $board['pagerView']['to']])
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

{{-- ローディングモーダル --}}
<div class="modal-html hidden">
    <div style="width:auto;height:auto;overflow: auto;position:relative;">
        <div style="width:auto;height:auto;overflow: auto;position:relative;">
            <div style="width:auto;height:auto;overflow: auto;position:relative;">
                <div id="fileUploadModal" class="modal-content">
                    <div>
                        <div class="c-area mg-b0 center pd-r20 pd-l20">
                            <table class="c-table mg-b0 loading-modal">
                                <tbody>
                                    <tr class="loading-modal_tr">
                                        <td class="loading-modal_td">
                                            <p class="tx-14 center mg-b20">ファイルをアップロードしています。</p>
                                            <div class="c-loading_bar mg-b20"></div>
                                            <p class="tx-12 center">通信環境によって時間がかかる場合があります。<br>しばらくお待ちください。</p>
                                        </td>
                                    </tr>
                                </tbody>
                                <!-- /.c-table -->
                            </table>
                            <!-- /.c-area -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('Games.modals.board.edit')

{!! Form::hidden('_token', csrf_token(), ['id' => 'fn-token']) !!}
{!! Form::hidden('signed-url-path', route($baseRoute.'.board.generateUrl', $data->id), ['id' => 'fn-signed-url-path']) !!}
{!! Form::hidden('board-post-url', route($baseRoute.'.board.comment', ['app_id' => $data->id]), ['id' => 'fn-board-post-url']) !!}
{!! Form::hidden('title', $data->title, ['id' => 'title']) !!}
{!! Form::hidden('auth_is_pf', (int)auth_is_pf(), ['id' => 'auth_is_pf']) !!}

<script src="{{ cache_asset('/js/readmore.min.js') }}"></script>
<script src="{{ cache_asset('/js/apply/board.js') }}"></script>
<script src="{{ cache_asset('/js/apply/file.js') }}"></script>
